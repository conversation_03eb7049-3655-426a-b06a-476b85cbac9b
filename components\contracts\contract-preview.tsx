'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { formatDate, formatCurrency, getStatusColor } from '@/lib/utils'
import { 
  Eye, 
  Download, 
  Send, 
  Edit,
  FileText,
  Calendar,
  User,
  Building,
  DollarSign,
  Printer,
  Share2
} from 'lucide-react'
import toast from 'react-hot-toast'

interface ContractPreviewProps {
  contract: {
    id: string
    contractNumber: string
    title: string
    description: string | null
    status: string
    startDate: Date | null
    endDate: Date | null
    value: number | null
    content: string
    terms: string | null
    notes: string | null
    customer: {
      id: string
      name: string
      email: string | null
      company: string | null
      address: string | null
      city: string | null
      state: string | null
      country: string | null
      postalCode: string | null
    }
    company: {
      name: string
      email: string | null
      phone: string | null
      address: string | null
      city: string | null
      state: string | null
      country: string | null
      postalCode: string | null
    }
    createdAt: Date
    updatedAt: Date
  }
}

export function ContractPreview({ contract }: ContractPreviewProps) {
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false)
  const [isSending, setIsSending] = useState(false)

  const handleGeneratePDF = async () => {
    setIsGeneratingPDF(true)
    try {
      const response = await fetch(`/api/contracts/${contract.id}/pdf`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to generate PDF')
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.style.display = 'none'
      a.href = url
      a.download = `${contract.contractNumber}.pdf`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)

      toast.success('PDF generated successfully')
    } catch (error) {
      console.error('Error generating PDF:', error)
      toast.error('Failed to generate PDF')
    } finally {
      setIsGeneratingPDF(false)
    }
  }

  const handleSendContract = async () => {
    setIsSending(true)
    try {
      const response = await fetch(`/api/contracts/${contract.id}/send`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to send contract')
      }

      toast.success('Contract sent successfully')
      window.location.reload()
    } catch (error) {
      console.error('Error sending contract:', error)
      toast.error('Failed to send contract')
    } finally {
      setIsSending(false)
    }
  }

  const handlePrint = () => {
    window.print()
  }

  const processedContent = contract.content
    .replace(/\{\{customer\.name\}\}/g, contract.customer.name)
    .replace(/\{\{customer\.company\}\}/g, contract.customer.company || '')
    .replace(/\{\{customer\.email\}\}/g, contract.customer.email || '')
    .replace(/\{\{contract\.title\}\}/g, contract.title)
    .replace(/\{\{contract\.number\}\}/g, contract.contractNumber)
    .replace(/\{\{contract\.value\}\}/g, contract.value ? formatCurrency(contract.value) : '')
    .replace(/\{\{company\.name\}\}/g, contract.company.name)
    .replace(/\{\{today\}\}/g, formatDate(new Date()))

  return (
    <div className="space-y-6">
      {/* Preview Actions */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <Eye className="h-5 w-5" />
              <span>Contract Preview</span>
            </CardTitle>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handlePrint}
              >
                <Printer className="h-4 w-4 mr-2" />
                Print
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleGeneratePDF}
                disabled={isGeneratingPDF}
              >
                <Download className="h-4 w-4 mr-2" />
                {isGeneratingPDF ? 'Generating...' : 'Download PDF'}
              </Button>
              {contract.status === 'DRAFT' && (
                <Button
                  size="sm"
                  onClick={handleSendContract}
                  disabled={isSending}
                >
                  <Send className="h-4 w-4 mr-2" />
                  {isSending ? 'Sending...' : 'Send Contract'}
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Contract Document */}
      <Card className="print:shadow-none print:border-none">
        <CardContent className="p-8 print:p-0">
          {/* Header */}
          <div className="text-center mb-8 print:mb-6">
            <h1 className="text-3xl font-bold text-gray-900 mb-2 print:text-2xl">
              {contract.title}
            </h1>
            <p className="text-lg text-gray-600 print:text-base">
              Contract #{contract.contractNumber}
            </p>
            <Badge className={`${getStatusColor(contract.status)} mt-2 print:hidden`} variant="outline">
              {contract.status}
            </Badge>
          </div>

          {/* Contract Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8 print:mb-6">
            {/* Company Information */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4 print:text-base">
                Service Provider
              </h3>
              <div className="space-y-2 text-sm print:text-xs">
                <p className="font-medium">{contract.company.name}</p>
                {contract.company.email && <p>{contract.company.email}</p>}
                {contract.company.phone && <p>{contract.company.phone}</p>}
                {contract.company.address && (
                  <div>
                    <p>{contract.company.address}</p>
                    <p>
                      {[contract.company.city, contract.company.state, contract.company.postalCode]
                        .filter(Boolean)
                        .join(', ')}
                    </p>
                    {contract.company.country && <p>{contract.company.country}</p>}
                  </div>
                )}
              </div>
            </div>

            {/* Customer Information */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4 print:text-base">
                Client
              </h3>
              <div className="space-y-2 text-sm print:text-xs">
                <p className="font-medium">{contract.customer.name}</p>
                {contract.customer.company && <p>{contract.customer.company}</p>}
                {contract.customer.email && <p>{contract.customer.email}</p>}
                {contract.customer.address && (
                  <div>
                    <p>{contract.customer.address}</p>
                    <p>
                      {[contract.customer.city, contract.customer.state, contract.customer.postalCode]
                        .filter(Boolean)
                        .join(', ')}
                    </p>
                    {contract.customer.country && <p>{contract.customer.country}</p>}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Contract Information */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 print:mb-6">
            {contract.startDate && (
              <div>
                <h4 className="font-medium text-gray-900 mb-1">Start Date</h4>
                <p className="text-sm text-gray-600">{formatDate(contract.startDate)}</p>
              </div>
            )}
            {contract.endDate && (
              <div>
                <h4 className="font-medium text-gray-900 mb-1">End Date</h4>
                <p className="text-sm text-gray-600">{formatDate(contract.endDate)}</p>
              </div>
            )}
            {contract.value && (
              <div>
                <h4 className="font-medium text-gray-900 mb-1">Contract Value</h4>
                <p className="text-sm text-gray-600">{formatCurrency(contract.value)}</p>
              </div>
            )}
          </div>

          {/* Contract Content */}
          <div className="mb-8 print:mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 print:text-base">
              Contract Terms
            </h3>
            <div className="prose max-w-none text-sm print:text-xs">
              <div dangerouslySetInnerHTML={{ __html: processedContent.replace(/\n/g, '<br>') }} />
            </div>
          </div>

          {/* Terms and Conditions */}
          {contract.terms && (
            <div className="mb-8 print:mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 print:text-base">
                Terms and Conditions
              </h3>
              <div className="text-sm text-gray-700 print:text-xs">
                <div dangerouslySetInnerHTML={{ __html: contract.terms.replace(/\n/g, '<br>') }} />
              </div>
            </div>
          )}

          {/* Notes */}
          {contract.notes && (
            <div className="mb-8 print:mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 print:text-base">
                Additional Notes
              </h3>
              <div className="text-sm text-gray-700 print:text-xs">
                <div dangerouslySetInnerHTML={{ __html: contract.notes.replace(/\n/g, '<br>') }} />
              </div>
            </div>
          )}

          {/* Signature Section */}
          <div className="mt-12 print:mt-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h4 className="font-medium text-gray-900 mb-4">Service Provider</h4>
                <div className="border-t border-gray-300 pt-2">
                  <p className="text-sm text-gray-600">Signature</p>
                </div>
                <div className="mt-4">
                  <p className="text-sm font-medium">{contract.company.name}</p>
                  <p className="text-sm text-gray-600">Date: _______________</p>
                </div>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-4">Client</h4>
                <div className="border-t border-gray-300 pt-2">
                  <p className="text-sm text-gray-600">Signature</p>
                </div>
                <div className="mt-4">
                  <p className="text-sm font-medium">{contract.customer.name}</p>
                  <p className="text-sm text-gray-600">Date: _______________</p>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="mt-8 pt-4 border-t border-gray-200 text-center print:mt-6">
            <p className="text-xs text-gray-500">
              Generated on {formatDate(new Date())} | Contract #{contract.contractNumber}
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
