'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { 
  Shield,
  LayoutDashboard,
  Building2,
  Users,
  DollarSign,
  BarChart3,
  Settings,
  Activity,
  Database,
  Globe,
  Zap,
  AlertTriangle,
  FileText,
  Mail,
  Bell,
  Crown,
  TrendingUp,
  Server,
  Monitor,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'

const navigation = [
  {
    name: 'Dashboard',
    href: '/super-admin',
    icon: LayoutDashboard,
    description: 'Platform overview and analytics'
  },
  {
    name: 'Companies',
    href: '/super-admin/companies',
    icon: Building2,
    description: 'Manage all companies'
  },
  {
    name: 'Users',
    href: '/super-admin/users',
    icon: Users,
    description: 'Platform user management'
  },
  {
    name: 'Revenue',
    href: '/super-admin/revenue',
    icon: DollarSign,
    description: 'Financial analytics'
  },
  {
    name: 'Analytics',
    href: '/super-admin/analytics',
    icon: BarChart3,
    description: 'Platform metrics'
  },
  {
    name: 'System Health',
    href: '/super-admin/system',
    icon: Monitor,
    description: 'Server and system status'
  },
  {
    name: 'Activity Logs',
    href: '/super-admin/activity',
    icon: Activity,
    description: 'Platform activity monitoring'
  },
  {
    name: 'Settings',
    href: '/super-admin/settings',
    icon: Settings,
    description: 'Platform configuration'
  }
]

export function SuperAdminSidebar() {
  const pathname = usePathname()
  const [isCollapsed, setIsCollapsed] = useState(false)

  return (
    <div className={cn(
      "fixed left-0 top-16 h-[calc(100vh-4rem)] bg-gray-900 text-white transition-all duration-300 z-40",
      isCollapsed ? "w-16" : "w-64"
    )}>
      {/* Collapse Toggle */}
      <button
        onClick={() => setIsCollapsed(!isCollapsed)}
        className="absolute -right-3 top-6 bg-gray-900 border border-gray-700 rounded-full p-1 hover:bg-gray-800 transition-colors"
      >
        {isCollapsed ? (
          <ChevronRight className="h-4 w-4" />
        ) : (
          <ChevronLeft className="h-4 w-4" />
        )}
      </button>

      {/* Super Admin Badge */}
      <div className="p-4 border-b border-gray-700">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center">
            <Crown className="h-5 w-5 text-white" />
          </div>
          {!isCollapsed && (
            <div>
              <h2 className="text-sm font-semibold text-purple-400">Super Admin</h2>
              <p className="text-xs text-gray-400">Platform Control</p>
            </div>
          )}
        </div>
      </div>

      {/* Navigation */}
      <nav className="mt-4 px-2">
        <div className="space-y-1">
          {navigation.map((item) => {
            const isActive = pathname === item.href
            const Icon = item.icon
            
            return (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  "group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors",
                  isActive
                    ? "bg-purple-600 text-white"
                    : "text-gray-300 hover:bg-gray-800 hover:text-white"
                )}
                title={isCollapsed ? item.name : undefined}
              >
                <Icon className={cn(
                  "flex-shrink-0 h-5 w-5",
                  isActive ? "text-white" : "text-gray-400 group-hover:text-white"
                )} />
                {!isCollapsed && (
                  <span className="ml-3">{item.name}</span>
                )}
              </Link>
            )
          })}
        </div>
      </nav>

      {/* Platform Status */}
      {!isCollapsed && (
        <div className="absolute bottom-4 left-4 right-4">
          <div className="bg-gray-800 rounded-lg p-3">
            <div className="flex items-center space-x-2 mb-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-xs font-medium text-green-400">System Online</span>
            </div>
            <div className="text-xs text-gray-400">
              <div>Uptime: 99.9%</div>
              <div>Active Users: 1,247</div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
