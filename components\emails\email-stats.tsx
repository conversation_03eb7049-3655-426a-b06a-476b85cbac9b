'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card'
import { 
  Mail, 
  Send, 
  CheckCircle, 
  Eye,
  MousePointer,
  <PERSON><PERSON><PERSON>riangle,
  TrendingUp,
  TrendingDown,
  Clock,
  Users,
  FileText,
  Receipt,

  Bell
} from 'lucide-react'

interface EmailStatsProps {
  stats: {
    total: number
    sent: number
    delivered: number
    opened: number
    deliveryRate: number
    openRate: number
    typeBreakdown: Array<{
      type: string
      _count: number
    }>
    statusBreakdown: Array<{
      status: string
      _count: number
    }>
  }
}

export function EmailStats({ stats }: EmailStatsProps) {
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'QUOTATION':
        return FileText
      case 'INVOICE':
        return Receipt
      case 'CONTRACT':
        return FileText
      case 'RECEIPT':
        return CheckCircle
      case 'REMINDER':
        return Clock
      case 'NOTIFICATION':
        return Bell
      case 'MARKETING':
        return TrendingUp
      default:
        return Mail
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'QUOTATION':
        return 'text-blue-600 bg-blue-100'
      case 'INVOICE':
        return 'text-green-600 bg-green-100'
      case 'CONTRACT':
        return 'text-purple-600 bg-purple-100'
      case 'RECEIPT':
        return 'text-emerald-600 bg-emerald-100'
      case 'REMINDER':
        return 'text-orange-600 bg-orange-100'
      case 'NOTIFICATION':
        return 'text-indigo-600 bg-indigo-100'
      case 'MARKETING':
        return 'text-pink-600 bg-pink-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'SENT':
        return 'text-blue-600 bg-blue-100'
      case 'DELIVERED':
        return 'text-green-600 bg-green-100'
      case 'OPENED':
        return 'text-purple-600 bg-purple-100'
      case 'CLICKED':
        return 'text-indigo-600 bg-indigo-100'
      case 'BOUNCED':
        return 'text-red-600 bg-red-100'
      case 'FAILED':
        return 'text-red-600 bg-red-100'
      case 'PENDING':
        return 'text-yellow-600 bg-yellow-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const formatTypeName = (type: string) => {
    return type.charAt(0).toUpperCase() + type.slice(1).toLowerCase()
  }

  const formatStatusName = (status: string) => {
    return status.charAt(0).toUpperCase() + status.slice(1).toLowerCase()
  }

  const overviewCards = [
    {
      title: 'Total Emails',
      value: stats.total.toString(),
      icon: Mail,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      description: 'All emails sent'
    },
    {
      title: 'Delivery Rate',
      value: `${stats.deliveryRate}%`,
      icon: stats.deliveryRate >= 95 ? CheckCircle : stats.deliveryRate >= 85 ? TrendingUp : AlertTriangle,
      color: stats.deliveryRate >= 95 ? 'text-green-600' : stats.deliveryRate >= 85 ? 'text-yellow-600' : 'text-red-600',
      bgColor: stats.deliveryRate >= 95 ? 'bg-green-100' : stats.deliveryRate >= 85 ? 'bg-yellow-100' : 'bg-red-100',
      description: 'Successfully delivered'
    },
    {
      title: 'Open Rate',
      value: `${stats.openRate}%`,
      icon: stats.openRate >= 25 ? Eye : stats.openRate >= 15 ? TrendingUp : TrendingDown,
      color: stats.openRate >= 25 ? 'text-green-600' : stats.openRate >= 15 ? 'text-yellow-600' : 'text-red-600',
      bgColor: stats.openRate >= 25 ? 'bg-green-100' : stats.openRate >= 15 ? 'bg-yellow-100' : 'bg-red-100',
      description: 'Emails opened'
    },
    {
      title: 'Delivered',
      value: stats.delivered.toString(),
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      description: 'Successfully delivered'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {overviewCards.map((card, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {card.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${card.bgColor}`}>
                <card.icon className={`h-4 w-4 ${card.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">
                {card.value}
              </div>
              <p className="text-xs text-gray-500 mt-1">
                {card.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Detailed Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <Send className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">{stats.sent}</div>
            <div className="text-sm text-gray-600">Sent</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">{stats.delivered}</div>
            <div className="text-sm text-gray-600">Delivered</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <Eye className="h-8 w-8 text-purple-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">{stats.opened}</div>
            <div className="text-sm text-gray-600">Opened</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <MousePointer className="h-8 w-8 text-indigo-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">
              {stats.statusBreakdown.find(s => s.status === 'CLICKED')?._count || 0}
            </div>
            <div className="text-sm text-gray-600">Clicked</div>
          </CardContent>
        </Card>
      </div>

      {/* Breakdown Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Email Types */}
        <Card>
          <CardHeader>
            <CardTitle>Emails by Type</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats.typeBreakdown.map((item, index) => {
                const TypeIcon = getTypeIcon(item.type)
                const percentage = stats.total > 0 ? ((item._count / stats.total) * 100).toFixed(1) : '0'
                
                return (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded-lg ${getTypeColor(item.type)}`}>
                        <TypeIcon className="h-4 w-4" />
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">
                          {formatTypeName(item.type)}
                        </div>
                        <div className="text-sm text-gray-600">
                          {percentage}% of total
                        </div>
                      </div>
                    </div>
                    <div className="text-lg font-bold text-gray-900">
                      {item._count}
                    </div>
                  </div>
                )
              })}
              {stats.typeBreakdown.length === 0 && (
                <div className="text-center py-6 text-gray-500">
                  No emails by type
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Email Status */}
        <Card>
          <CardHeader>
            <CardTitle>Emails by Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats.statusBreakdown.map((item, index) => {
                const percentage = stats.total > 0 ? ((item._count / stats.total) * 100).toFixed(1) : '0'
                
                return (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className={`w-4 h-4 rounded-full ${getStatusColor(item.status).replace('text-', 'bg-').replace('bg-bg-', 'bg-')}`}></div>
                      <div>
                        <div className="font-medium text-gray-900">
                          {formatStatusName(item.status)}
                        </div>
                        <div className="text-sm text-gray-600">
                          {percentage}% of total
                        </div>
                      </div>
                    </div>
                    <div className="text-lg font-bold text-gray-900">
                      {item._count}
                    </div>
                  </div>
                )
              })}
              {stats.statusBreakdown.length === 0 && (
                <div className="text-center py-6 text-gray-500">
                  No emails by status
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Insights */}
      <Card>
        <CardHeader>
          <CardTitle>Performance Insights</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <TrendingUp className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="font-medium text-gray-900">Delivery Performance</div>
              <div className="text-sm text-gray-600 mt-1">
                {stats.deliveryRate >= 95 ? 'Excellent' : stats.deliveryRate >= 85 ? 'Good' : 'Needs Improvement'}
              </div>
              <div className="text-xs text-gray-500 mt-1">
                Industry average: 95%
              </div>
            </div>
            
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <Eye className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <div className="font-medium text-gray-900">Engagement Rate</div>
              <div className="text-sm text-gray-600 mt-1">
                {stats.openRate >= 25 ? 'Excellent' : stats.openRate >= 15 ? 'Good' : 'Needs Improvement'}
              </div>
              <div className="text-xs text-gray-500 mt-1">
                Industry average: 21%
              </div>
            </div>
            
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <Users className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <div className="font-medium text-gray-900">Audience Reach</div>
              <div className="text-sm text-gray-600 mt-1">
                {stats.delivered} recipients reached
              </div>
              <div className="text-xs text-gray-500 mt-1">
                Across all email campaigns
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
