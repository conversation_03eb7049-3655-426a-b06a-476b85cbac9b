import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const emailSchema = z.object({
  type: z.enum([
    'QUOTATION',
    'INVOICE', 
    'CONTRACT',
    'RECEIPT',
    'REMINDER',
    'NOTIFICATION',
    'MARKETING',
    'CUSTOM'
  ]),
  toEmail: z.string().email('Invalid email address'),
  toName: z.string().optional(),
  subject: z.string().min(1, 'Subject is required'),
  content: z.string().min(1, 'Content is required'),
  templateId: z.string().optional(),
  entityId: z.string().optional(),
  entityType: z.string().optional(),
  metadata: z.any().optional()
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') || ''
    const status = searchParams.get('status') || ''
    const recipient = searchParams.get('recipient') || ''
    const search = searchParams.get('search') || ''
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = (page - 1) * limit

    // Build where clause
    const where: any = {
      companyId: session.user.companyId
    }

    if (type) {
      where.type = type
    }

    if (status) {
      where.status = status
    }

    if (recipient) {
      where.OR = [
        { toEmail: { contains: recipient } },
        { toName: { contains: recipient } },
      ]
    }

    if (search) {
      where.OR = [
        { subject: { contains: search } },
        { toEmail: { contains: search } },
        { toName: { contains: search } },
      ]
    }

    const [emails, totalCount] = await Promise.all([
      prisma.email.findMany({
        where,
        include: {
          createdBy: {
            select: { name: true, firstName: true, lastName: true, email: true }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip: offset,
        take: limit,
      }),
      prisma.email.count({ where })
    ])

    return NextResponse.json({
      emails,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      }
    })
  } catch (error) {
    console.error('Error fetching emails:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = emailSchema.parse(body)

    // Get company information for from fields
    const company = await prisma.company.findUnique({
      where: { id: session.user.companyId }
    })

    if (!company) {
      return NextResponse.json({ error: 'Company not found' }, { status: 404 })
    }

    const email = await prisma.email.create({
      data: {
        type: validatedData.type,
        subject: validatedData.subject,
        body: validatedData.content,
        to: validatedData.toEmail,
        toEmail: validatedData.toEmail,
        from: company.email || session.user.email || '',
        status: 'PENDING',
        templateId: validatedData.templateId,
        companyId: session.user.companyId,
        createdById: session.user.id,
      },
      include: {
        createdBy: {
          select: { name: true, firstName: true, lastName: true, email: true }
        }
      }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'EMAIL',
        title: 'Email created',
        description: `Email "${email.subject}" created for ${email.toEmail}`,
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    return NextResponse.json(email, { status: 201 })
  } catch (error) {
    console.error('Error creating email:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Utility function to send email (can be called from other parts of the app)
export async function sendEmail(data: {
  type: string
  toEmail: string
  toName?: string
  subject: string
  content: string
  companyId: string
  sentById: string
  templateId?: string
  entityId?: string
  entityType?: string
  metadata?: any
}) {
  try {
    // Get company information
    const company = await prisma.company.findUnique({
      where: { id: data.companyId }
    })

    if (!company) {
      throw new Error('Company not found')
    }

    // Create email record
    const email = await prisma.email.create({
      data: {
        type: data.type as any,
        subject: data.subject,
        body: data.content,
        to: data.toEmail,
        toEmail: data.toEmail,
        from: company.email || '',
        status: 'PENDING',
        companyId: data.companyId,
        createdById: data.sentById,
      }
    })

    // TODO: Integrate with actual email service (SendGrid, AWS SES, etc.)
    // For now, we'll simulate email sending
    await simulateEmailSending(email.id)

    return email
  } catch (error) {
    console.error('Error sending email:', error)
    throw error
  }
}

// Simulate email sending (replace with actual email service integration)
async function simulateEmailSending(emailId: string) {
  try {
    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Update email status to sent
    await prisma.email.update({
      where: { id: emailId },
      data: {
        status: 'SENT',
        sentAt: new Date()
      }
    })

    // Simulate delivery after a short delay
    setTimeout(async () => {
      try {
        await prisma.email.update({
          where: { id: emailId },
          data: {
            status: 'DELIVERED',
            deliveredAt: new Date()
          }
        })

        // Simulate opening after another delay (30% chance)
        if (Math.random() < 0.3) {
          setTimeout(async () => {
            try {
              await prisma.email.update({
                where: { id: emailId },
                data: {
                  status: 'OPENED',
                  openedAt: new Date()
                }
              })
            } catch (error) {
              console.error('Error updating email open status:', error)
            }
          }, Math.random() * 60000) // Random delay up to 1 minute
        }
      } catch (error) {
        console.error('Error updating email delivery status:', error)
      }
    }, Math.random() * 10000) // Random delay up to 10 seconds

  } catch (error) {
    console.error('Error in email simulation:', error)
    
    // Mark as failed
    await prisma.email.update({
      where: { id: emailId },
      data: {
        status: 'FAILED',
        failedAt: new Date()
      }
    })
  }
}

// Utility function to send business document emails
export async function sendDocumentEmail(
  documentType: 'quotation' | 'invoice' | 'contract' | 'receipt',
  documentId: string,
  companyId: string,
  sentById: string,
  customSubject?: string,
  customContent?: string
) {
  try {
    let document: any
    let customer: any
    let emailType: string
    let defaultSubject: string
    let defaultContent: string

    // Fetch document and customer based on type
    switch (documentType) {
      case 'quotation':
        document = await prisma.quotation.findFirst({
          where: { id: documentId, companyId },
          include: { customer: true }
        })
        emailType = 'QUOTATION'
        defaultSubject = `Quotation ${document?.quotationNumber} from {{company.name}}`
        defaultContent = `Dear {{customer.name}},\n\nPlease find attached your quotation ${document?.quotationNumber}.\n\nTotal Amount: $${document?.total}\nValid Until: ${document?.validUntil ? new Date(document.validUntil).toLocaleDateString() : 'N/A'}\n\nThank you for your business!\n\nBest regards,\n{{company.name}}`
        break
        
      case 'invoice':
        document = await prisma.invoice.findFirst({
          where: { id: documentId, companyId },
          include: { customer: true }
        })
        emailType = 'INVOICE'
        defaultSubject = `Invoice ${document?.invoiceNumber} from {{company.name}}`
        defaultContent = `Dear {{customer.name}},\n\nPlease find attached your invoice ${document?.invoiceNumber}.\n\nTotal Amount: $${document?.total}\nDue Date: ${document?.dueDate ? new Date(document.dueDate).toLocaleDateString() : 'N/A'}\n\nPlease process payment at your earliest convenience.\n\nBest regards,\n{{company.name}}`
        break
        
      case 'contract':
        document = await prisma.contract.findFirst({
          where: { id: documentId, companyId },
          include: { customer: true }
        })
        emailType = 'CONTRACT'
        defaultSubject = `Contract: ${document?.title} from {{company.name}}`
        defaultContent = `Dear {{customer.name}},\n\nPlease find attached the contract "${document?.title}".\n\nContract Value: $${document?.value}\nStart Date: ${document?.startDate ? new Date(document.startDate).toLocaleDateString() : 'N/A'}\n\nPlease review and sign the contract.\n\nBest regards,\n{{company.name}}`
        break
        
      case 'receipt':
        document = await prisma.receipt.findFirst({
          where: { id: documentId, companyId },
          include: { customer: true }
        })
        emailType = 'RECEIPT'
        defaultSubject = `Payment Receipt ${document?.receiptNumber} from {{company.name}}`
        defaultContent = `Dear {{customer.name}},\n\nThank you for your payment. Please find attached your receipt ${document?.receiptNumber}.\n\nAmount Paid: $${document?.amount}\nPayment Date: ${document?.paidAt ? new Date(document.paidAt).toLocaleDateString() : 'N/A'}\n\nThank you for your business!\n\nBest regards,\n{{company.name}}`
        break
        
      default:
        throw new Error('Invalid document type')
    }

    if (!document || !document.customer) {
      throw new Error('Document or customer not found')
    }

    customer = document.customer

    // Use custom content or default
    const subject = customSubject || defaultSubject
    const content = customContent || defaultContent

    // Send email
    const email = await sendEmail({
      type: emailType,
      toEmail: customer.email,
      toName: customer.name,
      subject,
      content,
      companyId,
      sentById,
      entityId: documentId,
      entityType: documentType,
      metadata: {
        documentType,
        documentNumber: document.quotationNumber || document.invoiceNumber || document.contractNumber || document.receiptNumber,
        customerName: customer.name,
        amount: document.total || document.value || document.amount
      }
    })

    return email
  } catch (error) {
    console.error('Error sending document email:', error)
    throw error
  }
}
