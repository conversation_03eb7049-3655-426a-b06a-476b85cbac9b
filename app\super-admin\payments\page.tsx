import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { redirect } from 'next/navigation'
import { 
  CreditCard, 
  DollarSign, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Download,
  Filter,
  Search,
  Calendar,
  Building2,
  User
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { formatCurrency, formatDate } from '@/lib/utils'

export default async function SuperAdminPaymentsPage() {
  const session = await getServerSession(authOptions)
  
  // Check if user is super admin
  if (!session?.user?.isSuperAdmin && session?.user?.role !== 'SUPER_ADMIN') {
    redirect('/dashboard')
  }

  // Calculate date ranges
  const now = new Date()
  const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const startOfWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)

  // Fetch payment data
  const [
    paymentStats,
    recentPayments,
    paymentsByStatus,
    paymentsByPlan,
    failedPayments,
    topPayingCompanies
  ] = await Promise.all([
    // Payment statistics
    Promise.all([
      prisma.payment.aggregate({
        _sum: { amount: true },
        _count: true
      }),
      prisma.payment.aggregate({
        where: { createdAt: { gte: startOfDay } },
        _sum: { amount: true },
        _count: true
      }),
      prisma.payment.aggregate({
        where: { createdAt: { gte: startOfWeek } },
        _sum: { amount: true },
        _count: true
      }),
      prisma.payment.aggregate({
        where: { createdAt: { gte: startOfMonth } },
        _sum: { amount: true },
        _count: true
      })
    ]).then(([total, today, week, month]) => ({ total, today, week, month })),
    
    // Recent payments
    prisma.payment.findMany({
      take: 20,
      orderBy: { createdAt: 'desc' },
      include: {
        company: {
          select: { name: true, plan: true, email: true }
        },
        subscription: {
          select: { plan: true, interval: true }
        }
      }
    }),
    
    // Payments by status
    prisma.payment.groupBy({
      by: ['status'],
      _count: true,
      _sum: { amount: true }
    }),
    
    // Payments by plan
    prisma.payment.groupBy({
      by: ['subscriptionId'],
      _count: true,
      _sum: { amount: true }
    }).then(async (results) => {
      const subscriptionIds = results.map(r => r.subscriptionId).filter(Boolean)
      const subscriptions = await prisma.subscription.findMany({
        where: { id: { in: subscriptionIds } },
        select: { id: true, plan: true }
      })
      return results.map(result => ({
        ...result,
        plan: subscriptions.find(s => s.id === result.subscriptionId)?.plan || 'Unknown'
      }))
    }),
    
    // Failed payments
    prisma.payment.findMany({
      where: { status: 'FAILED' },
      take: 10,
      orderBy: { createdAt: 'desc' },
      include: {
        company: {
          select: { name: true, email: true }
        }
      }
    }),
    
    // Top paying companies
    prisma.payment.groupBy({
      by: ['companyId'],
      _sum: { amount: true },
      _count: true,
      orderBy: { _sum: { amount: 'desc' } },
      take: 10
    }).then(async (results) => {
      const companyIds = results.map(r => r.companyId).filter(Boolean)
      const companies = await prisma.company.findMany({
        where: { id: { in: companyIds } },
        select: { id: true, name: true, plan: true, email: true }
      })
      return results.map(result => ({
        ...result,
        company: companies.find(c => c.id === result.companyId)
      }))
    })
  ])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'SUCCEEDED':
        return 'bg-green-100 text-green-800'
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800'
      case 'FAILED':
        return 'bg-red-100 text-red-800'
      case 'CANCELLED':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'SUCCEEDED':
        return CheckCircle
      case 'PENDING':
        return RefreshCw
      case 'FAILED':
        return XCircle
      case 'CANCELLED':
        return XCircle
      default:
        return AlertTriangle
    }
  }

  const getPlanColor = (plan: string) => {
    switch (plan?.toLowerCase()) {
      case 'starter':
        return 'text-blue-600'
      case 'professional':
        return 'text-purple-600'
      case 'enterprise':
        return 'text-yellow-600'
      default:
        return 'text-gray-600'
    }
  }

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="p-3 bg-green-100 rounded-lg">
            <DollarSign className="h-8 w-8 text-green-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Payment Management</h1>
            <p className="text-gray-600">Monitor payments, transactions, and billing</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
          <Button variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Payment Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(paymentStats.total._sum.amount || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              {paymentStats.total._count.toLocaleString()} payments
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Today</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(paymentStats.today._sum.amount || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              {paymentStats.today._count} payments today
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">This Week</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(paymentStats.week._sum.amount || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              {paymentStats.week._count} payments this week
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">This Month</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(paymentStats.month._sum.amount || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              {paymentStats.month._count} payments this month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Recent Payments - Takes up 2 columns */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Recent Payments</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentPayments.map((payment) => {
                  const StatusIcon = getStatusIcon(payment.status)
                  
                  return (
                    <div key={payment.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-4">
                        <StatusIcon className={`h-5 w-5 ${
                          payment.status === 'SUCCEEDED' ? 'text-green-600' :
                          payment.status === 'PENDING' ? 'text-yellow-600' : 'text-red-600'
                        }`} />
                        
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={`/avatars/${payment.company?.email?.split('@')[0]}.svg`} />
                            <AvatarFallback className="text-xs">
                              {payment.company?.name?.charAt(0) || 'C'}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium">{payment.company?.name || 'Unknown Company'}</p>
                            <p className="text-sm text-gray-500">
                              {payment.subscription?.plan || 'Unknown'} Plan • {payment.description}
                            </p>
                          </div>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <p className="font-medium">{formatCurrency(payment.amount)}</p>
                        <div className="flex items-center space-x-2 mt-1">
                          <Badge className={getStatusColor(payment.status)} variant="outline">
                            {payment.status}
                          </Badge>
                          <span className="text-xs text-gray-500">
                            {formatDate(payment.createdAt)}
                          </span>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Sidebar - Analytics */}
        <div className="space-y-6">
          {/* Payment Status Breakdown */}
          <Card>
            <CardHeader>
              <CardTitle>Payment Status</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {paymentsByStatus.map((status) => (
                <div key={status.status} className="flex items-center justify-between">
                  <span className="text-sm font-medium">{status.status}</span>
                  <div className="flex items-center space-x-2">
                    <Badge className={getStatusColor(status.status)} variant="outline">
                      {status._count}
                    </Badge>
                    <span className="text-sm text-gray-600">
                      {formatCurrency(status._sum.amount || 0)}
                    </span>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Revenue by Plan */}
          <Card>
            <CardHeader>
              <CardTitle>Revenue by Plan</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {paymentsByPlan.slice(0, 5).map((plan) => (
                <div key={plan.plan} className="flex items-center justify-between">
                  <span className={`text-sm font-medium ${getPlanColor(plan.plan)}`}>
                    {plan.plan}
                  </span>
                  <div className="text-right">
                    <div className="text-sm font-medium">
                      {formatCurrency(plan._sum.amount || 0)}
                    </div>
                    <div className="text-xs text-gray-500">
                      {plan._count} payments
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Top Paying Companies */}
          <Card>
            <CardHeader>
              <CardTitle>Top Paying Companies</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {topPayingCompanies.slice(0, 5).map((item, index) => (
                <div key={item.companyId} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="text-xs text-gray-500">#{index + 1}</span>
                    <span className="text-sm font-medium">
                      {item.company?.name || 'Unknown Company'}
                    </span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">
                      {formatCurrency(item._sum.amount || 0)}
                    </div>
                    <div className="text-xs text-gray-500">
                      {item._count} payments
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Failed Payments Alert */}
          {failedPayments.length > 0 && (
            <Card className="border-red-200">
              <CardHeader>
                <CardTitle className="text-red-600 flex items-center">
                  <AlertTriangle className="h-5 w-5 mr-2" />
                  Failed Payments
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {failedPayments.slice(0, 3).map((payment) => (
                  <div key={payment.id} className="flex items-center justify-between p-2 bg-red-50 rounded">
                    <div>
                      <p className="text-sm font-medium">{payment.company?.name}</p>
                      <p className="text-xs text-gray-500">{formatDate(payment.createdAt)}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">{formatCurrency(payment.amount)}</p>
                      <Badge variant="outline" className="text-red-600 border-red-200">
                        Failed
                      </Badge>
                    </div>
                  </div>
                ))}
                {failedPayments.length > 3 && (
                  <Button variant="outline" size="sm" className="w-full">
                    View All Failed Payments
                  </Button>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
