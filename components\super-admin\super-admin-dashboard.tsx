'use client'

import Link from 'next/link'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { formatCurrency } from '@/lib/utils'
import { 
  Building2, 
  Users, 
  TrendingUp,
  Eye,
  Settings,
  BarChart3,
  Crown,
  Star,
  Award,
  ExternalLink
} from 'lucide-react'

interface SuperAdminDashboardProps {
  data: {
    companies: {
      total: number
      active: number
      newThisMonth: number
      growthRate: number
    }
    breakdowns: {
      companiesByPlan: Array<{
        plan: string
        _count: number
      }>
      usersByRole: Array<{
        role: string
        _count: number
      }>
    }
    topCompanies: {
      byRevenue: Array<{
        id: string
        name: string
        email: string
        plan: string
        totalRevenue: number
        _count: { users: number }
      }>
      byUsers: Array<{
        id: string
        name: string
        email: string
        plan: string
        _count: { users: number }
      }>
    }
  }
}

export function SuperAdminDashboard({ data }: SuperAdminDashboardProps) {
  const getPlanIcon = (plan: string) => {
    switch (plan) {
      case 'ENTERPRISE':
        return Crown
      case 'PROFESSIONAL':
        return Star
      case 'BASIC':
        return Award
      default:
        return Building2
    }
  }

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'ENTERPRISE':
        return 'text-purple-600 bg-purple-100'
      case 'PROFESSIONAL':
        return 'text-blue-600 bg-blue-100'
      case 'BASIC':
        return 'text-green-600 bg-green-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'OWNER':
        return 'text-purple-600 bg-purple-100'
      case 'ADMIN':
        return 'text-blue-600 bg-blue-100'
      case 'MANAGER':
        return 'text-green-600 bg-green-100'
      case 'USER':
        return 'text-gray-600 bg-gray-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <div className="space-y-6">
      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Link href="/super-admin/companies">
              <Button variant="outline" className="w-full h-20 flex flex-col items-center justify-center">
                <Building2 className="h-6 w-6 mb-2" />
                <span className="text-sm">Manage Companies</span>
              </Button>
            </Link>
            <Link href="/super-admin/users">
              <Button variant="outline" className="w-full h-20 flex flex-col items-center justify-center">
                <Users className="h-6 w-6 mb-2" />
                <span className="text-sm">Manage Users</span>
              </Button>
            </Link>
            <Link href="/super-admin/analytics">
              <Button variant="outline" className="w-full h-20 flex flex-col items-center justify-center">
                <BarChart3 className="h-6 w-6 mb-2" />
                <span className="text-sm">Analytics</span>
              </Button>
            </Link>
            <Link href="/super-admin/settings">
              <Button variant="outline" className="w-full h-20 flex flex-col items-center justify-center">
                <Settings className="h-6 w-6 mb-2" />
                <span className="text-sm">System Settings</span>
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>

      {/* Plan Distribution */}
      <Card>
        <CardHeader>
          <CardTitle>Companies by Plan</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data.breakdowns.companiesByPlan.map((plan, index) => {
              const PlanIcon = getPlanIcon(plan.plan)
              const percentage = data.companies.total > 0 
                ? ((plan._count / data.companies.total) * 100).toFixed(1)
                : '0'
              
              return (
                <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${getPlanColor(plan.plan)}`}>
                      <PlanIcon className="h-5 w-5" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">
                        {plan.plan} Plan
                      </div>
                      <div className="text-sm text-gray-600">
                        {percentage}% of companies
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-gray-900">
                      {plan._count}
                    </div>
                    <div className="text-sm text-gray-600">companies</div>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* User Roles Distribution */}
      <Card>
        <CardHeader>
          <CardTitle>Users by Role</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            {data.breakdowns.usersByRole.map((role, index) => (
              <div key={index} className="text-center p-4 bg-gray-50 rounded-lg">
                <div className={`inline-flex items-center justify-center w-10 h-10 rounded-full mb-2 ${getRoleColor(role.role)}`}>
                  <Users className="h-5 w-5" />
                </div>
                <div className="text-lg font-bold text-gray-900">
                  {role._count}
                </div>
                <div className="text-sm text-gray-600">
                  {role.role}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Top Companies by Revenue */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Top Companies by Revenue</CardTitle>
            <Link href="/super-admin/companies">
              <Button variant="ghost" >
                <Eye className="h-4 w-4 mr-2" />
                View All
              </Button>
            </Link>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {data.topCompanies.byRevenue.map((company, index) => (
              <div key={company.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
                    <span className="text-sm font-bold text-blue-600">#{index + 1}</span>
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">{company.name}</div>
                    <div className="text-sm text-gray-600">{company.email}</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-bold text-gray-900">
                    {formatCurrency(company.totalRevenue)}
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge className={getPlanColor(company.plan)} variant="outline">
                      {company.plan}
                    </Badge>
                    <span className="text-xs text-gray-500">
                      {company._count.users} users
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Top Companies by Users */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Top Companies by Users</CardTitle>
            <Link href="/super-admin/companies">
              <Button variant="ghost" >
                <Eye className="h-4 w-4 mr-2" />
                View All
              </Button>
            </Link>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {data.topCompanies.byUsers.map((company, index) => (
              <div key={company.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center justify-center w-8 h-8 bg-green-100 rounded-full">
                    <span className="text-sm font-bold text-green-600">#{index + 1}</span>
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">{company.name}</div>
                    <div className="text-sm text-gray-600">{company.email}</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-bold text-gray-900">
                    {company._count.users} users
                  </div>
                  <Badge className={getPlanColor(company.plan)} variant="outline">
                    {company.plan}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Platform Insights */}
      <Card>
        <CardHeader>
          <CardTitle>Platform Insights</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">Growth Metrics</h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Company Growth Rate</span>
                  <span className={`text-sm font-medium ${data.companies.growthRate >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {data.companies.growthRate >= 0 ? '+' : ''}{data.companies.growthRate}%
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">New Companies</span>
                  <span className="text-sm font-medium text-gray-900">
                    {data.companies.newThisMonth} this month
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Active Rate</span>
                  <span className="text-sm font-medium text-gray-900">
                    {((data.companies.active / data.companies.total) * 100).toFixed(1)}%
                  </span>
                </div>
              </div>
            </div>
            
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">Quick Stats</h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Avg. Users per Company</span>
                  <span className="text-sm font-medium text-gray-900">
                    {data.companies.total > 0 ? (data.breakdowns.usersByRole.reduce((sum, role) => sum + role._count, 0) / data.companies.total).toFixed(1) : '0'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Enterprise Adoption</span>
                  <span className="text-sm font-medium text-gray-900">
                    {data.breakdowns.companiesByPlan.find(p => p.plan === 'ENTERPRISE')?._count || 0} companies
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Admin Users</span>
                  <span className="text-sm font-medium text-gray-900">
                    {data.breakdowns.usersByRole.filter(r => ['OWNER', 'ADMIN'].includes(r.role)).reduce((sum, role) => sum + role._count, 0)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
