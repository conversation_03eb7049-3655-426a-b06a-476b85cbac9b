'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { formatCurrency, formatDate } from '@/lib/utils'
import { 
  CreditCard, 
  Calendar, 
  TrendingUp,
  Users,
  FileText,
  Receipt,
  Mail,
  AlertTriangle,
  CheckCircle,
  Crown,
  Star,
  Award
} from 'lucide-react'

interface BillingOverviewProps {
  company: any
  subscription: any
  usage: {
    monthly: {
      quotations: number
      invoices: number
      contracts: number
      emails: number
      users: number
    }
    yearly: {
      quotations: number
      invoices: number
      contracts: number
      emails: number
      users: number
    }
    total: {
      customers: number
      quotations: number
      invoices: number
      contracts: number
      users: number
    }
  }
  limits: {
    users: number
    quotations: number
    invoices: number
    contracts: number
    emails: number
    storage: string
    support: string
  }
}

export function BillingOverview({ company, subscription, usage, limits }: BillingOverviewProps) {
  const getPlanIcon = (plan: string) => {
    switch (plan) {
      case 'ENTERPRISE':
        return Crown
      case 'PROFESSIONAL':
        return Star
      case 'BASIC':
        return Award
      default:
        return CreditCard
    }
  }

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'ENTERPRISE':
        return 'text-purple-600 bg-purple-100 border-purple-200'
      case 'PROFESSIONAL':
        return 'text-blue-600 bg-blue-100 border-blue-200'
      case 'BASIC':
        return 'text-green-600 bg-green-100 border-green-200'
      default:
        return 'text-gray-600 bg-gray-100 border-gray-200'
    }
  }

  const getUsagePercentage = (used: number, limit: number) => {
    if (limit === -1) return 0 // Unlimited
    return Math.min((used / limit) * 100, 100)
  }

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-600'
    if (percentage >= 75) return 'text-yellow-600'
    return 'text-green-600'
  }

  const isNearLimit = (used: number, limit: number) => {
    if (limit === -1) return false
    return (used / limit) >= 0.8
  }

  const PlanIcon = getPlanIcon(company.plan)

  const usageMetrics = [
    {
      name: 'Users',
      used: usage.monthly.users,
      limit: limits.users,
      icon: Users,
      color: 'text-blue-600'
    },
    {
      name: 'Quotations',
      used: usage.monthly.quotations,
      limit: limits.quotations,
      icon: FileText,
      color: 'text-green-600'
    },
    {
      name: 'Invoices',
      used: usage.monthly.invoices,
      limit: limits.invoices,
      icon: Receipt,
      color: 'text-purple-600'
    },
    {
      name: 'Contracts',
      used: usage.monthly.contracts,
      limit: limits.contracts,
      icon: FileText,
      color: 'text-indigo-600'
    },
    {
      name: 'Emails',
      used: usage.monthly.emails,
      limit: limits.emails,
      icon: Mail,
      color: 'text-orange-600'
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {/* Current Plan */}
      <Card className="hover:shadow-lg transition-shadow">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium text-gray-600">
              Current Plan
            </CardTitle>
            <div className={`p-2 rounded-lg ${getPlanColor(company.plan).split(' ').slice(1).join(' ')}`}>
              <PlanIcon className={`h-4 w-4 ${getPlanColor(company.plan).split(' ')[0]}`} />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Badge className={getPlanColor(company.plan)} variant="outline">
              {company.plan}
            </Badge>
            <div className="text-2xl font-bold text-gray-900">
              {subscription?.amount ? formatCurrency(subscription.amount) : 'Free'}
            </div>
            <div className="text-sm text-gray-600">
              {subscription?.interval === 'YEARLY' ? 'per year' : 'per month'}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Next Billing Date */}
      <Card className="hover:shadow-lg transition-shadow">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium text-gray-600">
              Next Billing
            </CardTitle>
            <div className="p-2 rounded-lg bg-blue-100">
              <Calendar className="h-4 w-4 text-blue-600" />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="text-2xl font-bold text-gray-900">
              {subscription?.nextBillingDate 
                ? formatDate(subscription.nextBillingDate)
                : 'N/A'
              }
            </div>
            <div className="flex items-center space-x-1">
              {subscription?.status === 'ACTIVE' ? (
                <>
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm text-green-600">Active</span>
                </>
              ) : (
                <>
                  <AlertTriangle className="h-4 w-4 text-yellow-600" />
                  <span className="text-sm text-yellow-600">
                    {subscription?.status || 'No Subscription'}
                  </span>
                </>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Monthly Usage */}
      <Card className="hover:shadow-lg transition-shadow">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium text-gray-600">
              Monthly Usage
            </CardTitle>
            <div className="p-2 rounded-lg bg-green-100">
              <TrendingUp className="h-4 w-4 text-green-600" />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="text-2xl font-bold text-gray-900">
              {usage.monthly.quotations + usage.monthly.invoices + usage.monthly.contracts}
            </div>
            <div className="text-sm text-gray-600">
              Documents created
            </div>
            <div className="text-xs text-gray-500">
              {usage.monthly.emails} emails sent
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Storage Usage */}
      <Card className="hover:shadow-lg transition-shadow">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium text-gray-600">
              Storage
            </CardTitle>
            <div className="p-2 rounded-lg bg-purple-100">
              <CreditCard className="h-4 w-4 text-purple-600" />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="text-2xl font-bold text-gray-900">
              {limits.storage}
            </div>
            <div className="text-sm text-gray-600">
              Available storage
            </div>
            <div className="text-xs text-gray-500">
              Includes documents & files
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Usage Breakdown */}
      <Card className="md:col-span-2 lg:col-span-4">
        <CardHeader>
          <CardTitle>Usage Breakdown</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-6">
            {usageMetrics.map((metric, index) => {
              const percentage = getUsagePercentage(metric.used, metric.limit)
              const isUnlimited = metric.limit === -1
              const nearLimit = isNearLimit(metric.used, metric.limit)
              
              return (
                <div key={index} className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <metric.icon className={`h-4 w-4 ${metric.color}`} />
                    <span className="text-sm font-medium text-gray-900">
                      {metric.name}
                    </span>
                    {nearLimit && !isUnlimited && (
                      <AlertTriangle className="h-4 w-4 text-yellow-600" />
                    )}
                  </div>
                  
                  <div className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">
                        {metric.used} {isUnlimited ? '' : `/ ${metric.limit}`}
                      </span>
                      {!isUnlimited && (
                        <span className={getUsageColor(percentage)}>
                          {percentage.toFixed(0)}%
                        </span>
                      )}
                    </div>
                    
                    {!isUnlimited && (
                      <Progress 
                        value={percentage} 
                        className="h-2"
                      />
                    )}
                    
                    {isUnlimited && (
                      <div className="text-xs text-green-600 font-medium">
                        Unlimited
                      </div>
                    )}
                  </div>
                  
                  {nearLimit && !isUnlimited && (
                    <div className="text-xs text-yellow-600">
                      Approaching limit
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
