'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Crown, 
  Star, 
  Award,
  Check,
  X,
  TrendingUp,
  Users,
  FileText,
  Receipt,
  Mail,
  HardDrive,
  Headphones,
  Zap,
  Shield,
  BarChart3,
  Palette,
  Code,
  Phone
} from 'lucide-react'
import toast from 'react-hot-toast'

interface PlanComparisonProps {
  currentPlan: string
  planLimits: any
  usage: any
}

export function PlanComparison({ currentPlan, planLimits, usage }: PlanComparisonProps) {
  const [isLoading, setIsLoading] = useState<string | null>(null)

  const plans = [
    {
      name: 'BASIC',
      displayName: 'Basic',
      icon: Award,
      price: 29,
      interval: 'month',
      color: 'text-green-600 bg-green-100 border-green-200',
      popular: false,
      description: 'Perfect for small businesses getting started',
      features: [
        { name: 'Up to 5 users', included: true, icon: Users },
        { name: '50 quotations/month', included: true, icon: FileText },
        { name: '50 invoices/month', included: true, icon: Receipt },
        { name: '10 contracts/month', included: true, icon: FileText },
        { name: '100 emails/month', included: true, icon: Mail },
        { name: '1GB storage', included: true, icon: HardDrive },
        { name: 'Email support', included: true, icon: Headphones },
        { name: 'Basic analytics', included: true, icon: BarChart3 },
        { name: 'Custom branding', included: false, icon: Palette },
        { name: 'API access', included: false, icon: Code },
        { name: 'Priority support', included: false, icon: Phone },
        { name: 'Advanced analytics', included: false, icon: BarChart3 }
      ]
    },
    {
      name: 'PROFESSIONAL',
      displayName: 'Professional',
      icon: Star,
      price: 79,
      interval: 'month',
      color: 'text-blue-600 bg-blue-100 border-blue-200',
      popular: true,
      description: 'Ideal for growing businesses with advanced needs',
      features: [
        { name: 'Up to 25 users', included: true, icon: Users },
        { name: '500 quotations/month', included: true, icon: FileText },
        { name: '500 invoices/month', included: true, icon: Receipt },
        { name: '100 contracts/month', included: true, icon: FileText },
        { name: '1,000 emails/month', included: true, icon: Mail },
        { name: '10GB storage', included: true, icon: HardDrive },
        { name: 'Priority email support', included: true, icon: Headphones },
        { name: 'Advanced analytics', included: true, icon: BarChart3 },
        { name: 'Custom branding', included: true, icon: Palette },
        { name: 'API access', included: false, icon: Code },
        { name: 'Phone support', included: false, icon: Phone },
        { name: 'Dedicated account manager', included: false, icon: Shield }
      ]
    },
    {
      name: 'ENTERPRISE',
      displayName: 'Enterprise',
      icon: Crown,
      price: 199,
      interval: 'month',
      color: 'text-purple-600 bg-purple-100 border-purple-200',
      popular: false,
      description: 'For large organizations with unlimited needs',
      features: [
        { name: 'Up to 100 users', included: true, icon: Users },
        { name: 'Unlimited quotations', included: true, icon: FileText },
        { name: 'Unlimited invoices', included: true, icon: Receipt },
        { name: 'Unlimited contracts', included: true, icon: FileText },
        { name: 'Unlimited emails', included: true, icon: Mail },
        { name: '100GB storage', included: true, icon: HardDrive },
        { name: 'Phone & email support', included: true, icon: Headphones },
        { name: 'Advanced analytics', included: true, icon: BarChart3 },
        { name: 'Custom branding', included: true, icon: Palette },
        { name: 'Full API access', included: true, icon: Code },
        { name: '24/7 phone support', included: true, icon: Phone },
        { name: 'Dedicated account manager', included: true, icon: Shield }
      ]
    }
  ]

  const handleUpgrade = async (planName: string) => {
    if (planName === currentPlan) {
      toast.error('You are already on this plan')
      return
    }

    setIsLoading(planName)
    try {
      const response = await fetch('/api/billing/subscription/upgrade', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ plan: planName }),
      })

      if (!response.ok) {
        throw new Error('Failed to upgrade plan')
      }

      const { url } = await response.json()
      window.location.href = url
    } catch (error) {
      toast.error('Failed to upgrade plan')
    } finally {
      setIsLoading(null)
    }
  }

  const isCurrentPlan = (planName: string) => planName === currentPlan

  const getButtonText = (planName: string) => {
    if (isCurrentPlan(planName)) return 'Current Plan'
    
    const planOrder = ['BASIC', 'PROFESSIONAL', 'ENTERPRISE']
    const currentIndex = planOrder.indexOf(currentPlan)
    const targetIndex = planOrder.indexOf(planName)
    
    if (targetIndex > currentIndex) return 'Upgrade'
    if (targetIndex < currentIndex) return 'Downgrade'
    return 'Select Plan'
  }

  const getUsageWarning = (planName: string) => {
    const limits = planLimits[planName]
    if (!limits) return null

    const warnings = []
    
    if (limits.users !== -1 && usage.monthly.users > limits.users) {
      warnings.push(`${usage.monthly.users - limits.users} users over limit`)
    }
    if (limits.quotations !== -1 && usage.monthly.quotations > limits.quotations) {
      warnings.push(`${usage.monthly.quotations - limits.quotations} quotations over limit`)
    }
    if (limits.invoices !== -1 && usage.monthly.invoices > limits.invoices) {
      warnings.push(`${usage.monthly.invoices - limits.invoices} invoices over limit`)
    }
    if (limits.contracts !== -1 && usage.monthly.contracts > limits.contracts) {
      warnings.push(`${usage.monthly.contracts - limits.contracts} contracts over limit`)
    }
    if (limits.emails !== -1 && usage.monthly.emails > limits.emails) {
      warnings.push(`${usage.monthly.emails - limits.emails} emails over limit`)
    }

    return warnings.length > 0 ? warnings : null
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <TrendingUp className="h-5 w-5" />
          <span>Plan Comparison</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {plans.map((plan) => {
            const PlanIcon = plan.icon
            const isCurrent = isCurrentPlan(plan.name)
            const warnings = getUsageWarning(plan.name)
            
            return (
              <div 
                key={plan.name} 
                className={`relative p-6 border-2 rounded-lg transition-all ${
                  isCurrent 
                    ? plan.color 
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-blue-600 text-white">
                      Most Popular
                    </Badge>
                  </div>
                )}
                
                {isCurrent && (
                  <div className="absolute -top-3 right-4">
                    <Badge className={plan.color}>
                      Current Plan
                    </Badge>
                  </div>
                )}

                <div className="space-y-4">
                  {/* Plan Header */}
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${plan.color.split(' ').slice(1).join(' ')}`}>
                      <PlanIcon className={`h-5 w-5 ${plan.color.split(' ')[0]}`} />
                    </div>
                    <div>
                      <h3 className="font-bold text-lg text-gray-900">
                        {plan.displayName}
                      </h3>
                      <p className="text-sm text-gray-600">
                        {plan.description}
                      </p>
                    </div>
                  </div>

                  {/* Pricing */}
                  <div className="flex items-baseline space-x-1">
                    <span className="text-3xl font-bold text-gray-900">
                      ${plan.price}
                    </span>
                    <span className="text-gray-600">
                      /{plan.interval}
                    </span>
                  </div>

                  {/* Features */}
                  <div className="space-y-2">
                    {plan.features.map((feature, index) => (
                      <div key={index} className="flex items-center space-x-3">
                        {feature.included ? (
                          <Check className="h-4 w-4 text-green-600" />
                        ) : (
                          <X className="h-4 w-4 text-gray-400" />
                        )}
                        <feature.icon className={`h-4 w-4 ${feature.included ? 'text-gray-600' : 'text-gray-400'}`} />
                        <span className={`text-sm ${feature.included ? 'text-gray-900' : 'text-gray-500'}`}>
                          {feature.name}
                        </span>
                      </div>
                    ))}
                  </div>

                  {/* Usage Warnings */}
                  {warnings && (
                    <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <div className="text-sm text-yellow-800">
                        <strong>Usage Warning:</strong>
                        <ul className="mt-1 list-disc list-inside">
                          {warnings.map((warning, index) => (
                            <li key={index}>{warning}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  )}

                  {/* Action Button */}
                  <Button
                    className="w-full"
                    variant={isCurrent ? "outline" : "default"}
                    disabled={isCurrent || isLoading === plan.name}
                    onClick={() => handleUpgrade(plan.name)}
                  >
                    {isLoading === plan.name ? (
                      <>
                        <Zap className="h-4 w-4 mr-2 animate-spin" />
                        Processing...
                      </>
                    ) : (
                      getButtonText(plan.name)
                    )}
                  </Button>
                </div>
              </div>
            )
          })}
        </div>

        {/* Additional Information */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-medium text-gray-900 mb-2">Need a custom plan?</h4>
          <p className="text-sm text-gray-600 mb-3">
            Contact our sales team for enterprise solutions with custom pricing and features.
          </p>
          <Button variant="outline" >
            Contact Sales
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
