import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { LeadsTable } from '@/components/leads/leads-table'
import { LeadStats } from '@/components/leads/lead-stats'
import { LeadsPipeline } from '@/components/leads/leads-pipeline'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Plus, Search, Filter, BarChart3 } from 'lucide-react'
import Link from 'next/link'

interface SearchParams {
  search?: string
  status?: string
  priority?: string
  customerId?: string
  page?: string
  view?: string
}

export default async function LeadsPage({
  searchParams,
}: {
  searchParams: SearchParams
}) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  const search = searchParams.search || ''
  const status = searchParams.status || ''
  const priority = searchParams.priority || ''
  const customerId = searchParams.customerId || ''
  const page = parseInt(searchParams.page || '1')
  const view = searchParams.view || 'table'
  const limit = 10
  const offset = (page - 1) * limit

  // Build where clause
  const where: any = {
    companyId: session.user.companyId,
  }

  if (search) {
    where.OR = [
      { title: { contains: search } },
      { description: { contains: search } },
      { source: { contains: search } },
      { customer: { name: { contains: search } } },
    ]
  }

  if (status) {
    where.status = status
  }

  if (priority) {
    where.priority = priority
  }

  if (customerId) {
    where.customerId = customerId
  }

  // Fetch leads and stats
  const [leads, totalCount, stats, customers] = await Promise.all([
    prisma.lead.findMany({
      where,
      include: {
        customer: {
          select: { id: true, name: true, email: true, company: true }
        },
        _count: {
          select: {
            quotations: true,
            activities: true,
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      skip: offset,
      take: limit,
    }),
    prisma.lead.count({ where }),
    prisma.lead.groupBy({
      by: ['status'],
      where: { companyId: session.user.companyId },
      _count: true,
      _sum: { value: true },
    }),
    prisma.customer.findMany({
      where: { companyId: session.user.companyId },
      select: { id: true, name: true },
      orderBy: { name: 'asc' },
    })
  ])

  const totalPages = Math.ceil(totalCount / limit)

  // Calculate stats
  const leadStats = {
    total: totalCount,
    new: stats.find(s => s.status === 'NEW')?._count || 0,
    contacted: stats.find(s => s.status === 'CONTACTED')?._count || 0,
    qualified: stats.find(s => s.status === 'QUALIFIED')?._count || 0,
    proposal: stats.find(s => s.status === 'PROPOSAL')?._count || 0,
    negotiation: stats.find(s => s.status === 'NEGOTIATION')?._count || 0,
    closedWon: stats.find(s => s.status === 'CLOSED_WON')?._count || 0,
    closedLost: stats.find(s => s.status === 'CLOSED_LOST')?._count || 0,
    totalValue: stats.reduce((sum, stat) => sum + (stat._sum.value || 0), 0),
    wonValue: stats.find(s => s.status === 'CLOSED_WON')?._sum.value || 0,
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Leads</h1>
          <p className="text-gray-600 mt-1">
            Track and manage your sales pipeline
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Link href={`?view=${view === 'pipeline' ? 'table' : 'pipeline'}`}>
            <Button variant="outline">
              <BarChart3 className="h-4 w-4 mr-2" />
              {view === 'pipeline' ? 'Table View' : 'Pipeline View'}
            </Button>
          </Link>
          <Link href="/dashboard/leads/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Lead
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats */}
      <LeadStats stats={leadStats} />

      {/* Pipeline View */}
      {view === 'pipeline' && (
        <LeadsPipeline leads={leads} />
      )}

      {/* Table View */}
      {view === 'table' && (
        <>
          {/* Filters and Search */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search leads..."
                  className="pl-10"
                  defaultValue={search}
                  name="search"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                defaultValue={status}
                name="status"
              >
                <option value="">All Status</option>
                <option value="NEW">New</option>
                <option value="CONTACTED">Contacted</option>
                <option value="QUALIFIED">Qualified</option>
                <option value="PROPOSAL">Proposal</option>
                <option value="NEGOTIATION">Negotiation</option>
                <option value="CLOSED_WON">Closed Won</option>
                <option value="CLOSED_LOST">Closed Lost</option>
              </select>
              <select
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                defaultValue={priority}
                name="priority"
              >
                <option value="">All Priority</option>
                <option value="LOW">Low</option>
                <option value="MEDIUM">Medium</option>
                <option value="HIGH">High</option>
                <option value="URGENT">Urgent</option>
              </select>
              <select
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                defaultValue={customerId}
                name="customerId"
              >
                <option value="">All Customers</option>
                {customers.map((customer) => (
                  <option key={customer.id} value={customer.id}>
                    {customer.name}
                  </option>
                ))}
              </select>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>
          </div>

          {/* Leads Table */}
          <LeadsTable 
            leads={leads}
            currentPage={page}
            totalPages={totalPages}
            totalCount={totalCount}
          />
        </>
      )}
    </div>
  )
}
