import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if contract exists and belongs to the company
    const contract = await prisma.contract.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      },
      include: {
        customer: {
          select: { 
            id: true, 
            name: true, 
            email: true, 
            company: true 
          }
        }
      }
    })

    if (!contract) {
      return NextResponse.json({ error: 'Contract not found' }, { status: 404 })
    }

    // Check if contract can be sent
    if (contract.status !== 'DRAFT') {
      return NextResponse.json(
        { error: 'Only draft contracts can be sent' },
        { status: 400 }
      )
    }

    // Check if customer has email
    if (!contract.customer?.email) {
      return NextResponse.json(
        { error: 'Customer email is required to send contract' },
        { status: 400 }
      )
    }

    // Update contract status to SENT
    const updatedContract = await prisma.contract.update({
      where: { id: params.id },
      data: {
        status: 'SENT',
        sentAt: new Date(),
      },
      include: {
        customer: {
          select: { id: true, name: true, email: true, company: true }
        },
        quotation: {
          select: { id: true, quotationNumber: true, title: true }
        },
        signatures: {
          include: {
            signedBy: {
              select: { name: true, firstName: true, lastName: true }
            }
          }
        },
        _count: {
          select: {
            signatures: true,
            activities: true,
          }
        }
      }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'CONTRACT_SENT',
        title: 'Contract sent',
        description: `Contract "${contract.contractNumber}" was sent to ${contract.customer.name}`,
        contractId: contract.id,
        customerId: contract.customerId,
        quotationId: contract.quotationId,
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    // TODO: Send email to customer with contract and signature link
    // This would integrate with your email service (SendGrid, AWS SES, etc.)
    // For now, we'll just simulate the email sending
    
    try {
      // Simulate email sending
      console.log(`Sending contract ${contract.contractNumber} to ${contract.customer.email}`)
      
      // In a real implementation, you would:
      // 1. Generate PDF of the contract
      // 2. Send email with contract attachment
      // 3. Include secure signature links
      // 4. Set up signature tracking
      
    } catch (emailError) {
      console.error('Error sending email:', emailError)
      // Don't fail the request if email fails, just log it
    }

    return NextResponse.json({
      message: 'Contract sent successfully',
      contract: updatedContract
    })
  } catch (error) {
    console.error('Error sending contract:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
