import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import crypto from 'crypto'

const apiKeySchema = z.object({
  name: z.string().min(1, 'API key name is required'),
  description: z.string().optional(),
  permissions: z.array(z.string()).min(1, 'At least one permission is required'),
  expiresIn: z.number().min(1).max(730) // 1 day to 2 years
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if company has API access
    const company = await prisma.company.findUnique({
      where: { id: session.user.companyId }
    })

    if (!company || !['PROFESSIONAL', 'ENTERPRISE'].includes(company.plan)) {
      return NextResponse.json({ error: 'API access not available for your plan' }, { status: 403 })
    }

    const apiKeys = await prisma.apiKey.findMany({
      where: { companyId: session.user.companyId },
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        name: true,
        description: true,
        keyPrefix: true,
        status: true,
        permissions: true,
        lastUsedAt: true,
        expiresAt: true,
        createdAt: true
      }
    })

    return NextResponse.json({ apiKeys })
  } catch (error) {
    console.error('Error fetching API keys:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if company has API access
    const company = await prisma.company.findUnique({
      where: { id: session.user.companyId }
    })

    if (!company || !['PROFESSIONAL', 'ENTERPRISE'].includes(company.plan)) {
      return NextResponse.json({ error: 'API access not available for your plan' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = apiKeySchema.parse(body)

    // Check API key limits based on plan
    const keyLimits = {
      PROFESSIONAL: 5,
      ENTERPRISE: 20
    }

    const currentKeyCount = await prisma.apiKey.count({
      where: { companyId: session.user.companyId }
    })

    const limit = keyLimits[company.plan as keyof typeof keyLimits]
    if (currentKeyCount >= limit) {
      return NextResponse.json(
        { error: `Maximum number of API keys reached for ${company.plan} plan (${limit} keys)` },
        { status: 400 }
      )
    }

    // Generate API key
    const apiKey = generateApiKey()
    const keyPrefix = apiKey.substring(0, 8)
    const hashedKey = hashApiKey(apiKey)

    // Calculate expiration date
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + validatedData.expiresIn)

    // Create API key
    const newApiKey = await prisma.apiKey.create({
      data: {
        companyId: session.user.companyId,
        name: validatedData.name,
        description: validatedData.description,
        key: apiKey,
        keyHash: hashedKey,
        keyPrefix,
        status: 'ACTIVE',
        permissions: validatedData.permissions,
        expiresAt,
        createdById: session.user.id
      }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'API_KEY',
        title: 'API key created',
        description: `API key "${validatedData.name}" created`,
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    return NextResponse.json({
      apiKey: {
        id: newApiKey.id,
        name: newApiKey.name,
        description: newApiKey.description,
        keyPrefix: newApiKey.keyPrefix,
        status: newApiKey.status,
        permissions: newApiKey.permissions,
        expiresAt: newApiKey.expiresAt,
        createdAt: newApiKey.createdAt
      },
      // Return the full key only once
      fullKey: apiKey
    }, { status: 201 })
  } catch (error) {
    console.error('Error creating API key:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Generate a secure API key
function generateApiKey(): string {
  const prefix = 'sk_live_'
  const randomBytes = crypto.randomBytes(32).toString('hex')
  return prefix + randomBytes
}

// Hash API key for storage
function hashApiKey(apiKey: string): string {
  return crypto.createHash('sha256').update(apiKey).digest('hex')
}

// Verify API key
function verifyApiKey(providedKey: string, storedHash: string): boolean {
  const hashedProvidedKey = hashApiKey(providedKey)
  return hashedProvidedKey === storedHash
}

// Middleware function to authenticate API requests
async function authenticateApiRequest(request: NextRequest) {
  const authHeader = request.headers.get('authorization')
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null
  }

  const apiKey = authHeader.substring(7) // Remove 'Bearer ' prefix
  
  if (!apiKey.startsWith('sk_live_')) {
    return null
  }

  const keyPrefix = apiKey.substring(0, 8)
  
  try {
    const apiKeyRecord = await prisma.apiKey.findFirst({
      where: {
        keyPrefix,
        status: 'ACTIVE'
      },
      include: {
        company: true
      }
    })

    if (!apiKeyRecord) {
      return null
    }

    // Check if key is expired
    if (apiKeyRecord.expiresAt && apiKeyRecord.expiresAt < new Date()) {
      // Mark as expired
      await prisma.apiKey.update({
        where: { id: apiKeyRecord.id },
        data: { status: 'EXPIRED' }
      })
      return null
    }

    // Verify the key hash
    if (!apiKeyRecord.keyHash || !verifyApiKey(apiKey, apiKeyRecord.keyHash)) {
      return null
    }

    // Update last used timestamp
    await prisma.apiKey.update({
      where: { id: apiKeyRecord.id },
      data: { lastUsedAt: new Date() }
    })

    return {
      apiKey: apiKeyRecord,
      company: apiKeyRecord.company
    }
  } catch (error) {
    console.error('Error authenticating API key:', error)
    return null
  }
}

// Rate limiting function
async function checkRateLimit(companyId: string, plan: string): Promise<boolean> {
  const rateLimits = {
    PROFESSIONAL: {
      requestsPerMinute: 100,
      requestsPerDay: 10000,
      requestsPerMonth: 100000
    },
    ENTERPRISE: {
      requestsPerMinute: 1000,
      requestsPerDay: 100000,
      requestsPerMonth: 1000000
    }
  }

  const limits = rateLimits[plan as keyof typeof rateLimits]
  if (!limits) return false

  const now = new Date()
  const oneMinuteAgo = new Date(now.getTime() - 60 * 1000)
  const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)
  const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)

  try {
    const [minuteCount, dayCount, monthCount] = await Promise.all([
      prisma.apiRequest.count({
        where: {
          companyId,
          createdAt: { gte: oneMinuteAgo }
        }
      }),
      prisma.apiRequest.count({
        where: {
          companyId,
          createdAt: { gte: oneDayAgo }
        }
      }),
      prisma.apiRequest.count({
        where: {
          companyId,
          createdAt: { gte: oneMonthAgo }
        }
      })
    ])

    return (
      minuteCount < limits.requestsPerMinute &&
      dayCount < limits.requestsPerDay &&
      monthCount < limits.requestsPerMonth
    )
  } catch (error) {
    console.error('Error checking rate limit:', error)
    return false
  }
}

// Log API request
async function logApiRequest(data: {
  companyId: string
  apiKeyId: string
  endpoint: string
  method: string
  statusCode: number
  responseTime: number
  userAgent?: string
  ipAddress?: string
}) {
  try {
    await prisma.apiRequest.create({
      data: {
        companyId: data.companyId,
        apiKeyId: data.apiKeyId,
        endpoint: data.endpoint,
        method: data.method,
        status: data.statusCode,
        statusCode: data.statusCode,
        responseTime: data.responseTime,
        userAgent: data.userAgent,
        ipAddress: data.ipAddress
      }
    })
  } catch (error) {
    console.error('Error logging API request:', error)
  }
}
