'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { formatDate, formatTimeAgo } from '@/lib/utils'
import { 
  Ticket, 
  Search,
  Filter,
  MessageSquare,
  Clock,
  CheckCircle,
  AlertTriangle,
  XCircle,
  User,
  Calendar,
  Eye
} from 'lucide-react'
import Link from 'next/link'

interface TicketListProps {
  tickets: Array<{
    id: string
    title: string
    description: string
    status: string
    priority: string
    category: string
    createdAt: Date
    updatedAt: Date
    createdBy: {
      name: string | null
      firstName: string | null
      lastName: string | null
      email: string
    } | null
    assignedTo: {
      name: string | null
      firstName: string | null
      lastName: string | null
      email: string
    } | null
    _count: {
      messages: number
    }
  }>
  companyId: string
}

export function TicketList({ tickets, companyId }: TicketListProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [priorityFilter, setPriorityFilter] = useState('all')
  const [categoryFilter, setCategoryFilter] = useState('all')

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'OPEN':
        return AlertTriangle
      case 'IN_PROGRESS':
        return Clock
      case 'RESOLVED':
        return CheckCircle
      case 'CLOSED':
        return XCircle
      default:
        return Ticket
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'OPEN':
        return 'text-yellow-600 border-yellow-200'
      case 'IN_PROGRESS':
        return 'text-blue-600 border-blue-200'
      case 'RESOLVED':
        return 'text-green-600 border-green-200'
      case 'CLOSED':
        return 'text-gray-600 border-gray-200'
      default:
        return 'text-gray-600 border-gray-200'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT':
        return 'text-red-600 border-red-200'
      case 'HIGH':
        return 'text-orange-600 border-orange-200'
      case 'MEDIUM':
        return 'text-yellow-600 border-yellow-200'
      case 'LOW':
        return 'text-green-600 border-green-200'
      default:
        return 'text-gray-600 border-gray-200'
    }
  }

  const getUserName = (user: any) => {
    if (!user) return 'Unknown'
    if (user.name) return user.name
    if (user.firstName && user.lastName) return `${user.firstName} ${user.lastName}`
    if (user.firstName) return user.firstName
    return user.email
  }

  // Filter tickets based on search and filters
  const filteredTickets = tickets.filter(ticket => {
    const matchesSearch = ticket.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ticket.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || ticket.status === statusFilter
    const matchesPriority = priorityFilter === 'all' || ticket.priority === priorityFilter
    const matchesCategory = categoryFilter === 'all' || ticket.category === categoryFilter

    return matchesSearch && matchesStatus && matchesPriority && matchesCategory
  })

  // Get unique categories for filter
  const categories = [...new Set(tickets.map(ticket => ticket.category))]

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Ticket className="h-5 w-5" />
            <span>Support Tickets</span>
          </CardTitle>
          <div className="text-sm text-gray-600">
            {filteredTickets.length} of {tickets.length} tickets
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Filters */}
        <div className="space-y-4 mb-6">
          <div className="flex items-center space-x-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search tickets..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline" >
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="All Statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="OPEN">Open</SelectItem>
                <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
                <SelectItem value="RESOLVED">Resolved</SelectItem>
                <SelectItem value="CLOSED">Closed</SelectItem>
              </SelectContent>
            </Select>

            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger>
                <SelectValue placeholder="All Priorities" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priorities</SelectItem>
                <SelectItem value="URGENT">Urgent</SelectItem>
                <SelectItem value="HIGH">High</SelectItem>
                <SelectItem value="MEDIUM">Medium</SelectItem>
                <SelectItem value="LOW">Low</SelectItem>
              </SelectContent>
            </Select>

            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger>
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Tickets List */}
        {filteredTickets.length > 0 ? (
          <div className="space-y-4">
            {filteredTickets.map((ticket) => {
              const StatusIcon = getStatusIcon(ticket.status)
              
              return (
                <div 
                  key={ticket.id} 
                  className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-start space-x-3 flex-1">
                      <div className={`p-2 rounded-lg ${
                        ticket.status === 'OPEN' ? 'bg-yellow-100' :
                        ticket.status === 'IN_PROGRESS' ? 'bg-blue-100' :
                        ticket.status === 'RESOLVED' ? 'bg-green-100' : 'bg-gray-100'
                      }`}>
                        <StatusIcon className={`h-4 w-4 ${getStatusColor(ticket.status).split(' ')[0]}`} />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-gray-900 mb-1">
                          {ticket.title}
                        </h4>
                        <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                          {ticket.description}
                        </p>
                        <div className="flex items-center space-x-4 text-xs text-gray-500">
                          <div className="flex items-center space-x-1">
                            <User className="h-3 w-3" />
                            <span>{getUserName(ticket.createdBy)}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-3 w-3" />
                            <span>{formatTimeAgo(ticket.createdAt)}</span>
                          </div>
                          {ticket._count.messages > 0 && (
                            <div className="flex items-center space-x-1">
                              <MessageSquare className="h-3 w-3" />
                              <span>{ticket._count.messages} messages</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2 ml-4">
                      <Badge className={getStatusColor(ticket.status)} variant="outline">
                        {ticket.status.replace('_', ' ')}
                      </Badge>
                      <Badge className={getPriorityColor(ticket.priority)} variant="outline">
                        {ticket.priority}
                      </Badge>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <span>Category: {ticket.category}</span>
                      {ticket.assignedTo && (
                        <span>Assigned to: {getUserName(ticket.assignedTo)}</span>
                      )}
                      <span>Updated: {formatDate(ticket.updatedAt)}</span>
                    </div>
                    
                    <Link href={`/dashboard/support/tickets/${ticket.id}`}>
                      <Button variant="ghost" >
                        <Eye className="h-4 w-4 mr-2" />
                        View Details
                      </Button>
                    </Link>
                  </div>
                </div>
              )
            })}
          </div>
        ) : (
          <div className="text-center py-8">
            <Ticket className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="font-medium text-gray-900 mb-2">
              {tickets.length === 0 ? 'No Support Tickets' : 'No Matching Tickets'}
            </h3>
            <p className="text-gray-500 mb-6">
              {tickets.length === 0 
                ? "You haven't created any support tickets yet."
                : "Try adjusting your search or filter criteria."
              }
            </p>
            {tickets.length === 0 && (
              <Button>
                <MessageSquare className="h-4 w-4 mr-2" />
                Create Your First Ticket
              </Button>
            )}
          </div>
        )}

        {/* Load More */}
        {filteredTickets.length >= 20 && (
          <div className="text-center pt-6 border-t">
            <Button variant="outline">
              Load More Tickets
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
