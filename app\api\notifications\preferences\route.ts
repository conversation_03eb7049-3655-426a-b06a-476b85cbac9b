import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const preferencesSchema = z.object({
  userId: z.string(),
  preferences: z.object({
    emailNotifications: z.boolean().default(true),
    pushNotifications: z.boolean().default(true),
    smsNotifications: z.boolean().default(false),
    invoiceOverdue: z.boolean().default(true),
    paymentReceived: z.boolean().default(true),
    quotationAccepted: z.boolean().default(true),
    quotationExpired: z.boolean().default(true),
    contractSigned: z.boolean().default(true),
    customerCreated: z.boolean().default(false),
    leadConverted: z.boolean().default(true),
    reminders: z.boolean().default(true),
    deadlines: z.boolean().default(true),
    systemUpdates: z.boolean().default(false),
    marketingEmails: z.boolean().default(false),
    weeklyDigest: z.boolean().default(true),
    monthlyReport: z.boolean().default(true),
    instantAlerts: z.boolean().default(true),
    dailySummary: z.boolean().default(false),
    quietHoursEnabled: z.boolean().default(false),
    quietHoursStart: z.string().default('22:00'),
    quietHoursEnd: z.string().default('08:00'),
    timezone: z.string().default('UTC')
  })
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const preferences = await prisma.notificationPreference.findFirst({
      where: { userId: session.user.id }
    })

    if (!preferences) {
      // Return default preferences
      return NextResponse.json({
        userId: session.user.id,
        preferences: {
          emailNotifications: true,
          pushNotifications: true,
          smsNotifications: false,
          invoiceOverdue: true,
          paymentReceived: true,
          quotationAccepted: true,
          quotationExpired: true,
          contractSigned: true,
          customerCreated: false,
          leadConverted: true,
          reminders: true,
          deadlines: true,
          systemUpdates: false,
          marketingEmails: false,
          weeklyDigest: true,
          monthlyReport: true,
          instantAlerts: true,
          dailySummary: false,
          quietHoursEnabled: false,
          quietHoursStart: '22:00',
          quietHoursEnd: '08:00',
          timezone: 'UTC'
        }
      })
    }

    return NextResponse.json(preferences)
  } catch (error) {
    console.error('Error fetching notification preferences:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = preferencesSchema.parse(body)

    // Ensure user can only update their own preferences
    if (validatedData.userId !== session.user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const preferences = await prisma.notificationPreference.upsert({
      where: { userId: session.user.id },
      update: {
        preferences: validatedData.preferences
      },
      create: {
        userId: session.user.id,
        preferences: validatedData.preferences
      }
    })

    return NextResponse.json(preferences)
  } catch (error) {
    console.error('Error saving notification preferences:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Utility function to check if user should receive notification
export async function shouldReceiveNotification(
  userId: string,
  notificationType: string,
  channel: 'email' | 'push' | 'sms' = 'push'
): Promise<boolean> {
  try {
    const preferences = await prisma.notificationPreference.findFirst({
      where: { userId }
    })

    if (!preferences) {
      // Default behavior - allow most notifications
      return true
    }

    const prefs = (preferences.preferences as any) || {}

    // Check if the channel is enabled
    if (channel === 'email' && !prefs.emailNotifications) return false
    if (channel === 'push' && !prefs.pushNotifications) return false
    if (channel === 'sms' && !prefs.smsNotifications) return false

    // Check if the specific notification type is enabled
    const typeMapping: { [key: string]: string } = {
      'INVOICE_OVERDUE': 'invoiceOverdue',
      'PAYMENT_RECEIVED': 'paymentReceived',
      'QUOTATION_ACCEPTED': 'quotationAccepted',
      'QUOTATION_EXPIRED': 'quotationExpired',
      'CONTRACT_SIGNED': 'contractSigned',
      'CUSTOMER_CREATED': 'customerCreated',
      'LEAD_CONVERTED': 'leadConverted',
      'REMINDER': 'reminders',
      'DEADLINE': 'deadlines',
      'SYSTEM': 'systemUpdates'
    }

    const prefKey = typeMapping[notificationType]
    if (prefKey && prefs[prefKey] === false) return false

    // Check quiet hours
    if (prefs.quietHoursEnabled && channel !== 'email') {
      const now = new Date()
      const userTimezone = prefs.timezone || 'UTC'
      
      // TODO: Implement proper timezone checking
      // For now, just check if it's within quiet hours in UTC
      const currentHour = now.getUTCHours()
      const quietStart = parseInt(prefs.quietHoursStart?.split(':')[0] || '22')
      const quietEnd = parseInt(prefs.quietHoursEnd?.split(':')[0] || '8')
      
      if (quietStart > quietEnd) {
        // Quiet hours span midnight
        if (currentHour >= quietStart || currentHour < quietEnd) {
          return false
        }
      } else {
        // Quiet hours within same day
        if (currentHour >= quietStart && currentHour < quietEnd) {
          return false
        }
      }
    }

    return true
  } catch (error) {
    console.error('Error checking notification preferences:', error)
    // Default to allowing notifications if there's an error
    return true
  }
}
