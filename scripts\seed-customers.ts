import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// Professional customer data
const sampleCustomers = [
  {
    name: 'TechCorp Solutions',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'TechCorp Solutions Inc.',
    address: '123 Innovation Drive',
    city: 'Silicon Valley',
    state: 'CA',
    country: 'USA',
    postalCode: '94025',
    type: 'BUSINESS',
    status: 'ACTIVE',
    website: 'https://techcorp.com',
    notes: 'Leading technology solutions provider specializing in enterprise software development. Industry: Technology. Contact: <PERSON> (CTO) - <EMAIL>'
  },
  {
    name: 'Global Manufacturing Ltd',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Global Manufacturing Ltd.',
    address: '456 Industrial Blvd, Detroit, MI 48201',
    type: 'BUSINESS',
    status: 'ACTIVE',
    industry: 'Manufacturing',
    website: 'https://globalmanufacturing.com',
    taxId: 'GM-2024-002',
    notes: 'Large-scale manufacturing company with operations across North America.',
    contacts: [
      {
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '+****************',
        position: 'Operations Director',
        isPrimary: true
      }
    ]
  },
  {
    name: 'Creative Design Studio',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Creative Design Studio LLC',
    address: '789 Art District, New York, NY 10001',
    type: 'BUSINESS',
    status: 'ACTIVE',
    industry: 'Design & Marketing',
    website: 'https://creativedesign.com',
    taxId: 'CDS-2024-003',
    notes: 'Award-winning design studio specializing in brand identity and digital experiences.',
    contacts: [
      {
        name: 'Emma Rodriguez',
        email: '<EMAIL>',
        phone: '+****************',
        position: 'Creative Director',
        isPrimary: true
      },
      {
        name: 'David Kim',
        email: '<EMAIL>',
        phone: '+****************',
        position: 'Account Manager',
        isPrimary: false
      }
    ]
  },
  {
    name: 'HealthCare Plus',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'HealthCare Plus Medical Center',
    address: '321 Medical Plaza, Boston, MA 02101',
    type: 'BUSINESS',
    status: 'ACTIVE',
    industry: 'Healthcare',
    website: 'https://healthcareplus.com',
    taxId: 'HCP-2024-004',
    notes: 'Modern medical facility providing comprehensive healthcare services.',
    contacts: [
      {
        name: 'Dr. Lisa Thompson',
        email: '<EMAIL>',
        phone: '+****************',
        position: 'Chief Medical Officer',
        isPrimary: true
      }
    ]
  },
  {
    name: 'EcoGreen Solutions',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'EcoGreen Solutions Inc.',
    address: '654 Sustainability Way, Portland, OR 97201',
    type: 'BUSINESS',
    status: 'ACTIVE',
    industry: 'Environmental Services',
    website: 'https://ecogreen.com',
    taxId: 'EGS-2024-005',
    notes: 'Environmental consulting firm focused on sustainable business practices.',
    contacts: [
      {
        name: 'Robert Green',
        email: '<EMAIL>',
        phone: '+****************',
        position: 'Environmental Consultant',
        isPrimary: true
      }
    ]
  },
  {
    name: 'Financial Advisors Group',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Financial Advisors Group LLC',
    address: '987 Wall Street, New York, NY 10005',
    type: 'BUSINESS',
    status: 'ACTIVE',
    industry: 'Financial Services',
    website: 'https://financialadvisors.com',
    taxId: 'FAG-2024-006',
    notes: 'Premier financial advisory firm serving high-net-worth individuals and corporations.',
    contacts: [
      {
        name: 'Jennifer Walsh',
        email: '<EMAIL>',
        phone: '+****************',
        position: 'Senior Partner',
        isPrimary: true
      }
    ]
  },
  {
    name: 'Alex Martinez',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Freelance Consultant',
    address: '123 Residential St, Austin, TX 78701',
    type: 'INDIVIDUAL',
    status: 'ACTIVE',
    industry: 'Consulting',
    notes: 'Independent business consultant specializing in small business growth strategies.',
    contacts: [
      {
        name: 'Alex Martinez',
        email: '<EMAIL>',
        phone: '+****************',
        position: 'Owner',
        isPrimary: true
      }
    ]
  },
  {
    name: 'Retail Chain Enterprises',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Retail Chain Enterprises Corp.',
    address: '555 Commerce Center, Chicago, IL 60601',
    type: 'BUSINESS',
    status: 'ACTIVE',
    industry: 'Retail',
    website: 'https://retailchain.com',
    taxId: 'RCE-2024-007',
    notes: 'Multi-location retail chain with over 200 stores nationwide.',
    contacts: [
      {
        name: 'Mark Johnson',
        email: '<EMAIL>',
        phone: '+****************',
        position: 'Procurement Director',
        isPrimary: true
      },
      {
        name: 'Lisa Chen',
        email: '<EMAIL>',
        phone: '+****************',
        position: 'Regional Manager',
        isPrimary: false
      }
    ]
  },
  {
    name: 'Educational Institute',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Educational Institute of Technology',
    address: '777 Campus Drive, Cambridge, MA 02139',
    type: 'BUSINESS',
    status: 'ACTIVE',
    industry: 'Education',
    website: 'https://educationalinstitute.edu',
    taxId: 'EIT-2024-008',
    notes: 'Leading educational institution offering technology and engineering programs.',
    contacts: [
      {
        name: 'Dr. Patricia Williams',
        email: '<EMAIL>',
        phone: '+****************',
        position: 'Dean of Technology',
        isPrimary: true
      }
    ]
  },
  {
    name: 'Startup Innovations',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Startup Innovations LLC',
    address: '888 Venture Blvd, San Francisco, CA 94107',
    type: 'BUSINESS',
    status: 'PROSPECT',
    industry: 'Technology',
    website: 'https://startupinnovations.com',
    taxId: 'SI-2024-009',
    notes: 'Fast-growing startup developing innovative mobile applications.',
    contacts: [
      {
        name: 'Kevin Park',
        email: '<EMAIL>',
        phone: '+****************',
        position: 'CEO',
        isPrimary: true
      }
    ]
  },
  {
    name: 'Construction Builders Inc',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Construction Builders Inc.',
    address: '999 Builder Lane, Phoenix, AZ 85001',
    type: 'BUSINESS',
    status: 'ACTIVE',
    industry: 'Construction',
    website: 'https://constructionbuilders.com',
    taxId: 'CBI-2024-010',
    notes: 'Full-service construction company specializing in commercial and residential projects.',
    contacts: [
      {
        name: 'Tony Rodriguez',
        email: '<EMAIL>',
        phone: '+****************',
        position: 'Project Manager',
        isPrimary: true
      }
    ]
  },
  {
    name: 'Maria Garcia',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Independent Contractor',
    address: '456 Home Street, Miami, FL 33101',
    type: 'INDIVIDUAL',
    status: 'ACTIVE',
    industry: 'Professional Services',
    notes: 'Independent marketing consultant with 10+ years of experience.',
    contacts: [
      {
        name: 'Maria Garcia',
        email: '<EMAIL>',
        phone: '+****************',
        position: 'Consultant',
        isPrimary: true
      }
    ]
  },
  {
    name: 'Hospitality Group',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Hospitality Group International',
    address: '111 Resort Boulevard, Las Vegas, NV 89101',
    type: 'BUSINESS',
    status: 'ACTIVE',
    industry: 'Hospitality',
    website: 'https://hospitalitygroup.com',
    taxId: 'HGI-2024-011',
    notes: 'Luxury hotel and resort management company with properties worldwide.',
    contacts: [
      {
        name: 'Amanda Foster',
        email: '<EMAIL>',
        phone: '+****************',
        position: 'Operations Manager',
        isPrimary: true
      }
    ]
  },
  {
    name: 'Legal Associates',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Legal Associates Law Firm',
    address: '222 Justice Plaza, Washington, DC 20001',
    type: 'BUSINESS',
    status: 'ACTIVE',
    industry: 'Legal Services',
    website: 'https://legalassociates.com',
    taxId: 'LAL-2024-012',
    notes: 'Full-service law firm specializing in corporate and commercial law.',
    contacts: [
      {
        name: 'James Wilson',
        email: '<EMAIL>',
        phone: '+****************',
        position: 'Managing Partner',
        isPrimary: true
      }
    ]
  },
  {
    name: 'Transportation Logistics',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Transportation Logistics Corp.',
    address: '333 Highway Center, Atlanta, GA 30301',
    type: 'BUSINESS',
    status: 'ACTIVE',
    industry: 'Transportation',
    website: 'https://transportationlogistics.com',
    taxId: 'TLC-2024-013',
    notes: 'National transportation and logistics provider with coast-to-coast coverage.',
    contacts: [
      {
        name: 'Carlos Mendez',
        email: '<EMAIL>',
        phone: '+****************',
        position: 'Fleet Manager',
        isPrimary: true
      }
    ]
  }
]

async function seedCustomers() {
  try {
    console.log('🌱 Starting customer seeding...')

    // Get the first company for seeding
    const company = await prisma.company.findFirst({
      include: {
        users: true
      }
    })

    if (!company || !company.users.length) {
      console.log('❌ No company or users found. Please ensure you have at least one company and user.')
      return
    }

    const user = company.users[0]
    console.log(`📍 Seeding customers for company: ${company.name}`)

    let createdCount = 0
    let skippedCount = 0

    for (const customerData of sampleCustomers) {
      console.log(`👤 Processing customer: ${customerData.name}`)
      
      // Check if customer already exists
      const existingCustomer = await prisma.customer.findFirst({
        where: {
          email: customerData.email,
          companyId: company.id
        }
      })

      if (existingCustomer) {
        console.log(`  ⏭️ Customer already exists: ${customerData.name}`)
        skippedCount++
        continue
      }

      // Extract contacts from customer data
      const { contacts, ...customerInfo } = customerData

      // Create customer
      const customer = await prisma.customer.create({
        data: {
          ...customerInfo,
          companyId: company.id,
          createdById: user.id,
          isActive: true
        }
      })

      console.log(`  ✅ Created customer: ${customer.name}`)
      createdCount++

      // Create customer contacts
      if (contacts && contacts.length > 0) {
        for (const contactData of contacts) {
          await prisma.customerContact.create({
            data: {
              ...contactData,
              customerId: customer.id,
              companyId: company.id,
              createdById: user.id
            }
          })
          console.log(`    📞 Created contact: ${contactData.name}`)
        }
      }

      // Create activity log
      await prisma.activity.create({
        data: {
          type: 'NOTE',
          title: 'Customer created',
          description: `Customer "${customer.name}" was added to the system`,
          companyId: company.id,
          createdById: user.id,
          customerId: customer.id,
          metadata: {
            customerId: customer.id,
            customerName: customer.name,
            customerType: customer.type
          }
        }
      })
    }

    console.log('🎉 Customer seeding completed successfully!')
    
    // Get final statistics
    const totalCustomers = await prisma.customer.count({
      where: { companyId: company.id }
    })

    const customersByType = await prisma.customer.groupBy({
      by: ['type'],
      where: { companyId: company.id },
      _count: true
    })

    const customersByStatus = await prisma.customer.groupBy({
      by: ['status'],
      where: { companyId: company.id },
      _count: true
    })

    const totalContacts = await prisma.customerContact.count({
      where: { companyId: company.id }
    })

    console.log(`📊 Final Statistics:`)
    console.log(`  👥 Total customers: ${totalCustomers}`)
    console.log(`  ➕ Created this run: ${createdCount}`)
    console.log(`  ⏭️ Skipped (existing): ${skippedCount}`)
    console.log(`  📞 Total contacts: ${totalContacts}`)
    console.log(`  📈 By type:`)
    customersByType.forEach(stat => {
      console.log(`    ${stat.type}: ${stat._count}`)
    })
    console.log(`  📊 By status:`)
    customersByStatus.forEach(stat => {
      console.log(`    ${stat.status}: ${stat._count}`)
    })
    
  } catch (error) {
    console.error('❌ Error seeding customers:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the seeding
seedCustomers()
