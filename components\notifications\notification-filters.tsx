'use client'

import { useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Search, 
  Filter, 
  X,
  Bell,
  AlertTriangle,
  Clock,
  CheckCircle,
  Mail,
  MessageSquare,
  Calendar,
  DollarSign,
  FileText,
  Users
} from 'lucide-react'

interface NotificationFiltersProps {
  currentType: string
  currentStatus: string
  currentPriority: string
  currentSearch: string
}

export function NotificationFilters({
  currentType,
  currentStatus,
  currentPriority,
  currentSearch
}: NotificationFiltersProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [searchValue, setSearchValue] = useState(currentSearch)

  const notificationTypes = [
    { value: 'INVOICE_OVERDUE', label: 'Invoice Overdue', icon: DollarSign, color: 'text-red-600' },
    { value: 'PAYMENT_RECEIVED', label: 'Payment Received', icon: DollarSign, color: 'text-green-600' },
    { value: 'QUOTATION_ACCEPTED', label: 'Quotation Accepted', icon: FileText, color: 'text-blue-600' },
    { value: 'QUOTATION_EXPIRED', label: 'Quotation Expired', icon: FileText, color: 'text-orange-600' },
    { value: 'CONTRACT_SIGNED', label: 'Contract Signed', icon: FileText, color: 'text-blue-600' },
    { value: 'CUSTOMER_CREATED', label: 'Customer Created', icon: Users, color: 'text-purple-600' },
    { value: 'LEAD_CONVERTED', label: 'Lead Converted', icon: Users, color: 'text-green-600' },
    { value: 'REMINDER', label: 'Reminder', icon: Calendar, color: 'text-yellow-600' },
    { value: 'DEADLINE', label: 'Deadline', icon: Calendar, color: 'text-red-600' },
    { value: 'EMAIL', label: 'Email', icon: Mail, color: 'text-indigo-600' },
    { value: 'SYSTEM', label: 'System', icon: Bell, color: 'text-gray-600' }
  ]

  const statusOptions = [
    { value: 'UNREAD', label: 'Unread', icon: Clock, color: 'text-orange-600' },
    { value: 'READ', label: 'Read', icon: CheckCircle, color: 'text-green-600' }
  ]

  const priorityOptions = [
    { value: 'LOW', label: 'Low', color: 'text-gray-600' },
    { value: 'MEDIUM', label: 'Medium', color: 'text-blue-600' },
    { value: 'HIGH', label: 'High', color: 'text-red-600' }
  ]

  const updateFilters = (key: string, value: string) => {
    const params = new URLSearchParams(searchParams.toString())
    
    if (value) {
      params.set(key, value)
    } else {
      params.delete(key)
    }
    
    // Reset to first page when filters change
    params.delete('page')
    
    router.push(`/dashboard/notifications?${params.toString()}`)
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    updateFilters('search', searchValue)
  }

  const clearFilters = () => {
    setSearchValue('')
    router.push('/dashboard/notifications')
  }

  const activeFiltersCount = [currentType, currentStatus, currentPriority, currentSearch]
    .filter(Boolean).length

  return (
    <Card>
      <CardContent className="p-6">
        <div className="space-y-6">
          {/* Search */}
          <div>
            <form onSubmit={handleSearch} className="flex items-center space-x-2">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search notifications..."
                  value={searchValue}
                  onChange={(e) => setSearchValue(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button type="submit">Search</Button>
              {activeFiltersCount > 0 && (
                <Button type="button" variant="outline" onClick={clearFilters}>
                  <X className="h-4 w-4 mr-2" />
                  Clear ({activeFiltersCount})
                </Button>
              )}
            </form>
          </div>

          {/* Active Filters */}
          {activeFiltersCount > 0 && (
            <div className="flex flex-wrap gap-2">
              {currentType && (
                <Badge variant="secondary" className="flex items-center space-x-1">
                  <span>Type: {notificationTypes.find(t => t.value === currentType)?.label}</span>
                  <button
                    onClick={() => updateFilters('type', '')}
                    className="ml-1 hover:bg-gray-200 rounded-full p-0.5"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              )}
              {currentStatus && (
                <Badge variant="secondary" className="flex items-center space-x-1">
                  <span>Status: {statusOptions.find(s => s.value === currentStatus)?.label}</span>
                  <button
                    onClick={() => updateFilters('status', '')}
                    className="ml-1 hover:bg-gray-200 rounded-full p-0.5"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              )}
              {currentPriority && (
                <Badge variant="secondary" className="flex items-center space-x-1">
                  <span>Priority: {priorityOptions.find(p => p.value === currentPriority)?.label}</span>
                  <button
                    onClick={() => updateFilters('priority', '')}
                    className="ml-1 hover:bg-gray-200 rounded-full p-0.5"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              )}
              {currentSearch && (
                <Badge variant="secondary" className="flex items-center space-x-1">
                  <span>Search: "{currentSearch}"</span>
                  <button
                    onClick={() => {
                      setSearchValue('')
                      updateFilters('search', '')
                    }}
                    className="ml-1 hover:bg-gray-200 rounded-full p-0.5"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              )}
            </div>
          )}

          {/* Filter Options */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Notification Types */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                <Filter className="h-4 w-4 mr-2" />
                Notification Type
              </h4>
              <div className="space-y-2 max-h-48 overflow-y-auto">
                {notificationTypes.map((type) => {
                  const TypeIcon = type.icon
                  const isActive = currentType === type.value
                  
                  return (
                    <button
                      key={type.value}
                      onClick={() => updateFilters('type', isActive ? '' : type.value)}
                      className={`w-full flex items-center space-x-3 p-2 rounded-lg text-left transition-colors ${
                        isActive
                          ? 'bg-blue-100 border-blue-200 border'
                          : 'hover:bg-gray-50 border border-transparent'
                      }`}
                    >
                      <TypeIcon className={`h-4 w-4 ${type.color}`} />
                      <span className={`text-sm ${isActive ? 'font-medium text-blue-900' : 'text-gray-700'}`}>
                        {type.label}
                      </span>
                    </button>
                  )
                })}
              </div>
            </div>

            {/* Status */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                <CheckCircle className="h-4 w-4 mr-2" />
                Status
              </h4>
              <div className="space-y-2">
                {statusOptions.map((status) => {
                  const StatusIcon = status.icon
                  const isActive = currentStatus === status.value
                  
                  return (
                    <button
                      key={status.value}
                      onClick={() => updateFilters('status', isActive ? '' : status.value)}
                      className={`w-full flex items-center space-x-3 p-2 rounded-lg text-left transition-colors ${
                        isActive
                          ? 'bg-blue-100 border-blue-200 border'
                          : 'hover:bg-gray-50 border border-transparent'
                      }`}
                    >
                      <StatusIcon className={`h-4 w-4 ${status.color}`} />
                      <span className={`text-sm ${isActive ? 'font-medium text-blue-900' : 'text-gray-700'}`}>
                        {status.label}
                      </span>
                    </button>
                  )
                })}
              </div>
            </div>

            {/* Priority */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                <AlertTriangle className="h-4 w-4 mr-2" />
                Priority
              </h4>
              <div className="space-y-2">
                {priorityOptions.map((priority) => {
                  const isActive = currentPriority === priority.value
                  
                  return (
                    <button
                      key={priority.value}
                      onClick={() => updateFilters('priority', isActive ? '' : priority.value)}
                      className={`w-full flex items-center space-x-3 p-2 rounded-lg text-left transition-colors ${
                        isActive
                          ? 'bg-blue-100 border-blue-200 border'
                          : 'hover:bg-gray-50 border border-transparent'
                      }`}
                    >
                      <div className={`w-3 h-3 rounded-full ${
                        priority.value === 'HIGH' ? 'bg-red-500' :
                        priority.value === 'MEDIUM' ? 'bg-yellow-500' : 'bg-gray-400'
                      }`} />
                      <span className={`text-sm ${isActive ? 'font-medium text-blue-900' : 'text-gray-700'}`}>
                        {priority.label}
                      </span>
                    </button>
                  )
                })}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
