'use client'

import Link from 'next/link'
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { formatCurrency } from '@/lib/utils'
import { 
  Package, 
  ShoppingCart, 
  Wrench, 
  BarChart3,
  FileText,
  Receipt,
  Tag,
  TrendingUp
} from 'lucide-react'

interface TopItemsProps {
  items: Array<{
    id: string
    name: string
    description: string | null
    sku: string | null
    type: string
    category: string | null
    price: number
    status: string
    totalUsage: number
    quotationUsage: number
    invoiceUsage: number
    quotationItems: Array<any>
    invoiceItems: Array<any>
  }>
}

export function TopItems({ items }: TopItemsProps) {
  if (items.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Package className="h-5 w-5 text-purple-600" />
            <span>Top Items</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <Package className="h-8 w-8 text-gray-300 mx-auto mb-2" />
            <p className="text-sm text-gray-500">No item usage data available</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const totalUsage = items.reduce((sum, item) => sum + item.totalUsage, 0)

  const getTypeIcon = (type: string) => {
    return type === 'PRODUCT' ? ShoppingCart : Wrench
  }

  const getTypeColor = (type: string) => {
    return type === 'PRODUCT' ? 'text-purple-600' : 'text-indigo-600'
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Package className="h-5 w-5 text-purple-600" />
            <span>Top Items</span>
          </div>
          <span className="text-sm font-normal text-gray-500">
            {totalUsage} total usage
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {items.map((item, index) => {
            const usagePercentage = totalUsage > 0 
              ? (item.totalUsage / totalUsage) * 100 
              : 0

            const TypeIcon = getTypeIcon(item.type)

            return (
              <div key={item.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-gray-500">#{index + 1}</span>
                      <Link 
                        href={`/dashboard/items/${item.id}`}
                        className="font-medium text-purple-600 hover:text-purple-800"
                      >
                        {item.name}
                      </Link>
                      <div className="flex items-center space-x-1">
                        <TypeIcon className={`h-3 w-3 ${getTypeColor(item.type)}`} />
                        <Badge 
                          variant="outline" 
                          className={getTypeColor(item.type)}
                        >
                          {item.type}
                        </Badge>
                      </div>
                    </div>
                    
                    {item.description && (
                      <p className="text-sm text-gray-600 mt-1 truncate">
                        {item.description}
                      </p>
                    )}
                    
                    <div className="flex items-center space-x-4 mt-2">
                      {item.sku && (
                        <span className="text-xs text-gray-500">SKU: {item.sku}</span>
                      )}
                      {item.category && (
                        <div className="flex items-center space-x-1">
                          <Tag className="h-3 w-3 text-gray-400" />
                          <span className="text-xs text-gray-500">{item.category}</span>
                        </div>
                      )}
                      <Badge 
                        variant="outline" 
                        className={
                          item.status === 'ACTIVE' ? 'text-green-600 border-green-200' :
                          'text-gray-600 border-gray-200'
                        }
                      >
                        {item.status}
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="flex items-center space-x-1">
                      <BarChart3 className="h-4 w-4 text-purple-600" />
                      <span className="font-bold text-purple-600">
                        {item.totalUsage}
                      </span>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      {usagePercentage.toFixed(1)}% of usage
                    </p>
                    <p className="text-xs font-medium text-gray-900 mt-1">
                      {formatCurrency(item.price)}
                    </p>
                  </div>
                </div>
                
                <div className="mt-3 pt-3 border-t">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-2 bg-blue-50 rounded">
                      <div className="flex items-center justify-center space-x-1">
                        <FileText className="h-3 w-3 text-blue-600" />
                        <span className="text-sm font-medium text-blue-600">
                          {item.quotationUsage}
                        </span>
                      </div>
                      <p className="text-xs text-blue-600">Quotations</p>
                    </div>
                    <div className="text-center p-2 bg-green-50 rounded">
                      <div className="flex items-center justify-center space-x-1">
                        <Receipt className="h-3 w-3 text-green-600" />
                        <span className="text-sm font-medium text-green-600">
                          {item.invoiceUsage}
                        </span>
                      </div>
                      <p className="text-xs text-green-600">Invoices</p>
                    </div>
                  </div>
                </div>
                
                {/* Usage Progress Bar */}
                <div className="mt-3">
                  <div className="w-full bg-gray-200 rounded-full h-1">
                    <div 
                      className="bg-purple-600 h-1 rounded-full transition-all duration-300"
                      style={{ width: `${Math.min(usagePercentage, 100)}%` }}
                    />
                  </div>
                </div>
                
                {/* Conversion Rate */}
                {item.quotationUsage > 0 && (
                  <div className="mt-2 flex items-center justify-between text-xs">
                    <span className="text-gray-500">Conversion Rate:</span>
                    <div className="flex items-center space-x-1">
                      <TrendingUp className="h-3 w-3 text-green-600" />
                      <span className="text-green-600 font-medium">
                        {((item.invoiceUsage / item.quotationUsage) * 100).toFixed(1)}%
                      </span>
                    </div>
                  </div>
                )}
              </div>
            )
          })}
          
          {items.length === 0 && (
            <div className="text-center py-6">
              <Package className="h-8 w-8 text-gray-300 mx-auto mb-2" />
              <p className="text-sm text-gray-500">No items used yet</p>
            </div>
          )}
          
          <div className="text-center pt-4 border-t">
            <Link 
              href="/dashboard/items"
              className="text-sm text-purple-600 hover:text-purple-800"
            >
              View all items →
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
