'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { formatDate, formatTimeAgo } from '@/lib/utils'
import { 
  Mail, 
  Send, 
  CheckCircle, 
  Eye,
  MousePointer,
  AlertTriangle,
  Clock,
  FileText,
  Receipt,

  Bell,
  TrendingUp,
  ExternalLink,
  Trash2,
  MoreHorizontal,
  ChevronLeft,
  ChevronRight,
  User,
  Calendar,
  Paperclip
} from 'lucide-react'
import toast from 'react-hot-toast'

interface EmailData {
  id: string
  type: string
  subject: string
  toEmail: string
  toName: string | null
  fromEmail: string
  fromName: string | null
  status: string
  sentAt: Date | null
  deliveredAt: Date | null
  openedAt: Date | null
  clickedAt: Date | null
  bouncedAt: Date | null
  failedAt: Date | null
  createdAt: Date
  metadata: any
  attachments: any[]
  sentBy: {
    name: string | null
    firstName: string | null
    lastName: string | null
    email: string
  } | null
}

interface EmailsListProps {
  emails: EmailData[]
  currentPage: number
  totalPages: number
  totalCount: number
}

export function EmailsList({ 
  emails, 
  currentPage, 
  totalPages, 
  totalCount 
}: EmailsListProps) {
  const [deleting, setDeleting] = useState<string | null>(null)

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'QUOTATION':
        return FileText
      case 'INVOICE':
        return Receipt
      case 'CONTRACT':
        return FileText
      case 'RECEIPT':
        return CheckCircle
      case 'REMINDER':
        return Clock
      case 'NOTIFICATION':
        return Bell
      case 'MARKETING':
        return TrendingUp
      default:
        return Mail
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'QUOTATION':
        return 'text-blue-600 bg-blue-100'
      case 'INVOICE':
        return 'text-green-600 bg-green-100'
      case 'CONTRACT':
        return 'text-purple-600 bg-purple-100'
      case 'RECEIPT':
        return 'text-emerald-600 bg-emerald-100'
      case 'REMINDER':
        return 'text-orange-600 bg-orange-100'
      case 'NOTIFICATION':
        return 'text-indigo-600 bg-indigo-100'
      case 'MARKETING':
        return 'text-pink-600 bg-pink-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'SENT':
        return Send
      case 'DELIVERED':
        return CheckCircle
      case 'OPENED':
        return Eye
      case 'CLICKED':
        return MousePointer
      case 'BOUNCED':
      case 'FAILED':
        return AlertTriangle
      case 'PENDING':
        return Clock
      default:
        return Mail
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'SENT':
        return 'text-blue-600 border-blue-200'
      case 'DELIVERED':
        return 'text-green-600 border-green-200'
      case 'OPENED':
        return 'text-purple-600 border-purple-200'
      case 'CLICKED':
        return 'text-indigo-600 border-indigo-200'
      case 'BOUNCED':
      case 'FAILED':
        return 'text-red-600 border-red-200'
      case 'PENDING':
        return 'text-yellow-600 border-yellow-200'
      default:
        return 'text-gray-600 border-gray-200'
    }
  }

  const formatTypeName = (type: string) => {
    return type.charAt(0).toUpperCase() + type.slice(1).toLowerCase()
  }

  const getUserName = (user: any) => {
    if (!user) return 'System'
    if (user.name) return user.name
    if (user.firstName && user.lastName) return `${user.firstName} ${user.lastName}`
    if (user.firstName) return user.firstName
    return user.email
  }

  const getRecipientName = (email: EmailData) => {
    return email.toName || email.toEmail
  }

  const handleDelete = async (emailId: string) => {
    if (!confirm('Are you sure you want to delete this email record?')) {
      return
    }

    setDeleting(emailId)
    try {
      const response = await fetch(`/api/emails/${emailId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to delete email')
      }

      toast.success('Email deleted successfully')
      window.location.reload()
    } catch (error) {
      toast.error('Failed to delete email')
    } finally {
      setDeleting(null)
    }
  }

  const getLatestTimestamp = (email: EmailData) => {
    if (email.clickedAt) return { time: email.clickedAt, label: 'Clicked' }
    if (email.openedAt) return { time: email.openedAt, label: 'Opened' }
    if (email.deliveredAt) return { time: email.deliveredAt, label: 'Delivered' }
    if (email.sentAt) return { time: email.sentAt, label: 'Sent' }
    if (email.bouncedAt) return { time: email.bouncedAt, label: 'Bounced' }
    if (email.failedAt) return { time: email.failedAt, label: 'Failed' }
    return { time: email.createdAt, label: 'Created' }
  }

  if (emails.length === 0) {
    return (
      <Card>
        <CardContent className="py-12">
          <div className="text-center">
            <Mail className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No emails found</h3>
            <p className="text-gray-500 mb-6">
              No emails match your current filters. Try adjusting your search criteria.
            </p>
            <Link href="/dashboard/emails/compose">
              <Button>
                Compose Email
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {/* Emails List */}
      <div className="space-y-3">
        {emails.map((email) => {
          const TypeIcon = getTypeIcon(email.type)
          const StatusIcon = getStatusIcon(email.status)
          const latestEvent = getLatestTimestamp(email)
          
          return (
            <Card key={email.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4 flex-1">
                    {/* Type Icon */}
                    <div className={`p-2 rounded-lg ${getTypeColor(email.type)} flex-shrink-0`}>
                      <TypeIcon className="h-5 w-5" />
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="font-medium text-gray-900 truncate">
                          {email.subject}
                        </h4>
                        <Badge className={getStatusColor(email.status)} variant="outline">
                          {email.status}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {formatTypeName(email.type)}
                        </Badge>
                        {email.attachments && email.attachments.length > 0 && (
                          <Paperclip className="h-4 w-4 text-gray-400" />
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-4 text-sm text-gray-600 mb-2">
                        <div className="flex items-center space-x-1">
                          <User className="h-4 w-4" />
                          <span>To: {getRecipientName(email)}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Mail className="h-4 w-4" />
                          <span>From: {email.fromName || email.fromEmail}</span>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <div className="flex items-center space-x-1">
                          <StatusIcon className="h-3 w-3" />
                          <span>{latestEvent.label}: {formatTimeAgo(latestEvent.time)}</span>
                        </div>
                        <span>•</span>
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3" />
                          <span>Created: {formatDate(email.createdAt)}</span>
                        </div>
                        {email.sentBy && (
                          <>
                            <span>•</span>
                            <span>By: {getUserName(email.sentBy)}</span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center space-x-2 flex-shrink-0">
                    <Link href={`/dashboard/emails/${email.id}`}>
                      <Button variant="ghost" size="sm" className="text-blue-600">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </Link>
                    
                    {email.metadata?.entityUrl && (
                      <Link href={email.metadata.entityUrl}>
                        <Button variant="ghost" size="sm" className="text-green-600">
                          <ExternalLink className="h-4 w-4" />
                        </Button>
                      </Link>
                    )}
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(email.id)}
                      disabled={deleting === email.id}
                      className="text-red-600"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* Email Timeline */}
                {(email.sentAt || email.deliveredAt || email.openedAt || email.clickedAt) && (
                  <div className="mt-3 pt-3 border-t border-gray-100">
                    <div className="flex items-center space-x-6 text-xs text-gray-500">
                      {email.sentAt && (
                        <div className="flex items-center space-x-1">
                          <Send className="h-3 w-3 text-blue-600" />
                          <span>Sent: {formatTimeAgo(email.sentAt)}</span>
                        </div>
                      )}
                      {email.deliveredAt && (
                        <div className="flex items-center space-x-1">
                          <CheckCircle className="h-3 w-3 text-green-600" />
                          <span>Delivered: {formatTimeAgo(email.deliveredAt)}</span>
                        </div>
                      )}
                      {email.openedAt && (
                        <div className="flex items-center space-x-1">
                          <Eye className="h-3 w-3 text-purple-600" />
                          <span>Opened: {formatTimeAgo(email.openedAt)}</span>
                        </div>
                      )}
                      {email.clickedAt && (
                        <div className="flex items-center space-x-1">
                          <MousePointer className="h-3 w-3 text-indigo-600" />
                          <span>Clicked: {formatTimeAgo(email.clickedAt)}</span>
                        </div>
                      )}
                      {(email.bouncedAt || email.failedAt) && (
                        <div className="flex items-center space-x-1">
                          <AlertTriangle className="h-3 w-3 text-red-600" />
                          <span>
                            {email.bouncedAt ? `Bounced: ${formatTimeAgo(email.bouncedAt)}` : 
                             email.failedAt ? `Failed: ${formatTimeAgo(email.failedAt)}` : ''}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Metadata */}
                {email.metadata && Object.keys(email.metadata).length > 0 && (
                  <div className="mt-3 pt-3 border-t border-gray-100">
                    <div className="text-xs text-gray-500">
                      {email.metadata.entityType && email.metadata.entityId && (
                        <span>Related to: {email.metadata.entityType} #{email.metadata.entityId}</span>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                Showing {((currentPage - 1) * 20) + 1} to {Math.min(currentPage * 20, totalCount)} of {totalCount} emails
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  disabled={currentPage <= 1}
                  asChild
                >
                  <Link href={`?page=${currentPage - 1}`}>
                    <ChevronLeft className="h-4 w-4" />
                    Previous
                  </Link>
                </Button>
                
                <div className="flex items-center space-x-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const pageNum = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i
                    const isActive = pageNum === currentPage
                    
                    return (
                      <Button
                        key={pageNum}
                        variant={isActive ? "default" : "outline"}
                        size="sm"
                        asChild
                      >
                        <Link href={`?page=${pageNum}`}>
                          {pageNum}
                        </Link>
                      </Button>
                    )
                  })}
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  disabled={currentPage >= totalPages}
                  asChild
                >
                  <Link href={`?page=${currentPage + 1}`}>
                    Next
                    <ChevronRight className="h-4 w-4" />
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
