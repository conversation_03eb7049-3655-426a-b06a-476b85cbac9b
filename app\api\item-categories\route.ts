import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const categorySchema = z.object({
  name: z.string().min(1, 'Category name is required'),
  description: z.string().optional(),
  color: z.string().optional(),
  icon: z.string().optional(),
  parentId: z.string().optional(),
  isActive: z.boolean().default(true)
})

// GET /api/item-categories - List categories
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search') || ''
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = (page - 1) * limit

    const where: any = {
      companyId: session.user.companyId,
    }

    if (search) {
      where.OR = [
        { name: { contains: search } },
        { description: { contains: search } },
      ]
    }

    const [categories, totalCount] = await Promise.all([
      prisma.itemCategory.findMany({
        where,
        include: {
          createdBy: {
            select: { name: true, firstName: true, lastName: true }
          },
          parent: {
            select: { id: true, name: true }
          },
          children: {
            select: { id: true, name: true }
          },
          _count: {
            select: {
              items: true,
              children: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip: offset,
        take: limit,
      }),
      prisma.itemCategory.count({ where })
    ])

    return NextResponse.json({
      categories,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit)
      }
    })

  } catch (error) {
    console.error('Error fetching categories:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/item-categories - Create category
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = categorySchema.parse(body)

    // Check if category name already exists
    const existingCategory = await prisma.itemCategory.findFirst({
      where: {
        name: validatedData.name,
        companyId: session.user.companyId
      }
    })

    if (existingCategory) {
      return NextResponse.json(
        { error: 'Category name already exists' },
        { status: 400 }
      )
    }

    // Validate parent category if provided
    if (validatedData.parentId) {
      const parentCategory = await prisma.itemCategory.findFirst({
        where: {
          id: validatedData.parentId,
          companyId: session.user.companyId
        }
      })

      if (!parentCategory) {
        return NextResponse.json(
          { error: 'Parent category not found' },
          { status: 400 }
        )
      }
    }

    // Create category
    const category = await prisma.itemCategory.create({
      data: {
        ...validatedData,
        companyId: session.user.companyId,
        createdById: session.user.id,
      },
      include: {
        createdBy: {
          select: { name: true, firstName: true, lastName: true }
        },
        parent: {
          select: { id: true, name: true }
        },
        _count: {
          select: {
            items: true,
            children: true
          }
        }
      }
    })

    // Log activity
    await prisma.activity.create({
      data: {
        type: 'NOTE',
        title: 'Item category created',
        description: `Item category "${category.name}" was created`,
        companyId: session.user.companyId,
        createdById: session.user.id,
        metadata: {
          categoryId: category.id,
          categoryName: category.name
        }
      }
    })

    return NextResponse.json(category, { status: 201 })

  } catch (error) {
    console.error('Error creating category:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/item-categories?id=xxx - Update category
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const categoryId = searchParams.get('id')

    if (!categoryId) {
      return NextResponse.json(
        { error: 'Category ID is required' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const validatedData = categorySchema.parse(body)

    // Check if category exists and belongs to company
    const existingCategory = await prisma.itemCategory.findFirst({
      where: {
        id: categoryId,
        companyId: session.user.companyId
      }
    })

    if (!existingCategory) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      )
    }

    // Check if name is unique (excluding current category)
    if (validatedData.name !== existingCategory.name) {
      const nameExists = await prisma.itemCategory.findFirst({
        where: {
          name: validatedData.name,
          companyId: session.user.companyId,
          id: { not: categoryId }
        }
      })

      if (nameExists) {
        return NextResponse.json(
          { error: 'Category name already exists' },
          { status: 400 }
        )
      }
    }

    // Validate parent category if provided
    if (validatedData.parentId && validatedData.parentId !== existingCategory.parentId) {
      if (validatedData.parentId === categoryId) {
        return NextResponse.json(
          { error: 'Category cannot be its own parent' },
          { status: 400 }
        )
      }

      const parentCategory = await prisma.itemCategory.findFirst({
        where: {
          id: validatedData.parentId,
          companyId: session.user.companyId
        }
      })

      if (!parentCategory) {
        return NextResponse.json(
          { error: 'Parent category not found' },
          { status: 400 }
        )
      }
    }

    // Update category
    const category = await prisma.itemCategory.update({
      where: { id: categoryId },
      data: validatedData,
      include: {
        createdBy: {
          select: { name: true, firstName: true, lastName: true }
        },
        parent: {
          select: { id: true, name: true }
        },
        _count: {
          select: {
            items: true,
            children: true
          }
        }
      }
    })

    // Log activity
    await prisma.activity.create({
      data: {
        type: 'NOTE',
        title: 'Item category updated',
        description: `Item category "${category.name}" was updated`,
        companyId: session.user.companyId,
        createdById: session.user.id,
        metadata: {
          categoryId: category.id,
          categoryName: category.name
        }
      }
    })

    return NextResponse.json(category)

  } catch (error) {
    console.error('Error updating category:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/item-categories?id=xxx - Delete category
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const categoryId = searchParams.get('id')

    if (!categoryId) {
      return NextResponse.json(
        { error: 'Category ID is required' },
        { status: 400 }
      )
    }

    // Check if category exists and belongs to company
    const category = await prisma.itemCategory.findFirst({
      where: {
        id: categoryId,
        companyId: session.user.companyId
      },
      include: {
        _count: {
          select: {
            items: true,
            children: true
          }
        }
      }
    })

    if (!category) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      )
    }

    // Check if category has items or subcategories
    if (category._count.items > 0) {
      return NextResponse.json(
        { error: 'Cannot delete category with items. Please move or delete items first.' },
        { status: 400 }
      )
    }

    if (category._count.children > 0) {
      return NextResponse.json(
        { error: 'Cannot delete category with subcategories. Please move or delete subcategories first.' },
        { status: 400 }
      )
    }

    // Delete category
    await prisma.itemCategory.delete({
      where: { id: categoryId }
    })

    // Log activity
    await prisma.activity.create({
      data: {
        type: 'NOTE',
        title: 'Item category deleted',
        description: `Item category "${category.name}" was deleted`,
        companyId: session.user.companyId,
        createdById: session.user.id,
        metadata: {
          categoryId: category.id,
          categoryName: category.name
        }
      }
    })

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Error deleting category:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
