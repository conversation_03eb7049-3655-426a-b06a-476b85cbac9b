import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { NotificationsList } from '@/components/notifications/notifications-list'
import { NotificationStats } from '@/components/notifications/notification-stats'
import { NotificationFilters } from '@/components/notifications/notification-filters'
import { Button } from '@/components/ui/button'
import { Bell, Settings, CheckCheck, Trash2, Filter } from 'lucide-react'
import Link from 'next/link'

interface SearchParams {
  type?: string
  status?: string
  priority?: string
  page?: string
  search?: string
}

export default async function NotificationsPage({
  searchParams,
}: {
  searchParams: SearchParams
}) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  const type = searchParams.type || ''
  const status = searchParams.status || ''
  const priority = searchParams.priority || ''
  const search = searchParams.search || ''
  const page = parseInt(searchParams.page || '1')
  const limit = 20
  const offset = (page - 1) * limit

  // Build where clause for notifications
  const where: any = {
    OR: [
      { companyId: session.user.companyId },
      { userId: session.user.id }
    ]
  }

  if (type) {
    where.type = type
  }

  if (status) {
    where.status = status
  }

  if (priority) {
    where.priority = priority
  }

  if (search) {
    where.OR = [
      { title: { contains: search } },
      { message: { contains: search } },
    ]
  }

  // Fetch notifications and statistics
  const [notifications, totalCount, stats] = await Promise.all([
    prisma.notification.findMany({
      where,
      include: {
        user: {
          select: { name: true, firstName: true, lastName: true, email: true }
        },
        createdBy: {
          select: { name: true, firstName: true, lastName: true }
        }
      },
      orderBy: [
        { priority: 'desc' },
        { createdAt: 'desc' }
      ],
      skip: offset,
      take: limit,
    }),
    prisma.notification.count({ where }),
    
    // Get notification statistics
    Promise.all([
      prisma.notification.count({
        where: {
          OR: [
            { companyId: session.user.companyId },
            { userId: session.user.id }
          ],
          status: 'UNREAD'
        }
      }),
      prisma.notification.count({
        where: {
          OR: [
            { companyId: session.user.companyId },
            { userId: session.user.id }
          ],
          priority: 'HIGH'
        }
      }),
      prisma.notification.groupBy({
        by: ['type'],
        where: {
          OR: [
            { companyId: session.user.companyId },
            { userId: session.user.id }
          ]
        },
        _count: true
      }),
      prisma.notification.groupBy({
        by: ['status'],
        where: {
          OR: [
            { companyId: session.user.companyId },
            { userId: session.user.id }
          ]
        },
        _count: true
      })
    ])
  ])

  const [unreadCount, highPriorityCount, typeBreakdown, statusBreakdown] = stats

  const notificationStats = {
    total: totalCount,
    unread: unreadCount,
    highPriority: highPriorityCount,
    typeBreakdown,
    statusBreakdown
  }

  const totalPages = Math.ceil(totalCount / limit)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Bell className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Notifications</h1>
            <p className="text-gray-600 mt-1">
              Manage your alerts and system notifications
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" >
            <CheckCheck className="h-4 w-4 mr-2" />
            Mark All Read
          </Button>
          <Button variant="outline" >
            <Trash2 className="h-4 w-4 mr-2" />
            Clear All
          </Button>
          <Link href="/dashboard/notifications/settings">
            <Button variant="outline" >
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
          </Link>
        </div>
      </div>

      {/* Statistics */}
      <NotificationStats stats={notificationStats} />

      {/* Filters */}
      <NotificationFilters 
        currentType={type}
        currentStatus={status}
        currentPriority={priority}
        currentSearch={search}
      />

      {/* Notifications List */}
      <NotificationsList 
        notifications={notifications}
        currentPage={page}
        totalPages={totalPages}
        totalCount={totalCount}
      />
    </div>
  )
}
