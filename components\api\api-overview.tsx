'use client'

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Code, 
  Key, 
  Webhook,
  Activity,
  TrendingUp,
  TrendingDown,
  AlertT<PERSON>gle,
  CheckCircle,
  Clock,
  Zap
} from 'lucide-react'

interface ApiOverviewProps {
  company: any
  usage: {
    monthlyRequests: number
    dailyRequests: number
    totalKeys: number
    activeKeys: number
    totalWebhooks: number
    activeWebhooks: number
    topEndpoints: Array<{
      endpoint: string
      _count: number
    }>
    statusCodes: Array<{
      statusCode: number
      _count: number
    }>
  }
  limits: {
    requestsPerMinute: number
    requestsPerDay: number
    requestsPerMonth: number
  }
}

export function ApiOverview({ company, usage, limits }: ApiOverviewProps) {
  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'ENTERPRISE':
        return 'text-purple-600 bg-purple-100 border-purple-200'
      case 'PROFESSIONAL':
        return 'text-blue-600 bg-blue-100 border-blue-200'
      default:
        return 'text-gray-600 bg-gray-100 border-gray-200'
    }
  }

  const getUsagePercentage = (used: number, limit: number) => {
    return Math.min((used / limit) * 100, 100)
  }

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-600'
    if (percentage >= 75) return 'text-yellow-600'
    return 'text-green-600'
  }

  const dailyUsagePercentage = getUsagePercentage(usage.dailyRequests, limits.requestsPerDay)
  const monthlyUsagePercentage = getUsagePercentage(usage.monthlyRequests, limits.requestsPerMonth)

  // Calculate success rate
  const totalRequests = usage.statusCodes.reduce((sum, status) => sum + status._count, 0)
  const successfulRequests = usage.statusCodes
    .filter(status => status.statusCode >= 200 && status.statusCode < 300)
    .reduce((sum, status) => sum + status._count, 0)
  const successRate = totalRequests > 0 ? (successfulRequests / totalRequests * 100).toFixed(1) : '0'

  const overviewCards = [
    {
      title: 'API Plan',
      value: company.plan,
      icon: Code,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      description: 'Current subscription plan'
    },
    {
      title: 'Monthly Requests',
      value: usage.monthlyRequests.toLocaleString(),
      icon: Activity,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      description: `${monthlyUsagePercentage.toFixed(1)}% of limit used`
    },
    {
      title: 'Active API Keys',
      value: `${usage.activeKeys}/${usage.totalKeys}`,
      icon: Key,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      description: 'Active API keys'
    },
    {
      title: 'Success Rate',
      value: `${successRate}%`,
      icon: parseFloat(successRate) >= 95 ? CheckCircle : parseFloat(successRate) >= 90 ? AlertTriangle : TrendingDown,
      color: parseFloat(successRate) >= 95 ? 'text-green-600' : parseFloat(successRate) >= 90 ? 'text-yellow-600' : 'text-red-600',
      bgColor: parseFloat(successRate) >= 95 ? 'bg-green-100' : parseFloat(successRate) >= 90 ? 'bg-yellow-100' : 'bg-red-100',
      description: 'API success rate'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {overviewCards.map((card, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {card.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${card.bgColor}`}>
                <card.icon className={`h-4 w-4 ${card.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">
                {card.value}
              </div>
              <p className="text-xs text-gray-500 mt-1">
                {card.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Usage and Limits */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Usage Metrics */}
        <Card>
          <CardHeader>
            <CardTitle>Usage Metrics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Daily Usage */}
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-gray-700">Daily Requests</span>
                  <span className={`text-sm font-medium ${getUsageColor(dailyUsagePercentage)}`}>
                    {usage.dailyRequests.toLocaleString()} / {limits.requestsPerDay.toLocaleString()}
                  </span>
                </div>
                <Progress value={dailyUsagePercentage} className="h-2" />
                <div className="text-xs text-gray-500 mt-1">
                  {dailyUsagePercentage.toFixed(1)}% of daily limit used
                </div>
              </div>

              {/* Monthly Usage */}
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-gray-700">Monthly Requests</span>
                  <span className={`text-sm font-medium ${getUsageColor(monthlyUsagePercentage)}`}>
                    {usage.monthlyRequests.toLocaleString()} / {limits.requestsPerMonth.toLocaleString()}
                  </span>
                </div>
                <Progress value={monthlyUsagePercentage} className="h-2" />
                <div className="text-xs text-gray-500 mt-1">
                  {monthlyUsagePercentage.toFixed(1)}% of monthly limit used
                </div>
              </div>

              {/* Rate Limit */}
              <div className="p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-2 mb-1">
                  <Zap className="h-4 w-4 text-yellow-600" />
                  <span className="text-sm font-medium text-gray-700">Rate Limit</span>
                </div>
                <div className="text-lg font-bold text-gray-900">
                  {limits.requestsPerMinute.toLocaleString()} req/min
                </div>
                <div className="text-xs text-gray-500">
                  Maximum requests per minute
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* API Health */}
        <Card>
          <CardHeader>
            <CardTitle>API Health</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Success Rate */}
              <div className="text-center p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg">
                <div className="text-3xl font-bold text-gray-900 mb-1">
                  {successRate}%
                </div>
                <div className="text-sm text-gray-600 mb-2">Success Rate</div>
                <div className="flex items-center justify-center space-x-1">
                  {parseFloat(successRate) >= 95 ? (
                    <>
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="text-xs text-green-600">Excellent</span>
                    </>
                  ) : parseFloat(successRate) >= 90 ? (
                    <>
                      <AlertTriangle className="h-4 w-4 text-yellow-600" />
                      <span className="text-xs text-yellow-600">Good</span>
                    </>
                  ) : (
                    <>
                      <TrendingDown className="h-4 w-4 text-red-600" />
                      <span className="text-xs text-red-600">Needs Attention</span>
                    </>
                  )}
                </div>
              </div>

              {/* Status Code Breakdown */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Response Status</h4>
                <div className="space-y-2">
                  {usage.statusCodes.map((status, index) => {
                    const percentage = totalRequests > 0 ? (status._count / totalRequests * 100).toFixed(1) : '0'
                    const isSuccess = status.statusCode >= 200 && status.statusCode < 300
                    const isClientError = status.statusCode >= 400 && status.statusCode < 500
                    const isServerError = status.statusCode >= 500
                    
                    return (
                      <div key={index} className="flex items-center justify-between text-sm">
                        <div className="flex items-center space-x-2">
                          <div className={`w-3 h-3 rounded-full ${
                            isSuccess ? 'bg-green-500' :
                            isClientError ? 'bg-yellow-500' :
                            isServerError ? 'bg-red-500' : 'bg-gray-500'
                          }`} />
                          <span className="text-gray-700">{status.statusCode}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-gray-900 font-medium">{status._count}</span>
                          <span className="text-gray-500">({percentage}%)</span>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>

              {/* API Resources */}
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <Key className="h-6 w-6 text-blue-600 mx-auto mb-1" />
                  <div className="text-lg font-bold text-blue-800">
                    {usage.activeKeys}
                  </div>
                  <div className="text-xs text-blue-600">Active Keys</div>
                </div>
                <div className="text-center p-3 bg-purple-50 rounded-lg">
                  <Webhook className="h-6 w-6 text-purple-600 mx-auto mb-1" />
                  <div className="text-lg font-bold text-purple-800">
                    {usage.activeWebhooks}
                  </div>
                  <div className="text-xs text-purple-600">Active Webhooks</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Endpoints */}
      <Card>
        <CardHeader>
          <CardTitle>Top API Endpoints</CardTitle>
        </CardHeader>
        <CardContent>
          {usage.topEndpoints.length > 0 ? (
            <div className="space-y-3">
              {usage.topEndpoints.map((endpoint, index) => {
                const percentage = usage.monthlyRequests > 0 
                  ? (endpoint._count / usage.monthlyRequests * 100).toFixed(1)
                  : '0'
                
                return (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center justify-center w-6 h-6 bg-blue-100 rounded-full">
                        <span className="text-xs font-bold text-blue-600">#{index + 1}</span>
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">
                          {endpoint.endpoint}
                        </div>
                        <div className="text-sm text-gray-600">
                          {percentage}% of total requests
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-gray-900">
                        {endpoint._count.toLocaleString()}
                      </div>
                      <div className="text-sm text-gray-600">requests</div>
                    </div>
                  </div>
                )
              })}
            </div>
          ) : (
            <div className="text-center py-8">
              <Activity className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <h3 className="font-medium text-gray-900 mb-2">No API Usage Yet</h3>
              <p className="text-gray-500">
                Start making API requests to see endpoint usage statistics.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
