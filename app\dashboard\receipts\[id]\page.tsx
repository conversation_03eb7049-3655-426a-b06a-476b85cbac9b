import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ReceiptDetails } from '@/components/receipts/receipt-details'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Edit, Download, Trash2, Copy } from 'lucide-react'
import Link from 'next/link'
import { notFound } from 'next/navigation'

export default async function ReceiptDetailPage({
  params,
}: {
  params: { id: string }
}) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  const receipt = await prisma.receipt.findFirst({
    where: {
      id: params.id,
      companyId: session.user.companyId,
    },
    include: {
      invoice: {
        include: {
          customer: {
            select: { 
              id: true, 
              name: true, 
              email: true, 
              company: true, 
              phone: true,
              address: true,
              city: true,
              state: true,
              postalCode: true,
              country: true
            }
          }
        }
      },
      createdBy: {
        select: { name: true, firstName: true, lastName: true }
      }
    }
  })

  if (!receipt) {
    notFound()
  }

  // Get company details for receipt header
  const company = await prisma.company.findUnique({
    where: { id: session.user.companyId },
    select: {
      name: true,
      email: true,
      phone: true,
      address: true,
      city: true,
      state: true,
      postalCode: true,
      country: true,
      logo: true
    }
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/receipts">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Receipts
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{receipt.receiptNumber}</h1>
            <p className="text-gray-600 mt-1">
              Payment receipt for {receipt.invoice.invoiceNumber}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Link href={`/dashboard/receipts/${receipt.id}/edit`}>
            <Button variant="outline">
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
          </Link>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Download PDF
          </Button>
          <Button variant="outline">
            <Copy className="h-4 w-4 mr-2" />
            Duplicate
          </Button>
          <Button variant="outline" className="text-red-600 hover:text-red-700">
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      {/* Receipt Details */}
      <ReceiptDetails receipt={receipt} company={company} />
    </div>
  )
}
