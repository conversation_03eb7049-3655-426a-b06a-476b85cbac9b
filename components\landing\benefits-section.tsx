'use client'

import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Clock,
  DollarSign,
  TrendingUp,
  Shield,
  Users,
  Zap,
  Target,
  BarChart3,
  CheckCircle,
  ArrowRight
} from 'lucide-react'

export function BenefitsSection() {
  const benefits = [
    {
      icon: Clock,
      title: 'Save 15+ Hours Per Week',
      description: 'Automate repetitive tasks like quotation generation, invoice creation, and follow-up reminders.',
      metrics: '87% time reduction',
      color: 'text-blue-600 bg-blue-100'
    },
    {
      icon: DollarSign,
      title: 'Increase Revenue by 30%',
      description: 'Faster quote turnaround and better customer management lead to more closed deals.',
      metrics: 'Average 30% growth',
      color: 'text-green-600 bg-green-100'
    },
    {
      icon: TrendingUp,
      title: 'Improve Cash Flow',
      description: 'Automated payment reminders and tracking help you get paid faster and more consistently.',
      metrics: '40% faster payments',
      color: 'text-purple-600 bg-purple-100'
    },
    {
      icon: Shield,
      title: 'Reduce Errors by 95%',
      description: 'Eliminate manual data entry errors with automated calculations and template systems.',
      metrics: '95% error reduction',
      color: 'text-red-600 bg-red-100'
    },
    {
      icon: Users,
      title: 'Better Customer Experience',
      description: 'Professional documents and faster response times improve customer satisfaction.',
      metrics: '4.8/5 satisfaction',
      color: 'text-orange-600 bg-orange-100'
    },
    {
      icon: BarChart3,
      title: 'Data-Driven Decisions',
      description: 'Real-time analytics and reporting help you make informed business decisions.',
      metrics: 'Real-time insights',
      color: 'text-indigo-600 bg-indigo-100'
    }
  ]

  const processSteps = [
    {
      step: '1',
      title: 'Quick Setup',
      description: 'Get started in minutes with our guided onboarding process',
      icon: Zap
    },
    {
      step: '2',
      title: 'Import Data',
      description: 'Easily migrate your existing customer and business data',
      icon: Users
    },
    {
      step: '3',
      title: 'Start Creating',
      description: 'Generate your first quotation and see immediate results',
      icon: Target
    },
    {
      step: '4',
      title: 'Scale & Grow',
      description: 'Use analytics to optimize and grow your business',
      icon: TrendingUp
    }
  ]

  return (
    <section className="py-20 px-4 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="container mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <Badge variant="outline" className="mb-4">
            <TrendingUp className="h-4 w-4 mr-2" />
            Business Benefits
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Transform Your Business
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent block">
              with Measurable Results
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Don't just take our word for it. See the real, measurable impact BusinessSaaS 
            has on businesses like yours.
          </p>
        </div>

        {/* Benefits Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {benefits.map((benefit, index) => (
            <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 group bg-white">
              <CardContent className="p-8">
                <div className={`w-16 h-16 rounded-2xl flex items-center justify-center mb-6 ${benefit.color} group-hover:scale-110 transition-transform`}>
                  <benefit.icon className="h-8 w-8" />
                </div>
                
                <h3 className="text-xl font-bold text-gray-900 mb-3">
                  {benefit.title}
                </h3>
                
                <p className="text-gray-600 leading-relaxed mb-4">
                  {benefit.description}
                </p>
                
                <div className="flex items-center text-sm font-semibold text-blue-600">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  {benefit.metrics}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Process Section */}
        <div className="bg-white rounded-2xl shadow-lg p-8 md:p-12">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              Get Started in 4 Simple Steps
            </h3>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Our streamlined onboarding process gets you up and running quickly, 
              so you can start seeing results immediately.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {processSteps.map((step, index) => (
              <div key={index} className="text-center relative">
                {/* Step Number */}
                <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4 text-white font-bold text-xl">
                  {step.step}
                </div>
                
                {/* Icon */}
                <div className="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <step.icon className="h-6 w-6 text-gray-600" />
                </div>
                
                {/* Content */}
                <h4 className="font-bold text-gray-900 mb-2">{step.title}</h4>
                <p className="text-gray-600 text-sm leading-relaxed">{step.description}</p>
                
                {/* Arrow */}
                {index < processSteps.length - 1 && (
                  <div className="hidden lg:block absolute top-8 left-full w-full">
                    <ArrowRight className="h-6 w-6 text-gray-300 mx-auto" />
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* ROI Calculator Teaser */}
        <div className="mt-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 md:p-12 text-center text-white">
          <h3 className="text-3xl font-bold mb-4">
            Calculate Your ROI
          </h3>
          <p className="text-xl opacity-90 mb-8 max-w-2xl mx-auto">
            See how much time and money you could save with BusinessSaaS. 
            Our ROI calculator shows your potential savings based on your business size.
          </p>
          <div className="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto mb-8">
            <div className="bg-white/10 rounded-lg p-6">
              <div className="text-3xl font-bold mb-2">15+ hrs</div>
              <div className="text-sm opacity-80">Time Saved Weekly</div>
            </div>
            <div className="bg-white/10 rounded-lg p-6">
              <div className="text-3xl font-bold mb-2">30%</div>
              <div className="text-sm opacity-80">Revenue Increase</div>
            </div>
            <div className="bg-white/10 rounded-lg p-6">
              <div className="text-3xl font-bold mb-2">$50K+</div>
              <div className="text-sm opacity-80">Annual Savings</div>
            </div>
          </div>
          <button className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
            Calculate Your Savings
            <ArrowRight className="ml-2 h-5 w-5 inline" />
          </button>
        </div>
      </div>
    </section>
  )
}
