import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { LeadForm } from '@/components/leads/lead-form'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'

interface SearchParams {
  customerId?: string
}

export default async function NewLeadPage({
  searchParams,
}: {
  searchParams: SearchParams
}) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  // Fetch customers for the dropdown
  const customers = await prisma.customer.findMany({
    where: { 
      companyId: session.user.companyId,
      status: { in: ['ACTIVE', 'PROSPECT'] }
    },
    select: { id: true, name: true, email: true, company: true },
    orderBy: { name: 'asc' },
  })

  const preselectedCustomerId = searchParams.customerId

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href="/dashboard/leads">
          <Button variant="ghost" >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Leads
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Add New Lead</h1>
          <p className="text-gray-600 mt-1">
            Create a new lead to track sales opportunities
          </p>
        </div>
      </div>

      {/* Lead Form */}
      <LeadForm 
        mode="create" 
        customers={customers}
        preselectedCustomerId={preselectedCustomerId}
      />
    </div>
  )
}
