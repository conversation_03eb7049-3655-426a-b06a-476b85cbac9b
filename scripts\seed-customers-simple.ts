import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// Professional customer data matching the actual Customer model
const sampleCustomers = [
  {
    name: 'TechCorp Solutions',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'TechCorp Solutions Inc.',
    address: '123 Innovation Drive',
    city: 'Silicon Valley',
    state: 'CA',
    country: 'USA',
    postalCode: '94025',
    type: 'BUSINESS',
    status: 'ACTIVE',
    website: 'https://techcorp.com',
    notes: 'Technology solutions provider. Contact: <PERSON> (CTO) - <EMAIL>'
  },
  {
    name: 'Global Manufacturing Ltd',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Global Manufacturing Ltd.',
    address: '456 Industrial Blvd',
    city: 'Detroit',
    state: 'MI',
    country: 'USA',
    postalCode: '48201',
    type: 'BUSINESS',
    status: 'ACTIVE',
    website: 'https://globalmanufacturing.com',
    notes: 'Manufacturing company. Contact: <PERSON> (Operations Director)'
  },
  {
    name: 'Creative Design Studio',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Creative Design Studio LLC',
    address: '789 Art District',
    city: 'New York',
    state: 'NY',
    country: 'USA',
    postalCode: '10001',
    type: 'BUSINESS',
    status: 'ACTIVE',
    website: 'https://creativedesign.com',
    notes: 'Award-winning design studio specializing in brand identity and digital experiences. Industry: Design & Marketing. Primary Contact: Emma Rodriguez (Creative Director) - <EMAIL>'
  },
  {
    name: 'HealthCare Plus',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'HealthCare Plus Medical Center',
    address: '321 Medical Plaza',
    city: 'Boston',
    state: 'MA',
    country: 'USA',
    postalCode: '02101',
    type: 'BUSINESS',
    status: 'ACTIVE',
    website: 'https://healthcareplus.com',
    notes: 'Modern medical facility providing comprehensive healthcare services. Industry: Healthcare. Primary Contact: Dr. Lisa Thompson (Chief Medical Officer) - <EMAIL>'
  },
  {
    name: 'EcoGreen Solutions',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'EcoGreen Solutions Inc.',
    address: '654 Sustainability Way',
    city: 'Portland',
    state: 'OR',
    country: 'USA',
    postalCode: '97201',
    type: 'BUSINESS',
    status: 'ACTIVE',
    website: 'https://ecogreen.com',
    notes: 'Environmental consulting firm focused on sustainable business practices. Industry: Environmental Services. Primary Contact: Robert Green (Environmental Consultant) - <EMAIL>'
  },
  {
    name: 'Financial Advisors Group',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Financial Advisors Group LLC',
    address: '987 Wall Street',
    city: 'New York',
    state: 'NY',
    country: 'USA',
    postalCode: '10005',
    type: 'BUSINESS',
    status: 'ACTIVE',
    website: 'https://financialadvisors.com',
    notes: 'Premier financial advisory firm serving high-net-worth individuals and corporations. Industry: Financial Services. Primary Contact: Jennifer Walsh (Senior Partner) - <EMAIL>'
  },
  {
    name: 'Alex Martinez',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Freelance Consultant',
    address: '123 Residential St',
    city: 'Austin',
    state: 'TX',
    country: 'USA',
    postalCode: '78701',
    type: 'INDIVIDUAL',
    status: 'ACTIVE',
    notes: 'Independent business consultant specializing in small business growth strategies. Industry: Consulting. Self-employed professional with 8+ years of experience.'
  },
  {
    name: 'Retail Chain Enterprises',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Retail Chain Enterprises Corp.',
    address: '555 Commerce Center',
    city: 'Chicago',
    state: 'IL',
    country: 'USA',
    postalCode: '60601',
    type: 'BUSINESS',
    status: 'ACTIVE',
    website: 'https://retailchain.com',
    notes: 'Multi-location retail chain with over 200 stores nationwide. Industry: Retail. Primary Contact: Mark Johnson (Procurement Director) - <EMAIL>'
  },
  {
    name: 'Educational Institute',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Educational Institute of Technology',
    address: '777 Campus Drive',
    city: 'Cambridge',
    state: 'MA',
    country: 'USA',
    postalCode: '02139',
    type: 'BUSINESS',
    status: 'ACTIVE',
    website: 'https://educationalinstitute.edu',
    notes: 'Leading educational institution offering technology and engineering programs. Industry: Education. Primary Contact: Dr. Patricia Williams (Dean of Technology) - <EMAIL>'
  },
  {
    name: 'Startup Innovations',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Startup Innovations LLC',
    address: '888 Venture Blvd',
    city: 'San Francisco',
    state: 'CA',
    country: 'USA',
    postalCode: '94107',
    type: 'BUSINESS',
    status: 'PROSPECT',
    website: 'https://startupinnovations.com',
    notes: 'Fast-growing startup developing innovative mobile applications. Industry: Technology. Primary Contact: Kevin Park (CEO) - <EMAIL>. Currently in prospect stage.'
  },
  {
    name: 'Construction Builders Inc',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Construction Builders Inc.',
    address: '999 Builder Lane',
    city: 'Phoenix',
    state: 'AZ',
    country: 'USA',
    postalCode: '85001',
    type: 'BUSINESS',
    status: 'ACTIVE',
    website: 'https://constructionbuilders.com',
    notes: 'Full-service construction company specializing in commercial and residential projects. Industry: Construction. Primary Contact: Tony Rodriguez (Project Manager) - <EMAIL>'
  },
  {
    name: 'Maria Garcia',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Independent Contractor',
    address: '456 Home Street',
    city: 'Miami',
    state: 'FL',
    country: 'USA',
    postalCode: '33101',
    type: 'INDIVIDUAL',
    status: 'ACTIVE',
    notes: 'Independent marketing consultant with 10+ years of experience. Industry: Professional Services. Specializes in digital marketing and brand strategy for small businesses.'
  },
  {
    name: 'Hospitality Group',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Hospitality Group International',
    address: '111 Resort Boulevard',
    city: 'Las Vegas',
    state: 'NV',
    country: 'USA',
    postalCode: '89101',
    type: 'BUSINESS',
    status: 'ACTIVE',
    website: 'https://hospitalitygroup.com',
    notes: 'Luxury hotel and resort management company with properties worldwide. Industry: Hospitality. Primary Contact: Amanda Foster (Operations Manager) - <EMAIL>'
  },
  {
    name: 'Legal Associates',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Legal Associates Law Firm',
    address: '222 Justice Plaza',
    city: 'Washington',
    state: 'DC',
    country: 'USA',
    postalCode: '20001',
    type: 'BUSINESS',
    status: 'ACTIVE',
    website: 'https://legalassociates.com',
    notes: 'Full-service law firm specializing in corporate and commercial law. Industry: Legal Services. Primary Contact: James Wilson (Managing Partner) - <EMAIL>'
  },
  {
    name: 'Transportation Logistics',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Transportation Logistics Corp.',
    address: '333 Highway Center',
    city: 'Atlanta',
    state: 'GA',
    country: 'USA',
    postalCode: '30301',
    type: 'BUSINESS',
    status: 'ACTIVE',
    website: 'https://transportationlogistics.com',
    notes: 'National transportation and logistics provider with coast-to-coast coverage. Industry: Transportation. Primary Contact: Carlos Mendez (Fleet Manager) - <EMAIL>'
  }
]

async function seedCustomers() {
  try {
    console.log('🌱 Starting customer seeding...')

    // Get the first company for seeding
    const company = await prisma.company.findFirst({
      include: {
        users: true
      }
    })

    if (!company || !company.users.length) {
      console.log('❌ No company or users found. Please ensure you have at least one company and user.')
      return
    }

    const user = company.users[0]
    console.log(`📍 Seeding customers for company: ${company.name}`)

    let createdCount = 0
    let skippedCount = 0

    for (const customerData of sampleCustomers) {
      console.log(`👤 Processing customer: ${customerData.name}`)
      
      // Check if customer already exists
      const existingCustomer = await prisma.customer.findFirst({
        where: {
          email: customerData.email,
          companyId: company.id
        }
      })

      if (existingCustomer) {
        console.log(`  ⏭️ Customer already exists: ${customerData.name}`)
        skippedCount++
        continue
      }

      // Create customer
      const customer = await prisma.customer.create({
        data: {
          ...customerData,
          companyId: company.id,
          createdById: user.id
        }
      })

      console.log(`  ✅ Created customer: ${customer.name}`)
      createdCount++

      // Create activity log
      await prisma.activity.create({
        data: {
          type: 'NOTE',
          title: 'Customer created',
          description: `Customer "${customer.name}" was added to the system`,
          companyId: company.id,
          createdById: user.id,
          customerId: customer.id,
          metadata: {
            customerId: customer.id,
            customerName: customer.name,
            customerType: customer.type
          }
        }
      })
    }

    console.log('🎉 Customer seeding completed successfully!')
    
    // Get final statistics
    const totalCustomers = await prisma.customer.count({
      where: { companyId: company.id }
    })

    const customersByType = await prisma.customer.groupBy({
      by: ['type'],
      where: { companyId: company.id },
      _count: true
    })

    const customersByStatus = await prisma.customer.groupBy({
      by: ['status'],
      where: { companyId: company.id },
      _count: true
    })

    console.log(`📊 Final Statistics:`)
    console.log(`  👥 Total customers: ${totalCustomers}`)
    console.log(`  ➕ Created this run: ${createdCount}`)
    console.log(`  ⏭️ Skipped (existing): ${skippedCount}`)
    console.log(`  📈 By type:`)
    customersByType.forEach(stat => {
      console.log(`    ${stat.type}: ${stat._count}`)
    })
    console.log(`  📊 By status:`)
    customersByStatus.forEach(stat => {
      console.log(`    ${stat.status}: ${stat._count}`)
    })
    
  } catch (error) {
    console.error('❌ Error seeding customers:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the seeding
seedCustomers()
