'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useRouter } from 'next/navigation'
import { toast } from 'react-hot-toast'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Save, Loader2, Eye, Code, Plus, Trash2 } from 'lucide-react'

const templateSchema = z.object({
  name: z.string().min(1, 'Template name is required'),
  description: z.string().optional(),
  content: z.string().min(1, 'Template content is required'),
  category: z.string().optional(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'DRAFT']),
  variables: z.array(z.object({
    name: z.string().min(1, 'Variable name is required'),
    label: z.string().min(1, 'Variable label is required'),
    type: z.enum(['TEXT', 'NUMBER', 'DATE', 'BOOLEAN', 'EMAIL', 'PHONE']),
    required: z.boolean().default(false),
    defaultValue: z.string().optional(),
    description: z.string().optional(),
  })).optional()
})

type TemplateFormData = z.infer<typeof templateSchema>

interface InvoiceTemplateFormProps {
  mode: 'create' | 'edit'
  template?: any
}

export function InvoiceTemplateForm({ mode, template }: InvoiceTemplateFormProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [previewMode, setPreviewMode] = useState(false)
  const [variables, setVariables] = useState(template?.variables || [])

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue
  } = useForm<TemplateFormData>({
    resolver: zodResolver(templateSchema),
    defaultValues: template || {
      name: '',
      description: '',
      content: '',
      category: '',
      status: 'DRAFT',
      variables: []
    }
  })

  const onSubmit = async (data: TemplateFormData) => {
    setIsLoading(true)

    try {
      const url = mode === 'create' ? '/api/invoice-templates' : `/api/invoice-templates?id=${template.id}`
      const method = mode === 'create' ? 'POST' : 'PUT'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          variables
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to save template')
      }

      const result = await response.json()
      
      toast.success(mode === 'create' ? 'Template created successfully!' : 'Template updated successfully!')
      
      if (mode === 'create') {
        router.push(`/dashboard/invoices/templates/${result.id}`)
      } else {
        router.push('/dashboard/invoices/templates')
      }
    } catch (error) {
      console.error('Error saving template:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to save template'
      toast.error(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const addVariable = () => {
    setVariables([...variables, {
      name: '',
      label: '',
      type: 'TEXT',
      required: false,
      defaultValue: '',
      description: ''
    }])
  }

  const removeVariable = (index: number) => {
    setVariables(variables.filter((_, i) => i !== index))
  }

  const updateVariable = (index: number, field: string, value: any) => {
    const updated = [...variables]
    updated[index] = { ...updated[index], [field]: value }
    setVariables(updated)
  }

  const insertVariable = (variableName: string) => {
    const currentContent = watch('content')
    setValue('content', currentContent + `{{${variableName}}}`)
  }

  const defaultVariables = [
    'company.name',
    'company.email',
    'company.phone',
    'company.address',
    'customer.name',
    'customer.email',
    'customer.company',
    'invoice.number',
    'invoice.date',
    'invoice.dueDate',
    'invoice.total',
    'invoice.subtotal',
    'invoice.tax',
    'user.name',
    'user.email'
  ]

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Form */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Template Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Template Name</Label>
                  <Input
                    id="name"
                    {...register('name')}
                    placeholder="Enter template name"
                  />
                  {errors.name && (
                    <p className="text-sm text-red-600">{errors.name.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">Category</Label>
                  <Input
                    id="category"
                    {...register('category')}
                    placeholder="e.g., Standard, Premium, Service"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Input
                  id="description"
                  {...register('description')}
                  placeholder="Brief description of the template"
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="content">Template Content (HTML)</Label>
                  <div className="flex items-center space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setPreviewMode(!previewMode)}
                    >
                      {previewMode ? <Code className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      {previewMode ? 'Edit' : 'Preview'}
                    </Button>
                  </div>
                </div>
                
                {previewMode ? (
                  <div 
                    className="min-h-[400px] p-4 border border-gray-300 rounded-md bg-white"
                    dangerouslySetInnerHTML={{ __html: watch('content') }}
                  />
                ) : (
                  <Textarea
                    id="content"
                    {...register('content')}
                    placeholder="Enter HTML template content..."
                    rows={15}
                    className="font-mono text-sm"
                  />
                )}
                {errors.content && (
                  <p className="text-sm text-red-600">{errors.content.message}</p>
                )}
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Template Status</Label>
                  <p className="text-sm text-gray-500">Set template as active or draft</p>
                </div>
                <select
                  {...register('status')}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                >
                  <option value="DRAFT">Draft</option>
                  <option value="ACTIVE">Active</option>
                  <option value="INACTIVE">Inactive</option>
                </select>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Default Variables</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p className="text-sm text-gray-600 mb-4">
                  Click to insert default variables:
                </p>
                <div className="space-y-2">
                  {defaultVariables.map((variable) => (
                    <Button
                      key={variable}
                      type="button"
                      variant="outline"
                      size="sm"
                      className="w-full justify-start text-xs"
                      onClick={() => insertVariable(variable)}
                    >
                      {`{{${variable}}}`}
                    </Button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Template Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label className="text-xs">Name:</Label>
                  <p className="text-sm font-medium">{watch('name') || 'Untitled Template'}</p>
                </div>
                <div>
                  <Label className="text-xs">Category:</Label>
                  <Badge variant="outline">{watch('category') || 'General'}</Badge>
                </div>
                <div>
                  <Label className="text-xs">Status:</Label>
                  <Badge variant={watch('status') === 'ACTIVE' ? 'default' : 'secondary'}>
                    {watch('status')}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center justify-end space-x-4 p-6 bg-gray-50 rounded-lg">
        <Button
          type="button"
          variant="outline"
          onClick={() => router.back()}
        >
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <Save className="h-4 w-4 mr-2" />
          )}
          {mode === 'create' ? 'Create Template' : 'Update Template'}
        </Button>
      </div>
    </form>
  )
}
