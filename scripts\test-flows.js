#!/usr/bin/env node

/**
 * 🧪 SaaS Platform Flow Testing Script
 * 
 * This script tests all critical SaaS flows to ensure the platform
 * is ready for production deployment and revenue generation.
 */

const axios = require('axios')
const chalk = require('chalk')

const BASE_URL = 'http://localhost:3000'
const API_URL = `${BASE_URL}/api`

// Test credentials
const SUPER_ADMIN = {
  email: '<EMAIL>',
  password: 'superadmin123'
}

const DEMO_ADMIN = {
  email: '<EMAIL>',
  password: 'demo123'
}

const DEMO_USER = {
  email: '<EMAIL>',
  password: 'demo123'
}

class SaaSFlowTester {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      tests: []
    }
  }

  log(message, type = 'info') {
    const colors = {
      info: chalk.blue,
      success: chalk.green,
      error: chalk.red,
      warning: chalk.yellow
    }
    console.log(colors[type](`${type.toUpperCase()}: ${message}`))
  }

  async test(name, testFn) {
    try {
      this.log(`Testing: ${name}`)
      await testFn()
      this.results.passed++
      this.results.tests.push({ name, status: 'PASSED' })
      this.log(`✅ ${name} - PASSED`, 'success')
    } catch (error) {
      this.results.failed++
      this.results.tests.push({ name, status: 'FAILED', error: error.message })
      this.log(`❌ ${name} - FAILED: ${error.message}`, 'error')
    }
  }

  async testHealthCheck() {
    const response = await axios.get(`${API_URL}/health`)
    if (response.status !== 200) {
      throw new Error('Health check failed')
    }
  }

  async testAuthenticationFlow() {
    // Test login endpoint
    const response = await axios.post(`${API_URL}/auth/signin`, {
      email: DEMO_ADMIN.email,
      password: DEMO_ADMIN.password
    })
    
    if (response.status !== 200) {
      throw new Error('Authentication failed')
    }
  }

  async testSuperAdminAccess() {
    // Test super admin login
    const response = await axios.post(`${API_URL}/auth/signin`, {
      email: SUPER_ADMIN.email,
      password: SUPER_ADMIN.password
    })
    
    if (response.status !== 200) {
      throw new Error('Super admin authentication failed')
    }
  }

  async testDatabaseConnection() {
    // Test database connectivity through API
    const response = await axios.get(`${API_URL}/companies`)
    if (response.status !== 200) {
      throw new Error('Database connection failed')
    }
  }

  async testCustomerAPI() {
    // Test customer endpoints
    const response = await axios.get(`${API_URL}/customers`)
    if (response.status !== 200) {
      throw new Error('Customer API failed')
    }
  }

  async testQuotationAPI() {
    // Test quotation endpoints
    const response = await axios.get(`${API_URL}/quotations`)
    if (response.status !== 200) {
      throw new Error('Quotation API failed')
    }
  }

  async testInvoiceAPI() {
    // Test invoice endpoints
    const response = await axios.get(`${API_URL}/invoices`)
    if (response.status !== 200) {
      throw new Error('Invoice API failed')
    }
  }

  async testContractAPI() {
    // Test contract endpoints
    const response = await axios.get(`${API_URL}/contracts`)
    if (response.status !== 200) {
      throw new Error('Contract API failed')
    }
  }

  async testItemAPI() {
    // Test item endpoints
    const response = await axios.get(`${API_URL}/items`)
    if (response.status !== 200) {
      throw new Error('Item API failed')
    }
  }

  async testEmailAPI() {
    // Test email endpoints
    const response = await axios.get(`${API_URL}/emails`)
    if (response.status !== 200) {
      throw new Error('Email API failed')
    }
  }

  async testSupportAPI() {
    // Test support ticket endpoints
    const response = await axios.get(`${API_URL}/support`)
    if (response.status !== 200) {
      throw new Error('Support API failed')
    }
  }

  async testNotificationAPI() {
    // Test notification endpoints
    const response = await axios.get(`${API_URL}/notifications`)
    if (response.status !== 200) {
      throw new Error('Notification API failed')
    }
  }

  async testReportAPI() {
    // Test report endpoints
    const response = await axios.get(`${API_URL}/reports`)
    if (response.status !== 200) {
      throw new Error('Report API failed')
    }
  }

  async testWebhookAPI() {
    // Test webhook endpoints
    const response = await axios.get(`${API_URL}/webhooks`)
    if (response.status !== 200) {
      throw new Error('Webhook API failed')
    }
  }

  async testAPIKeyAPI() {
    // Test API key endpoints
    const response = await axios.get(`${API_URL}/api-keys`)
    if (response.status !== 200) {
      throw new Error('API Key API failed')
    }
  }

  async runAllTests() {
    this.log('🚀 Starting SaaS Platform Flow Testing...', 'info')
    this.log('=' * 50, 'info')

    // Core System Tests
    await this.test('Health Check', () => this.testHealthCheck())
    await this.test('Database Connection', () => this.testDatabaseConnection())
    
    // Authentication Tests
    await this.test('Demo Admin Authentication', () => this.testAuthenticationFlow())
    await this.test('Super Admin Access', () => this.testSuperAdminAccess())
    
    // Core Business API Tests
    await this.test('Customer API', () => this.testCustomerAPI())
    await this.test('Quotation API', () => this.testQuotationAPI())
    await this.test('Invoice API', () => this.testInvoiceAPI())
    await this.test('Contract API', () => this.testContractAPI())
    await this.test('Item API', () => this.testItemAPI())
    
    // Advanced Feature Tests
    await this.test('Email API', () => this.testEmailAPI())
    await this.test('Support API', () => this.testSupportAPI())
    await this.test('Notification API', () => this.testNotificationAPI())
    await this.test('Report API', () => this.testReportAPI())
    await this.test('Webhook API', () => this.testWebhookAPI())
    await this.test('API Key API', () => this.testAPIKeyAPI())

    // Print Results
    this.printResults()
  }

  printResults() {
    this.log('=' * 50, 'info')
    this.log('🎯 TEST RESULTS SUMMARY', 'info')
    this.log('=' * 50, 'info')
    
    this.log(`✅ Tests Passed: ${this.results.passed}`, 'success')
    this.log(`❌ Tests Failed: ${this.results.failed}`, 'error')
    this.log(`📊 Total Tests: ${this.results.passed + this.results.failed}`, 'info')
    
    const successRate = (this.results.passed / (this.results.passed + this.results.failed)) * 100
    this.log(`🎯 Success Rate: ${successRate.toFixed(1)}%`, successRate > 90 ? 'success' : 'warning')
    
    if (this.results.failed > 0) {
      this.log('\n❌ FAILED TESTS:', 'error')
      this.results.tests
        .filter(test => test.status === 'FAILED')
        .forEach(test => {
          this.log(`  - ${test.name}: ${test.error}`, 'error')
        })
    }
    
    if (successRate >= 90) {
      this.log('\n🎉 CONGRATULATIONS!', 'success')
      this.log('Your SaaS platform is ready for production deployment!', 'success')
      this.log('All critical flows are working correctly.', 'success')
    } else {
      this.log('\n⚠️  WARNING!', 'warning')
      this.log('Some tests failed. Please review and fix issues before production deployment.', 'warning')
    }
    
    this.log('\n🚀 Next Steps:', 'info')
    this.log('1. Review the Production Guide (PRODUCTION_GUIDE.md)', 'info')
    this.log('2. Configure production environment variables', 'info')
    this.log('3. Set up Stripe for payment processing', 'info')
    this.log('4. Deploy to your preferred platform', 'info')
    this.log('5. Start generating revenue! 💰', 'success')
  }
}

// Run the tests
async function main() {
  const tester = new SaaSFlowTester()
  
  try {
    await tester.runAllTests()
  } catch (error) {
    console.error(chalk.red('Fatal error during testing:'), error.message)
    process.exit(1)
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log(chalk.yellow('\n🛑 Testing interrupted by user'))
  process.exit(0)
})

if (require.main === module) {
  main()
}

module.exports = SaaSFlowTester
