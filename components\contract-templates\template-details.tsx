'use client'

import { useState } from 'react'
import Link from 'next/link'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { getStatusColor, formatDate } from '@/lib/utils'
import { 
  FileText,
  Code,
  Calendar, 
  User,
  Tag,
  CheckCircle,
  Edit3,
  Archive,
  TrendingUp,
  Copy,
  Trash2,
  Plus,
  Eye,
  
} from 'lucide-react'
import toast from 'react-hot-toast'

interface TemplateDetailsProps {
  template: {
    id: string
    name: string
    description: string | null
    category: string | null
    status: string
    content: string
    variables: any[]
    createdAt: Date
    updatedAt: Date
    createdBy: {
      name: string | null
      firstName: string | null
      lastName: string | null
    } | null
    contracts: Array<{
      id: string
      title: string
      status: string
      createdAt: Date
      customer: {
        name: string
      } | null
    }>
    _count: {
      contracts: number
    }
  }
}

export function TemplateDetails({ template }: TemplateDetailsProps) {
  const [isDeleting, setIsDeleting] = useState(false)
  const [showFull<PERSON>ontent, setShowFullContent] = useState(false)

  const handleDelete = async () => {
    if (template._count.contracts > 0) {
      toast.error(`Cannot delete template. It is being used by ${template._count.contracts} contract(s).`)
      return
    }

    if (!confirm('Are you sure you want to delete this template? This action cannot be undone.')) {
      return
    }

    setIsDeleting(true)
    try {
      const response = await fetch(`/api/contract-templates/${template.id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete template')
      }

      toast.success('Template deleted successfully')
      window.location.href = '/dashboard/contract-templates'
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete template'
      toast.error(errorMessage)
    } finally {
      setIsDeleting(false)
    }
  }

  const handleDuplicate = async () => {
    try {
      const response = await fetch(`/api/contract-templates/${template.id}/duplicate`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to duplicate template')
      }

      const result = await response.json()
      toast.success('Template duplicated successfully')
      window.location.href = `/dashboard/contract-templates/${result.id}/edit`
    } catch (error) {
      toast.error('Failed to duplicate template')
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return CheckCircle
      case 'DRAFT':
        return Edit3
      case 'ARCHIVED':
        return Archive
      default:
        return FileText
    }
  }

  const getUserName = (user: any) => {
    if (!user) return 'System'
    if (user.name) return user.name
    if (user.firstName && user.lastName) return `${user.firstName} ${user.lastName}`
    if (user.firstName) return user.firstName
    return 'Unknown User'
  }

  const StatusIcon = getStatusIcon(template.status)

  // Process content for display
  const contentLines = template.content.split('\n')
  const previewLines = showFullContent ? contentLines : contentLines.slice(0, 10)
  const hasMoreContent = contentLines.length > 10

  return (
    <div className="space-y-6">
      {/* Template Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FileText className="h-5 w-5" />
                <span>Template Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">Name</label>
                  <p className="text-lg font-medium text-gray-900">{template.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Status</label>
                  <div className="flex items-center space-x-2 mt-1">
                    <StatusIcon className={`h-4 w-4 ${getStatusColor(template.status).replace('border-', 'text-').replace('text-', 'text-')}`} />
                    <Badge className={getStatusColor(template.status)} variant="outline">
                      {template.status}
                    </Badge>
                  </div>
                </div>
              </div>

              {template.description && (
                <div>
                  <label className="text-sm font-medium text-gray-600">Description</label>
                  <p className="text-gray-900 mt-1">{template.description}</p>
                </div>
              )}

              {template.category && (
                <div>
                  <label className="text-sm font-medium text-gray-600">Category</label>
                  <div className="flex items-center space-x-2 mt-1">
                    <Tag className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-900">{template.category}</span>
                  </div>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">Created</label>
                  <div className="flex items-center space-x-2 mt-1">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-900">{formatDate(template.createdAt)}</span>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Last Updated</label>
                  <div className="flex items-center space-x-2 mt-1">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-900">{formatDate(template.updatedAt)}</span>
                  </div>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-600">Created By</label>
                <div className="flex items-center space-x-2 mt-1">
                  <User className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-900">{getUserName(template.createdBy)}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5" />
                <span>Usage Statistics</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  {template._count.contracts}
                </div>
                <div className="text-sm text-gray-600 mb-4">
                  Contracts Generated
                </div>
                {template.status === 'ACTIVE' && (
                  <Link href={`/dashboard/contracts/new?templateId=${template.id}`}>
                    <Button className="w-full">
                      <Plus className="h-4 w-4 mr-2" />
                      Create Contract
                    </Button>
                  </Link>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Variables */}
      {template.variables && template.variables.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Code className="h-5 w-5" />
              <span>Template Variables</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {template.variables.map((variable: any, index: number) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <code className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                      {`{{${variable.name}}}`}
                    </code>
                    <Badge variant="outline" className="text-xs">
                      {variable.type}
                    </Badge>
                  </div>
                  <div className="space-y-1">
                    <p className="font-medium text-gray-900">{variable.label}</p>
                    {variable.description && (
                      <p className="text-sm text-gray-600">{variable.description}</p>
                    )}
                    {variable.required && (
                      <Badge variant="outline" className="text-xs text-red-600 border-red-200">
                        Required
                      </Badge>
                    )}
                    {variable.defaultValue && (
                      <p className="text-xs text-gray-500">
                        Default: {variable.defaultValue}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Template Content */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Template Content</CardTitle>
            <div className="flex items-center space-x-2">
              {hasMoreContent && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowFullContent(!showFullContent)}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  {showFullContent ? 'Show Less' : 'Show All'}
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="bg-gray-50 rounded-lg p-4 font-mono text-sm whitespace-pre-wrap max-h-96 overflow-y-auto">
            {previewLines.join('\n')}
            {!showFullContent && hasMoreContent && (
              <div className="text-gray-500 mt-4">
                ... {contentLines.length - 10} more lines
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Recent Contracts */}
      {template.contracts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5" />
              <span>Recent Contracts</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {template.contracts.map((contract) => (
                <div key={contract.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <Link 
                      href={`/dashboard/contracts/${contract.id}`}
                      className="font-medium text-blue-600 hover:text-blue-800"
                    >
                      {contract.title}
                    </Link>
                    <div className="text-sm text-gray-600 mt-1">
                      {contract.customer?.name} • {formatDate(contract.createdAt)}
                    </div>
                  </div>
                  <Badge className={getStatusColor(contract.status)} variant="outline">
                    {contract.status}
                  </Badge>
                </div>
              ))}
              {template._count.contracts > 10 && (
                <div className="text-center pt-2">
                  <Link 
                    href={`/dashboard/contracts?templateId=${template.id}`}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    View all {template._count.contracts} contracts →
                  </Link>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex items-center justify-end space-x-2">
        <Link href={`/dashboard/contract-templates/${template.id}/edit`}>
          <Button variant="outline">
            <Edit3 className="h-4 w-4 mr-2" />
            Edit Template
          </Button>
        </Link>
        <Button variant="outline" onClick={handleDuplicate}>
          <Copy className="h-4 w-4 mr-2" />
          Duplicate
        </Button>
        <Button 
          variant="outline" 
          onClick={handleDelete}
          disabled={isDeleting || template._count.contracts > 0}
          className="text-red-600 hover:text-red-700"
        >
          <Trash2 className="h-4 w-4 mr-2" />
          {isDeleting ? 'Deleting...' : 'Delete'}
        </Button>
      </div>
    </div>
  )
}
