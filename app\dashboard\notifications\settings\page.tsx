import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { NotificationSettings } from '@/components/notifications/notification-settings'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Settings } from 'lucide-react'
import Link from 'next/link'

export default async function NotificationSettingsPage() {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.id) {
    return <div>Error: No user found</div>
  }

  // Fetch user's notification preferences
  const userPreferences = await prisma.notificationPreference.findFirst({
    where: { userId: session.user.id }
  })

  // Default preferences if none exist
  const defaultPreferences = {
    emailNotifications: true,
    pushNotifications: true,
    smsNotifications: false,
    invoiceOverdue: true,
    paymentReceived: true,
    quotationAccepted: true,
    quotationExpired: true,
    contractSigned: true,
    customerCreated: false,
    leadConverted: true,
    reminders: true,
    deadlines: true,
    systemUpdates: false,
    marketingEmails: false,
    weeklyDigest: true,
    monthlyReport: true,
    instantAlerts: true,
    dailySummary: false,
    quietHoursEnabled: false,
    quietHoursStart: '22:00',
    quietHoursEnd: '08:00',
    timezone: 'UTC'
  }

  const preferences = userPreferences ? {
    ...defaultPreferences,
    ...userPreferences.preferences
  } : defaultPreferences

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href="/dashboard/notifications">
          <Button variant="ghost" >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Notifications
          </Button>
        </Link>
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Settings className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Notification Settings</h1>
            <p className="text-gray-600 mt-1">
              Configure how and when you receive notifications
            </p>
          </div>
        </div>
      </div>

      {/* Settings Form */}
      <NotificationSettings 
        userId={session.user.id}
        initialPreferences={preferences}
      />
    </div>
  )
}
