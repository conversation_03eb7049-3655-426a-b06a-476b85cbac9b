import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const templateSchema = z.object({
  name: z.string().min(1, 'Template name is required'),
  description: z.string().optional(),
  content: z.string().min(1, 'Template content is required'),
  category: z.string().optional(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'DRAFT']),
  variables: z.array(z.object({
    name: z.string().min(1, 'Variable name is required'),
    label: z.string().min(1, 'Variable label is required'),
    type: z.enum(['TEXT', 'NUMBER', 'DATE', 'BOOLEAN', 'EMAIL', 'PHONE']),
    required: z.boolean().default(false),
    defaultValue: z.string().optional(),
    description: z.string().optional(),
  })).optional()
})

// Get quotation templates
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search') || ''
    const category = searchParams.get('category') || ''
    const status = searchParams.get('status') || ''
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = (page - 1) * limit

    // Build where clause
    const where: any = {
      companyId: session.user.companyId,
    }

    if (search) {
      where.OR = [
        { name: { contains: search } },
        { description: { contains: search } },
        { content: { contains: search } },
      ]
    }

    if (category) {
      where.category = category
    }

    if (status) {
      where.status = status
    }

    // Fetch templates
    const [templates, totalCount] = await Promise.all([
      prisma.quotationTemplate.findMany({
        where,
        include: {
          createdBy: {
            select: { name: true, firstName: true, lastName: true }
          },
          _count: {
            select: {
              quotations: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip: offset,
        take: limit,
      }),
      prisma.quotationTemplate.count({ where })
    ])

    return NextResponse.json({
      templates,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching quotation templates:', error)
    return NextResponse.json(
      { error: 'Failed to fetch quotation templates' },
      { status: 500 }
    )
  }
}

// Create quotation template
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = templateSchema.parse(body)

    // Check if template name already exists
    const existingTemplate = await prisma.quotationTemplate.findFirst({
      where: {
        name: validatedData.name,
        companyId: session.user.companyId,
      }
    })

    if (existingTemplate) {
      return NextResponse.json(
        { error: 'Template name already exists' },
        { status: 400 }
      )
    }

    // Create template
    const template = await prisma.quotationTemplate.create({
      data: {
        ...validatedData,
        companyId: session.user.companyId,
        createdById: session.user.id,
      },
      include: {
        createdBy: {
          select: { name: true, firstName: true, lastName: true }
        },
        _count: {
          select: {
            quotations: true
          }
        }
      }
    })

    // Log activity
    await prisma.activity.create({
      data: {
        type: 'NOTE',
        title: 'Quotation template created',
        description: `Quotation template "${template.name}" was created`,
        companyId: session.user.companyId,
        createdById: session.user.id,
        metadata: {
          templateId: template.id,
          templateName: template.name,
          templateCategory: template.category
        }
      }
    })

    return NextResponse.json(template, { status: 201 })
  } catch (error) {
    console.error('Error creating quotation template:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Failed to create quotation template' },
      { status: 500 }
    )
  }
}

// Update quotation template
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const templateId = searchParams.get('id')

    if (!templateId) {
      return NextResponse.json({ error: 'Template ID is required' }, { status: 400 })
    }

    const body = await request.json()
    const validatedData = templateSchema.parse(body)

    // Check if template exists and belongs to company
    const existingTemplate = await prisma.quotationTemplate.findFirst({
      where: {
        id: templateId,
        companyId: session.user.companyId
      }
    })

    if (!existingTemplate) {
      return NextResponse.json({ error: 'Template not found' }, { status: 404 })
    }

    // Update template
    const template = await prisma.quotationTemplate.update({
      where: { id: templateId },
      data: {
        ...validatedData,
        updatedAt: new Date()
      },
      include: {
        createdBy: {
          select: { name: true, firstName: true, lastName: true }
        },
        _count: {
          select: {
            quotations: true
          }
        }
      }
    })

    // Log activity
    await prisma.activity.create({
      data: {
        type: 'QUOTATION_TEMPLATE',
        title: 'Quotation template updated',
        description: `Quotation template "${template.name}" was updated`,
        companyId: session.user.companyId,
        createdById: session.user.id,
        metadata: {
          templateId: template.id,
          templateName: template.name,
          templateCategory: template.category
        }
      }
    })

    return NextResponse.json(template)
  } catch (error) {
    console.error('Error updating quotation template:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Failed to update quotation template' },
      { status: 500 }
    )
  }
}

// Delete quotation template
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const templateId = searchParams.get('id')

    if (!templateId) {
      return NextResponse.json({ error: 'Template ID is required' }, { status: 400 })
    }

    // Check if template exists and belongs to company
    const existingTemplate = await prisma.quotationTemplate.findFirst({
      where: {
        id: templateId,
        companyId: session.user.companyId
      }
    })

    if (!existingTemplate) {
      return NextResponse.json({ error: 'Template not found' }, { status: 404 })
    }

    // Delete template
    await prisma.quotationTemplate.delete({
      where: { id: templateId }
    })

    // Log activity
    await prisma.activity.create({
      data: {
        type: 'QUOTATION_TEMPLATE',
        title: 'Quotation template deleted',
        description: `Quotation template "${existingTemplate.name}" was deleted`,
        companyId: session.user.companyId,
        createdById: session.user.id,
        metadata: {
          templateId: existingTemplate.id,
          templateName: existingTemplate.name,
          templateCategory: existingTemplate.category
        }
      }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting quotation template:', error)
    return NextResponse.json(
      { error: 'Failed to delete quotation template' },
      { status: 500 }
    )
  }
}
