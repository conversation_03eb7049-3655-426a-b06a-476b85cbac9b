'use client'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { formatCurrency } from '@/lib/utils'
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown,
  DollarSign, 
  Users, 
  FileText,
  Receipt,

  Package,
  Target,
  Calendar,
  AlertTriangle,
  CheckCircle
} from 'lucide-react'

interface ReportsOverviewProps {
  data: {
    totalCustomers: number
    totalLeads: number
    totalQuotations: number
    totalInvoices: number
    totalContracts: number
    totalReceipts: number
    totalItems: number
    totalTemplates: number
    monthlyRevenue: number
    lastMonthRevenue: number
    yearlyRevenue: number
    outstandingAmount: number
    outstandingCount: number
    quotationConversion: number
    invoicePayment: number
    contractSignature: number
  }
}

export function ReportsOverview({ data }: ReportsOverviewProps) {
  // Calculate growth rates
  const revenueGrowth = data.lastMonthRevenue > 0 
    ? ((data.monthlyRevenue - data.lastMonthRevenue) / data.lastMonthRevenue * 100).toFixed(1)
    : '0'
  const isRevenueGrowthPositive = parseFloat(revenueGrowth) >= 0

  const overviewCards = [
    {
      title: 'Monthly Revenue',
      value: formatCurrency(data.monthlyRevenue),
      icon: DollarSign,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      change: `${isRevenueGrowthPositive ? '+' : ''}${revenueGrowth}%`,
      changeColor: isRevenueGrowthPositive ? 'text-green-600' : 'text-red-600',
      changeIcon: isRevenueGrowthPositive ? TrendingUp : TrendingDown
    },
    {
      title: 'Total Customers',
      value: data.totalCustomers.toString(),
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      change: 'Active',
      changeColor: 'text-blue-600',
      changeIcon: CheckCircle
    },
    {
      title: 'Outstanding Invoices',
      value: formatCurrency(data.outstandingAmount),
      icon: AlertTriangle,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      change: `${data.outstandingCount} invoices`,
      changeColor: 'text-orange-600',
      changeIcon: FileText
    },
    {
      title: 'Yearly Revenue',
      value: formatCurrency(data.yearlyRevenue),
      icon: BarChart3,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      change: 'YTD Total',
      changeColor: 'text-purple-600',
      changeIcon: Calendar
    }
  ]

  const performanceCards = [
    {
      title: 'Quotation Conversion',
      value: `${data.quotationConversion}%`,
      icon: Target,
      color: data.quotationConversion >= 70 ? 'text-green-600' : data.quotationConversion >= 40 ? 'text-yellow-600' : 'text-red-600',
      bgColor: data.quotationConversion >= 70 ? 'bg-green-100' : data.quotationConversion >= 40 ? 'bg-yellow-100' : 'bg-red-100',
      description: 'Quotes to contracts'
    },
    {
      title: 'Invoice Payment Rate',
      value: `${data.invoicePayment}%`,
      icon: Receipt,
      color: data.invoicePayment >= 80 ? 'text-green-600' : data.invoicePayment >= 60 ? 'text-yellow-600' : 'text-red-600',
      bgColor: data.invoicePayment >= 80 ? 'bg-green-100' : data.invoicePayment >= 60 ? 'bg-yellow-100' : 'bg-red-100',
      description: 'Invoices paid on time'
    },
    {
      title: 'Contract Signature Rate',
      value: `${data.contractSignature}%`,
      icon: FileText,
 Receipt,
      color: data.contractSignature >= 75 ? 'text-green-600' : data.contractSignature >= 50 ? 'text-yellow-600' : 'text-red-600',
      bgColor: data.contractSignature >= 75 ? 'bg-green-100' : data.contractSignature >= 50 ? 'bg-yellow-100' : 'bg-red-100',
      description: 'Contracts signed'
    }
  ]

  const businessMetrics = [
    {
      title: 'Total Leads',
      value: data.totalLeads,
      icon: Users,
      color: 'text-indigo-600'
    },
    {
      title: 'Total Quotations',
      value: data.totalQuotations,
      icon: FileText,
 Receipt,
      color: 'text-blue-600'
    },
    {
      title: 'Total Invoices',
      value: data.totalInvoices,
      icon: Receipt,
      color: 'text-green-600'
    },
    {
      title: 'Total Contracts',
      value: data.totalContracts,
      icon: FileText,
 Receipt,
      color: 'text-purple-600'
    },
    {
      title: 'Payment Receipts',
      value: data.totalReceipts,
      icon: DollarSign,
      color: 'text-emerald-600'
    },
    {
      title: 'Items/Services',
      value: data.totalItems,
      icon: Package,
      color: 'text-orange-600'
    },
    {
      title: 'Contract Templates',
      value: data.totalTemplates,
      icon: FileText,
 Receipt,
      color: 'text-gray-600'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Key Financial Metrics */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Key Financial Metrics</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {overviewCards.map((card, index) => {
            const ChangeIcon = card.changeIcon
            
            return (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">
                    {card.title}
                  </CardTitle>
                  <div className={`p-2 rounded-lg ${card.bgColor}`}>
                    <card.icon className={`h-4 w-4 ${card.color}`} />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-gray-900">
                    {card.value}
                  </div>
                  <div className={`flex items-center text-xs mt-1 ${card.changeColor}`}>
                    <ChangeIcon className="h-3 w-3 mr-1" />
                    <span>{card.change}</span>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      </div>

      {/* Performance Indicators */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Performance Indicators</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {performanceCards.map((card, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {card.title}
                </CardTitle>
                <div className={`p-2 rounded-lg ${card.bgColor}`}>
                  <card.icon className={`h-4 w-4 ${card.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold ${card.color}`}>
                  {card.value}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {card.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Business Overview */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Business Overview</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
          {businessMetrics.map((metric, index) => (
            <Card key={index} className="text-center hover:shadow-lg transition-shadow">
              <CardContent className="p-4">
                <div className={`inline-flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 mb-2`}>
                  <metric.icon className={`h-4 w-4 ${metric.color}`} />
                </div>
                <div className="text-lg font-bold text-gray-900">
                  {metric.value}
                </div>
                <div className="text-xs text-gray-600">
                  {metric.title}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Quick Insights */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Insights</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-800">
                {data.totalCustomers > 0 ? (data.totalQuotations / data.totalCustomers).toFixed(1) : '0'}
              </div>
              <div className="text-sm text-blue-600">Avg. Quotes per Customer</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-800">
                {data.totalInvoices > 0 ? formatCurrency(data.yearlyRevenue / data.totalInvoices) : formatCurrency(0)}
              </div>
              <div className="text-sm text-green-600">Avg. Invoice Value</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-800">
                {data.totalCustomers > 0 ? formatCurrency(data.yearlyRevenue / data.totalCustomers) : formatCurrency(0)}
              </div>
              <div className="text-sm text-purple-600">Revenue per Customer</div>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-800">
                {data.outstandingCount > 0 ? (data.outstandingAmount / data.outstandingCount).toFixed(0) : '0'}
              </div>
              <div className="text-sm text-orange-600">Avg. Outstanding Amount</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
