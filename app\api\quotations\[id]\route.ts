import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const quotationItemSchema = z.object({
  itemId: z.string().optional(),
  name: z.string().min(1, 'Item name is required'),
  description: z.string().optional(),
  quantity: z.number().min(0.01, 'Quantity must be greater than 0'),
  unitPrice: z.number().min(0, 'Unit price must be non-negative'),
  discount: z.number().min(0).max(100).default(0),
  taxRate: z.number().min(0).max(100).default(18),
})

const quotationSchema = z.object({
  quotationNumber: z.string().min(1, 'Quotation number is required'),
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  customerId: z.string().min(1, 'Customer is required'),
  leadId: z.string().optional(),
  validUntil: z.date().optional(),
  terms: z.string().optional(),
  paymentTerms: z.string().optional(),
  taxRate: z.number().min(0).max(100).default(18),
  discountType: z.enum(['PERCENTAGE', 'FIXED']).default('PERCENTAGE'),
  discountValue: z.number().min(0).default(0),
  subtotal: z.number().min(0),
  total: z.number().min(0),
  items: z.array(quotationItemSchema).min(1, 'At least one item is required'),
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const quotation = await prisma.quotation.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      },
      include: {
        customer: {
          select: { 
            id: true, 
            name: true, 
            email: true, 
            company: true, 
            phone: true,
            address: true,
            city: true,
            state: true,
            country: true,
            postalCode: true
          }
        },
        lead: {
          select: { id: true, title: true, value: true }
        },
        items: {
          include: {
            item: {
              select: { name: true, category: true, unit: true }
            }
          }
        },
        activities: {
          include: {
            createdBy: {
              select: { name: true, firstName: true, lastName: true }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: 15,
        },
        _count: {
          select: {
            items: true,
            activities: true,
          }
        }
      }
    })

    if (!quotation) {
      return NextResponse.json({ error: 'Quotation not found' }, { status: 404 })
    }

    return NextResponse.json(quotation)
  } catch (error) {
    console.error('Error fetching quotation:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = quotationSchema.parse(body)

    // Check if quotation exists and belongs to the company
    const existingQuotation = await prisma.quotation.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      }
    })

    if (!existingQuotation) {
      return NextResponse.json({ error: 'Quotation not found' }, { status: 404 })
    }

    // Check if quotation can be edited (only DRAFT quotations can be fully edited)
    if (existingQuotation.status !== 'DRAFT') {
      return NextResponse.json(
        { error: 'Only draft quotations can be edited' },
        { status: 400 }
      )
    }

    // Verify customer belongs to the company
    const customer = await prisma.customer.findFirst({
      where: {
        id: validatedData.customerId,
        companyId: session.user.companyId,
      }
    })

    if (!customer) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 400 }
      )
    }

    // Verify lead belongs to the company (if provided)
    if (validatedData.leadId) {
      const lead = await prisma.lead.findFirst({
        where: {
          id: validatedData.leadId,
          companyId: session.user.companyId,
        }
      })

      if (!lead) {
        return NextResponse.json(
          { error: 'Lead not found' },
          { status: 400 }
        )
      }
    }

    // Check if quotation number conflicts with another quotation
    if (validatedData.quotationNumber !== existingQuotation.quotationNumber) {
      const conflictingQuotation = await prisma.quotation.findFirst({
        where: {
          quotationNumber: validatedData.quotationNumber,
          companyId: session.user.companyId,
          id: { not: params.id },
        }
      })

      if (conflictingQuotation) {
        return NextResponse.json(
          { error: 'Quotation number already exists' },
          { status: 400 }
        )
      }
    }

    const quotation = await prisma.quotation.update({
      where: { id: params.id },
      data: {
        quotationNumber: validatedData.quotationNumber,
        title: validatedData.title,
        description: validatedData.description,
        validUntil: validatedData.validUntil,
        terms: validatedData.terms,
        paymentTerms: validatedData.paymentTerms,
        taxRate: validatedData.taxRate,
        discountType: validatedData.discountType,
        discountValue: validatedData.discountValue,
        subtotal: validatedData.subtotal,
        total: validatedData.total,
        customerId: validatedData.customerId,
        leadId: validatedData.leadId,
        items: {
          deleteMany: {},
          create: validatedData.items.map(item => ({
            itemId: item.itemId,
            name: item.name,
            description: item.description,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            discount: item.discount,
            taxRate: item.taxRate,
          }))
        }
      },
      include: {
        customer: {
          select: { id: true, name: true, email: true, company: true }
        },
        lead: {
          select: { id: true, title: true }
        },
        items: {
          include: {
            item: {
              select: { name: true, category: true }
            }
          }
        },
        _count: {
          select: {
            items: true,
            activities: true,
          }
        }
      }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'NOTE',
        title: 'Quotation updated',
        description: `Quotation "${quotation.quotationNumber}" was updated`,
        quotationId: quotation.id,
        customerId: quotation.customerId,
        leadId: quotation.leadId,
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    return NextResponse.json(quotation)
  } catch (error) {
    console.error('Error updating quotation:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if quotation exists and belongs to the company
    const quotation = await prisma.quotation.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      }
    })

    if (!quotation) {
      return NextResponse.json({ error: 'Quotation not found' }, { status: 404 })
    }

    // Check if quotation can be deleted (only DRAFT quotations can be deleted)
    if (quotation.status !== 'DRAFT') {
      return NextResponse.json(
        { error: 'Only draft quotations can be deleted' },
        { status: 400 }
      )
    }

    await prisma.quotation.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: 'Quotation deleted successfully' })
  } catch (error) {
    console.error('Error deleting quotation:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
