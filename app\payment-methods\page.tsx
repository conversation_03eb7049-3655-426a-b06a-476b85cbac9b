import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  CreditCard, 
  Smartphone, 
  Building, 
  Wallet, 
  Globe,
  Shield,
  Zap,
  CheckCircle,
  Star,
  ArrowRight
} from 'lucide-react'
import Link from 'next/link'

export default function PaymentMethodsPage() {
  const paymentMethods = [
    {
      category: 'Credit & Debit Cards',
      icon: CreditCard,
      color: 'blue',
      methods: [
        { name: 'Visa', supported: true },
        { name: 'Mastercard', supported: true },
        { name: 'American Express', supported: true },
        { name: 'RuPay', supported: true },
        { name: 'Diners Club', supported: true },
        { name: 'Discover', supported: true }
      ],
      description: 'All major credit and debit cards accepted worldwide',
      features: ['Instant processing', '3D Secure authentication', 'International cards supported']
    },
    {
      category: 'UPI Payments',
      icon: Smartphone,
      color: 'green',
      methods: [
        { name: 'PhonePe', supported: true },
        { name: 'Google Pay', supported: true },
        { name: 'Paytm', supported: true },
        { name: 'BHIM UPI', supported: true },
        { name: 'Amazon Pay', supported: true },
        { name: 'WhatsApp Pay', supported: true }
      ],
      description: 'Unified Payments Interface - India\'s instant payment system',
      features: ['Instant transfers', 'QR code payments', '24/7 availability']
    },
    {
      category: 'Net Banking',
      icon: Building,
      color: 'purple',
      methods: [
        { name: 'State Bank of India', supported: true },
        { name: 'HDFC Bank', supported: true },
        { name: 'ICICI Bank', supported: true },
        { name: 'Axis Bank', supported: true },
        { name: 'Kotak Mahindra Bank', supported: true },
        { name: '50+ Other Banks', supported: true }
      ],
      description: 'Direct bank transfers through internet banking',
      features: ['Secure bank-grade encryption', 'No additional charges', 'All major banks supported']
    },
    {
      category: 'Digital Wallets',
      icon: Wallet,
      color: 'orange',
      methods: [
        { name: 'Paytm Wallet', supported: true },
        { name: 'PhonePe Wallet', supported: true },
        { name: 'Amazon Pay Wallet', supported: true },
        { name: 'Mobikwik', supported: true },
        { name: 'Freecharge', supported: true },
        { name: 'Airtel Money', supported: true }
      ],
      description: 'Popular digital wallet services for quick payments',
      features: ['Quick checkout', 'Cashback offers', 'Loyalty rewards']
    }
  ]

  const securityFeatures = [
    {
      icon: Shield,
      title: 'Bank-Grade Security',
      description: 'All transactions are protected with 256-bit SSL encryption'
    },
    {
      icon: CheckCircle,
      title: 'PCI DSS Compliant',
      description: 'Certified compliance with Payment Card Industry standards'
    },
    {
      icon: Zap,
      title: 'Instant Processing',
      description: 'Most payments are processed instantly with real-time confirmation'
    },
    {
      icon: Globe,
      title: 'Global Acceptance',
      description: 'Accept payments from customers worldwide in multiple currencies'
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Payment Methods</h1>
              <p className="text-gray-600 mt-2">Multiple secure payment options for your convenience</p>
            </div>
            <Link href="/auth/signup-new">
              <Button size="lg">
                Get Started
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Payment Methods Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
          {paymentMethods.map((category, index) => {
            const Icon = category.icon
            return (
              <Card key={index} className="h-full">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className={`p-3 rounded-lg bg-${category.color}-100`}>
                      <Icon className={`h-6 w-6 text-${category.color}-600`} />
                    </div>
                    <div>
                      <CardTitle className="text-xl">{category.category}</CardTitle>
                      <CardDescription>{category.description}</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Supported Methods */}
                  <div>
                    <h4 className="font-medium mb-3">Supported Options:</h4>
                    <div className="grid grid-cols-2 gap-2">
                      {category.methods.map((method, methodIndex) => (
                        <div key={methodIndex} className="flex items-center space-x-2 p-2 bg-gray-50 rounded">
                          <CheckCircle className="h-3 w-3 text-green-500" />
                          <span className="text-sm">{method.name}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Features */}
                  <div>
                    <h4 className="font-medium mb-3">Key Features:</h4>
                    <ul className="space-y-1">
                      {category.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                          <Star className="h-3 w-3 text-yellow-500 mr-2" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Security Features */}
        <div className="mb-16">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Security & Trust</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Your payment security is our top priority. We use industry-leading security measures to protect your financial information.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {securityFeatures.map((feature, index) => {
              const Icon = feature.icon
              return (
                <Card key={index} className="text-center">
                  <CardContent className="p-6">
                    <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                      <Icon className="h-6 w-6 text-green-600" />
                    </div>
                    <h3 className="font-medium text-gray-900 mb-2">{feature.title}</h3>
                    <p className="text-sm text-gray-600">{feature.description}</p>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>

        {/* Currency Support */}
        <Card className="mb-16">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl">Multi-Currency Support</CardTitle>
            <CardDescription>Accept payments in multiple currencies worldwide</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-gray-900">₹ INR</div>
                <div className="text-sm text-gray-600">Indian Rupee</div>
              </div>
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-gray-900">$ USD</div>
                <div className="text-sm text-gray-600">US Dollar</div>
              </div>
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-gray-900">€ EUR</div>
                <div className="text-sm text-gray-600">Euro</div>
              </div>
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-gray-900">£ GBP</div>
                <div className="text-sm text-gray-600">British Pound</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Call to Action */}
        <div className="text-center">
          <Card className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold mb-4">Ready to Get Started?</h2>
              <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
                Choose from our flexible plans and start accepting payments with all these secure payment methods.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/auth/signup-new">
                  <Button size="lg" variant="secondary" className="w-full sm:w-auto">
                    Start Free Trial
                  </Button>
                </Link>
                <Link href="/pricing">
                  <Button size="lg" variant="outline" className="w-full sm:w-auto text-white border-white hover:bg-white hover:text-blue-600">
                    View Pricing
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
