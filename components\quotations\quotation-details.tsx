'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { getStatusColor, formatDate, formatCurrency } from '@/lib/utils'
import { 
  User, 
  Building, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar,
  DollarSign,
  FileText,
  TrendingUp,
  Clock,
  Edit,
  Send,
  Copy,
  Download
} from 'lucide-react'
import toast from 'react-hot-toast'

interface QuotationDetailsProps {
  quotation: {
    id: string
    quotationNumber: string
    title: string
    description: string | null
    status: string
    total: number
    subtotal: number
    taxRate: number
    discountType: string
    discountValue: number
    validUntil: Date | null
    terms: string | null
    paymentTerms: string | null
    createdAt: Date
    sentAt: Date | null
    customer: {
      id: string
      name: string
      email: string | null
      company: string | null
      phone: string | null
      address: string | null
      city: string | null
      state: string | null
      country: string | null
      postalCode: string | null
    } | null
    lead: {
      id: string
      title: string
      value: number | null
    } | null
    items: Array<{
      id: string
      name: string
      description: string | null
      quantity: number
      unitPrice: number
      discount: number
      taxRate: number
    }>
    _count: {
      items: number
      activities: number
    }
  }
}

export function QuotationDetails({ quotation }: QuotationDetailsProps) {
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false)
  const [isSending, setIsSending] = useState(false)

  const handleStatusChange = async (newStatus: string) => {
    setIsUpdatingStatus(true)
    try {
      const response = await fetch(`/api/quotations/${quotation.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      })

      if (!response.ok) {
        throw new Error('Failed to update quotation status')
      }

      toast.success('Quotation status updated successfully')
      window.location.reload()
    } catch (error) {
      toast.error('Failed to update quotation status')
    } finally {
      setIsUpdatingStatus(false)
    }
  }

  const handleSend = async () => {
    setIsSending(true)
    try {
      const response = await fetch(`/api/quotations/${quotation.id}/send`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to send quotation')
      }

      toast.success('Quotation sent successfully')
      window.location.reload()
    } catch (error) {
      toast.error('Failed to send quotation')
    } finally {
      setIsSending(false)
    }
  }

  const handleDuplicate = async () => {
    try {
      const response = await fetch(`/api/quotations/${quotation.id}/duplicate`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to duplicate quotation')
      }

      const result = await response.json()
      toast.success('Quotation duplicated successfully')
      window.location.href = `/dashboard/quotations/${result.id}/edit`
    } catch (error) {
      toast.error('Failed to duplicate quotation')
    }
  }

  const fullAddress = quotation.customer ? [
    quotation.customer.address,
    quotation.customer.city,
    quotation.customer.state,
    quotation.customer.country
  ].filter(Boolean).join(', ') : null

  const statusOptions = [
    { value: 'DRAFT', label: 'Draft' },
    { value: 'SENT', label: 'Sent' },
    { value: 'VIEWED', label: 'Viewed' },
    { value: 'ACCEPTED', label: 'Accepted' },
    { value: 'REJECTED', label: 'Rejected' },
    { value: 'EXPIRED', label: 'Expired' },
  ]

  const isExpired = quotation.validUntil && new Date(quotation.validUntil) < new Date()
  const daysUntilExpiry = quotation.validUntil 
    ? Math.ceil((new Date(quotation.validUntil).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
    : null

  return (
    <div className="space-y-6">
      {/* Quotation Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Quotation Information</CardTitle>
            <div className="flex items-center space-x-2">
              {quotation.status === 'DRAFT' && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSend}
                    disabled={isSending}
                    className="text-blue-600 hover:text-blue-700"
                  >
                    <Send className="h-4 w-4 mr-2" />
                    {isSending ? 'Sending...' : 'Send'}
                  </Button>
                  <Link href={`/dashboard/quotations/${quotation.id}/edit`}>
                    <Button variant="outline" >
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                  </Link>
                </>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={handleDuplicate}
                className="text-green-600 hover:text-green-700"
              >
                <Copy className="h-4 w-4 mr-2" />
                Duplicate
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Quotation Information */}
            <div className="space-y-4">
              <h3 className="font-medium text-gray-900">Quotation Details</h3>
              
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-gray-600">Quotation Number</p>
                  <p className="font-medium text-gray-900">{quotation.quotationNumber}</p>
                </div>

                <div>
                  <p className="text-sm text-gray-600">Title</p>
                  <p className="font-medium text-gray-900">{quotation.title}</p>
                </div>

                {quotation.description && (
                  <div>
                    <p className="text-sm text-gray-600">Description</p>
                    <p className="text-gray-900 whitespace-pre-wrap">{quotation.description}</p>
                  </div>
                )}

                <div className="flex items-center space-x-4">
                  <div>
                    <p className="text-sm text-gray-600">Status</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <Badge className={getStatusColor(quotation.status)} variant="outline">
                        {quotation.status}
                      </Badge>
                      {quotation.status === 'DRAFT' && (
                        <select
                          value={quotation.status}
                          onChange={(e) => handleStatusChange(e.target.value)}
                          disabled={isUpdatingStatus}
                          className="text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          {statusOptions.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <DollarSign className="h-4 w-4 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-600">Total Amount</p>
                    <p className="font-medium text-gray-900 text-lg">{formatCurrency(quotation.total)}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-600">Created</p>
                    <p className="font-medium text-gray-900">{formatDate(quotation.createdAt)}</p>
                  </div>
                </div>

                {quotation.sentAt && (
                  <div className="flex items-center space-x-3">
                    <Send className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">Sent</p>
                      <p className="font-medium text-gray-900">{formatDate(quotation.sentAt)}</p>
                    </div>
                  </div>
                )}

                {quotation.validUntil && (
                  <div className="flex items-center space-x-3">
                    <Clock className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">Valid Until</p>
                      <div className="flex items-center space-x-2">
                        <p className={`font-medium ${isExpired ? 'text-red-600' : 'text-gray-900'}`}>
                          {formatDate(quotation.validUntil)}
                        </p>
                        {daysUntilExpiry !== null && (
                          <Badge 
                            variant={isExpired ? 'destructive' : daysUntilExpiry <= 7 ? 'secondary' : 'outline'}
                            className="text-xs"
                          >
                            {isExpired ? 'Expired' : `${daysUntilExpiry} days left`}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Customer Information */}
            <div className="space-y-4">
              <h3 className="font-medium text-gray-900">Customer Information</h3>
              
              {quotation.customer ? (
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <User className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">Name</p>
                      <Link 
                        href={`/dashboard/customers/${quotation.customer.id}`}
                        className="font-medium text-blue-600 hover:text-blue-800"
                      >
                        {quotation.customer.name}
                      </Link>
                    </div>
                  </div>

                  {quotation.customer.company && (
                    <div className="flex items-center space-x-3">
                      <Building className="h-4 w-4 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-600">Company</p>
                        <p className="font-medium text-gray-900">{quotation.customer.company}</p>
                      </div>
                    </div>
                  )}

                  {quotation.customer.email && (
                    <div className="flex items-center space-x-3">
                      <Mail className="h-4 w-4 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-600">Email</p>
                        <a 
                          href={`mailto:${quotation.customer.email}`}
                          className="font-medium text-blue-600 hover:text-blue-800"
                        >
                          {quotation.customer.email}
                        </a>
                      </div>
                    </div>
                  )}

                  {quotation.customer.phone && (
                    <div className="flex items-center space-x-3">
                      <Phone className="h-4 w-4 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-600">Phone</p>
                        <a 
                          href={`tel:${quotation.customer.phone}`}
                          className="font-medium text-blue-600 hover:text-blue-800"
                        >
                          {quotation.customer.phone}
                        </a>
                      </div>
                    </div>
                  )}

                  {fullAddress && (
                    <div className="flex items-start space-x-3">
                      <MapPin className="h-4 w-4 text-gray-400 mt-1" />
                      <div>
                        <p className="text-sm text-gray-600">Address</p>
                        <p className="font-medium text-gray-900">{fullAddress}</p>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <p className="text-gray-500 italic">No customer assigned</p>
              )}

              {/* Related Lead */}
              {quotation.lead && (
                <div className="pt-4 border-t">
                  <h4 className="font-medium text-gray-900 mb-2">Related Lead</h4>
                  <div className="flex items-center space-x-3">
                    <TrendingUp className="h-4 w-4 text-gray-400" />
                    <div>
                      <Link 
                        href={`/dashboard/leads/${quotation.lead.id}`}
                        className="font-medium text-blue-600 hover:text-blue-800"
                      >
                        {quotation.lead.title}
                      </Link>
                      {quotation.lead.value && (
                        <p className="text-sm text-gray-600">
                          Estimated Value: {formatCurrency(quotation.lead.value)}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Activity Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 mb-2">
                <FileText className="h-4 w-4 text-blue-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900">{quotation._count.items}</div>
              <div className="text-sm text-gray-600">Items</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-green-100 mb-2">
                <DollarSign className="h-4 w-4 text-green-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900">{formatCurrency(quotation.subtotal)}</div>
              <div className="text-sm text-gray-600">Subtotal</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-purple-100 mb-2">
                <Calendar className="h-4 w-4 text-purple-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900">{quotation._count.activities}</div>
              <div className="text-sm text-gray-600">Activities</div>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-orange-100 mb-2">
                <Clock className="h-4 w-4 text-orange-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900">
                {Math.ceil((new Date().getTime() - new Date(quotation.createdAt).getTime()) / (1000 * 60 * 60 * 24))}
              </div>
              <div className="text-sm text-gray-600">Days Old</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
