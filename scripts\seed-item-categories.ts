import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// Sample item categories
const sampleCategories = [
  {
    name: 'Electronics',
    description: 'Electronic devices and components',
    color: '#3B82F6',
    icon: 'package',
    children: [
      {
        name: 'Computers',
        description: 'Desktop and laptop computers',
        color: '#1E40AF',
        icon: 'package'
      },
      {
        name: 'Mobile Devices',
        description: 'Smartphones and tablets',
        color: '#2563EB',
        icon: 'package'
      },
      {
        name: 'Accessories',
        description: 'Electronic accessories and peripherals',
        color: '#3B82F6',
        icon: 'package'
      }
    ]
  },
  {
    name: 'Office Supplies',
    description: 'Office equipment and supplies',
    color: '#10B981',
    icon: 'folder',
    children: [
      {
        name: 'Stationery',
        description: 'Pens, papers, and writing materials',
        color: '#059669',
        icon: 'tag'
      },
      {
        name: 'Furniture',
        description: 'Office desks, chairs, and furniture',
        color: '#047857',
        icon: 'package'
      }
    ]
  },
  {
    name: 'Services',
    description: 'Professional services and consulting',
    color: '#8B5CF6',
    icon: 'tool',
    children: [
      {
        name: 'Consulting',
        description: 'Business and technical consulting services',
        color: '#7C3AED',
        icon: 'star'
      },
      {
        name: 'Support',
        description: 'Technical support and maintenance',
        color: '#6D28D9',
        icon: 'shield'
      },
      {
        name: 'Training',
        description: 'Training and educational services',
        color: '#5B21B6',
        icon: 'award'
      }
    ]
  },
  {
    name: 'Software',
    description: 'Software products and licenses',
    color: '#F59E0B',
    icon: 'settings',
    children: [
      {
        name: 'Productivity',
        description: 'Productivity and office software',
        color: '#D97706',
        icon: 'tool'
      },
      {
        name: 'Development',
        description: 'Development tools and IDEs',
        color: '#B45309',
        icon: 'settings'
      }
    ]
  },
  {
    name: 'Marketing',
    description: 'Marketing and advertising services',
    color: '#EF4444',
    icon: 'flag',
    children: [
      {
        name: 'Digital Marketing',
        description: 'Online marketing and SEO services',
        color: '#DC2626',
        icon: 'star'
      },
      {
        name: 'Print Marketing',
        description: 'Print advertising and materials',
        color: '#B91C1C',
        icon: 'bookmark'
      }
    ]
  }
]

async function seedItemCategories() {
  try {
    console.log('🌱 Starting item category seeding...')

    // Get the first company for seeding
    const company = await prisma.company.findFirst({
      include: {
        users: true
      }
    })

    if (!company || !company.users.length) {
      console.log('❌ No company or users found. Please ensure you have at least one company and user.')
      return
    }

    const user = company.users[0]
    console.log(`📍 Seeding categories for company: ${company.name}`)

    // Seed categories
    for (const categoryData of sampleCategories) {
      console.log(`📁 Creating category: ${categoryData.name}`)
      
      // Create parent category
      const existingParent = await prisma.itemCategory.findFirst({
        where: {
          name: categoryData.name,
          companyId: company.id
        }
      })

      const parentCategory = existingParent || await prisma.itemCategory.create({
        data: {
          name: categoryData.name,
          description: categoryData.description,
          color: categoryData.color,
          icon: categoryData.icon,
          companyId: company.id,
          createdById: user.id,
          isActive: true
        }
      })

      console.log(`  ✅ Created parent category: ${parentCategory.name}`)

      // Create child categories
      if (categoryData.children) {
        for (const childData of categoryData.children) {
          const existingChild = await prisma.itemCategory.findFirst({
            where: {
              name: childData.name,
              companyId: company.id
            }
          })

          const childCategory = existingChild || await prisma.itemCategory.create({
            data: {
              name: childData.name,
              description: childData.description,
              color: childData.color,
              icon: childData.icon,
              parentId: parentCategory.id,
              companyId: company.id,
              createdById: user.id,
              isActive: true
            }
          })

          console.log(`    ✅ Created child category: ${childCategory.name}`)
        }
      }
    }

    // Create some sample items with categories
    console.log('📦 Creating sample items with categories...')

    const categories = await prisma.itemCategory.findMany({
      where: { companyId: company.id }
    })

    const sampleItems = [
      {
        name: 'MacBook Pro 16"',
        description: 'High-performance laptop for professionals',
        price: 2499.00,
        costPrice: 2000.00,
        type: 'PRODUCT',
        sku: 'MBP-16-001',
        unit: 'piece',
        stockQuantity: 10,
        lowStockThreshold: 2,
        status: 'ACTIVE',
        categoryName: 'Computers'
      },
      {
        name: 'iPhone 15 Pro',
        description: 'Latest smartphone with advanced features',
        price: 999.00,
        costPrice: 750.00,
        type: 'PRODUCT',
        sku: 'IPH-15P-001',
        unit: 'piece',
        stockQuantity: 25,
        lowStockThreshold: 5,
        status: 'ACTIVE',
        categoryName: 'Mobile Devices'
      },
      {
        name: 'Business Consulting',
        description: 'Strategic business consulting services',
        price: 150.00,
        type: 'SERVICE',
        unit: 'hour',
        status: 'ACTIVE',
        categoryName: 'Consulting'
      },
      {
        name: 'Office Chair',
        description: 'Ergonomic office chair with lumbar support',
        price: 299.00,
        costPrice: 180.00,
        type: 'PRODUCT',
        sku: 'OFC-CHR-001',
        unit: 'piece',
        stockQuantity: 15,
        lowStockThreshold: 3,
        status: 'ACTIVE',
        categoryName: 'Furniture'
      },
      {
        name: 'Microsoft Office 365',
        description: 'Annual subscription to Office 365',
        price: 99.00,
        costPrice: 70.00,
        type: 'PRODUCT',
        sku: 'MSO-365-001',
        unit: 'license',
        stockQuantity: 100,
        lowStockThreshold: 10,
        status: 'ACTIVE',
        categoryName: 'Productivity'
      }
    ]

    for (const itemData of sampleItems) {
      const category = categories.find(c => c.name === itemData.categoryName)
      
      if (category) {
        const existingItem = await prisma.item.findFirst({
          where: {
            name: itemData.name,
            companyId: company.id
          }
        })

        if (!existingItem) {
          await prisma.item.create({
            data: {
              name: itemData.name,
              description: itemData.description,
              price: itemData.price,
              costPrice: itemData.costPrice || null,
              type: itemData.type,
              sku: itemData.sku || null,
              unit: itemData.unit || null,
              stockQuantity: itemData.stockQuantity || null,
              lowStockThreshold: itemData.lowStockThreshold || null,
              status: itemData.status,
              categoryId: category.id,
              category: category.name, // Keep legacy field for compatibility
              companyId: company.id,
              createdById: user.id,
              isActive: true
            }
          })

          console.log(`  📦 Created item: ${itemData.name} in category ${category.name}`)
        }
      }
    }

    console.log('🎉 Item category seeding completed successfully!')
    
    const finalStats = await prisma.itemCategory.groupBy({
      by: ['isActive'],
      where: { companyId: company.id },
      _count: true
    })

    const itemCount = await prisma.item.count({
      where: { companyId: company.id }
    })

    console.log(`📊 Summary:`)
    console.log(`  📁 Categories created: ${finalStats.reduce((sum, stat) => sum + stat._count, 0)}`)
    console.log(`  📦 Items created: ${itemCount}`)
    
  } catch (error) {
    console.error('❌ Error seeding item categories:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the seeding
seedItemCategories()
