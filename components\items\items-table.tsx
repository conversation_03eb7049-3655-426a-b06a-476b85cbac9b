'use client'

import Link from 'next/link'
import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { formatCurrency, getStatusColor } from '@/lib/utils'
import { 
  Eye, 
  Edit, 
  Trash2, 
  Copy,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  Package,
  ShoppingCart,
  Wrench,
  AlertTriangle,
  Archive,
  DollarSign,
  Tag,
  BarChart3
} from 'lucide-react'
import toast from 'react-hot-toast'

interface Item {
  id: string
  name: string
  description: string | null
  sku: string | null
  type: string
  category: string | null
  price: number
  costPrice: number | null
  unit: string | null
  stockQuantity: number | null
  lowStockThreshold: number | null
  status: string
  taxRate: number | null
  createdAt: Date
  _count: {
    quotationItems: number
    invoiceItems: number
  }
}

interface ItemsTableProps {
  items: Item[]
  currentPage: number
  totalPages: number
  totalCount: number
}

export function ItemsTable({ 
  items, 
  currentPage, 
  totalPages, 
  totalCount 
}: ItemsTableProps) {
  const [deletingId, setDeletingId] = useState<string | null>(null)

  const handleDelete = async (itemId: string) => {
    if (!confirm('Are you sure you want to delete this item?')) {
      return
    }

    setDeletingId(itemId)
    try {
      const response = await fetch(`/api/items/${itemId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to delete item')
      }

      toast.success('Item deleted successfully')
      window.location.reload()
    } catch (error) {
      toast.error('Failed to delete item')
    } finally {
      setDeletingId(null)
    }
  }

  const handleDuplicate = async (itemId: string) => {
    try {
      const response = await fetch(`/api/items/${itemId}/duplicate`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to duplicate item')
      }

      const result = await response.json()
      toast.success('Item duplicated successfully')
      window.location.href = `/dashboard/items/${result.id}/edit`
    } catch (error) {
      toast.error('Failed to duplicate item')
    }
  }

  const isLowStock = (item: Item) => {
    return item.type === 'PRODUCT' && 
           item.stockQuantity !== null && 
           item.lowStockThreshold !== null &&
           item.stockQuantity <= item.lowStockThreshold
  }

  const getTypeIcon = (type: string) => {
    return type === 'PRODUCT' ? ShoppingCart : Wrench
  }

  const getTypeColor = (type: string) => {
    return type === 'PRODUCT' ? 'text-purple-600' : 'text-indigo-600'
  }

  if (items.length === 0) {
    return (
      <Card>
        <CardContent className="py-12">
          <div className="text-center">
            <Package className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No items found</h3>
            <p className="text-gray-500 mb-6">
              Get started by adding your first product or service.
            </p>
            <Link href="/dashboard/items/new">
              <Button>
                Add Item
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Items ({totalCount})</span>
            <span className="text-sm font-normal text-gray-500">
              Page {currentPage} of {totalPages}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Desktop Table */}
          <div className="hidden md:block overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Item</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Type</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Category</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Price</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Stock</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Usage</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">Actions</th>
                </tr>
              </thead>
              <tbody>
                {items.map((item) => {
                  const TypeIcon = getTypeIcon(item.type)
                  return (
                    <tr key={item.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-4 px-4">
                        <div>
                          <div className="font-medium text-gray-900">{item.name}</div>
                          {item.description && (
                            <div className="text-sm text-gray-600 truncate max-w-xs">
                              {item.description}
                            </div>
                          )}
                          {item.sku && (
                            <div className="text-xs text-gray-400">SKU: {item.sku}</div>
                          )}
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center space-x-2">
                          <TypeIcon className={`h-4 w-4 ${getTypeColor(item.type)}`} />
                          <span className="text-sm font-medium text-gray-900">
                            {item.type}
                          </span>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        {item.category ? (
                          <div className="flex items-center space-x-1">
                            <Tag className="h-3 w-3 text-gray-400" />
                            <span className="text-sm text-gray-600">{item.category}</span>
                          </div>
                        ) : (
                          <span className="text-gray-400 text-sm">No category</span>
                        )}
                      </td>
                      <td className="py-4 px-4">
                        <div>
                          <span className="font-medium text-gray-900">
                            {formatCurrency(item.price)}
                          </span>
                          {item.unit && (
                            <span className="text-sm text-gray-500">/{item.unit}</span>
                          )}
                          {item.costPrice && (
                            <div className="text-xs text-gray-400">
                              Cost: {formatCurrency(item.costPrice)}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        {item.type === 'PRODUCT' ? (
                          <div className="space-y-1">
                            <div className="flex items-center space-x-2">
                              <Archive className="h-3 w-3 text-gray-400" />
                              <span className="text-sm font-medium text-gray-900">
                                {item.stockQuantity || 0}
                              </span>
                            </div>
                            {isLowStock(item) && (
                              <div className="flex items-center space-x-1 text-xs text-red-600">
                                <AlertTriangle className="h-3 w-3" />
                                <span>Low stock</span>
                              </div>
                            )}
                          </div>
                        ) : (
                          <span className="text-gray-400 text-sm">N/A</span>
                        )}
                      </td>
                      <td className="py-4 px-4">
                        <Badge className={getStatusColor(item.status)} variant="outline">
                          {item.status}
                        </Badge>
                      </td>
                      <td className="py-4 px-4">
                        <div className="text-sm text-gray-600">
                          <div>{item._count.quotationItems} quotes</div>
                          <div>{item._count.invoiceItems} invoices</div>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center justify-end space-x-2">
                          <Link href={`/dashboard/items/${item.id}`}>
                            <Button variant="ghost" >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </Link>
                          <Link href={`/dashboard/items/${item.id}/edit`}>
                            <Button variant="ghost" >
                              <Edit className="h-4 w-4" />
                            </Button>
                          </Link>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDuplicate(item.id)}
                            className="text-green-600 hover:text-green-700"
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(item.id)}
                            disabled={deletingId === item.id}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>

          {/* Mobile Cards */}
          <div className="md:hidden space-y-4">
            {items.map((item) => {
              const TypeIcon = getTypeIcon(item.type)
              return (
                <Card key={item.id} className="p-4">
                  <div className="space-y-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">{item.name}</div>
                        {item.description && (
                          <div className="text-sm text-gray-600 mt-1">{item.description}</div>
                        )}
                        {item.sku && (
                          <div className="text-xs text-gray-400 mt-1">SKU: {item.sku}</div>
                        )}
                      </div>
                      <Button variant="ghost" >
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <TypeIcon className={`h-4 w-4 ${getTypeColor(item.type)}`} />
                        <span className="text-sm font-medium text-gray-900">{item.type}</span>
                        {item.category && (
                          <>
                            <span className="text-gray-300">•</span>
                            <span className="text-sm text-gray-600">{item.category}</span>
                          </>
                        )}
                      </div>
                      <Badge className={getStatusColor(item.status)} variant="outline">
                        {item.status}
                      </Badge>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-1">
                        <DollarSign className="h-4 w-4 text-gray-400" />
                        <span className="font-medium text-gray-900">
                          {formatCurrency(item.price)}
                          {item.unit && <span className="text-gray-500">/{item.unit}</span>}
                        </span>
                      </div>
                      {item.type === 'PRODUCT' && (
                        <div className="flex items-center space-x-1">
                          <Archive className="h-4 w-4 text-gray-400" />
                          <span className="text-sm text-gray-600">
                            {item.stockQuantity || 0} in stock
                          </span>
                          {isLowStock(item) && (
                            <AlertTriangle className="h-3 w-3 text-red-600" />
                          )}
                        </div>
                      )}
                    </div>

                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <div className="flex items-center space-x-1">
                        <BarChart3 className="h-4 w-4" />
                        <span>{item._count.quotationItems} quotes • {item._count.invoiceItems} invoices</span>
                      </div>
                    </div>

                    <div className="flex items-center justify-end space-x-2 pt-2 border-t">
                      <Link href={`/dashboard/items/${item.id}`}>
                        <Button variant="ghost" >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </Link>
                      <Link href={`/dashboard/items/${item.id}/edit`}>
                        <Button variant="ghost" >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </Link>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDuplicate(item.id)}
                        className="text-green-600"
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </Card>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            Showing {((currentPage - 1) * 10) + 1} to {Math.min(currentPage * 10, totalCount)} of {totalCount} items
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage <= 1}
              asChild
            >
              <Link href={`?page=${currentPage - 1}`}>
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Link>
            </Button>
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage >= totalPages}
              asChild
            >
              <Link href={`?page=${currentPage + 1}`}>
                Next
                <ChevronRight className="h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
