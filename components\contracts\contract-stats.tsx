'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { formatCurrency } from '@/lib/utils'
import { 
  FileText, 
  Send, 
  Eye, 
  CheckCircle, 
  Play, 
  XCircle,
  Clock,
  DollarSign,
  TrendingUp,
  PenTool
} from 'lucide-react'

interface ContractStatsProps {
  stats: {
    total: number
    draft: number
    sent: number
    viewed: number
    signed: number
    executed: number
    expired: number
    cancelled: number
    totalValue: number
    signedValue: number
    pendingValue: number
  }
}

export function ContractStats({ stats }: ContractStatsProps) {
  const signatureRate = stats.total > 0 ? ((stats.signed / stats.total) * 100).toFixed(1) : '0'
  const executionRate = stats.signed > 0 ? ((stats.executed / stats.signed) * 100).toFixed(1) : '0'
  const conversionRate = (stats.sent + stats.viewed) > 0 ? 
    ((stats.signed / (stats.sent + stats.viewed)) * 100).toFixed(1) : '0'

  const statCards = [
    {
      title: 'Total Contracts',
      value: stats.total.toString(),
      icon: FileText,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      description: 'All contracts created'
    },
    {
      title: 'Draft',
      value: stats.draft.toString(),
      icon: FileText,
      color: 'text-gray-600',
      bgColor: 'bg-gray-100',
      description: 'Pending completion'
    },
    {
      title: 'Sent',
      value: stats.sent.toString(),
      icon: Send,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      description: 'Sent to customers'
    },
    {
      title: 'Viewed',
      value: stats.viewed.toString(),
      icon: Eye,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
      description: 'Opened by customers'
    },
    {
      title: 'Signed',
      value: stats.signed.toString(),
      icon: PenTool,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      description: 'Digitally signed'
    },
    {
      title: 'Executed',
      value: stats.executed.toString(),
      icon: Play,
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-100',
      description: 'Active contracts'
    },
    {
      title: 'Expired',
      value: stats.expired.toString(),
      icon: Clock,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      description: 'Past expiry date'
    },
    {
      title: 'Cancelled',
      value: stats.cancelled.toString(),
      icon: XCircle,
      color: 'text-red-600',
      bgColor: 'bg-red-100',
      description: 'Cancelled contracts'
    },
    {
      title: 'Total Value',
      value: formatCurrency(stats.totalValue),
      icon: DollarSign,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      description: 'Total contract value'
    },
    {
      title: 'Signed Value',
      value: formatCurrency(stats.signedValue),
      icon: CheckCircle,
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-100',
      description: 'Value of signed contracts'
    },
    {
      title: 'Pending Value',
      value: formatCurrency(stats.pendingValue),
      icon: Clock,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      description: 'Awaiting signature'
    },
    {
      title: 'Signature Rate',
      value: `${signatureRate}%`,
      icon: PenTool,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      description: 'Overall signature rate'
    },
    {
      title: 'Execution Rate',
      value: `${executionRate}%`,
      icon: Play,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-100',
      description: 'Signed to executed rate'
    },
    {
      title: 'Conversion Rate',
      value: `${conversionRate}%`,
      icon: TrendingUp,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      description: 'Sent to signed rate'
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-7 gap-4">
      {statCards.map((stat, index) => (
        <Card key={index} className="hover:shadow-lg transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              {stat.title}
            </CardTitle>
            <div className={`p-2 rounded-lg ${stat.bgColor}`}>
              <stat.icon className={`h-4 w-4 ${stat.color}`} />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">
              {stat.value}
            </div>
            <p className="text-xs text-gray-500 mt-1">
              {stat.description}
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
