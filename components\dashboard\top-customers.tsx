'use client'

import Link from 'next/link'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { formatCurrency } from '@/lib/utils'
import { 
  Users, 
  DollarSign, 
  FileText, 
  TrendingUp,
  Building,
  Mail,
  Phone
} from 'lucide-react'

interface TopCustomersProps {
  customers: Array<{
    id: string
    name: string
    email: string | null
    company: string | null
    phone: string | null
    status: string
    totalRevenue: number
    totalQuotations: number
    quotationValue: number
    invoices: Array<{ total: number }>
    quotations: Array<{ total: number }>
  }>
}

export function TopCustomers({ customers }: TopCustomersProps) {
  if (customers.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5 text-blue-600" />
            <span>Top Customers</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <Users className="h-8 w-8 text-gray-300 mx-auto mb-2" />
            <p className="text-sm text-gray-500">No customer data available</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const totalRevenue = customers.reduce((sum, customer) => sum + customer.totalRevenue, 0)

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Users className="h-5 w-5 text-blue-600" />
            <span>Top Customers</span>
          </div>
          <span className="text-sm font-normal text-gray-500">
            {formatCurrency(totalRevenue)} total
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {customers.map((customer, index) => {
            const revenuePercentage = totalRevenue > 0 
              ? (customer.totalRevenue / totalRevenue) * 100 
              : 0

            return (
              <div key={customer.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-gray-500">#{index + 1}</span>
                      <Link 
                        href={`/dashboard/customers/${customer.id}`}
                        className="font-medium text-blue-600 hover:text-blue-800"
                      >
                        {customer.name}
                      </Link>
                      <Badge 
                        variant="outline" 
                        className={
                          customer.status === 'ACTIVE' ? 'text-green-600 border-green-200' :
                          customer.status === 'PROSPECT' ? 'text-blue-600 border-blue-200' :
                          'text-gray-600 border-gray-200'
                        }
                      >
                        {customer.status}
                      </Badge>
                    </div>
                    
                    {customer.company && (
                      <div className="flex items-center space-x-1 mt-1">
                        <Building className="h-3 w-3 text-gray-400" />
                        <span className="text-sm text-gray-600">{customer.company}</span>
                      </div>
                    )}
                    
                    <div className="flex items-center space-x-4 mt-2">
                      {customer.email && (
                        <div className="flex items-center space-x-1">
                          <Mail className="h-3 w-3 text-gray-400" />
                          <span className="text-xs text-gray-500">{customer.email}</span>
                        </div>
                      )}
                      {customer.phone && (
                        <div className="flex items-center space-x-1">
                          <Phone className="h-3 w-3 text-gray-400" />
                          <span className="text-xs text-gray-500">{customer.phone}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="flex items-center space-x-1">
                      <DollarSign className="h-4 w-4 text-green-600" />
                      <span className="font-bold text-green-600">
                        {formatCurrency(customer.totalRevenue)}
                      </span>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      {revenuePercentage.toFixed(1)}% of total
                    </p>
                  </div>
                </div>
                
                <div className="mt-3 pt-3 border-t">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-2 bg-blue-50 rounded">
                      <div className="flex items-center justify-center space-x-1">
                        <FileText className="h-3 w-3 text-blue-600" />
                        <span className="text-sm font-medium text-blue-600">
                          {customer.totalQuotations}
                        </span>
                      </div>
                      <p className="text-xs text-blue-600">Quotations</p>
                      <p className="text-xs text-gray-500">
                        {formatCurrency(customer.quotationValue)}
                      </p>
                    </div>
                    <div className="text-center p-2 bg-green-50 rounded">
                      <div className="flex items-center justify-center space-x-1">
                        <DollarSign className="h-3 w-3 text-green-600" />
                        <span className="text-sm font-medium text-green-600">
                          {customer.invoices.length}
                        </span>
                      </div>
                      <p className="text-xs text-green-600">Invoices</p>
                      <p className="text-xs text-gray-500">
                        {formatCurrency(customer.totalRevenue)}
                      </p>
                    </div>
                  </div>
                </div>
                
                {/* Revenue Progress Bar */}
                <div className="mt-3">
                  <div className="w-full bg-gray-200 rounded-full h-1">
                    <div 
                      className="bg-green-600 h-1 rounded-full transition-all duration-300"
                      style={{ width: `${Math.min(revenuePercentage, 100)}%` }}
                    />
                  </div>
                </div>
              </div>
            )
          })}
          
          {customers.length === 0 && (
            <div className="text-center py-6">
              <Users className="h-8 w-8 text-gray-300 mx-auto mb-2" />
              <p className="text-sm text-gray-500">No customers with revenue yet</p>
            </div>
          )}
          
          <div className="text-center pt-4 border-t">
            <Link 
              href="/dashboard/customers"
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              View all customers →
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
