'use client'

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { formatDateTime } from '@/lib/utils'
import { 
  Activity,
  FileText,
  Receipt,
  TrendingUp,
  MessageSquare,
  Phone,
  Mail,
  Calendar
} from 'lucide-react'

interface CustomerActivityProps {
  activities: Array<{
    id: string
    type: string
    title: string
    description: string | null
    createdAt: Date
    createdBy: {
      name: string | null
      firstName: string | null
      lastName: string | null
    }
  }>
}

export function CustomerActivity({ activities }: CustomerActivityProps) {
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'QUOTATION_CREATED':
      case 'QUOTATION_SENT':
      case 'QUOTATION_VIEWED':
      case 'QUOTATION_ACCEPTED':
      case 'QUOTATION_REJECTED':
        return FileText
      case 'INVOICE_CREATED':
      case 'INVOICE_SENT':
      case 'INVOICE_VIEWED':
      case 'INVOICE_PAID':
        return Receipt
      case 'CONTRACT_CREATED':
      case 'CONTRACT_SIGNED':
        return FileText
      case 'LEAD_CREATED':
      case 'LEAD_CONVERTED':
        return TrendingUp
      case 'CALL':
        return Phone
      case 'EMAIL':
        return Mail
      case 'MEETING':
        return Calendar
      case 'NOTE':
        return MessageSquare
      default:
        return Activity
    }
  }

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'QUOTATION_ACCEPTED':
      case 'INVOICE_PAID':
      case 'CONTRACT_SIGNED':
      case 'LEAD_CONVERTED':
        return 'text-green-600 bg-green-100'
      case 'QUOTATION_REJECTED':
        return 'text-red-600 bg-red-100'
      case 'QUOTATION_SENT':
      case 'INVOICE_SENT':
        return 'text-blue-600 bg-blue-100'
      case 'QUOTATION_VIEWED':
      case 'INVOICE_VIEWED':
        return 'text-yellow-600 bg-yellow-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const formatActivityType = (type: string) => {
    return type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())
  }

  const getUserName = (user: any) => {
    return user.name || `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'Unknown User'
  }

  if (activities.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Activity className="h-5 w-5 mr-2" />
            Recent Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Activity className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500">No activity yet</p>
            <p className="text-sm text-gray-400 mt-1">
              Activity will appear here as you interact with this customer
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Activity className="h-5 w-5 mr-2" />
          Recent Activity
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => {
            const IconComponent = getActivityIcon(activity.type)
            const colorClasses = getActivityColor(activity.type)
            
            return (
              <div key={activity.id} className="flex items-start space-x-3">
                <div className={`p-2 rounded-full ${colorClasses} flex-shrink-0`}>
                  <IconComponent className="h-3 w-3" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">
                    {activity.title}
                  </p>
                  {activity.description && (
                    <p className="text-sm text-gray-600 mt-1">
                      {activity.description}
                    </p>
                  )}
                  <div className="flex items-center justify-between mt-2">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-700">
                      {formatActivityType(activity.type)}
                    </span>
                    <span className="text-xs text-gray-500">
                      {formatDateTime(activity.createdAt)}
                    </span>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    by {getUserName(activity.createdBy)}
                  </p>
                </div>
              </div>
            )
          })}
        </div>
        
        {activities.length >= 10 && (
          <div className="mt-6 text-center">
            <button className="text-sm text-blue-600 hover:text-blue-800 font-medium">
              View all activity
            </button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
