import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import crypto from 'crypto'

const webhookSchema = z.object({
  name: z.string().min(1, 'Webhook name is required'),
  url: z.string().url('Invalid URL'),
  events: z.array(z.string()).min(1, 'At least one event is required'),
  description: z.string().optional()
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if company has API access
    const company = await prisma.company.findUnique({
      where: { id: session.user.companyId }
    })

    if (!company || !['PROFESSIONAL', 'ENTERPRISE'].includes(company.plan)) {
      return NextResponse.json({ error: 'Webhook access not available for your plan' }, { status: 403 })
    }

    const webhooks = await prisma.webhook.findMany({
      where: { companyId: session.user.companyId },
      orderBy: { createdAt: 'desc' }
    })

    return NextResponse.json({ webhooks })
  } catch (error) {
    console.error('Error fetching webhooks:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if company has API access
    const company = await prisma.company.findUnique({
      where: { id: session.user.companyId }
    })

    if (!company || !['PROFESSIONAL', 'ENTERPRISE'].includes(company.plan)) {
      return NextResponse.json({ error: 'Webhook access not available for your plan' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = webhookSchema.parse(body)

    // Check webhook limits based on plan
    const webhookLimits = {
      PROFESSIONAL: 10,
      ENTERPRISE: 50
    }

    const currentWebhookCount = await prisma.webhook.count({
      where: { companyId: session.user.companyId }
    })

    const limit = webhookLimits[company.plan as keyof typeof webhookLimits]
    if (currentWebhookCount >= limit) {
      return NextResponse.json(
        { error: `Maximum number of webhooks reached for ${company.plan} plan (${limit} webhooks)` },
        { status: 400 }
      )
    }

    // Validate URL accessibility (optional - you might want to ping the URL)
    try {
      const url = new URL(validatedData.url)
      if (!['http:', 'https:'].includes(url.protocol)) {
        return NextResponse.json(
          { error: 'Only HTTP and HTTPS URLs are allowed' },
          { status: 400 }
        )
      }
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid URL format' },
        { status: 400 }
      )
    }

    // Generate webhook secret
    const secret = generateWebhookSecret()

    // Create webhook
    const webhook = await prisma.webhook.create({
      data: {
        companyId: session.user.companyId,
        name: validatedData.name,
        url: validatedData.url,
        events: validatedData.events,
        description: validatedData.description,
        secret,
        status: 'ACTIVE',
        createdById: session.user.id
      }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'WEBHOOK',
        title: 'Webhook created',
        description: `Webhook "${validatedData.name}" created`,
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    return NextResponse.json(webhook, { status: 201 })
  } catch (error) {
    console.error('Error creating webhook:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Generate a secure webhook secret
function generateWebhookSecret(): string {
  return crypto.randomBytes(32).toString('hex')
}

// Create webhook signature
export function createWebhookSignature(payload: string, secret: string): string {
  return crypto.createHmac('sha256', secret).update(payload).digest('hex')
}

// Verify webhook signature
export function verifyWebhookSignature(payload: string, signature: string, secret: string): boolean {
  const expectedSignature = createWebhookSignature(payload, secret)
  return crypto.timingSafeEqual(
    Buffer.from(signature, 'hex'),
    Buffer.from(expectedSignature, 'hex')
  )
}

// Send webhook function
export async function sendWebhook(webhookId: string, eventType: string, data: any) {
  try {
    const webhook = await prisma.webhook.findUnique({
      where: { id: webhookId }
    })

    if (!webhook || webhook.status !== 'ACTIVE') {
      return false
    }

    // Check if webhook is subscribed to this event
    const events = webhook.events as string[]
    if (!events.includes(eventType)) {
      return false
    }

    const payload = {
      id: crypto.randomUUID(),
      event: eventType,
      created: Math.floor(Date.now() / 1000),
      data: data
    }

    const payloadString = JSON.stringify(payload)
    const signature = createWebhookSignature(payloadString, webhook.secret || '')

    const response = await fetch(webhook.url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Webhook-Signature': signature,
        'X-Webhook-Event': eventType,
        'User-Agent': 'BusinessSaaS-Webhooks/1.0'
      },
      body: payloadString,
      signal: AbortSignal.timeout(30000) // 30 second timeout
    })

    const success = response.ok
    const statusCode = response.status

    // Log webhook delivery
    await prisma.webhookDelivery.create({
      data: {
        webhookId: webhook.id,
        eventType,
        payload: payloadString,
        statusCode,
        success,
        responseTime: 0, // You could measure this
        attempts: 1
      }
    })

    // Update webhook last triggered time
    await prisma.webhook.update({
      where: { id: webhook.id },
      data: { 
        lastTriggeredAt: new Date(),
        ...(success ? {} : { status: 'ERROR' })
      }
    })

    return success
  } catch (error) {
    console.error('Error sending webhook:', error)
    
    // Log failed delivery
    try {
      await prisma.webhookDelivery.create({
        data: {
          webhookId: webhookId,
          eventType,
          payload: JSON.stringify(data),
          statusCode: 0,
          success: false,
          responseTime: 0,
          attempts: 1,
          errorMessage: error instanceof Error ? error.message : 'Unknown error'
        }
      })

      // Mark webhook as error
      await prisma.webhook.update({
        where: { id: webhookId },
        data: { status: 'ERROR' }
      })
    } catch (logError) {
      console.error('Error logging webhook failure:', logError)
    }

    return false
  }
}

// Send webhooks for an event to all subscribed webhooks
export async function triggerWebhooks(companyId: string, eventType: string, data: any) {
  try {
    const webhooks = await prisma.webhook.findMany({
      where: {
        companyId,
        status: 'ACTIVE'
      }
    })

    const results = await Promise.allSettled(
      webhooks.map(webhook => sendWebhook(webhook.id, eventType, data))
    )

    const successCount = results.filter(result => 
      result.status === 'fulfilled' && result.value === true
    ).length

    console.log(`Triggered ${successCount}/${webhooks.length} webhooks for event ${eventType}`)

    return {
      total: webhooks.length,
      successful: successCount,
      failed: webhooks.length - successCount
    }
  } catch (error) {
    console.error('Error triggering webhooks:', error)
    return {
      total: 0,
      successful: 0,
      failed: 0
    }
  }
}

// Retry failed webhook deliveries
export async function retryFailedWebhooks() {
  try {
    const failedDeliveries = await prisma.webhookDelivery.findMany({
      where: {
        success: false,
        attempts: { lt: 3 }, // Max 3 attempts
        createdAt: { gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } // Last 24 hours
      },
      include: {
        webhook: true
      }
    })

    for (const delivery of failedDeliveries) {
      if (delivery.webhook.status === 'ACTIVE') {
        const success = await sendWebhook(
          delivery.webhook.id,
          delivery.eventType,
          JSON.parse(delivery.payload as string)
        )

        // Update delivery attempt
        await prisma.webhookDelivery.update({
          where: { id: delivery.id },
          data: {
            attempts: delivery.attempts + 1,
            success,
            lastAttemptAt: new Date()
          }
        })
      }
    }
  } catch (error) {
    console.error('Error retrying failed webhooks:', error)
  }
}
