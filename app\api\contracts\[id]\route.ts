import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const contractSchema = z.object({
  contractNumber: z.string().min(1, 'Contract number is required'),
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  customerId: z.string().min(1, 'Customer is required'),
  quotationId: z.string().optional(),
  content: z.string().min(1, 'Contract content is required'),
  value: z.number().min(0).optional().nullable(),
  startDate: z.date().optional().nullable(),
  endDate: z.date().optional().nullable(),
  terms: z.string().optional(),
  signatureRequired: z.boolean().default(true),
  autoExecute: z.boolean().default(false),
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const contract = await prisma.contract.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      },
      include: {
        customer: {
          select: { 
            id: true, 
            name: true, 
            email: true, 
            company: true, 
            phone: true,
            address: true,
            city: true,
            state: true,
            country: true,
            postalCode: true
          }
        },
        quotation: {
          select: { id: true, quotationNumber: true, title: true }
        },
        signatures: {
          include: {
            signedBy: {
              select: { name: true, firstName: true, lastName: true, email: true }
            }
          },
          orderBy: { signedAt: 'desc' }
        },
        activities: {
          include: {
            createdBy: {
              select: { name: true, firstName: true, lastName: true }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: 15,
        },
        _count: {
          select: {
            signatures: true,
            activities: true,
          }
        }
      }
    })

    if (!contract) {
      return NextResponse.json({ error: 'Contract not found' }, { status: 404 })
    }

    return NextResponse.json(contract)
  } catch (error) {
    console.error('Error fetching contract:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = contractSchema.parse(body)

    // Check if contract exists and belongs to the company
    const existingContract = await prisma.contract.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      }
    })

    if (!existingContract) {
      return NextResponse.json({ error: 'Contract not found' }, { status: 404 })
    }

    // Check if contract can be edited (only DRAFT contracts can be fully edited)
    if (existingContract.status !== 'DRAFT') {
      return NextResponse.json(
        { error: 'Only draft contracts can be edited' },
        { status: 400 }
      )
    }

    // Verify customer belongs to the company
    const customer = await prisma.customer.findFirst({
      where: {
        id: validatedData.customerId,
        companyId: session.user.companyId,
      }
    })

    if (!customer) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 400 }
      )
    }

    // Verify quotation belongs to the company (if provided)
    if (validatedData.quotationId) {
      const quotation = await prisma.quotation.findFirst({
        where: {
          id: validatedData.quotationId,
          companyId: session.user.companyId,
        }
      })

      if (!quotation) {
        return NextResponse.json(
          { error: 'Quotation not found' },
          { status: 400 }
        )
      }
    }

    // Check if contract number conflicts with another contract
    if (validatedData.contractNumber !== existingContract.contractNumber) {
      const conflictingContract = await prisma.contract.findFirst({
        where: {
          contractNumber: validatedData.contractNumber,
          companyId: session.user.companyId,
          id: { not: params.id },
        }
      })

      if (conflictingContract) {
        return NextResponse.json(
          { error: 'Contract number already exists' },
          { status: 400 }
        )
      }
    }

    const contract = await prisma.contract.update({
      where: { id: params.id },
      data: {
        contractNumber: validatedData.contractNumber,
        title: validatedData.title,
        description: validatedData.description,
        content: validatedData.content,
        value: validatedData.value,
        startDate: validatedData.startDate,
        endDate: validatedData.endDate,
        terms: validatedData.terms,
        signatureRequired: validatedData.signatureRequired,
        autoExecute: validatedData.autoExecute,
        customerId: validatedData.customerId,
        quotationId: validatedData.quotationId,
      },
      include: {
        customer: {
          select: { id: true, name: true, email: true, company: true }
        },
        quotation: {
          select: { id: true, quotationNumber: true, title: true }
        },
        signatures: {
          include: {
            signedBy: {
              select: { name: true, firstName: true, lastName: true }
            }
          }
        },
        _count: {
          select: {
            signatures: true,
            activities: true,
          }
        }
      }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'NOTE',
        title: 'Contract updated',
        description: `Contract "${contract.contractNumber}" was updated`,
        contractId: contract.id,
        customerId: contract.customerId,
        quotationId: contract.quotationId,
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    return NextResponse.json(contract)
  } catch (error) {
    console.error('Error updating contract:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if contract exists and belongs to the company
    const contract = await prisma.contract.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      }
    })

    if (!contract) {
      return NextResponse.json({ error: 'Contract not found' }, { status: 404 })
    }

    // Check if contract can be deleted (only DRAFT contracts can be deleted)
    if (contract.status !== 'DRAFT') {
      return NextResponse.json(
        { error: 'Only draft contracts can be deleted' },
        { status: 400 }
      )
    }

    await prisma.contract.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: 'Contract deleted successfully' })
  } catch (error) {
    console.error('Error deleting contract:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
