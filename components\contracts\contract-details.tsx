'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { getStatusColor, formatDate, formatCurrency } from '@/lib/utils'
import { 
  User, 
  Building, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar,
  DollarSign,
  FileText,
  Clock,
  Edit,
  Send,
  Copy,
  Download,
  PenTool,
  Play,
  CheckCircle
} from 'lucide-react'
import toast from 'react-hot-toast'

interface ContractDetailsProps {
  contract: {
    id: string
    contractNumber: string
    title: string
    description: string | null
    status: string
    value: number | null
    startDate: Date | null
    endDate: Date | null
    signedAt: Date | null
    executedAt: Date | null
    signatureRequired: boolean
    autoExecute: boolean
    createdAt: Date
    sentAt: Date | null
    customer: {
      id: string
      name: string
      email: string | null
      company: string | null
      phone: string | null
      address: string | null
      city: string | null
      state: string | null
      country: string | null
      postalCode: string | null
    } | null
    quotation: {
      id: string
      quotationNumber: string
      title: string
    } | null
    signatures: Array<{
      id: string
      signerName: string
      signerEmail: string
      signedAt: Date
    }>
    _count: {
      signatures: number
      activities: number
    }
  }
}

export function ContractDetails({ contract }: ContractDetailsProps) {
  const [isSending, setIsSending] = useState(false)
  const [isExecuting, setIsExecuting] = useState(false)

  const handleSend = async () => {
    setIsSending(true)
    try {
      const response = await fetch(`/api/contracts/${contract.id}/send`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to send contract')
      }

      toast.success('Contract sent successfully')
      window.location.reload()
    } catch (error) {
      toast.error('Failed to send contract')
    } finally {
      setIsSending(false)
    }
  }

  const handleExecute = async () => {
    setIsExecuting(true)
    try {
      const response = await fetch(`/api/contracts/${contract.id}/execute`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to execute contract')
      }

      toast.success('Contract executed successfully')
      window.location.reload()
    } catch (error) {
      toast.error('Failed to execute contract')
    } finally {
      setIsExecuting(false)
    }
  }

  const handleDuplicate = async () => {
    try {
      const response = await fetch(`/api/contracts/${contract.id}/duplicate`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to duplicate contract')
      }

      const result = await response.json()
      toast.success('Contract duplicated successfully')
      window.location.href = `/dashboard/contracts/${result.id}/edit`
    } catch (error) {
      toast.error('Failed to duplicate contract')
    }
  }

  const fullAddress = contract.customer ? [
    contract.customer.address,
    contract.customer.city,
    contract.customer.state,
    contract.customer.country
  ].filter(Boolean).join(', ') : null

  const isExpired = contract.endDate && 
                   new Date(contract.endDate) < new Date() && 
                   !['CANCELLED', 'EXECUTED'].includes(contract.status)

  const daysUntilExpiry = contract.endDate 
    ? Math.ceil((new Date(contract.endDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
    : null

  return (
    <div className="space-y-6">
      {/* Contract Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Contract Information</CardTitle>
            <div className="flex items-center space-x-2">
              {contract.status === 'DRAFT' && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSend}
                    disabled={isSending}
                    className="text-blue-600 hover:text-blue-700"
                  >
                    <Send className="h-4 w-4 mr-2" />
                    {isSending ? 'Sending...' : 'Send'}
                  </Button>
                  <Link href={`/dashboard/contracts/${contract.id}/edit`}>
                    <Button variant="outline" >
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                  </Link>
                </>
              )}
              {contract.status === 'SIGNED' && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleExecute}
                  disabled={isExecuting}
                  className="text-green-600 hover:text-green-700"
                >
                  <Play className="h-4 w-4 mr-2" />
                  {isExecuting ? 'Executing...' : 'Execute'}
                </Button>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={handleDuplicate}
                className="text-green-600 hover:text-green-700"
              >
                <Copy className="h-4 w-4 mr-2" />
                Duplicate
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Contract Information */}
            <div className="space-y-4">
              <h3 className="font-medium text-gray-900">Contract Details</h3>
              
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-gray-600">Contract Number</p>
                  <p className="font-medium text-gray-900">{contract.contractNumber}</p>
                </div>

                <div>
                  <p className="text-sm text-gray-600">Title</p>
                  <p className="font-medium text-gray-900">{contract.title}</p>
                </div>

                {contract.description && (
                  <div>
                    <p className="text-sm text-gray-600">Description</p>
                    <p className="text-gray-900 whitespace-pre-wrap">{contract.description}</p>
                  </div>
                )}

                <div className="flex items-center space-x-4">
                  <div>
                    <p className="text-sm text-gray-600">Status</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <Badge className={getStatusColor(contract.status)} variant="outline">
                        {contract.status}
                      </Badge>
                      {isExpired && (
                        <Badge variant="destructive" className="text-xs">
                          Expired
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>

                {contract.value && (
                  <div className="flex items-center space-x-3">
                    <DollarSign className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">Contract Value</p>
                      <p className="font-medium text-gray-900 text-lg">{formatCurrency(contract.value)}</p>
                    </div>
                  </div>
                )}

                <div className="flex items-center space-x-3">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-600">Created</p>
                    <p className="font-medium text-gray-900">{formatDate(contract.createdAt)}</p>
                  </div>
                </div>

                {contract.sentAt && (
                  <div className="flex items-center space-x-3">
                    <Send className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">Sent</p>
                      <p className="font-medium text-gray-900">{formatDate(contract.sentAt)}</p>
                    </div>
                  </div>
                )}

                {contract.signedAt && (
                  <div className="flex items-center space-x-3">
                    <PenTool className="h-4 w-4 text-green-600" />
                    <div>
                      <p className="text-sm text-gray-600">Signed</p>
                      <p className="font-medium text-green-600">{formatDate(contract.signedAt)}</p>
                    </div>
                  </div>
                )}

                {contract.executedAt && (
                  <div className="flex items-center space-x-3">
                    <Play className="h-4 w-4 text-emerald-600" />
                    <div>
                      <p className="text-sm text-gray-600">Executed</p>
                      <p className="font-medium text-emerald-600">{formatDate(contract.executedAt)}</p>
                    </div>
                  </div>
                )}

                {contract.startDate && contract.endDate && (
                  <div className="flex items-center space-x-3">
                    <Clock className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">Contract Period</p>
                      <p className="font-medium text-gray-900">
                        {formatDate(contract.startDate)} - {formatDate(contract.endDate)}
                      </p>
                      {daysUntilExpiry !== null && !isExpired && (
                        <p className="text-sm text-gray-500">
                          {daysUntilExpiry} days remaining
                        </p>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Customer Information */}
            <div className="space-y-4">
              <h3 className="font-medium text-gray-900">Customer Information</h3>
              
              {contract.customer ? (
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <User className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">Name</p>
                      <Link 
                        href={`/dashboard/customers/${contract.customer.id}`}
                        className="font-medium text-blue-600 hover:text-blue-800"
                      >
                        {contract.customer.name}
                      </Link>
                    </div>
                  </div>

                  {contract.customer.company && (
                    <div className="flex items-center space-x-3">
                      <Building className="h-4 w-4 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-600">Company</p>
                        <p className="font-medium text-gray-900">{contract.customer.company}</p>
                      </div>
                    </div>
                  )}

                  {contract.customer.email && (
                    <div className="flex items-center space-x-3">
                      <Mail className="h-4 w-4 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-600">Email</p>
                        <a 
                          href={`mailto:${contract.customer.email}`}
                          className="font-medium text-blue-600 hover:text-blue-800"
                        >
                          {contract.customer.email}
                        </a>
                      </div>
                    </div>
                  )}

                  {contract.customer.phone && (
                    <div className="flex items-center space-x-3">
                      <Phone className="h-4 w-4 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-600">Phone</p>
                        <a 
                          href={`tel:${contract.customer.phone}`}
                          className="font-medium text-blue-600 hover:text-blue-800"
                        >
                          {contract.customer.phone}
                        </a>
                      </div>
                    </div>
                  )}

                  {fullAddress && (
                    <div className="flex items-start space-x-3">
                      <MapPin className="h-4 w-4 text-gray-400 mt-1" />
                      <div>
                        <p className="text-sm text-gray-600">Address</p>
                        <p className="font-medium text-gray-900">{fullAddress}</p>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <p className="text-gray-500 italic">No customer assigned</p>
              )}

              {/* Related Quotation */}
              {contract.quotation && (
                <div className="pt-4 border-t">
                  <h4 className="font-medium text-gray-900 mb-2">Related Quotation</h4>
                  <div className="flex items-center space-x-3">
                    <FileText className="h-4 w-4 text-gray-400" />
                    <div>
                      <Link 
                        href={`/dashboard/quotations/${contract.quotation.id}`}
                        className="font-medium text-blue-600 hover:text-blue-800"
                      >
                        {contract.quotation.quotationNumber}
                      </Link>
                      <p className="text-sm text-gray-600">{contract.quotation.title}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Contract Settings */}
              <div className="pt-4 border-t">
                <h4 className="font-medium text-gray-900 mb-2">Contract Settings</h4>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className={`h-4 w-4 ${contract.signatureRequired ? 'text-green-600' : 'text-gray-400'}`} />
                    <span className="text-sm text-gray-600">
                      Digital signature {contract.signatureRequired ? 'required' : 'not required'}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Play className={`h-4 w-4 ${contract.autoExecute ? 'text-green-600' : 'text-gray-400'}`} />
                    <span className="text-sm text-gray-600">
                      Auto-execute {contract.autoExecute ? 'enabled' : 'disabled'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Activity Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 mb-2">
                <FileText className="h-4 w-4 text-blue-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900">{contract._count.signatures}</div>
              <div className="text-sm text-gray-600">Signatures</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-green-100 mb-2">
                <DollarSign className="h-4 w-4 text-green-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900">
                {contract.value ? formatCurrency(contract.value) : 'N/A'}
              </div>
              <div className="text-sm text-gray-600">Value</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-purple-100 mb-2">
                <Calendar className="h-4 w-4 text-purple-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900">{contract._count.activities}</div>
              <div className="text-sm text-gray-600">Activities</div>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-orange-100 mb-2">
                <Clock className="h-4 w-4 text-orange-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900">
                {Math.ceil((new Date().getTime() - new Date(contract.createdAt).getTime()) / (1000 * 60 * 60 * 24))}
              </div>
              <div className="text-sm text-gray-600">Days Old</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
