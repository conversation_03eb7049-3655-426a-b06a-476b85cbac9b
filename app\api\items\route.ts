import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const itemSchema = z.object({
  name: z.string().min(1, 'Item name is required'),
  description: z.string().optional().nullable(),
  sku: z.string().optional().nullable(),
  type: z.enum(['PRODUCT', 'SERVICE']),
  category: z.string().optional().nullable(),
  price: z.number().min(0, 'Price must be non-negative'),
  costPrice: z.number().min(0).optional().nullable(),
  unit: z.string().optional().nullable(),
  stockQuantity: z.number().min(0).optional().nullable(),
  lowStockThreshold: z.number().min(0).optional().nullable(),
  status: z.enum(['ACTIVE', 'INACTIVE']).default('ACTIVE'),
  taxRate: z.number().min(0).max(100).default(18),
  notes: z.string().optional().nullable(),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search') || ''
    const category = searchParams.get('category') || ''
    const status = searchParams.get('status') || ''
    const type = searchParams.get('type') || ''
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = (page - 1) * limit

    // Build where clause
    const where: any = {
      companyId: session.user.companyId,
    }

    if (search) {
      where.OR = [
        { name: { contains: search } },
        { description: { contains: search } },
        { sku: { contains: search } },
        { category: { contains: search } },
      ]
    }

    if (category) {
      where.category = category
    }

    if (status) {
      where.status = status
    }

    if (type) {
      where.type = type
    }

    const [items, totalCount] = await Promise.all([
      prisma.item.findMany({
        where,
        include: {
          _count: {
            select: {
              quotationItems: true,
              invoiceItems: true,
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip: offset,
        take: limit,
      }),
      prisma.item.count({ where })
    ])

    return NextResponse.json({
      items,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      }
    })
  } catch (error) {
    console.error('Error fetching items:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = itemSchema.parse(body)

    // Check if SKU already exists (if provided)
    if (validatedData.sku) {
      const existingItem = await prisma.item.findFirst({
        where: {
          sku: validatedData.sku,
          companyId: session.user.companyId,
        }
      })

      if (existingItem) {
        return NextResponse.json(
          { error: 'SKU already exists' },
          { status: 400 }
        )
      }
    }

    const item = await prisma.item.create({
      data: {
        name: validatedData.name,
        description: validatedData.description,
        sku: validatedData.sku,
        type: validatedData.type,
        category: validatedData.category,
        price: validatedData.price,
        costPrice: validatedData.costPrice,
        unit: validatedData.unit,
        stockQuantity: validatedData.stockQuantity,
        lowStockThreshold: validatedData.lowStockThreshold,
        status: validatedData.status,
        taxRate: validatedData.taxRate,
        notes: validatedData.notes,
        companyId: session.user.companyId,
        createdById: session.user.id,
      },
      include: {
        _count: {
          select: {
            quotationItems: true,
            invoiceItems: true,
          }
        }
      }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'ITEM_CREATED',
        title: 'Item created',
        description: `Item "${item.name}" was created`,
        itemId: item.id,
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    return NextResponse.json(item, { status: 201 })
  } catch (error) {
    console.error('Error creating item:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/items?id=xxx - Update item
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.companyId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const itemId = searchParams.get('id')

    if (!itemId) {
      return NextResponse.json(
        { error: 'Item ID is required' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const validatedData = itemSchema.parse(body)

    // Check if item exists and belongs to company
    const existingItem = await prisma.item.findFirst({
      where: {
        id: itemId,
        companyId: session.user.companyId
      }
    })

    if (!existingItem) {
      return NextResponse.json(
        { error: 'Item not found' },
        { status: 404 }
      )
    }

    // Check if name is unique (excluding current item)
    if (validatedData.name !== existingItem.name) {
      const nameExists = await prisma.item.findFirst({
        where: {
          name: validatedData.name,
          companyId: session.user.companyId,
          id: { not: itemId }
        }
      })

      if (nameExists) {
        return NextResponse.json(
          { error: 'Item name already exists' },
          { status: 400 }
        )
      }
    }

    // Check if SKU is unique (excluding current item)
    if (validatedData.sku && validatedData.sku !== existingItem.sku) {
      const skuExists = await prisma.item.findFirst({
        where: {
          sku: validatedData.sku,
          companyId: session.user.companyId,
          id: { not: itemId }
        }
      })

      if (skuExists) {
        return NextResponse.json(
          { error: 'SKU already exists' },
          { status: 400 }
        )
      }
    }

    // Update item
    const item = await prisma.item.update({
      where: { id: itemId },
      data: validatedData,
      include: {
        itemCategory: {
          select: { id: true, name: true, color: true }
        },
        _count: {
          select: {
            quotationItems: true,
            invoiceItems: true,
          }
        }
      }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'NOTE',
        title: 'Item updated',
        description: `Item "${item.name}" was updated`,
        companyId: session.user.companyId,
        createdById: session.user.id,
        metadata: {
          itemId: item.id,
          itemName: item.name
        }
      }
    })

    return NextResponse.json(item)

  } catch (error) {
    console.error('Error updating item:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
