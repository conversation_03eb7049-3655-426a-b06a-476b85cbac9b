import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const signatureSchema = z.object({
  signatureData: z.string().min(1, 'Signature data is required'),
  signerName: z.string().min(1, 'Signer name is required'),
  signerEmail: z.string().email('Valid email is required'),
  ipAddress: z.string().optional(),
  userAgent: z.string().optional(),
})

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = signatureSchema.parse(body)

    // Check if contract exists and belongs to the company
    const contract = await prisma.contract.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      },
      include: {
        customer: {
          select: { 
            id: true, 
            name: true, 
            email: true, 
            company: true 
          }
        }
      }
    })

    if (!contract) {
      return NextResponse.json({ error: 'Contract not found' }, { status: 404 })
    }

    // Check if contract can be signed
    if (!['SENT', 'VIEWED'].includes(contract.status)) {
      return NextResponse.json(
        { error: 'Only sent or viewed contracts can be signed' },
        { status: 400 }
      )
    }

    // Check if signature is required
    if (!contract.signatureRequired) {
      return NextResponse.json(
        { error: 'This contract does not require a signature' },
        { status: 400 }
      )
    }

    // Check if already signed by this email
    const existingSignature = await prisma.contractSignature.findFirst({
      where: {
        contractId: contract.id,
        signerEmail: validatedData.signerEmail,
      }
    })

    if (existingSignature) {
      return NextResponse.json(
        { error: 'Contract already signed by this email' },
        { status: 400 }
      )
    }

    // Create signature record
    const signature = await prisma.contractSignature.create({
      data: {
        contractId: contract.id,
        signature: validatedData.signatureData,
        signatureData: validatedData.signatureData,
        signerName: validatedData.signerName,
        signerEmail: validatedData.signerEmail,
        ipAddress: validatedData.ipAddress,
        userAgent: validatedData.userAgent,
        signedAt: new Date(),
        signedById: session.user.id,
      }
    })

    // Update contract status to SIGNED and set signedAt
    const updatedContract = await prisma.contract.update({
      where: { id: params.id },
      data: {
        status: 'SIGNED',
        signedAt: new Date(),
      },
      include: {
        customer: {
          select: { id: true, name: true, email: true, company: true }
        },
        quotation: {
          select: { id: true, quotationNumber: true, title: true }
        },
        signatures: {
          include: {
            signedBy: {
              select: { name: true, firstName: true, lastName: true }
            }
          }
        },
        _count: {
          select: {
            signatures: true,
            activities: true,
          }
        }
      }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'CONTRACT_SIGNED',
        title: 'Contract signed',
        description: `Contract "${contract.contractNumber}" was signed by ${validatedData.signerName}`,
        contractId: contract.id,
        customerId: contract.customerId,
        quotationId: contract.quotationId,
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    // Auto-execute if enabled
    if (contract.autoExecute) {
      await prisma.contract.update({
        where: { id: params.id },
        data: {
          status: 'EXECUTED',
          executedAt: new Date(),
        }
      })

      // Create execution activity log
      await prisma.activity.create({
        data: {
          type: 'CONTRACT_EXECUTED',
          title: 'Contract executed',
          description: `Contract "${contract.contractNumber}" was automatically executed after signing`,
          contractId: contract.id,
          customerId: contract.customerId,
          quotationId: contract.quotationId,
          companyId: session.user.companyId,
          createdById: session.user.id,
        }
      })
    }

    return NextResponse.json({
      message: 'Contract signed successfully',
      contract: updatedContract,
      signature: {
        id: signature.id,
        signerName: signature.signerName,
        signerEmail: signature.signerEmail,
        signedAt: signature.signedAt,
      }
    })
  } catch (error) {
    console.error('Error signing contract:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
