import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const subscriptionSchema = z.object({
  plan: z.enum(['BASIC', 'PROFESSIONAL', 'ENTERPRISE']),
  interval: z.enum(['MONTHLY', 'YEARLY']).default('MONTHLY'),
  paymentMethodId: z.string().optional()
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const subscription = await prisma.subscription.findFirst({
      where: { companyId: session.user.companyId },
      include: {
        payments: {
          orderBy: { createdAt: 'desc' },
          take: 10
        }
      }
    })

    return NextResponse.json({ subscription })
  } catch (error) {
    console.error('Error fetching subscription:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = subscriptionSchema.parse(body)

    // Check if company already has a subscription
    const existingSubscription = await prisma.subscription.findFirst({
      where: { companyId: session.user.companyId }
    })

    if (existingSubscription) {
      return NextResponse.json(
        { error: 'Company already has a subscription' },
        { status: 400 }
      )
    }

    // Get plan pricing
    const planPricing = {
      BASIC: { monthly: 29, yearly: 290 },
      PROFESSIONAL: { monthly: 79, yearly: 790 },
      ENTERPRISE: { monthly: 199, yearly: 1990 }
    }

    const amount = validatedData.interval === 'YEARLY' 
      ? planPricing[validatedData.plan].yearly
      : planPricing[validatedData.plan].monthly

    // Create subscription
    const subscription = await prisma.subscription.create({
      data: {
        companyId: session.user.companyId,
        plan: validatedData.plan,
        status: 'ACTIVE',
        amount,
        currency: 'USD',
        interval: validatedData.interval,
        currentPeriodStart: new Date(),
        currentPeriodEnd: new Date(
          Date.now() + (validatedData.interval === 'YEARLY' ? 365 : 30) * 24 * 60 * 60 * 1000
        ),
        nextBillingDate: new Date(
          Date.now() + (validatedData.interval === 'YEARLY' ? 365 : 30) * 24 * 60 * 60 * 1000
        ),
        stripeSubscriptionId: `sub_${Math.random().toString(36).substr(2, 9)}`, // Mock Stripe ID
        stripeCustomerId: `cus_${Math.random().toString(36).substr(2, 9)}`, // Mock Stripe ID
        paymentMethod: validatedData.paymentMethodId ? {
          id: validatedData.paymentMethodId,
          brand: 'visa',
          last4: '4242',
          expMonth: 12,
          expYear: 2025
        } : null
      }
    })

    // Update company plan
    await prisma.company.update({
      where: { id: session.user.companyId },
      data: { plan: validatedData.plan }
    })

    // Create initial payment record
    await prisma.payment.create({
      data: {
        subscriptionId: subscription.id,
        amount,
        currency: 'USD',
        status: 'SUCCEEDED',
        description: `${validatedData.plan} plan subscription`,
        stripePaymentIntentId: `pi_${Math.random().toString(36).substr(2, 9)}`,
        paidAt: new Date()
      }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'SUBSCRIPTION',
        title: 'Subscription created',
        description: `${validatedData.plan} subscription created`,
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    return NextResponse.json(subscription, { status: 201 })
  } catch (error) {
    console.error('Error creating subscription:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { plan, interval } = body

    if (!plan || !interval) {
      return NextResponse.json(
        { error: 'Plan and interval are required' },
        { status: 400 }
      )
    }

    // Find existing subscription
    const existingSubscription = await prisma.subscription.findFirst({
      where: { companyId: session.user.companyId }
    })

    if (!existingSubscription) {
      return NextResponse.json(
        { error: 'No subscription found' },
        { status: 404 }
      )
    }

    // Get new plan pricing
    const planPricing = {
      BASIC: { monthly: 29, yearly: 290 },
      PROFESSIONAL: { monthly: 79, yearly: 790 },
      ENTERPRISE: { monthly: 199, yearly: 1990 }
    }

    const newAmount = interval === 'YEARLY' 
      ? planPricing[plan as keyof typeof planPricing].yearly
      : planPricing[plan as keyof typeof planPricing].monthly

    // Update subscription
    const updatedSubscription = await prisma.subscription.update({
      where: { id: existingSubscription.id },
      data: {
        plan,
        amount: newAmount,
        interval
      }
    })

    // Update company plan
    await prisma.company.update({
      where: { id: session.user.companyId },
      data: { plan }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'SUBSCRIPTION',
        title: 'Subscription updated',
        description: `Subscription updated to ${plan} plan`,
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    return NextResponse.json(updatedSubscription)
  } catch (error) {
    console.error('Error updating subscription:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Utility function to create subscription (can be called from other parts of the app)
export async function createSubscription(data: {
  companyId: string
  plan: string
  interval: string
  paymentMethodId?: string
  stripeSubscriptionId?: string
  stripeCustomerId?: string
}) {
  try {
    const planPricing = {
      BASIC: { monthly: 29, yearly: 290 },
      PROFESSIONAL: { monthly: 79, yearly: 790 },
      ENTERPRISE: { monthly: 199, yearly: 1990 }
    }

    const amount = data.interval === 'YEARLY' 
      ? planPricing[data.plan as keyof typeof planPricing].yearly
      : planPricing[data.plan as keyof typeof planPricing].monthly

    const subscription = await prisma.subscription.create({
      data: {
        companyId: data.companyId,
        plan: data.plan as any,
        status: 'ACTIVE',
        amount,
        currency: 'USD',
        interval: data.interval as any,
        currentPeriodStart: new Date(),
        currentPeriodEnd: new Date(
          Date.now() + (data.interval === 'YEARLY' ? 365 : 30) * 24 * 60 * 60 * 1000
        ),
        nextBillingDate: new Date(
          Date.now() + (data.interval === 'YEARLY' ? 365 : 30) * 24 * 60 * 60 * 1000
        ),
        stripeSubscriptionId: data.stripeSubscriptionId,
        stripeCustomerId: data.stripeCustomerId,
        paymentMethod: data.paymentMethodId ? {
          id: data.paymentMethodId,
          brand: 'visa',
          last4: '4242',
          expMonth: 12,
          expYear: 2025
        } : null
      }
    })

    return subscription
  } catch (error) {
    console.error('Error creating subscription:', error)
    throw error
  }
}

// Utility function to handle subscription webhooks
export async function handleSubscriptionWebhook(event: any) {
  try {
    switch (event.type) {
      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(event.data.object)
        break
      case 'invoice.payment_failed':
        await handlePaymentFailed(event.data.object)
        break
      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object)
        break
      case 'customer.subscription.deleted':
        await handleSubscriptionCancelled(event.data.object)
        break
      default:
        console.log(`Unhandled event type: ${event.type}`)
    }
  } catch (error) {
    console.error('Error handling subscription webhook:', error)
    throw error
  }
}

async function handlePaymentSucceeded(invoice: any) {
  const subscription = await prisma.subscription.findFirst({
    where: { stripeSubscriptionId: invoice.subscription }
  })

  if (subscription) {
    await prisma.payment.create({
      data: {
        subscriptionId: subscription.id,
        amount: invoice.amount_paid / 100, // Convert from cents
        currency: invoice.currency.toUpperCase(),
        status: 'SUCCEEDED',
        description: invoice.description || 'Subscription payment',
        stripePaymentIntentId: invoice.payment_intent,
        paidAt: new Date(invoice.status_transitions.paid_at * 1000)
      }
    })
  }
}

async function handlePaymentFailed(invoice: any) {
  const subscription = await prisma.subscription.findFirst({
    where: { stripeSubscriptionId: invoice.subscription }
  })

  if (subscription) {
    await prisma.payment.create({
      data: {
        subscriptionId: subscription.id,
        amount: invoice.amount_due / 100,
        currency: invoice.currency.toUpperCase(),
        status: 'FAILED',
        description: invoice.description || 'Subscription payment',
        stripePaymentIntentId: invoice.payment_intent,
        failedAt: new Date()
      }
    })

    // Update subscription status
    await prisma.subscription.update({
      where: { id: subscription.id },
      data: { status: 'PAST_DUE' }
    })
  }
}

async function handleSubscriptionUpdated(stripeSubscription: any) {
  const subscription = await prisma.subscription.findFirst({
    where: { stripeSubscriptionId: stripeSubscription.id }
  })

  if (subscription) {
    await prisma.subscription.update({
      where: { id: subscription.id },
      data: {
        status: stripeSubscription.status.toUpperCase(),
        currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
        currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
        nextBillingDate: new Date(stripeSubscription.current_period_end * 1000)
      }
    })
  }
}

async function handleSubscriptionCancelled(stripeSubscription: any) {
  const subscription = await prisma.subscription.findFirst({
    where: { stripeSubscriptionId: stripeSubscription.id }
  })

  if (subscription) {
    await prisma.subscription.update({
      where: { id: subscription.id },
      data: { 
        status: 'CANCELLED',
        cancelledAt: new Date()
      }
    })
  }
}
