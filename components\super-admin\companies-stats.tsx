'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { formatCurrency } from '@/lib/utils'
import { 
  Building2, 
  Activity, 
  Users,
  DollarSign,
  TrendingUp,
  Crown,
  Star,
  Award,
  Zap
} from 'lucide-react'

interface CompaniesStatsProps {
  stats: {
    total: number
    active: number
    inactive: number
    totalRevenue: number
    planBreakdown: Array<{
      plan: string
      _count: number
    }>
  }
}

export function CompaniesStats({ stats }: CompaniesStatsProps) {
  const getPlanIcon = (plan: string) => {
    switch (plan) {
      case 'ENTERPRISE':
        return Crown
      case 'PROFESSIONAL':
        return Star
      case 'BASIC':
        return Award
      default:
        return Building2
    }
  }

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'ENTERPRISE':
        return 'text-purple-600 bg-purple-100'
      case 'PROFESSIONAL':
        return 'text-blue-600 bg-blue-100'
      case 'BASIC':
        return 'text-green-600 bg-green-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const overviewCards = [
    {
      title: 'Total Companies',
      value: stats.total.toString(),
      icon: Building2,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      description: 'All registered companies'
    },
    {
      title: 'Active Companies',
      value: stats.active.toString(),
      icon: Activity,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      description: 'Active in last 30 days'
    },
    {
      title: 'Inactive Companies',
      value: stats.inactive.toString(),
      icon: Users,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      description: 'No activity in 30+ days'
    },
    {
      title: 'Total Revenue',
      value: formatCurrency(stats.totalRevenue),
      icon: DollarSign,
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-100',
      description: 'Platform-wide revenue'
    }
  ]

  const activityRate = stats.total > 0 ? ((stats.active / stats.total) * 100).toFixed(1) : '0'

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {overviewCards.map((card, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {card.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${card.bgColor}`}>
                <card.icon className={`h-5 w-5 ${card.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-900">
                {card.value}
              </div>
              <p className="text-xs text-gray-500 mt-1">
                {card.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Plan Distribution and Activity Rate */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Plan Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Companies by Plan</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stats.planBreakdown.map((plan, index) => {
                const PlanIcon = getPlanIcon(plan.plan)
                const percentage = stats.total > 0 
                  ? ((plan._count / stats.total) * 100).toFixed(1)
                  : '0'
                
                return (
                  <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded-lg ${getPlanColor(plan.plan)}`}>
                        <PlanIcon className="h-5 w-5" />
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">
                          {plan.plan} Plan
                        </div>
                        <div className="text-sm text-gray-600">
                          {percentage}% of companies
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-gray-900">
                        {plan._count}
                      </div>
                      <div className="text-sm text-gray-600">companies</div>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Activity Metrics */}
        <Card>
          <CardHeader>
            <CardTitle>Activity Metrics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Activity Rate */}
              <div className="text-center p-6 bg-gradient-to-r from-blue-50 to-green-50 rounded-lg">
                <div className="text-4xl font-bold text-gray-900 mb-2">
                  {activityRate}%
                </div>
                <div className="text-lg font-medium text-gray-700 mb-1">
                  Activity Rate
                </div>
                <div className="text-sm text-gray-600">
                  Companies active in last 30 days
                </div>
              </div>

              {/* Activity Breakdown */}
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <Activity className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-green-800">
                    {stats.active}
                  </div>
                  <div className="text-sm text-green-600">Active</div>
                </div>
                <div className="text-center p-4 bg-orange-50 rounded-lg">
                  <Users className="h-8 w-8 text-orange-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-orange-800">
                    {stats.inactive}
                  </div>
                  <div className="text-sm text-orange-600">Inactive</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Insights */}
      <Card>
        <CardHeader>
          <CardTitle>Performance Insights</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <TrendingUp className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="font-medium text-gray-900">Growth Rate</div>
              <div className="text-sm text-gray-600 mt-1">
                {stats.total > 0 ? 'Steady growth' : 'Getting started'}
              </div>
              <div className="text-xs text-gray-500 mt-1">
                Based on registration trends
              </div>
            </div>
            
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <Zap className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <div className="font-medium text-gray-900">Engagement</div>
              <div className="text-sm text-gray-600 mt-1">
                {parseFloat(activityRate) >= 70 ? 'Excellent' : parseFloat(activityRate) >= 50 ? 'Good' : 'Needs Improvement'}
              </div>
              <div className="text-xs text-gray-500 mt-1">
                User activity levels
              </div>
            </div>
            
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <DollarSign className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <div className="font-medium text-gray-900">Revenue Health</div>
              <div className="text-sm text-gray-600 mt-1">
                {stats.totalRevenue > 0 ? 'Revenue generating' : 'Early stage'}
              </div>
              <div className="text-xs text-gray-500 mt-1">
                Platform monetization
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
