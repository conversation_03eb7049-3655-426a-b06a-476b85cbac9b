'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { getStatusColor, formatDate, formatCurrency } from '@/lib/utils'
import { 
  User, 
  Building, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar,
  DollarSign,
  Receipt,
  FileText,
  Clock,
  Edit,
  Send,
  Copy,
  Download,
  CreditCard,
  AlertTriangle
} from 'lucide-react'
import toast from 'react-hot-toast'

interface InvoiceDetailsProps {
  invoice: {
    id: string
    invoiceNumber: string
    title: string
    description: string | null
    status: string
    total: number
    subtotal: number
    taxRate: number
    discountType: string
    discountValue: number
    dueDate: Date | null
    paidAt: Date | null
    paymentMethod: string | null
    paymentReference: string | null
    terms: string | null
    paymentTerms: string | null
    createdAt: Date
    sentAt: Date | null
    customer: {
      id: string
      name: string
      email: string | null
      company: string | null
      phone: string | null
      address: string | null
      city: string | null
      state: string | null
      country: string | null
      postalCode: string | null
    } | null
    quotation: {
      id: string
      quotationNumber: string
      title: string
    } | null
    items: Array<{
      id: string
      name: string
      description: string | null
      quantity: number
      unitPrice: number
      discount: number
      taxRate: number
    }>
    _count: {
      items: number
      activities: number
    }
  }
}

export function InvoiceDetails({ invoice }: InvoiceDetailsProps) {
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false)
  const [isSending, setIsSending] = useState(false)
  const [isMarkingPaid, setIsMarkingPaid] = useState(false)

  const handleSend = async () => {
    setIsSending(true)
    try {
      const response = await fetch(`/api/invoices/${invoice.id}/send`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to send invoice')
      }

      toast.success('Invoice sent successfully')
      window.location.reload()
    } catch (error) {
      toast.error('Failed to send invoice')
    } finally {
      setIsSending(false)
    }
  }

  const handleMarkPaid = async () => {
    setIsMarkingPaid(true)
    try {
      const response = await fetch(`/api/invoices/${invoice.id}/mark-paid`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paymentDate: new Date().toISOString(),
          paymentMethod: 'Manual',
          notes: 'Marked as paid manually'
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to mark invoice as paid')
      }

      toast.success('Invoice marked as paid')
      window.location.reload()
    } catch (error) {
      toast.error('Failed to mark invoice as paid')
    } finally {
      setIsMarkingPaid(false)
    }
  }

  const handleDuplicate = async () => {
    try {
      const response = await fetch(`/api/invoices/${invoice.id}/duplicate`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to duplicate invoice')
      }

      const result = await response.json()
      toast.success('Invoice duplicated successfully')
      window.location.href = `/dashboard/invoices/${result.id}/edit`
    } catch (error) {
      toast.error('Failed to duplicate invoice')
    }
  }

  const fullAddress = invoice.customer ? [
    invoice.customer.address,
    invoice.customer.city,
    invoice.customer.state,
    invoice.customer.country
  ].filter(Boolean).join(', ') : null

  const isOverdue = invoice.dueDate && 
                   new Date(invoice.dueDate) < new Date() && 
                   !['PAID', 'CANCELLED'].includes(invoice.status)

  const daysOverdue = isOverdue 
    ? Math.ceil((new Date().getTime() - new Date(invoice.dueDate!).getTime()) / (1000 * 60 * 60 * 24))
    : null

  return (
    <div className="space-y-6">
      {/* Invoice Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Invoice Information</CardTitle>
            <div className="flex items-center space-x-2">
              {invoice.status === 'DRAFT' && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSend}
                    disabled={isSending}
                    className="text-blue-600 hover:text-blue-700"
                  >
                    <Send className="h-4 w-4 mr-2" />
                    {isSending ? 'Sending...' : 'Send'}
                  </Button>
                  <Link href={`/dashboard/invoices/${invoice.id}/edit`}>
                    <Button variant="outline" >
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                  </Link>
                </>
              )}
              {['SENT', 'VIEWED', 'OVERDUE'].includes(invoice.status) && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleMarkPaid}
                  disabled={isMarkingPaid}
                  className="text-green-600 hover:text-green-700"
                >
                  <CreditCard className="h-4 w-4 mr-2" />
                  {isMarkingPaid ? 'Processing...' : 'Mark Paid'}
                </Button>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={handleDuplicate}
                className="text-green-600 hover:text-green-700"
              >
                <Copy className="h-4 w-4 mr-2" />
                Duplicate
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Invoice Information */}
            <div className="space-y-4">
              <h3 className="font-medium text-gray-900">Invoice Details</h3>
              
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-gray-600">Invoice Number</p>
                  <p className="font-medium text-gray-900">{invoice.invoiceNumber}</p>
                </div>

                <div>
                  <p className="text-sm text-gray-600">Title</p>
                  <p className="font-medium text-gray-900">{invoice.title}</p>
                </div>

                {invoice.description && (
                  <div>
                    <p className="text-sm text-gray-600">Description</p>
                    <p className="text-gray-900 whitespace-pre-wrap">{invoice.description}</p>
                  </div>
                )}

                <div className="flex items-center space-x-4">
                  <div>
                    <p className="text-sm text-gray-600">Status</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <Badge className={getStatusColor(invoice.status)} variant="outline">
                        {invoice.status}
                      </Badge>
                      {isOverdue && (
                        <Badge variant="destructive" className="text-xs">
                          {daysOverdue} days overdue
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <DollarSign className="h-4 w-4 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-600">Total Amount</p>
                    <p className="font-medium text-gray-900 text-lg">{formatCurrency(invoice.total)}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-600">Created</p>
                    <p className="font-medium text-gray-900">{formatDate(invoice.createdAt)}</p>
                  </div>
                </div>

                {invoice.sentAt && (
                  <div className="flex items-center space-x-3">
                    <Send className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">Sent</p>
                      <p className="font-medium text-gray-900">{formatDate(invoice.sentAt)}</p>
                    </div>
                  </div>
                )}

                {invoice.dueDate && (
                  <div className="flex items-center space-x-3">
                    <Clock className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">Due Date</p>
                      <p className={`font-medium ${isOverdue ? 'text-red-600' : 'text-gray-900'}`}>
                        {formatDate(invoice.dueDate)}
                      </p>
                    </div>
                  </div>
                )}

                {invoice.paidAt && (
                  <div className="flex items-center space-x-3">
                    <CreditCard className="h-4 w-4 text-green-600" />
                    <div>
                      <p className="text-sm text-gray-600">Paid Date</p>
                      <p className="font-medium text-green-600">{formatDate(invoice.paidAt)}</p>
                      {invoice.paymentMethod && (
                        <p className="text-sm text-gray-500">via {invoice.paymentMethod}</p>
                      )}
                      {invoice.paymentReference && (
                        <p className="text-sm text-gray-500">Ref: {invoice.paymentReference}</p>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Customer Information */}
            <div className="space-y-4">
              <h3 className="font-medium text-gray-900">Customer Information</h3>
              
              {invoice.customer ? (
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <User className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">Name</p>
                      <Link 
                        href={`/dashboard/customers/${invoice.customer.id}`}
                        className="font-medium text-blue-600 hover:text-blue-800"
                      >
                        {invoice.customer.name}
                      </Link>
                    </div>
                  </div>

                  {invoice.customer.company && (
                    <div className="flex items-center space-x-3">
                      <Building className="h-4 w-4 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-600">Company</p>
                        <p className="font-medium text-gray-900">{invoice.customer.company}</p>
                      </div>
                    </div>
                  )}

                  {invoice.customer.email && (
                    <div className="flex items-center space-x-3">
                      <Mail className="h-4 w-4 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-600">Email</p>
                        <a 
                          href={`mailto:${invoice.customer.email}`}
                          className="font-medium text-blue-600 hover:text-blue-800"
                        >
                          {invoice.customer.email}
                        </a>
                      </div>
                    </div>
                  )}

                  {invoice.customer.phone && (
                    <div className="flex items-center space-x-3">
                      <Phone className="h-4 w-4 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-600">Phone</p>
                        <a 
                          href={`tel:${invoice.customer.phone}`}
                          className="font-medium text-blue-600 hover:text-blue-800"
                        >
                          {invoice.customer.phone}
                        </a>
                      </div>
                    </div>
                  )}

                  {fullAddress && (
                    <div className="flex items-start space-x-3">
                      <MapPin className="h-4 w-4 text-gray-400 mt-1" />
                      <div>
                        <p className="text-sm text-gray-600">Address</p>
                        <p className="font-medium text-gray-900">{fullAddress}</p>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <p className="text-gray-500 italic">No customer assigned</p>
              )}

              {/* Related Quotation */}
              {invoice.quotation && (
                <div className="pt-4 border-t">
                  <h4 className="font-medium text-gray-900 mb-2">Related Quotation</h4>
                  <div className="flex items-center space-x-3">
                    <FileText className="h-4 w-4 text-gray-400" />
                    <div>
                      <Link 
                        href={`/dashboard/quotations/${invoice.quotation.id}`}
                        className="font-medium text-blue-600 hover:text-blue-800"
                      >
                        {invoice.quotation.quotationNumber}
                      </Link>
                      <p className="text-sm text-gray-600">{invoice.quotation.title}</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Activity Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 mb-2">
                <Receipt className="h-4 w-4 text-blue-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900">{invoice._count.items}</div>
              <div className="text-sm text-gray-600">Items</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-green-100 mb-2">
                <DollarSign className="h-4 w-4 text-green-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900">{formatCurrency(invoice.subtotal)}</div>
              <div className="text-sm text-gray-600">Subtotal</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-purple-100 mb-2">
                <Calendar className="h-4 w-4 text-purple-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900">{invoice._count.activities}</div>
              <div className="text-sm text-gray-600">Activities</div>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-orange-100 mb-2">
                <Clock className="h-4 w-4 text-orange-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900">
                {Math.ceil((new Date().getTime() - new Date(invoice.createdAt).getTime()) / (1000 * 60 * 60 * 24))}
              </div>
              <div className="text-sm text-gray-600">Days Old</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
