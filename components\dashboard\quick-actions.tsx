'use client'

import Link from 'next/link'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  Plus,
  FileText,
  Receipt,

  Users,
  TrendingUp,
  Package
} from 'lucide-react'

export function QuickActions() {
  const actions = [
    {
      title: 'New Customer',
      description: 'Add a new customer',
      href: '/dashboard/customers/new',
      icon: Users,
      color: 'text-blue-600 bg-blue-100'
    },
    {
      title: 'New Lead',
      description: 'Create a new lead',
      href: '/dashboard/leads/new',
      icon: TrendingUp,
      color: 'text-green-600 bg-green-100'
    },
    {
      title: 'New Quotation',
      description: 'Create a quotation',
      href: '/dashboard/quotations/new',
      icon: FileText,
 Receipt,
      color: 'text-purple-600 bg-purple-100'
    },
    {
      title: 'New Invoice',
      description: 'Generate an invoice',
      href: '/dashboard/invoices/new',
      icon: Receipt,
      color: 'text-orange-600 bg-orange-100'
    },
    {
      title: 'New Contract',
      description: 'Create a contract',
      href: '/dashboard/contracts/new',
      icon: FileText,
 Receipt,
      color: 'text-indigo-600 bg-indigo-100'
    },
    {
      title: 'New Item',
      description: 'Add product/service',
      href: '/dashboard/items/new',
      icon: Package,
      color: 'text-emerald-600 bg-emerald-100'
    }
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Plus className="h-5 w-5 mr-2" />
          Quick Actions
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 gap-3">
          {actions.map((action, index) => (
            <Link key={index} href={action.href}>
              <Button
                variant="ghost"
                className="w-full justify-start h-auto p-3 hover:bg-gray-50"
              >
                <div className={`p-2 rounded-lg mr-3 ${action.color}`}>
                  <action.icon className="h-4 w-4" />
                </div>
                <div className="text-left">
                  <div className="font-medium text-gray-900">
                    {action.title}
                  </div>
                  <div className="text-sm text-gray-500">
                    {action.description}
                  </div>
                </div>
              </Button>
            </Link>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
