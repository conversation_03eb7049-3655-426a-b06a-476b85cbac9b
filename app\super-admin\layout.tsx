import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { redirect } from 'next/navigation'
import { SuperAdminSidebar } from '@/components/super-admin/super-admin-sidebar'
import { SuperAdminHeader } from '@/components/super-admin/super-admin-header'

export default async function SuperAdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const session = await getServerSession(authOptions)
  
  // Check if user is super admin
  if (!session?.user?.isSuperAdmin && session?.user?.role !== 'SUPER_ADMIN') {
    redirect('/dashboard')
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Super Admin Header */}
      <SuperAdminHeader />
      
      <div className="flex">
        {/* Super Admin Sidebar */}
        <SuperAdminSidebar />
        
        {/* Main Content */}
        <main className="flex-1 ml-64">
          <div className="p-6">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}
