import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { TemplateStats } from '@/components/contract-templates/template-stats'
import { TemplatesTable } from '@/components/contract-templates/templates-table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Plus, Search, Filter, Download, FileText } from 'lucide-react'
import Link from 'next/link'

interface SearchParams {
  search?: string
  category?: string
  status?: string
  page?: string
}

export default async function ContractTemplatesPage({
  searchParams,
}: {
  searchParams: SearchParams
}) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  const search = searchParams.search || ''
  const category = searchParams.category || ''
  const status = searchParams.status || ''
  const page = parseInt(searchParams.page || '1')
  const limit = 10
  const offset = (page - 1) * limit

  // Build where clause for templates
  const where: any = {
    companyId: session.user.companyId,
  }

  if (search) {
    where.OR = [
      { name: { contains: search } },
      { description: { contains: search } },
      { category: { contains: search } },
      { content: { contains: search } },
    ]
  }

  if (category) {
    where.category = category
  }

  if (status) {
    where.status = status
  }

  // Fetch templates and stats
  const [templates, totalCount, templateStats] = await Promise.all([
    prisma.contractTemplate.findMany({
      where,
      include: {
        createdBy: {
          select: { name: true, firstName: true, lastName: true }
        },
        _count: {
          select: {
            contracts: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      skip: offset,
      take: limit,
    }),
    prisma.contractTemplate.count({ where }),
    
    // Template statistics
    Promise.all([
      // Total templates
      prisma.contractTemplate.count({
        where: { companyId: session.user.companyId }
      }),
      
      // Templates by status
      prisma.contractTemplate.groupBy({
        by: ['status'],
        where: { companyId: session.user.companyId },
        _count: true
      }),
      
      // Templates by category
      prisma.contractTemplate.groupBy({
        by: ['category'],
        where: { 
          companyId: session.user.companyId,
          category: { not: null }
        },
        _count: true
      }),
      
      // Template usage
      prisma.contract.groupBy({
        by: ['templateId'],
        where: { 
          companyId: session.user.companyId,
          templateId: { not: null }
        },
        _count: true
      }),
      
      // Recent templates count (last 30 days)
      prisma.contractTemplate.count({
        where: {
          companyId: session.user.companyId,
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
          }
        }
      }),
    ])
  ])

  const [
    totalTemplates,
    templatesByStatus,
    templatesByCategory,
    templateUsage,
    recentTemplatesCount
  ] = templateStats

  const stats = {
    totalTemplates,
    templatesByStatus,
    templatesByCategory,
    templateUsage,
    recentTemplatesCount,
    activeTemplates: templatesByStatus.find(s => s.status === 'ACTIVE')?._count || 0,
    draftTemplates: templatesByStatus.find(s => s.status === 'DRAFT')?._count || 0,
  }

  const totalPages = Math.ceil(totalCount / limit)

  // Get unique categories for filter
  const categories = await prisma.contractTemplate.findMany({
    where: { 
      companyId: session.user.companyId,
      category: { not: null }
    },
    select: { category: true },
    distinct: ['category']
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Contract Templates</h1>
          <p className="text-gray-600 mt-1">
            Manage contract templates and streamline contract creation
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Link href="/dashboard/contract-templates/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Template
            </Button>
          </Link>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Download
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <TemplateStats stats={{
        ...stats,
        templatesByStatus: stats.templatesByStatus.map(item => ({
          status: item.status,
          _count: item._count
        })),
        templatesByCategory: stats.templatesByCategory.map(item => ({
          category: item.category,
          _count: item._count
        })),
        templateUsage: stats.templateUsage
          .filter(item => item.templateId !== null)
          .map(item => ({
            templateId: item.templateId!,
            _count: item._count
          }))
      }} />

      {/* Filters */}
      <div className="flex items-center space-x-4 p-4 bg-white rounded-lg border">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search templates by name, description, or content..."
              className="pl-10"
              defaultValue={search}
              name="search"
            />
          </div>
        </div>
        <select
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          defaultValue={category}
          name="category"
        >
          <option value="">All Categories</option>
          {categories.map((cat) => (
            <option key={cat.category} value={cat.category || ''}>
              {cat.category}
            </option>
          ))}
        </select>
        <select
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          defaultValue={status}
          name="status"
        >
          <option value="">All Status</option>
          <option value="ACTIVE">Active</option>
          <option value="DRAFT">Draft</option>
          <option value="ARCHIVED">Archived</option>
        </select>
        <Button variant="outline">
          <Filter className="h-4 w-4 mr-2" />
          More Filters
        </Button>
      </div>

      {/* Templates Table */}
      <TemplatesTable 
        templates={templates}
        currentPage={page}
        totalPages={totalPages}
        totalCount={totalCount}
      />
    </div>
  )
}
