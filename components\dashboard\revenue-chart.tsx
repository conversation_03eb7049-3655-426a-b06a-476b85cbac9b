'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { formatCurrency } from '@/lib/utils'
import { TrendingUp, TrendingDown, DollarSign } from 'lucide-react'

interface RevenueChartProps {
  data: {
    revenue: Array<{
      month: string
      revenue: number
      count: number
    }>
    quotations: Array<{
      month: string
      count: number
      value: number
    }>
    invoices: Array<{
      month: string
      count: number
      value: number
    }>
    contracts: Array<{
      month: string
      count: number
      value: number
    }>
  }
}

export function RevenueChart({ data }: RevenueChartProps) {
  // Process revenue data for the last 6 months
  const last6Months = data.revenue.slice(-6)
  const maxRevenue = Math.max(...last6Months.map(item => item.revenue || 0))
  
  // Calculate growth rate
  const currentMonth = last6Months[last6Months.length - 1]?.revenue || 0
  const previousMonth = last6Months[last6Months.length - 2]?.revenue || 0
  const growthRate = previousMonth > 0 
    ? (((currentMonth - previousMonth) / previousMonth) * 100).toFixed(1)
    : '0'

  // Calculate total revenue
  const totalRevenue = last6Months.reduce((sum, item) => sum + (item.revenue || 0), 0)
  const avgRevenue = totalRevenue / last6Months.length

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <DollarSign className="h-5 w-5 text-green-600" />
            <span>Revenue Trends</span>
          </CardTitle>
          <div className="flex items-center space-x-2">
            {parseFloat(growthRate) > 0 ? (
              <TrendingUp className="h-4 w-4 text-green-600" />
            ) : (
              <TrendingDown className="h-4 w-4 text-red-600" />
            )}
            <span className={`text-sm font-medium ${
              parseFloat(growthRate) > 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {parseFloat(growthRate) > 0 ? '+' : ''}{growthRate}%
            </span>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Revenue Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <p className="text-sm text-green-600 font-medium">Total Revenue</p>
            <p className="text-xl font-bold text-green-800">{formatCurrency(totalRevenue)}</p>
          </div>
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-600 font-medium">Monthly Average</p>
            <p className="text-xl font-bold text-blue-800">{formatCurrency(avgRevenue)}</p>
          </div>
          <div className="text-center p-3 bg-purple-50 rounded-lg">
            <p className="text-sm text-purple-600 font-medium">This Month</p>
            <p className="text-xl font-bold text-purple-800">{formatCurrency(currentMonth)}</p>
          </div>
        </div>

        {/* Simple Bar Chart */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-900">Revenue by Month</h4>
          <div className="space-y-3">
            {last6Months.map((item, index) => {
              const percentage = maxRevenue > 0 ? (item.revenue / maxRevenue) * 100 : 0
              const monthName = new Date(item.month + '-01').toLocaleDateString('en-US', { 
                month: 'short', 
                year: '2-digit' 
              })
              
              return (
                <div key={index} className="space-y-1">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">{monthName}</span>
                    <span className="font-medium text-gray-900">
                      {formatCurrency(item.revenue || 0)}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-green-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${percentage}%` }}
                    />
                  </div>
                  <div className="text-xs text-gray-500">
                    {item.count || 0} paid invoices
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Pipeline Value */}
        <div className="mt-6 pt-6 border-t">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Pipeline Value</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-600 font-medium">Quotations</p>
              <p className="text-lg font-bold text-blue-800">
                {formatCurrency(
                  data.quotations.reduce((sum, item) => sum + (item.value || 0), 0)
                )}
              </p>
              <p className="text-xs text-blue-600">
                {data.quotations.reduce((sum, item) => sum + (item.count || 0), 0)} total
              </p>
            </div>
            <div className="text-center p-3 bg-orange-50 rounded-lg">
              <p className="text-sm text-orange-600 font-medium">Invoices</p>
              <p className="text-lg font-bold text-orange-800">
                {formatCurrency(
                  data.invoices.reduce((sum, item) => sum + (item.value || 0), 0)
                )}
              </p>
              <p className="text-xs text-orange-600">
                {data.invoices.reduce((sum, item) => sum + (item.count || 0), 0)} total
              </p>
            </div>
            <div className="text-center p-3 bg-purple-50 rounded-lg">
              <p className="text-sm text-purple-600 font-medium">Contracts</p>
              <p className="text-lg font-bold text-purple-800">
                {formatCurrency(
                  data.contracts.reduce((sum, item) => sum + (item.value || 0), 0)
                )}
              </p>
              <p className="text-xs text-purple-600">
                {data.contracts.reduce((sum, item) => sum + (item.count || 0), 0)} total
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
