import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { redirect } from 'next/navigation'
import {
  Monitor,
  Server,
  Activity,
  Database,
  Cpu,
  HardDrive,
  Wifi,
  AlertTriangle,
  CheckCircle,
  Clock,
  Zap,
  Globe,
  Shield,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  Users
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { formatDate } from '@/lib/utils'

export default async function SuperAdminSystemPage() {
  const session = await getServerSession(authOptions)

  // Check if user is super admin
  if (!session?.user?.isSuperAdmin && session?.user?.role !== 'SUPER_ADMIN') {
    redirect('/dashboard')
  }

  // Get system metrics (in a real app, these would come from monitoring services)
  const now = new Date()
  const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const startOfHour = new Date(now.getFullYear(), now.getMonth(), now.getDate(), now.getHours())

  // Fetch database metrics
  const [
    totalUsers,
    totalCompanies,
    totalActivities,
    recentErrors,
    databaseSize,
    activeConnections
  ] = await Promise.all([
    prisma.user.count(),
    prisma.company.count(),
    prisma.activity.count({ where: { createdAt: { gte: startOfDay } } }),
    // Mock error logs for demo
    Promise.resolve([
      { id: 1, level: 'ERROR', message: 'Database connection timeout', timestamp: new Date(now.getTime() - 2 * 60 * 60 * 1000) },
      { id: 2, level: 'WARN', message: 'High memory usage detected', timestamp: new Date(now.getTime() - 4 * 60 * 60 * 1000) },
      { id: 3, level: 'ERROR', message: 'Failed to send email notification', timestamp: new Date(now.getTime() - 6 * 60 * 60 * 1000) }
    ]),
    // Mock database size
    Promise.resolve(2.4), // GB
    // Mock active connections
    Promise.resolve(47)
  ])

  // System health metrics (mock data for demo)
  const systemMetrics = {
    uptime: '99.9%',
    responseTime: 120, // ms
    errorRate: 0.1, // %
    throughput: 1247, // requests/min
    cpuUsage: 45, // %
    memoryUsage: 68, // %
    diskUsage: 34, // %
    networkLatency: 23, // ms
    lastBackup: new Date(now.getTime() - 6 * 60 * 60 * 1000),
    nextMaintenance: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
  }

  // Performance metrics over time (mock data)
  const performanceHistory = Array.from({ length: 24 }, (_, i) => ({
    hour: new Date(now.getTime() - (23 - i) * 60 * 60 * 1000).getHours(),
    responseTime: Math.floor(Math.random() * 50) + 100,
    requests: Math.floor(Math.random() * 500) + 800,
    errors: Math.floor(Math.random() * 5)
  }))

  // Service status
  const services = [
    { name: 'Web Server', status: 'healthy', uptime: '99.9%', lastCheck: now },
    { name: 'Database', status: 'healthy', uptime: '99.8%', lastCheck: now },
    { name: 'Email Service', status: 'warning', uptime: '98.5%', lastCheck: new Date(now.getTime() - 5 * 60 * 1000) },
    { name: 'File Storage', status: 'healthy', uptime: '99.9%', lastCheck: now },
    { name: 'Background Jobs', status: 'healthy', uptime: '99.7%', lastCheck: now },
    { name: 'API Gateway', status: 'healthy', uptime: '99.9%', lastCheck: now }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'bg-green-100 text-green-800'
      case 'warning':
        return 'bg-yellow-100 text-yellow-800'
      case 'error':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return CheckCircle
      case 'warning':
        return AlertTriangle
      case 'error':
        return AlertTriangle
      default:
        return Clock
    }
  }

  // Key system statistics
  const systemStats = [
    {
      title: 'System Uptime',
      value: systemMetrics.uptime,
      subtitle: 'Last 30 days',
      icon: Server,
      color: 'green',
      trend: 'stable'
    },
    {
      title: 'Response Time',
      value: `${systemMetrics.responseTime}ms`,
      subtitle: 'Average response',
      icon: Zap,
      color: 'blue',
      trend: 'down'
    },
    {
      title: 'Active Users',
      value: systemMetrics.throughput.toLocaleString(),
      subtitle: 'Current connections',
      icon: Users,
      color: 'purple',
      trend: 'up'
    },
    {
      title: 'Error Rate',
      value: `${systemMetrics.errorRate}%`,
      subtitle: 'Last 24 hours',
      icon: AlertTriangle,
      color: 'orange',
      trend: 'down'
    }
  ]

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="p-3 bg-red-100 rounded-lg">
            <Monitor className="h-8 w-8 text-red-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">System Health</h1>
            <p className="text-gray-600">Real-time monitoring and system diagnostics</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Badge variant="outline" className="text-green-600 border-green-200">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
            All Systems Operational
          </Badge>
          <Button variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* System Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {systemStats.map((stat, index) => {
          const Icon = stat.icon
          const TrendIcon = stat.trend === 'up' ? TrendingUp : stat.trend === 'down' ? TrendingDown : Clock
          return (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {stat.title}
                </CardTitle>
                <Icon className={`h-4 w-4 text-${stat.color}-600`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <div className="flex items-center justify-between mt-1">
                  <p className="text-xs text-gray-500">{stat.subtitle}</p>
                  <TrendIcon className={`h-3 w-3 ${
                    stat.trend === 'up' ? 'text-green-600' :
                    stat.trend === 'down' ? 'text-red-600' : 'text-gray-400'
                  }`} />
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Main System Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left Column - Services and Performance */}
        <div className="lg:col-span-2 space-y-6">
          {/* Service Status */}
          <Card>
            <CardHeader>
              <CardTitle>Service Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {services.map((service) => {
                  const StatusIcon = getStatusIcon(service.status)
                  return (
                    <div key={service.name} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <StatusIcon className={`h-5 w-5 ${
                          service.status === 'healthy' ? 'text-green-600' :
                          service.status === 'warning' ? 'text-yellow-600' : 'text-red-600'
                        }`} />
                        <div>
                          <p className="font-medium">{service.name}</p>
                          <p className="text-sm text-gray-500">Uptime: {service.uptime}</p>
                        </div>
                      </div>
                      <Badge className={getStatusColor(service.status)} variant="outline">
                        {service.status}
                      </Badge>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>

          {/* Performance Metrics */}
          <Card>
            <CardHeader>
              <CardTitle>Performance Metrics (Last 24 Hours)</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Response Time</span>
                    <span className="text-sm text-gray-500">Average: {systemMetrics.responseTime}ms</span>
                  </div>
                  <div className="flex items-end space-x-1 h-20">
                    {performanceHistory.slice(-12).map((hour, index) => (
                      <div key={index} className="flex-1 bg-blue-200 rounded-t" style={{
                        height: `${(hour.responseTime / 200) * 100}%`,
                        minHeight: '4px'
                      }}></div>
                    ))}
                  </div>
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Request Volume</span>
                    <span className="text-sm text-gray-500">Peak: 1,247/min</span>
                  </div>
                  <div className="flex items-end space-x-1 h-20">
                    {performanceHistory.slice(-12).map((hour, index) => (
                      <div key={index} className="flex-1 bg-green-200 rounded-t" style={{
                        height: `${(hour.requests / 1300) * 100}%`,
                        minHeight: '4px'
                      }}></div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Recent System Events */}
          <Card>
            <CardHeader>
              <CardTitle>Recent System Events</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {recentErrors.map((error) => (
                  <div key={error.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                    <AlertTriangle className={`h-4 w-4 mt-1 ${
                      error.level === 'ERROR' ? 'text-red-600' : 'text-yellow-600'
                    }`} />
                    <div className="flex-1">
                      <p className="text-sm font-medium">{error.message}</p>
                      <p className="text-xs text-gray-500">{formatDate(error.timestamp)}</p>
                    </div>
                    <Badge variant="outline" className={
                      error.level === 'ERROR' ? 'text-red-600 border-red-200' : 'text-yellow-600 border-yellow-200'
                    }>
                      {error.level}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Column - System Resources and Info */}
        <div className="space-y-6">
          {/* Resource Usage */}
          <Card>
            <CardHeader>
              <CardTitle>Resource Usage</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <Cpu className="h-4 w-4 text-blue-600" />
                    <span className="text-sm">CPU Usage</span>
                  </div>
                  <span className="text-sm font-medium">{systemMetrics.cpuUsage}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full"
                    style={{ width: `${systemMetrics.cpuUsage}%` }}
                  ></div>
                </div>
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <Database className="h-4 w-4 text-green-600" />
                    <span className="text-sm">Memory Usage</span>
                  </div>
                  <span className="text-sm font-medium">{systemMetrics.memoryUsage}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-green-600 h-2 rounded-full"
                    style={{ width: `${systemMetrics.memoryUsage}%` }}
                  ></div>
                </div>
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <HardDrive className="h-4 w-4 text-purple-600" />
                    <span className="text-sm">Disk Usage</span>
                  </div>
                  <span className="text-sm font-medium">{systemMetrics.diskUsage}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-purple-600 h-2 rounded-full"
                    style={{ width: `${systemMetrics.diskUsage}%` }}
                  ></div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Database Metrics */}
          <Card>
            <CardHeader>
              <CardTitle>Database Metrics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm">Total Records</span>
                <span className="text-sm font-medium">{(totalUsers + totalCompanies).toLocaleString()}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Database Size</span>
                <span className="text-sm font-medium">{databaseSize} GB</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Active Connections</span>
                <span className="text-sm font-medium">{activeConnections}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Daily Activities</span>
                <span className="text-sm font-medium">{totalActivities.toLocaleString()}</span>
              </div>
            </CardContent>
          </Card>

          {/* System Information */}
          <Card>
            <CardHeader>
              <CardTitle>System Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm">Last Backup</span>
                <span className="text-sm text-gray-600">{formatDate(systemMetrics.lastBackup)}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Next Maintenance</span>
                <span className="text-sm text-gray-600">{formatDate(systemMetrics.nextMaintenance)}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Network Latency</span>
                <Badge variant="outline" className="text-green-600">
                  {systemMetrics.networkLatency}ms
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Security Status</span>
                <Badge variant="outline" className="text-green-600">
                  <Shield className="h-3 w-3 mr-1" />
                  Secure
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
