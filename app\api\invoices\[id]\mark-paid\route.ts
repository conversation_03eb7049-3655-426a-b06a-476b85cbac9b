import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const markPaidSchema = z.object({
  paymentDate: z.string().optional(),
  paymentMethod: z.string().optional(),
  paymentReference: z.string().optional(),
  notes: z.string().optional(),
})

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = markPaidSchema.parse(body)

    // Check if invoice exists and belongs to the company
    const invoice = await prisma.invoice.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      },
      include: {
        customer: {
          select: { 
            id: true, 
            name: true, 
            email: true, 
            company: true 
          }
        }
      }
    })

    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 })
    }

    // Check if invoice can be marked as paid
    if (!['SENT', 'VIEWED', 'OVERDUE'].includes(invoice.status)) {
      return NextResponse.json(
        { error: 'Only sent, viewed, or overdue invoices can be marked as paid' },
        { status: 400 }
      )
    }

    // Check if invoice is already paid
    if (invoice.status === 'PAID') {
      return NextResponse.json(
        { error: 'Invoice is already marked as paid' },
        { status: 400 }
      )
    }

    const paymentDate = validatedData.paymentDate ? new Date(validatedData.paymentDate) : new Date()

    // Update invoice status to PAID
    const updatedInvoice = await prisma.invoice.update({
      where: { id: params.id },
      data: {
        status: 'PAID',
        paidAt: paymentDate,
        paymentMethod: validatedData.paymentMethod,
        paymentReference: validatedData.paymentReference,
      },
      include: {
        customer: {
          select: { id: true, name: true, email: true, company: true }
        },
        quotation: {
          select: { id: true, quotationNumber: true, title: true }
        },
        items: {
          include: {
            item: {
              select: { name: true, category: true }
            }
          }
        },
        _count: {
          select: {
            items: true,
            activities: true,
          }
        }
      }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'INVOICE_PAID',
        title: 'Invoice paid',
        description: `Invoice "${invoice.invoiceNumber}" was marked as paid${validatedData.paymentMethod ? ` via ${validatedData.paymentMethod}` : ''}${validatedData.notes ? `. Notes: ${validatedData.notes}` : ''}`,
        invoiceId: invoice.id,
        customerId: invoice.customerId,
        quotationId: invoice.quotationId,
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    // Update customer status to ACTIVE if they were a PROSPECT
    if (invoice.customer) {
      await prisma.customer.updateMany({
        where: {
          id: invoice.customerId,
          status: 'PROSPECT',
        },
        data: {
          status: 'ACTIVE',
        }
      })
    }

    return NextResponse.json({
      message: 'Invoice marked as paid successfully',
      invoice: updatedInvoice
    })
  } catch (error) {
    console.error('Error marking invoice as paid:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
