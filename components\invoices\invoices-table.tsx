'use client'

import Link from 'next/link'
import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { formatDate, formatCurrency, getStatusColor } from '@/lib/utils'
import { 
  Eye, 
  Edit, 
  Trash2, 
  Send, 
  Download,
  Copy,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  Receipt,
  User,
  Building,
  Calendar,
  DollarSign,
  CreditCard,
  AlertTriangle
} from 'lucide-react'
import toast from 'react-hot-toast'

interface Invoice {
  id: string
  invoiceNumber: string
  title: string
  description: string | null
  status: string
  total: number
  dueDate: Date | null
  paidAt: Date | null
  createdAt: Date
  customer: {
    id: string
    name: string
    email: string | null
    company: string | null
  } | null
  quotation: {
    id: string
    quotationNumber: string
    title: string
  } | null
  items: Array<{
    id: string
    name: string
    quantity: number
    unitPrice: number
  }>
  _count: {
    items: number
    activities: number
  }
}

interface InvoicesTableProps {
  invoices: Invoice[]
  currentPage: number
  totalPages: number
  totalCount: number
}

export function InvoicesTable({ 
  invoices, 
  currentPage, 
  totalPages, 
  totalCount 
}: InvoicesTableProps) {
  const [deletingId, setDeletingId] = useState<string | null>(null)
  const [sendingId, setSendingId] = useState<string | null>(null)
  const [markingPaidId, setMarkingPaidId] = useState<string | null>(null)

  const handleDelete = async (invoiceId: string) => {
    if (!confirm('Are you sure you want to delete this invoice?')) {
      return
    }

    setDeletingId(invoiceId)
    try {
      const response = await fetch(`/api/invoices/${invoiceId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to delete invoice')
      }

      toast.success('Invoice deleted successfully')
      window.location.reload()
    } catch (error) {
      toast.error('Failed to delete invoice')
    } finally {
      setDeletingId(null)
    }
  }

  const handleSend = async (invoiceId: string) => {
    setSendingId(invoiceId)
    try {
      const response = await fetch(`/api/invoices/${invoiceId}/send`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to send invoice')
      }

      toast.success('Invoice sent successfully')
      window.location.reload()
    } catch (error) {
      toast.error('Failed to send invoice')
    } finally {
      setSendingId(null)
    }
  }

  const handleMarkPaid = async (invoiceId: string) => {
    setMarkingPaidId(invoiceId)
    try {
      const response = await fetch(`/api/invoices/${invoiceId}/mark-paid`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to mark invoice as paid')
      }

      toast.success('Invoice marked as paid')
      window.location.reload()
    } catch (error) {
      toast.error('Failed to mark invoice as paid')
    } finally {
      setMarkingPaidId(null)
    }
  }

  const handleDuplicate = async (invoiceId: string) => {
    try {
      const response = await fetch(`/api/invoices/${invoiceId}/duplicate`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to duplicate invoice')
      }

      const result = await response.json()
      toast.success('Invoice duplicated successfully')
      window.location.href = `/dashboard/invoices/${result.id}/edit`
    } catch (error) {
      toast.error('Failed to duplicate invoice')
    }
  }

  const isOverdue = (invoice: Invoice) => {
    return invoice.dueDate && 
           new Date(invoice.dueDate) < new Date() && 
           !['PAID', 'CANCELLED'].includes(invoice.status)
  }

  const getDaysOverdue = (dueDate: Date) => {
    return Math.ceil((new Date().getTime() - new Date(dueDate).getTime()) / (1000 * 60 * 60 * 24))
  }

  if (invoices.length === 0) {
    return (
      <Card>
        <CardContent className="py-12">
          <div className="text-center">
            <Receipt className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No invoices found</h3>
            <p className="text-gray-500 mb-6">
              Get started by creating your first invoice.
            </p>
            <Link href="/dashboard/invoices/new">
              <Button>
                Create Invoice
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Invoices ({totalCount})</span>
            <span className="text-sm font-normal text-gray-500">
              Page {currentPage} of {totalPages}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Desktop Table */}
          <div className="hidden md:block overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Invoice</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Customer</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Amount</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Due Date</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Created</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">Actions</th>
                </tr>
              </thead>
              <tbody>
                {invoices.map((invoice) => (
                  <tr key={invoice.id} className="border-b border-gray-100 hover:bg-gray-50">
                    <td className="py-4 px-4">
                      <div>
                        <div className="font-medium text-gray-900">{invoice.invoiceNumber}</div>
                        <div className="text-sm text-gray-600">{invoice.title}</div>
                        {invoice.quotation && (
                          <div className="text-xs text-blue-600">
                            From: {invoice.quotation.quotationNumber}
                          </div>
                        )}
                        <div className="text-xs text-gray-400 mt-1">
                          {invoice._count.items} items • {invoice._count.activities} activities
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      {invoice.customer ? (
                        <div>
                          <div className="font-medium text-gray-900">{invoice.customer.name}</div>
                          {invoice.customer.company && (
                            <div className="text-sm text-gray-500">{invoice.customer.company}</div>
                          )}
                          {invoice.customer.email && (
                            <div className="text-sm text-gray-500">{invoice.customer.email}</div>
                          )}
                        </div>
                      ) : (
                        <span className="text-gray-400 italic">No customer</span>
                      )}
                    </td>
                    <td className="py-4 px-4">
                      <div className="space-y-1">
                        <Badge className={getStatusColor(invoice.status)} variant="outline">
                          {invoice.status}
                        </Badge>
                        {isOverdue(invoice) && (
                          <div className="flex items-center text-xs text-red-600">
                            <AlertTriangle className="h-3 w-3 mr-1" />
                            {getDaysOverdue(invoice.dueDate!)} days overdue
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <span className="font-medium text-gray-900">
                        {formatCurrency(invoice.total)}
                      </span>
                      {invoice.paidAt && (
                        <div className="text-xs text-green-600">
                          Paid: {formatDate(invoice.paidAt)}
                        </div>
                      )}
                    </td>
                    <td className="py-4 px-4 text-sm text-gray-600">
                      {invoice.dueDate ? (
                        <span className={isOverdue(invoice) ? 'text-red-600 font-medium' : ''}>
                          {formatDate(invoice.dueDate)}
                        </span>
                      ) : (
                        '-'
                      )}
                    </td>
                    <td className="py-4 px-4 text-sm text-gray-600">
                      {formatDate(invoice.createdAt)}
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center justify-end space-x-2">
                        <Link href={`/dashboard/invoices/${invoice.id}`}>
                          <Button variant="ghost" >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </Link>
                        {invoice.status === 'DRAFT' && (
                          <Link href={`/dashboard/invoices/${invoice.id}/edit`}>
                            <Button variant="ghost" >
                              <Edit className="h-4 w-4" />
                            </Button>
                          </Link>
                        )}
                        {invoice.status === 'DRAFT' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleSend(invoice.id)}
                            disabled={sendingId === invoice.id}
                            className="text-blue-600 hover:text-blue-700"
                          >
                            <Send className="h-4 w-4" />
                          </Button>
                        )}
                        {['SENT', 'VIEWED', 'OVERDUE'].includes(invoice.status) && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleMarkPaid(invoice.id)}
                            disabled={markingPaidId === invoice.id}
                            className="text-green-600 hover:text-green-700"
                          >
                            <CreditCard className="h-4 w-4" />
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDuplicate(invoice.id)}
                          className="text-green-600 hover:text-green-700"
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-gray-600 hover:text-gray-700"
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                        {invoice.status === 'DRAFT' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(invoice.id)}
                            disabled={deletingId === invoice.id}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Mobile Cards */}
          <div className="md:hidden space-y-4">
            {invoices.map((invoice) => (
              <Card key={invoice.id} className="p-4">
                <div className="space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">{invoice.invoiceNumber}</div>
                      <div className="text-sm text-gray-600 mt-1">{invoice.title}</div>
                      {invoice.quotation && (
                        <div className="text-xs text-blue-600 mt-1">
                          From: {invoice.quotation.quotationNumber}
                        </div>
                      )}
                    </div>
                    <Button variant="ghost" >
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>

                  {invoice.customer && (
                    <div className="flex items-center space-x-2">
                      <User className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600">{invoice.customer.name}</span>
                      {invoice.customer.company && (
                        <>
                          <Building className="h-4 w-4 text-gray-400" />
                          <span className="text-sm text-gray-600">{invoice.customer.company}</span>
                        </>
                      )}
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Badge className={getStatusColor(invoice.status)} variant="outline">
                        {invoice.status}
                      </Badge>
                      {isOverdue(invoice) && (
                        <Badge variant="destructive" className="text-xs">
                          {getDaysOverdue(invoice.dueDate!)} days overdue
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center space-x-1">
                      <DollarSign className="h-4 w-4 text-gray-400" />
                      <span className="font-medium text-gray-900">
                        {formatCurrency(invoice.total)}
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4" />
                      <span>Due: {invoice.dueDate ? formatDate(invoice.dueDate) : 'No due date'}</span>
                    </div>
                    <div>
                      {invoice._count.items} items • {invoice._count.activities} activities
                    </div>
                  </div>

                  <div className="flex items-center justify-end space-x-2 pt-2 border-t">
                    <Link href={`/dashboard/invoices/${invoice.id}`}>
                      <Button variant="ghost" >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </Link>
                    {invoice.status === 'DRAFT' && (
                      <>
                        <Link href={`/dashboard/invoices/${invoice.id}/edit`}>
                          <Button variant="ghost" >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </Link>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleSend(invoice.id)}
                          disabled={sendingId === invoice.id}
                          className="text-blue-600"
                        >
                          <Send className="h-4 w-4" />
                        </Button>
                      </>
                    )}
                    {['SENT', 'VIEWED', 'OVERDUE'].includes(invoice.status) && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleMarkPaid(invoice.id)}
                        disabled={markingPaidId === invoice.id}
                        className="text-green-600"
                      >
                        <CreditCard className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            Showing {((currentPage - 1) * 10) + 1} to {Math.min(currentPage * 10, totalCount)} of {totalCount} invoices
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage <= 1}
              asChild
            >
              <Link href={`?page=${currentPage - 1}`}>
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Link>
            </Button>
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage >= totalPages}
              asChild
            >
              <Link href={`?page=${currentPage + 1}`}>
                Next
                <ChevronRight className="h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
