import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if invoice exists and belongs to the company
    const originalInvoice = await prisma.invoice.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      },
      include: {
        items: true,
      }
    })

    if (!originalInvoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 })
    }

    // Generate new invoice number
    const lastInvoice = await prisma.invoice.findFirst({
      where: { companyId: session.user.companyId },
      orderBy: { createdAt: 'desc' },
      select: { invoiceNumber: true }
    })

    const newInvoiceNumber = generateInvoiceNumber(lastInvoice?.invoiceNumber)

    // Create duplicate invoice
    const duplicatedInvoice = await prisma.invoice.create({
      data: {
        invoiceNumber: newInvoiceNumber,
        title: `${originalInvoice.title} (Copy)`,
        description: originalInvoice.description,
        status: 'DRAFT',
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        terms: originalInvoice.terms,
        paymentTerms: originalInvoice.paymentTerms,
        taxRate: originalInvoice.taxRate,
        discountType: originalInvoice.discountType,
        discountValue: originalInvoice.discountValue,
        subtotal: originalInvoice.subtotal,
        total: originalInvoice.total,
        customerId: originalInvoice.customerId,
        quotationId: originalInvoice.quotationId,
        companyId: session.user.companyId,
        createdById: session.user.id,
        items: {
          create: originalInvoice.items.map(item => ({
            itemId: item.itemId,
            name: item.name,
            description: item.description,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            discount: item.discount,
            taxRate: item.taxRate,
          }))
        }
      },
      include: {
        customer: {
          select: { id: true, name: true, email: true, company: true }
        },
        quotation: {
          select: { id: true, quotationNumber: true, title: true }
        },
        items: {
          include: {
            item: {
              select: { name: true, category: true }
            }
          }
        },
        _count: {
          select: {
            items: true,
            activities: true,
          }
        }
      }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'INVOICE_CREATED',
        title: 'Invoice duplicated',
        description: `Invoice "${duplicatedInvoice.invoiceNumber}" was created as a copy of "${originalInvoice.invoiceNumber}"`,
        invoiceId: duplicatedInvoice.id,
        customerId: duplicatedInvoice.customerId,
        quotationId: duplicatedInvoice.quotationId,
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    return NextResponse.json(duplicatedInvoice, { status: 201 })
  } catch (error) {
    console.error('Error duplicating invoice:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function generateInvoiceNumber(lastNumber?: string): string {
  const currentYear = new Date().getFullYear()
  const currentMonth = String(new Date().getMonth() + 1).padStart(2, '0')
  
  let nextNumber = 1
  
  if (lastNumber) {
    const match = lastNumber.match(/INV-(\d{4})(\d{2})-(\d{4})/)
    if (match) {
      const [, year, month, num] = match
      if (year === currentYear.toString() && month === currentMonth) {
        nextNumber = parseInt(num) + 1
      }
    }
  }
  
  return `INV-${currentYear}${currentMonth}-${String(nextNumber).padStart(4, '0')}`
}
