'use client'

import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { formatCurrency, formatDate } from '@/lib/utils'
import { 
  CreditCard, 
  Calendar, 
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Download,
  Edit,
  Crown,
  Star,
  Award
} from 'lucide-react'
import toast from 'react-hot-toast'

interface SubscriptionDetailsProps {
  company: any
  subscription: any
  usage: any
  limits: any
}

export function SubscriptionDetails({ company, subscription, usage, limits }: SubscriptionDetailsProps) {
  const [isLoading, setIsLoading] = useState(false)

  const getPlanIcon = (plan: string) => {
    switch (plan) {
      case 'ENTERPRISE':
        return Crown
      case 'PROFESSIONAL':
        return Star
      case 'BASIC':
        return Award
      default:
        return CreditCard
    }
  }

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'ENTERPRISE':
        return 'text-purple-600 bg-purple-100 border-purple-200'
      case 'PROFESSIONAL':
        return 'text-blue-600 bg-blue-100 border-blue-200'
      case 'BASIC':
        return 'text-green-600 bg-green-100 border-green-200'
      default:
        return 'text-gray-600 bg-gray-100 border-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return CheckCircle
      case 'CANCELLED':
        return XCircle
      case 'PAST_DUE':
        return AlertTriangle
      default:
        return AlertTriangle
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'text-green-600 border-green-200'
      case 'CANCELLED':
        return 'text-red-600 border-red-200'
      case 'PAST_DUE':
        return 'text-yellow-600 border-yellow-200'
      default:
        return 'text-gray-600 border-gray-200'
    }
  }

  const handleCancelSubscription = async () => {
    if (!confirm('Are you sure you want to cancel your subscription? This action cannot be undone.')) {
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch('/api/billing/subscription/cancel', {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to cancel subscription')
      }

      toast.success('Subscription cancelled successfully')
      window.location.reload()
    } catch (error) {
      toast.error('Failed to cancel subscription')
    } finally {
      setIsLoading(false)
    }
  }

  const handleReactivateSubscription = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/billing/subscription/reactivate', {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to reactivate subscription')
      }

      toast.success('Subscription reactivated successfully')
      window.location.reload()
    } catch (error) {
      toast.error('Failed to reactivate subscription')
    } finally {
      setIsLoading(false)
    }
  }

  const handleUpdatePaymentMethod = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/billing/payment-method/update', {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to update payment method')
      }

      const { url } = await response.json()
      window.location.href = url
    } catch (error) {
      toast.error('Failed to update payment method')
    } finally {
      setIsLoading(false)
    }
  }

  const PlanIcon = getPlanIcon(company.plan)
  const StatusIcon = subscription ? getStatusIcon(subscription.status) : AlertTriangle

  return (
    <div className="space-y-6">
      {/* Subscription Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CreditCard className="h-5 w-5" />
            <span>Subscription Details</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {subscription ? (
            <div className="space-y-6">
              {/* Current Plan */}
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className={`p-3 rounded-lg ${getPlanColor(company.plan).split(' ').slice(1).join(' ')}`}>
                    <PlanIcon className={`h-6 w-6 ${getPlanColor(company.plan).split(' ')[0]}`} />
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">
                      {company.plan} Plan
                    </div>
                    <div className="text-sm text-gray-600">
                      {formatCurrency(subscription.amount)} / {subscription.interval.toLowerCase()}
                    </div>
                  </div>
                </div>
                <Badge className={getStatusColor(subscription.status)} variant="outline">
                  <StatusIcon className="h-3 w-3 mr-1" />
                  {subscription.status}
                </Badge>
              </div>

              {/* Billing Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Billing Cycle</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Current Period</span>
                      <span className="text-sm font-medium text-gray-900">
                        {formatDate(subscription.currentPeriodStart)} - {formatDate(subscription.currentPeriodEnd)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Next Billing Date</span>
                      <span className="text-sm font-medium text-gray-900">
                        {formatDate(subscription.nextBillingDate)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Billing Interval</span>
                      <span className="text-sm font-medium text-gray-900">
                        {subscription.interval}
                      </span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Payment Method</h4>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <CreditCard className="h-4 w-4 text-gray-600" />
                      <span className="text-sm text-gray-900">
                        •••• •••• •••• {subscription.paymentMethod?.last4 || '****'}
                      </span>
                    </div>
                    <div className="text-sm text-gray-600">
                      {subscription.paymentMethod?.brand || 'Card'} expires {subscription.paymentMethod?.expMonth || '**'}/{subscription.paymentMethod?.expYear || '****'}
                    </div>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={handleUpdatePaymentMethod}
                      disabled={isLoading}
                    >
                      <Edit className="h-4 w-4 mr-2" />
                      Update Payment Method
                    </Button>
                  </div>
                </div>
              </div>

              {/* Subscription Actions */}
              <div className="flex items-center space-x-4 pt-4 border-t">
                {subscription.status === 'ACTIVE' ? (
                  <>
                    <Button 
                      variant="outline"
                      onClick={handleCancelSubscription}
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <XCircle className="h-4 w-4 mr-2" />
                      )}
                      Cancel Subscription
                    </Button>
                    <Button variant="outline">
                      <Download className="h-4 w-4 mr-2" />
                      Download Invoice
                    </Button>
                  </>
                ) : (
                  <Button 
                    onClick={handleReactivateSubscription}
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <CheckCircle className="h-4 w-4 mr-2" />
                    )}
                    Reactivate Subscription
                  </Button>
                )}
              </div>

              {/* Alerts */}
              {subscription.status === 'PAST_DUE' && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Your subscription is past due. Please update your payment method to avoid service interruption.
                  </AlertDescription>
                </Alert>
              )}

              {subscription.status === 'CANCELLED' && (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Your subscription has been cancelled. You can continue using the service until {formatDate(subscription.currentPeriodEnd)}.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          ) : (
            <div className="text-center py-8">
              <CreditCard className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <h3 className="font-medium text-gray-900 mb-2">No Active Subscription</h3>
              <p className="text-gray-500 mb-6">
                You're currently on the free plan. Upgrade to unlock more features.
              </p>
              <Button>
                <TrendingUp className="h-4 w-4 mr-2" />
                Upgrade Plan
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Usage Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Usage Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-800">
                {usage.monthly.users}
              </div>
              <div className="text-sm text-blue-600">Active Users</div>
              <div className="text-xs text-blue-500 mt-1">
                Limit: {limits.users === -1 ? 'Unlimited' : limits.users}
              </div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-800">
                {usage.monthly.quotations}
              </div>
              <div className="text-sm text-green-600">Quotations</div>
              <div className="text-xs text-green-500 mt-1">
                This month
              </div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-800">
                {usage.monthly.invoices}
              </div>
              <div className="text-sm text-purple-600">Invoices</div>
              <div className="text-xs text-purple-500 mt-1">
                This month
              </div>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-800">
                {usage.monthly.emails}
              </div>
              <div className="text-sm text-orange-600">Emails Sent</div>
              <div className="text-xs text-orange-500 mt-1">
                This month
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
