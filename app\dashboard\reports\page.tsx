import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ReportsOverview } from '@/components/reports/reports-overview'
import { ReportsList } from '@/components/reports/reports-list'
import { QuickReports } from '@/components/reports/quick-reports'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Plus, Search, Filter, Download, BarChart3 } from 'lucide-react'
import Link from 'next/link'

interface SearchParams {
  search?: string
  category?: string
  type?: string
  page?: string
}

export default async function ReportsPage({
  searchParams,
}: {
  searchParams: SearchParams
}) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  const search = searchParams.search || ''
  const category = searchParams.category || ''
  const type = searchParams.type || ''
  const page = parseInt(searchParams.page || '1')
  const limit = 10
  const offset = (page - 1) * limit

  // Calculate date ranges for analytics
  const now = new Date()
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
  const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
  const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0)
  const startOfYear = new Date(now.getFullYear(), 0, 1)
  const last30Days = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)

  // Fetch comprehensive analytics data
  const [
    // Overview metrics
    totalCustomers,
    totalLeads,
    totalQuotations,
    totalInvoices,
    totalContracts,
    totalReceipts,
    totalItems,
    totalTemplates,
    
    // Financial metrics
    monthlyRevenue,
    lastMonthRevenue,
    yearlyRevenue,
    outstandingInvoices,
    
    // Performance metrics
    quotationConversionRate,
    invoicePaymentRate,
    contractSignatureRate,
    
    // Recent activity
    recentReports,
    
    // Trend data
    monthlyTrends,
    categoryBreakdowns,
    statusDistributions
  ] = await Promise.all([
    // Overview counts
    prisma.customer.count({ where: { companyId: session.user.companyId } }),
    prisma.lead.count({ where: { companyId: session.user.companyId } }),
    prisma.quotation.count({ where: { companyId: session.user.companyId } }),
    prisma.invoice.count({ where: { companyId: session.user.companyId } }),
    prisma.contract.count({ where: { companyId: session.user.companyId } }),
    prisma.receipt.count({ where: { companyId: session.user.companyId } }),
    prisma.item.count({ where: { companyId: session.user.companyId } }),
    prisma.contractTemplate.count({ where: { companyId: session.user.companyId } }),
    
    // Financial aggregations
    prisma.invoice.aggregate({
      where: {
        companyId: session.user.companyId,
        status: 'PAID',
        paidAt: { gte: startOfMonth, lte: now }
      },
      _sum: { total: true }
    }),
    
    prisma.invoice.aggregate({
      where: {
        companyId: session.user.companyId,
        status: 'PAID',
        paidAt: { gte: startOfLastMonth, lte: endOfLastMonth }
      },
      _sum: { total: true }
    }),
    
    prisma.invoice.aggregate({
      where: {
        companyId: session.user.companyId,
        status: 'PAID',
        paidAt: { gte: startOfYear, lte: now }
      },
      _sum: { total: true }
    }),
    
    prisma.invoice.aggregate({
      where: {
        companyId: session.user.companyId,
        status: { in: ['SENT', 'VIEWED', 'OVERDUE'] }
      },
      _sum: { total: true },
      _count: true
    }),
    
    // Performance calculations (simplified)
    prisma.quotation.groupBy({
      by: ['status'],
      where: { companyId: session.user.companyId },
      _count: true
    }),
    
    prisma.invoice.groupBy({
      by: ['status'],
      where: { companyId: session.user.companyId },
      _count: true
    }),
    
    prisma.contract.groupBy({
      by: ['status'],
      where: { companyId: session.user.companyId },
      _count: true
    }),
    
    // Recent reports (placeholder - would be actual saved reports)
    [],
    
    // Trend data (simplified)
    [],
    [],
    []
  ])

  // Process performance metrics
  const quotationTotal = quotationConversionRate.reduce((sum, q) => sum + q._count, 0)
  const quotationAccepted = quotationConversionRate.find(q => q.status === 'ACCEPTED')?._count || 0
  const quotationConversion = quotationTotal > 0 ? (quotationAccepted / quotationTotal * 100).toFixed(1) : '0'

  const invoiceTotal = invoicePaymentRate.reduce((sum, i) => sum + i._count, 0)
  const invoicePaid = invoicePaymentRate.find(i => i.status === 'PAID')?._count || 0
  const invoicePayment = invoiceTotal > 0 ? (invoicePaid / invoiceTotal * 100).toFixed(1) : '0'

  const contractTotal = contractSignatureRate.reduce((sum, c) => sum + c._count, 0)
  const contractSigned = contractSignatureRate.filter(c => ['SIGNED', 'EXECUTED'].includes(c.status))
    .reduce((sum, c) => sum + c._count, 0)
  const contractSignature = contractTotal > 0 ? (contractSigned / contractTotal * 100).toFixed(1) : '0'

  const overviewData = {
    totalCustomers,
    totalLeads,
    totalQuotations,
    totalInvoices,
    totalContracts,
    totalReceipts,
    totalItems,
    totalTemplates,
    monthlyRevenue: monthlyRevenue._sum.total || 0,
    lastMonthRevenue: lastMonthRevenue._sum.total || 0,
    yearlyRevenue: yearlyRevenue._sum.total || 0,
    outstandingAmount: outstandingInvoices._sum.total || 0,
    outstandingCount: outstandingInvoices._count || 0,
    quotationConversion: parseFloat(quotationConversion),
    invoicePayment: parseFloat(invoicePayment),
    contractSignature: parseFloat(contractSignature)
  }

  const totalPages = Math.ceil(recentReports.length / limit)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Business Reports</h1>
          <p className="text-gray-600 mt-1">
            Comprehensive analytics and reporting for your business
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Link href="/dashboard/reports/builder">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Report
            </Button>
          </Link>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Download All
          </Button>
        </div>
      </div>

      {/* Overview Dashboard */}
      <ReportsOverview data={overviewData} />

      {/* Quick Reports */}
      <QuickReports companyId={session.user.companyId} />

      {/* Filters */}
      <div className="flex items-center space-x-4 p-4 bg-white rounded-lg border">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search reports..."
              className="pl-10"
              defaultValue={search}
              name="search"
            />
          </div>
        </div>
        <select
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          defaultValue={category}
          name="category"
        >
          <option value="">All Categories</option>
          <option value="financial">Financial</option>
          <option value="sales">Sales</option>
          <option value="customer">Customer</option>
          <option value="operational">Operational</option>
        </select>
        <select
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          defaultValue={type}
          name="type"
        >
          <option value="">All Types</option>
          <option value="summary">Summary</option>
          <option value="detailed">Detailed</option>
          <option value="trend">Trend Analysis</option>
          <option value="comparison">Comparison</option>
        </select>
        <Button variant="outline">
          <Filter className="h-4 w-4 mr-2" />
          More Filters
        </Button>
      </div>

      {/* Reports List */}
      <ReportsList 
        reports={recentReports}
        currentPage={page}
        totalPages={totalPages}
        totalCount={recentReports.length}
      />
    </div>
  )
}
