import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Receipt } from 'lucide-react'
import { InvoiceTemplateForm } from '@/components/invoices/invoice-template-form'

export default async function NewInvoiceTemplatePage() {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href="/dashboard/invoices/templates">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Templates
          </Button>
        </Link>
        <div className="p-2 bg-green-100 rounded-lg">
          <Receipt className="h-6 w-6 text-green-600" />
        </div>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Create Invoice Template</h1>
          <p className="text-gray-600 mt-1">
            Create a reusable template for invoice documents
          </p>
        </div>
      </div>

      {/* Template Form */}
      <InvoiceTemplateForm mode="create" />
    </div>
  )
}
