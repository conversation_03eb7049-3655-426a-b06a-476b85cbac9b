{"name": "nextjs-saas-production", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts"}, "dependencies": {"@auth/prisma-adapter": "^1.6.0", "@hookform/resolvers": "^3.3.2", "@prisma/client": "^5.7.1", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@stripe/stripe-js": "^7.6.1", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "lucide-react": "^0.294.0", "next": "14.0.4", "next-auth": "^4.24.5", "next-themes": "^0.2.1", "puppeteer": "^21.6.1", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "stripe": "^18.3.0", "tailwind-merge": "^2.1.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8", "prisma": "^5.7.1", "tailwindcss": "^3.3.0", "tsx": "^4.6.2", "typescript": "^5"}, "prisma": {"seed": "tsx prisma/seed.ts"}}