import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { TemplateDetails } from '@/components/contract-templates/template-details'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Edit, Copy, Trash2, Plus } from 'lucide-react'
import Link from 'next/link'
import { notFound } from 'next/navigation'

export default async function TemplateDetailPage({
  params,
}: {
  params: { id: string }
}) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  const template = await prisma.contractTemplate.findFirst({
    where: {
      id: params.id,
      companyId: session.user.companyId,
    },
    include: {
      createdBy: {
        select: { name: true, firstName: true, lastName: true }
      },
      contracts: {
        include: {
          customer: {
            select: { name: true }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 10
      },
      _count: {
        select: {
          contracts: true
        }
      }
    }
  })

  if (!template) {
    notFound()
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/contract-templates">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Templates
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{template.name}</h1>
            <p className="text-gray-600 mt-1">
              Contract template details and usage
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {template.status === 'ACTIVE' && (
            <Link href={`/dashboard/contracts/new?templateId=${template.id}`}>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Contract
              </Button>
            </Link>
          )}
          <Link href={`/dashboard/contract-templates/${template.id}/edit`}>
            <Button variant="outline">
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
          </Link>
          <Button variant="outline">
            <Copy className="h-4 w-4 mr-2" />
            Duplicate
          </Button>
          <Button variant="outline" className="text-red-600 hover:text-red-700">
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      {/* Template Details */}
      <TemplateDetails template={template} />
    </div>
  )
}
