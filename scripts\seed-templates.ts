import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// Professional Email Templates
const emailTemplates = [
  {
    name: 'Welcome Email',
    subject: 'Welcome to {{company.name}} - Let\'s Get Started!',
    body: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Welcome to {{company.name}}</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #4F46E5; color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
        .button { display: inline-block; background: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Welcome to {{company.name}}!</h1>
            <p>We're excited to have you on board</p>
        </div>
        <div class="content">
            <h2>Hi {{customer.name}},</h2>
            <p>Thank you for choosing {{company.name}}! We're thrilled to welcome you to our community of satisfied customers.</p>
            
            <p>Here's what you can expect from us:</p>
            <ul>
                <li>Professional service and support</li>
                <li>Regular updates on your projects</li>
                <li>24/7 customer support</li>
                <li>Access to our client portal</li>
            </ul>
            
            <p>To get started, please click the button below to access your account:</p>
            <a href="#" class="button">Access Your Account</a>
            
            <p>If you have any questions, don't hesitate to reach out to us at {{company.email}} or call us at {{company.phone}}.</p>
            
            <p>Best regards,<br>
            {{user.name}}<br>
            {{company.name}} Team</p>
        </div>
        <div class="footer">
            <p>{{company.name}} | {{company.address}} | {{company.email}}</p>
        </div>
    </div>
</body>
</html>`,
    type: 'NOTIFICATION',
    status: 'ACTIVE'
  },
  {
    name: 'Invoice Reminder',
    subject: 'Payment Reminder - Invoice {{invoice.number}}',
    body: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Payment Reminder</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #F59E0B; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #fff; padding: 30px; border: 1px solid #ddd; border-radius: 0 0 8px 8px; }
        .invoice-details { background: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0; }
        .button { display: inline-block; background: #10B981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Payment Reminder</h1>
        </div>
        <div class="content">
            <h2>Dear {{customer.name}},</h2>
            <p>This is a friendly reminder that payment for the following invoice is now due:</p>
            
            <div class="invoice-details">
                <h3>Invoice Details</h3>
                <p><strong>Invoice Number:</strong> {{invoice.number}}</p>
                <p><strong>Invoice Date:</strong> {{invoice.date}}</p>
                <p><strong>Due Date:</strong> {{invoice.dueDate}}</p>
                <p><strong>Amount Due:</strong> {{invoice.total}}</p>
            </div>
            
            <p>Please process this payment at your earliest convenience to avoid any late fees.</p>
            
            <a href="#" class="button">Pay Now</a>
            
            <p>If you have already made this payment, please disregard this notice. If you have any questions about this invoice, please contact us at {{company.email}}.</p>
            
            <p>Thank you for your business!</p>
            
            <p>Best regards,<br>
            {{user.name}}<br>
            {{company.name}}</p>
        </div>
    </div>
</body>
</html>`,
    type: 'REMINDER',
    status: 'ACTIVE'
  },
  {
    name: 'Quotation Follow-up',
    subject: 'Following up on your quotation - {{quotation.number}}',
    body: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Quotation Follow-up</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #6366F1; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #fff; padding: 30px; border: 1px solid #ddd; border-radius: 0 0 8px 8px; }
        .quotation-summary { background: #f1f5f9; padding: 20px; border-radius: 6px; margin: 20px 0; }
        .button { display: inline-block; background: #6366F1; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Quotation Follow-up</h1>
        </div>
        <div class="content">
            <h2>Hello {{customer.name}},</h2>
            <p>I hope this email finds you well. I wanted to follow up on the quotation we sent you recently.</p>
            
            <div class="quotation-summary">
                <h3>Quotation Summary</h3>
                <p><strong>Quotation Number:</strong> {{quotation.number}}</p>
                <p><strong>Date:</strong> {{quotation.date}}</p>
                <p><strong>Valid Until:</strong> {{quotation.validUntil}}</p>
                <p><strong>Total Amount:</strong> {{quotation.total}}</p>
            </div>
            
            <p>Do you have any questions about our proposal? We'd be happy to discuss any aspects of the quotation or make adjustments to better meet your needs.</p>
            
            <p>Our team is standing by to help you move forward with this project. Please don't hesitate to reach out if you need any clarification or would like to schedule a call to discuss further.</p>
            
            <a href="#" class="button">Accept Quotation</a>
            
            <p>Looking forward to hearing from you soon!</p>
            
            <p>Best regards,<br>
            {{user.name}}<br>
            {{user.email}}<br>
            {{company.name}}</p>
        </div>
    </div>
</body>
</html>`,
    type: 'QUOTATION',
    status: 'ACTIVE'
  },
  {
    name: 'Marketing Newsletter',
    subject: 'Monthly Update from {{company.name}} - {{date}}',
    body: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Monthly Newsletter</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 40px 20px; text-align: center; }
        .content { padding: 30px 20px; }
        .section { margin-bottom: 30px; }
        .feature-box { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #667eea; }
        .button { display: inline-block; background: #667eea; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
        .footer { background: #f1f5f9; padding: 20px; text-align: center; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{company.name}} Monthly Update</h1>
            <p>Keeping you informed about our latest developments</p>
        </div>
        <div class="content">
            <div class="section">
                <h2>What's New This Month</h2>
                <p>We're excited to share some amazing updates and improvements we've made to better serve you.</p>
            </div>
            
            <div class="feature-box">
                <h3>🚀 New Features Released</h3>
                <p>We've launched several new features based on your feedback, including enhanced reporting capabilities and improved user interface.</p>
            </div>
            
            <div class="feature-box">
                <h3>📈 Performance Improvements</h3>
                <p>Our platform is now 40% faster with improved reliability and uptime of 99.9%.</p>
            </div>
            
            <div class="feature-box">
                <h3>🎓 New Training Resources</h3>
                <p>Check out our new video tutorials and documentation to get the most out of our platform.</p>
            </div>
            
            <div class="section">
                <h2>Customer Spotlight</h2>
                <p>This month we're featuring success stories from our amazing customers and how they're achieving great results with our platform.</p>
                <a href="#" class="button">Read Success Stories</a>
            </div>
            
            <div class="section">
                <h2>Upcoming Events</h2>
                <p>Join us for our monthly webinar series where we share tips, best practices, and answer your questions.</p>
                <a href="#" class="button">Register for Webinar</a>
            </div>
        </div>
        <div class="footer">
            <p>Thank you for being a valued customer!</p>
            <p>{{company.name}} | {{company.email}} | {{company.phone}}</p>
            <p><a href="#">Unsubscribe</a> | <a href="#">Update Preferences</a></p>
        </div>
    </div>
</body>
</html>`,
    type: 'MARKETING',
    status: 'ACTIVE'
  }
]

// Professional Quotation Templates
const quotationTemplates = [
  {
    name: 'Standard Business Quotation',
    description: 'Professional quotation template for general business services',
    category: 'Standard',
    content: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Quotation - {{quotation.number}}</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; color: #333; }
        .container { max-width: 800px; margin: 0 auto; }
        .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 40px; padding-bottom: 20px; border-bottom: 3px solid #4F46E5; }
        .company-info { flex: 1; }
        .company-logo { font-size: 28px; font-weight: bold; color: #4F46E5; margin-bottom: 10px; }
        .quotation-info { text-align: right; }
        .quotation-title { font-size: 24px; font-weight: bold; color: #4F46E5; margin-bottom: 10px; }
        .client-section { display: flex; justify-content: space-between; margin-bottom: 40px; }
        .client-info, .quotation-details { flex: 1; }
        .client-info { margin-right: 40px; }
        .section-title { font-size: 16px; font-weight: bold; color: #4F46E5; margin-bottom: 15px; text-transform: uppercase; }
        .items-table { width: 100%; border-collapse: collapse; margin: 30px 0; }
        .items-table th { background: #4F46E5; color: white; padding: 15px; text-align: left; }
        .items-table td { padding: 12px 15px; border-bottom: 1px solid #ddd; }
        .items-table tr:nth-child(even) { background: #f9f9f9; }
        .totals { text-align: right; margin-top: 20px; }
        .total-row { display: flex; justify-content: flex-end; margin: 8px 0; }
        .total-label { width: 150px; font-weight: bold; }
        .total-amount { width: 120px; text-align: right; }
        .grand-total { font-size: 18px; font-weight: bold; color: #4F46E5; border-top: 2px solid #4F46E5; padding-top: 10px; }
        .terms { margin-top: 40px; padding: 20px; background: #f8f9fa; border-radius: 8px; }
        .footer { margin-top: 40px; text-align: center; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="company-info">
                <div class="company-logo">{{company.name}}</div>
                <div>{{company.address}}</div>
                <div>{{company.email}} | {{company.phone}}</div>
            </div>
            <div class="quotation-info">
                <div class="quotation-title">QUOTATION</div>
                <div><strong>Number:</strong> {{quotation.number}}</div>
                <div><strong>Date:</strong> {{quotation.date}}</div>
                <div><strong>Valid Until:</strong> {{quotation.validUntil}}</div>
            </div>
        </div>

        <div class="client-section">
            <div class="client-info">
                <div class="section-title">Bill To</div>
                <div><strong>{{customer.name}}</strong></div>
                <div>{{customer.company}}</div>
                <div>{{customer.address}}</div>
                <div>{{customer.email}}</div>
                <div>{{customer.phone}}</div>
            </div>
            <div class="quotation-details">
                <div class="section-title">Quotation Details</div>
                <div><strong>Project:</strong> {{project.name}}</div>
                <div><strong>Prepared by:</strong> {{user.name}}</div>
                <div><strong>Email:</strong> {{user.email}}</div>
                <div><strong>Phone:</strong> {{user.phone}}</div>
            </div>
        </div>

        <table class="items-table">
            <thead>
                <tr>
                    <th>Description</th>
                    <th>Quantity</th>
                    <th>Unit Price</th>
                    <th>Total</th>
                </tr>
            </thead>
            <tbody>
                {{#each items}}
                <tr>
                    <td>{{description}}</td>
                    <td>{{quantity}}</td>
                    <td>{{unitPrice}}</td>
                    <td>{{total}}</td>
                </tr>
                {{/each}}
            </tbody>
        </table>

        <div class="totals">
            <div class="total-row">
                <div class="total-label">Subtotal:</div>
                <div class="total-amount">{{quotation.subtotal}}</div>
            </div>
            <div class="total-row">
                <div class="total-label">Tax ({{tax.rate}}%):</div>
                <div class="total-amount">{{quotation.tax}}</div>
            </div>
            <div class="total-row grand-total">
                <div class="total-label">Total:</div>
                <div class="total-amount">{{quotation.total}}</div>
            </div>
        </div>

        <div class="terms">
            <div class="section-title">Terms & Conditions</div>
            <ul>
                <li>This quotation is valid for {{quotation.validityDays}} days from the date of issue.</li>
                <li>Payment terms: {{payment.terms}}</li>
                <li>All prices are in {{currency}} and exclude applicable taxes unless stated otherwise.</li>
                <li>Work will commence upon receipt of signed agreement and initial payment.</li>
                <li>Any changes to the scope of work may result in additional charges.</li>
            </ul>
        </div>

        <div class="footer">
            <p>Thank you for considering {{company.name}} for your project needs.</p>
            <p>We look forward to working with you!</p>
        </div>
    </div>
</body>
</html>`,
    status: 'ACTIVE',
    variables: [
      { name: 'project.name', label: 'Project Name', type: 'TEXT', required: true },
      { name: 'tax.rate', label: 'Tax Rate', type: 'NUMBER', required: true },
      { name: 'payment.terms', label: 'Payment Terms', type: 'TEXT', required: true },
      { name: 'currency', label: 'Currency', type: 'TEXT', required: true },
      { name: 'quotation.validityDays', label: 'Validity Days', type: 'NUMBER', required: true }
    ]
  },
  {
    name: 'Service Quotation Template',
    description: 'Specialized template for service-based quotations',
    category: 'Service',
    content: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Service Quotation - {{quotation.number}}</title>
    <style>
        body { font-family: 'Arial', sans-serif; margin: 0; padding: 20px; color: #2d3748; background: #f7fafc; }
        .container { max-width: 800px; margin: 0 auto; background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 40px; }
        .header-grid { display: grid; grid-template-columns: 1fr auto; gap: 40px; align-items: center; }
        .company-info h1 { margin: 0 0 10px 0; font-size: 32px; font-weight: 300; }
        .quotation-badge { background: rgba(255,255,255,0.2); padding: 20px; border-radius: 8px; text-align: center; }
        .quotation-number { font-size: 24px; font-weight: bold; margin-bottom: 5px; }
        .content { padding: 40px; }
        .client-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 40px; margin-bottom: 40px; }
        .section-header { font-size: 14px; font-weight: bold; color: #667eea; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 15px; }
        .service-section { background: #f8f9fa; padding: 30px; border-radius: 8px; margin: 30px 0; }
        .service-item { background: white; padding: 20px; border-radius: 6px; margin: 15px 0; border-left: 4px solid #667eea; }
        .service-title { font-size: 18px; font-weight: bold; color: #2d3748; margin-bottom: 10px; }
        .service-description { color: #4a5568; line-height: 1.6; margin-bottom: 15px; }
        .service-details { display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 15px; }
        .detail-item { text-align: center; }
        .detail-label { font-size: 12px; color: #718096; text-transform: uppercase; }
        .detail-value { font-size: 16px; font-weight: bold; color: #2d3748; }
        .totals-card { background: #667eea; color: white; padding: 30px; border-radius: 8px; margin: 30px 0; }
        .total-grid { display: grid; grid-template-columns: 1fr auto; gap: 20px; align-items: center; }
        .total-breakdown { }
        .total-row { display: flex; justify-content: space-between; margin: 8px 0; }
        .grand-total { font-size: 28px; font-weight: bold; text-align: right; }
        .terms-section { background: #edf2f7; padding: 25px; border-radius: 8px; margin: 30px 0; }
        .signature-section { display: grid; grid-template-columns: 1fr 1fr; gap: 40px; margin: 40px 0; padding: 30px 0; border-top: 1px solid #e2e8f0; }
        .signature-box { text-align: center; }
        .signature-line { border-bottom: 2px solid #cbd5e0; margin: 20px 0 10px 0; height: 40px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-grid">
                <div class="company-info">
                    <h1>{{company.name}}</h1>
                    <p>{{company.tagline}}</p>
                    <p>{{company.address}}</p>
                    <p>{{company.email}} | {{company.phone}}</p>
                </div>
                <div class="quotation-badge">
                    <div class="quotation-number">{{quotation.number}}</div>
                    <div>Service Quotation</div>
                </div>
            </div>
        </div>

        <div class="content">
            <div class="client-grid">
                <div>
                    <div class="section-header">Client Information</div>
                    <div><strong>{{customer.name}}</strong></div>
                    <div>{{customer.company}}</div>
                    <div>{{customer.address}}</div>
                    <div>{{customer.email}}</div>
                    <div>{{customer.phone}}</div>
                </div>
                <div>
                    <div class="section-header">Project Details</div>
                    <div><strong>Date:</strong> {{quotation.date}}</div>
                    <div><strong>Valid Until:</strong> {{quotation.validUntil}}</div>
                    <div><strong>Project:</strong> {{project.name}}</div>
                    <div><strong>Timeline:</strong> {{project.timeline}}</div>
                    <div><strong>Contact:</strong> {{user.name}}</div>
                </div>
            </div>

            <div class="service-section">
                <div class="section-header">Proposed Services</div>

                {{#each services}}
                <div class="service-item">
                    <div class="service-title">{{title}}</div>
                    <div class="service-description">{{description}}</div>
                    <div class="service-details">
                        <div class="detail-item">
                            <div class="detail-label">Duration</div>
                            <div class="detail-value">{{duration}}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Rate</div>
                            <div class="detail-value">{{rate}}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Total</div>
                            <div class="detail-value">{{total}}</div>
                        </div>
                    </div>
                </div>
                {{/each}}
            </div>

            <div class="totals-card">
                <div class="total-grid">
                    <div class="total-breakdown">
                        <div class="total-row">
                            <span>Subtotal:</span>
                            <span>{{quotation.subtotal}}</span>
                        </div>
                        <div class="total-row">
                            <span>Tax ({{tax.rate}}%):</span>
                            <span>{{quotation.tax}}</span>
                        </div>
                    </div>
                    <div class="grand-total">{{quotation.total}}</div>
                </div>
            </div>

            <div class="terms-section">
                <div class="section-header">Terms & Conditions</div>
                <ul style="margin: 0; padding-left: 20px;">
                    <li>This quotation is valid for {{quotation.validityDays}} days from the date of issue.</li>
                    <li>50% deposit required to commence work, balance due upon completion.</li>
                    <li>All services include {{included.support}} of post-completion support.</li>
                    <li>Additional revisions beyond the agreed scope will be charged at {{hourly.rate}}/hour.</li>
                    <li>Timeline may vary based on client feedback and approval cycles.</li>
                </ul>
            </div>

            <div class="signature-section">
                <div class="signature-box">
                    <div class="signature-line"></div>
                    <div><strong>{{company.name}}</strong></div>
                    <div>{{user.name}}, {{user.title}}</div>
                    <div>Date: _______________</div>
                </div>
                <div class="signature-box">
                    <div class="signature-line"></div>
                    <div><strong>Client Approval</strong></div>
                    <div>{{customer.name}}</div>
                    <div>Date: _______________</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>`,
    status: 'ACTIVE',
    variables: [
      { name: 'company.tagline', label: 'Company Tagline', type: 'TEXT', required: false },
      { name: 'project.timeline', label: 'Project Timeline', type: 'TEXT', required: true },
      { name: 'user.title', label: 'User Title', type: 'TEXT', required: true },
      { name: 'included.support', label: 'Included Support', type: 'TEXT', required: true },
      { name: 'hourly.rate', label: 'Hourly Rate', type: 'TEXT', required: true }
    ]
  }
]

// Professional Invoice Templates
const invoiceTemplates = [
  {
    name: 'Modern Invoice Template',
    description: 'Clean and professional invoice template with modern design',
    category: 'Standard',
    content: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Invoice - {{invoice.number}}</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; color: #333; }
        .container { max-width: 800px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #10B981 0%, #**********%); color: white; padding: 30px; border-radius: 12px 12px 0 0; }
        .header-content { display: flex; justify-content: space-between; align-items: center; }
        .company-info h1 { margin: 0; font-size: 28px; }
        .invoice-info { text-align: right; }
        .invoice-title { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
        .content { background: white; padding: 30px; border: 1px solid #e5e7eb; border-radius: 0 0 12px 12px; }
        .billing-section { display: flex; justify-content: space-between; margin-bottom: 40px; }
        .billing-info { flex: 1; margin-right: 40px; }
        .section-title { font-size: 16px; font-weight: bold; color: #10B981; margin-bottom: 15px; text-transform: uppercase; letter-spacing: 1px; }
        .items-table { width: 100%; border-collapse: collapse; margin: 30px 0; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        .items-table th { background: #10B981; color: white; padding: 15px; text-align: left; font-weight: 600; }
        .items-table td { padding: 15px; border-bottom: 1px solid #e5e7eb; }
        .items-table tr:hover { background: #f9fafb; }
        .totals-section { background: #f9fafb; padding: 25px; border-radius: 8px; margin-top: 30px; }
        .total-row { display: flex; justify-content: space-between; margin: 10px 0; }
        .total-label { font-weight: 600; }
        .grand-total { font-size: 20px; font-weight: bold; color: #10B981; border-top: 2px solid #10B981; padding-top: 15px; margin-top: 15px; }
        .payment-info { background: #eff6ff; padding: 20px; border-radius: 8px; margin-top: 30px; border-left: 4px solid #3b82f6; }
        .footer { margin-top: 40px; text-align: center; color: #6b7280; padding: 20px; border-top: 1px solid #e5e7eb; }
        .status-badge { display: inline-block; padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: bold; text-transform: uppercase; }
        .status-paid { background: #d1fae5; color: #065f46; }
        .status-pending { background: #fef3c7; color: #92400e; }
        .status-overdue { background: #fee2e2; color: #991b1b; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <div class="company-info">
                    <h1>{{company.name}}</h1>
                    <p>{{company.address}}</p>
                    <p>{{company.email}} | {{company.phone}}</p>
                </div>
                <div class="invoice-info">
                    <div class="invoice-title">INVOICE</div>
                    <div><strong>{{invoice.number}}</strong></div>
                    <div class="status-badge status-{{invoice.status}}">{{invoice.status}}</div>
                </div>
            </div>
        </div>

        <div class="content">
            <div class="billing-section">
                <div class="billing-info">
                    <div class="section-title">Bill To</div>
                    <div><strong>{{customer.name}}</strong></div>
                    <div>{{customer.company}}</div>
                    <div>{{customer.address}}</div>
                    <div>{{customer.email}}</div>
                </div>
                <div class="billing-info">
                    <div class="section-title">Invoice Details</div>
                    <div><strong>Invoice Date:</strong> {{invoice.date}}</div>
                    <div><strong>Due Date:</strong> {{invoice.dueDate}}</div>
                    <div><strong>Payment Terms:</strong> {{invoice.paymentTerms}}</div>
                    <div><strong>Reference:</strong> {{invoice.reference}}</div>
                </div>
            </div>

            <table class="items-table">
                <thead>
                    <tr>
                        <th>Description</th>
                        <th>Qty</th>
                        <th>Rate</th>
                        <th>Amount</th>
                    </tr>
                </thead>
                <tbody>
                    {{#each items}}
                    <tr>
                        <td>
                            <strong>{{description}}</strong>
                            {{#if details}}<br><small>{{details}}</small>{{/if}}
                        </td>
                        <td>{{quantity}}</td>
                        <td>{{rate}}</td>
                        <td><strong>{{amount}}</strong></td>
                    </tr>
                    {{/each}}
                </tbody>
            </table>

            <div class="totals-section">
                <div class="total-row">
                    <div class="total-label">Subtotal:</div>
                    <div>{{invoice.subtotal}}</div>
                </div>
                {{#if invoice.discount}}
                <div class="total-row">
                    <div class="total-label">Discount:</div>
                    <div>-{{invoice.discount}}</div>
                </div>
                {{/if}}
                <div class="total-row">
                    <div class="total-label">Tax ({{tax.rate}}%):</div>
                    <div>{{invoice.tax}}</div>
                </div>
                <div class="total-row grand-total">
                    <div class="total-label">Total Amount:</div>
                    <div>{{invoice.total}}</div>
                </div>
            </div>

            <div class="payment-info">
                <div class="section-title">Payment Information</div>
                <p><strong>Payment Method:</strong> {{payment.method}}</p>
                {{#if payment.bankDetails}}
                <p><strong>Bank Details:</strong><br>
                {{payment.bankDetails}}</p>
                {{/if}}
                <p><strong>Payment Terms:</strong> {{payment.terms}}</p>
            </div>

            <div class="footer">
                <p>Thank you for your business!</p>
                <p>If you have any questions about this invoice, please contact us at {{company.email}}</p>
            </div>
        </div>
    </div>
</body>
</html>`,
    status: 'ACTIVE',
    variables: [
      { name: 'invoice.reference', label: 'Reference Number', type: 'TEXT', required: false },
      { name: 'invoice.paymentTerms', label: 'Payment Terms', type: 'TEXT', required: true },
      { name: 'payment.method', label: 'Payment Method', type: 'TEXT', required: true },
      { name: 'payment.bankDetails', label: 'Bank Details', type: 'TEXT', required: false },
      { name: 'payment.terms', label: 'Payment Terms Description', type: 'TEXT', required: true }
    ]
  },
  {
    name: 'Minimalist Invoice Template',
    description: 'Clean and simple invoice template with minimal design',
    category: 'Minimalist',
    content: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Invoice {{invoice.number}}</title>
    <style>
        body { font-family: 'Helvetica Neue', Arial, sans-serif; margin: 0; padding: 40px; color: #333; background: #fff; }
        .container { max-width: 700px; margin: 0 auto; }
        .header { border-bottom: 3px solid #000; padding-bottom: 30px; margin-bottom: 40px; }
        .header-grid { display: flex; justify-content: space-between; align-items: flex-start; }
        .company-name { font-size: 36px; font-weight: 300; margin: 0; }
        .invoice-title { font-size: 48px; font-weight: 100; margin: 0; text-align: right; }
        .invoice-number { font-size: 18px; margin-top: 10px; text-align: right; }
        .details-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 60px; margin-bottom: 50px; }
        .section-title { font-size: 12px; font-weight: bold; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 15px; }
        .items-table { width: 100%; border-collapse: collapse; margin: 40px 0; }
        .items-table th { border-bottom: 2px solid #000; padding: 15px 0; text-align: left; font-weight: 600; }
        .items-table td { padding: 15px 0; border-bottom: 1px solid #eee; }
        .totals { text-align: right; margin-top: 40px; }
        .total-line { display: flex; justify-content: flex-end; margin: 10px 0; }
        .total-label { width: 150px; }
        .total-amount { width: 100px; font-weight: 600; }
        .grand-total { font-size: 24px; font-weight: bold; border-top: 2px solid #000; padding-top: 15px; margin-top: 20px; }
        .footer { margin-top: 60px; padding-top: 30px; border-top: 1px solid #eee; text-align: center; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-grid">
                <div>
                    <h1 class="company-name">{{company.name}}</h1>
                    <div>{{company.address}}</div>
                    <div>{{company.email}}</div>
                </div>
                <div>
                    <h1 class="invoice-title">INVOICE</h1>
                    <div class="invoice-number">{{invoice.number}}</div>
                </div>
            </div>
        </div>

        <div class="details-grid">
            <div>
                <div class="section-title">Bill To</div>
                <div><strong>{{customer.name}}</strong></div>
                <div>{{customer.company}}</div>
                <div>{{customer.address}}</div>
            </div>
            <div>
                <div class="section-title">Invoice Details</div>
                <div>Date: {{invoice.date}}</div>
                <div>Due: {{invoice.dueDate}}</div>
                <div>Terms: {{invoice.terms}}</div>
            </div>
        </div>

        <table class="items-table">
            <thead>
                <tr>
                    <th>Description</th>
                    <th style="text-align: right;">Qty</th>
                    <th style="text-align: right;">Rate</th>
                    <th style="text-align: right;">Amount</th>
                </tr>
            </thead>
            <tbody>
                {{#each items}}
                <tr>
                    <td>{{description}}</td>
                    <td style="text-align: right;">{{quantity}}</td>
                    <td style="text-align: right;">{{rate}}</td>
                    <td style="text-align: right;">{{amount}}</td>
                </tr>
                {{/each}}
            </tbody>
        </table>

        <div class="totals">
            <div class="total-line">
                <div class="total-label">Subtotal</div>
                <div class="total-amount">{{invoice.subtotal}}</div>
            </div>
            <div class="total-line">
                <div class="total-label">Tax</div>
                <div class="total-amount">{{invoice.tax}}</div>
            </div>
            <div class="total-line grand-total">
                <div class="total-label">Total</div>
                <div class="total-amount">{{invoice.total}}</div>
            </div>
        </div>

        <div class="footer">
            <p>Thank you for your business</p>
        </div>
    </div>
</body>
</html>`,
    status: 'ACTIVE',
    variables: [
      { name: 'invoice.terms', label: 'Invoice Terms', type: 'TEXT', required: true }
    ]
  }
]

// Professional Receipt Templates
const receiptTemplates = [
  {
    name: 'Standard Receipt Template',
    description: 'Professional receipt template for payments',
    category: 'Standard',
    content: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Receipt - {{receipt.number}}</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; color: #333; }
        .container { max-width: 600px; margin: 0 auto; border: 2px solid #8B5CF6; border-radius: 12px; overflow: hidden; }
        .header { background: #8B5CF6; color: white; padding: 25px; text-align: center; }
        .header h1 { margin: 0; font-size: 28px; }
        .receipt-number { font-size: 18px; margin-top: 10px; opacity: 0.9; }
        .content { padding: 30px; }
        .company-info { text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 1px solid #e5e7eb; }
        .payment-details { background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .detail-row { display: flex; justify-content: space-between; margin: 10px 0; }
        .detail-label { font-weight: 600; color: #4b5563; }
        .detail-value { font-weight: 500; }
        .amount-section { background: #8B5CF6; color: white; padding: 20px; margin: 20px -30px; text-align: center; }
        .amount-received { font-size: 24px; font-weight: bold; }
        .payment-method { background: #eff6ff; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #3b82f6; }
        .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; color: #6b7280; }
        .thank-you { font-size: 18px; font-weight: 600; color: #8B5CF6; margin-bottom: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>PAYMENT RECEIPT</h1>
            <div class="receipt-number">Receipt #{{receipt.number}}</div>
        </div>

        <div class="content">
            <div class="company-info">
                <h2>{{company.name}}</h2>
                <p>{{company.address}}</p>
                <p>{{company.email}} | {{company.phone}}</p>
            </div>

            <div class="payment-details">
                <h3 style="margin-top: 0; color: #8B5CF6;">Payment Details</h3>
                <div class="detail-row">
                    <div class="detail-label">Received From:</div>
                    <div class="detail-value">{{customer.name}}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Date:</div>
                    <div class="detail-value">{{receipt.date}}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Invoice Number:</div>
                    <div class="detail-value">{{invoice.number}}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Payment For:</div>
                    <div class="detail-value">{{payment.description}}</div>
                </div>
            </div>

            <div class="amount-section">
                <div>Amount Received</div>
                <div class="amount-received">{{receipt.amount}}</div>
            </div>

            <div class="payment-method">
                <h4 style="margin-top: 0; color: #3b82f6;">Payment Method</h4>
                <div class="detail-row">
                    <div class="detail-label">Method:</div>
                    <div class="detail-value">{{payment.method}}</div>
                </div>
                {{#if payment.reference}}
                <div class="detail-row">
                    <div class="detail-label">Reference:</div>
                    <div class="detail-value">{{payment.reference}}</div>
                </div>
                {{/if}}
                {{#if payment.transactionId}}
                <div class="detail-row">
                    <div class="detail-label">Transaction ID:</div>
                    <div class="detail-value">{{payment.transactionId}}</div>
                </div>
                {{/if}}
            </div>

            <div class="footer">
                <div class="thank-you">Thank You for Your Payment!</div>
                <p>This receipt serves as proof of payment. Please keep it for your records.</p>
                <p>If you have any questions, please contact us at {{company.email}}</p>
            </div>
        </div>
    </div>
</body>
</html>`,
    status: 'ACTIVE',
    variables: [
      { name: 'payment.description', label: 'Payment Description', type: 'TEXT', required: true },
      { name: 'payment.reference', label: 'Payment Reference', type: 'TEXT', required: false },
      { name: 'payment.transactionId', label: 'Transaction ID', type: 'TEXT', required: false }
    ]
  }
]

async function seedTemplates() {
  try {
    console.log('🌱 Starting template seeding...')

    // Get the first company for seeding
    const company = await prisma.company.findFirst({
      include: {
        users: true
      }
    })

    if (!company || !company.users.length) {
      console.log('❌ No company or users found. Please ensure you have at least one company and user.')
      return
    }

    const user = company.users[0]
    console.log(`📍 Seeding templates for company: ${company.name}`)

    // Seed Email Templates
    console.log('📧 Seeding email templates...')
    for (const template of emailTemplates) {
      await prisma.emailTemplate.upsert({
        where: {
          name_companyId: {
            name: template.name,
            companyId: company.id
          }
        },
        update: {},
        create: {
          ...template,
          companyId: company.id,
          createdById: user.id
        }
      })
      console.log(`  ✅ Created email template: ${template.name}`)
    }

    // Seed Quotation Templates
    console.log('📄 Seeding quotation templates...')
    for (const template of quotationTemplates) {
      await prisma.quotationTemplate.upsert({
        where: {
          name_companyId: {
            name: template.name,
            companyId: company.id
          }
        },
        update: {},
        create: {
          ...template,
          companyId: company.id,
          createdById: user.id
        }
      })
      console.log(`  ✅ Created quotation template: ${template.name}`)
    }

    // Seed Invoice Templates
    console.log('🧾 Seeding invoice templates...')
    for (const template of invoiceTemplates) {
      await prisma.invoiceTemplate.upsert({
        where: {
          name_companyId: {
            name: template.name,
            companyId: company.id
          }
        },
        update: {},
        create: {
          ...template,
          companyId: company.id,
          createdById: user.id
        }
      })
      console.log(`  ✅ Created invoice template: ${template.name}`)
    }

    // Seed Receipt Templates
    console.log('🧾 Seeding receipt templates...')
    for (const template of receiptTemplates) {
      await prisma.receiptTemplate.upsert({
        where: {
          name_companyId: {
            name: template.name,
            companyId: company.id
          }
        },
        update: {},
        create: {
          ...template,
          companyId: company.id,
          createdById: user.id
        }
      })
      console.log(`  ✅ Created receipt template: ${template.name}`)
    }

    console.log('🎉 Template seeding completed successfully!')
    console.log(`📊 Summary:`)
    console.log(`  📧 Email templates: ${emailTemplates.length}`)
    console.log(`  📄 Quotation templates: ${quotationTemplates.length}`)
    console.log(`  🧾 Invoice templates: ${invoiceTemplates.length}`)
    console.log(`  🧾 Receipt templates: ${receiptTemplates.length}`)
    
  } catch (error) {
    console.error('❌ Error seeding templates:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the seeding
seedTemplates()
