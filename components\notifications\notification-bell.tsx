'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { formatTimeAgo } from '@/lib/utils'
import { 
  Bell, 
  X,
  CheckCircle,
  AlertTriangle,
  DollarSign,
  FileText,
  Users,
  Calendar,
  Mail,
  MessageSquare,
  Settings,
  Eye
} from 'lucide-react'

interface NotificationBellProps {
  userId: string
}

export function NotificationBell({ userId }: NotificationBellProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [notifications, setNotifications] = useState<any[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    fetchNotifications()
    
    // Set up polling for new notifications
    const interval = setInterval(fetchNotifications, 30000) // Poll every 30 seconds
    
    return () => clearInterval(interval)
  }, [])

  const fetchNotifications = async () => {
    try {
      const response = await fetch('/api/notifications?limit=10&status=UNREAD')
      if (response.ok) {
        const data = await response.json()
        setNotifications(data.notifications || [])
        setUnreadCount(data.notifications?.length || 0)
      }
    } catch (error) {
      console.error('Error fetching notifications:', error)
    }
  }

  const markAsRead = async (notificationId: string) => {
    try {
      const response = await fetch(`/api/notifications/${notificationId}/read`, {
        method: 'POST',
      })
      
      if (response.ok) {
        // Update local state
        setNotifications(prev => 
          prev.filter(n => n.id !== notificationId)
        )
        setUnreadCount(prev => Math.max(0, prev - 1))
      }
    } catch (error) {
      console.error('Error marking notification as read:', error)
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'INVOICE_OVERDUE':
      case 'PAYMENT_RECEIVED':
        return DollarSign
      case 'QUOTATION_ACCEPTED':
      case 'QUOTATION_EXPIRED':
      case 'CONTRACT_SIGNED':
        return FileText
      case 'CUSTOMER_CREATED':
      case 'LEAD_CONVERTED':
        return Users
      case 'REMINDER':
      case 'DEADLINE':
        return Calendar
      case 'EMAIL':
        return Mail
      case 'SYSTEM':
        return Bell
      default:
        return MessageSquare
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'INVOICE_OVERDUE':
        return 'text-red-600'
      case 'PAYMENT_RECEIVED':
        return 'text-green-600'
      case 'QUOTATION_ACCEPTED':
      case 'CONTRACT_SIGNED':
        return 'text-blue-600'
      case 'QUOTATION_EXPIRED':
        return 'text-orange-600'
      case 'CUSTOMER_CREATED':
      case 'LEAD_CONVERTED':
        return 'text-purple-600'
      case 'REMINDER':
      case 'DEADLINE':
        return 'text-yellow-600'
      case 'EMAIL':
        return 'text-indigo-600'
      case 'SYSTEM':
        return 'text-gray-600'
      default:
        return 'text-gray-600'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'HIGH':
        return 'border-red-500'
      case 'MEDIUM':
        return 'border-yellow-500'
      case 'LOW':
        return 'border-gray-300'
      default:
        return 'border-gray-300'
    }
  }

  return (
    <div className="relative">
      {/* Bell Button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        className="relative"
      >
        <Bell className="h-5 w-5" />
        {unreadCount > 0 && (
          <Badge 
            variant="destructive" 
            className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
          >
            {unreadCount > 99 ? '99+' : unreadCount}
          </Badge>
        )}
      </Button>

      {/* Dropdown */}
      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-40" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown Content */}
          <Card className="absolute right-0 top-full mt-2 w-96 z-50 shadow-lg border">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">Notifications</CardTitle>
                <div className="flex items-center space-x-2">
                  <Link href="/dashboard/notifications">
                    <Button variant="ghost" >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </Link>
                  <Link href="/dashboard/notifications/settings">
                    <Button variant="ghost" >
                      <Settings className="h-4 w-4" />
                    </Button>
                  </Link>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => setIsOpen(false)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              {unreadCount > 0 && (
                <p className="text-sm text-gray-600">
                  You have {unreadCount} unread notification{unreadCount !== 1 ? 's' : ''}
                </p>
              )}
            </CardHeader>
            
            <CardContent className="p-0">
              {notifications.length > 0 ? (
                <div className="max-h-96 overflow-y-auto">
                  {notifications.map((notification) => {
                    const TypeIcon = getTypeIcon(notification.type)
                    
                    return (
                      <div 
                        key={notification.id}
                        className={`p-4 border-b border-gray-100 hover:bg-gray-50 transition-colors ${
                          notification.priority === 'HIGH' ? 'border-l-4 border-l-red-500' : ''
                        }`}
                      >
                        <div className="flex items-start space-x-3">
                          <TypeIcon className={`h-5 w-5 mt-0.5 ${getTypeColor(notification.type)}`} />
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between mb-1">
                              <h4 className="font-medium text-gray-900 text-sm truncate">
                                {notification.title}
                              </h4>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => markAsRead(notification.id)}
                                className="text-green-600 hover:text-green-700 p-1"
                              >
                                <CheckCircle className="h-4 w-4" />
                              </Button>
                            </div>
                            <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                              {notification.message}
                            </p>
                            <div className="flex items-center justify-between">
                              <span className="text-xs text-gray-500">
                                {formatTimeAgo(notification.createdAt)}
                              </span>
                              {notification.priority === 'HIGH' && (
                                <Badge variant="destructive" className="text-xs">
                                  High Priority
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              ) : (
                <div className="p-8 text-center">
                  <Bell className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <h3 className="font-medium text-gray-900 mb-2">All caught up!</h3>
                  <p className="text-sm text-gray-500">
                    No new notifications at the moment.
                  </p>
                </div>
              )}
              
              {notifications.length > 0 && (
                <div className="p-4 border-t bg-gray-50">
                  <Link href="/dashboard/notifications">
                    <Button variant="outline" className="w-full" >
                      View All Notifications
                    </Button>
                  </Link>
                </div>
              )}
            </CardContent>
          </Card>
        </>
      )}
    </div>
  )
}
