'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Lightbulb, 
  Users,
  FileText,
  BarChart3,
  CreditCard,
  Code,
  HelpCircle,
  Mail,
  Bell,
  Settings,
  Crown,
  Star,
  Award,
  ArrowRight,
  Lock,
  Zap
} from 'lucide-react'
import Link from 'next/link'

interface FeatureDiscoveryProps {
  plan: string
  isOnboardingCompleted: boolean
}

export function FeatureDiscovery({ plan, isOnboardingCompleted }: FeatureDiscoveryProps) {
  const features = [
    {
      id: 'customers',
      title: 'Customer Management',
      description: 'Organize and track all your customer relationships',
      icon: Users,
      available: true,
      link: '/dashboard/customers',
      category: 'Core'
    },
    {
      id: 'documents',
      title: 'Document Generation',
      description: 'Create professional quotations, invoices, and contracts',
      icon: FileText,
      available: true,
      link: '/dashboard/quotations',
      category: 'Core'
    },
    {
      id: 'analytics',
      title: 'Business Analytics',
      description: 'Track performance with detailed reports and insights',
      icon: BarChart3,
      available: true,
      link: '/dashboard/analytics',
      category: 'Core'
    },
    {
      id: 'billing',
      title: 'Subscription & Billing',
      description: 'Manage your subscription and payment methods',
      icon: CreditCard,
      available: true,
      link: '/dashboard/billing',
      category: 'Core'
    },
    {
      id: 'notifications',
      title: 'Smart Notifications',
      description: 'Stay updated with real-time alerts and reminders',
      icon: Bell,
      available: true,
      link: '/dashboard/notifications',
      category: 'Core'
    },
    {
      id: 'email',
      title: 'Email Integration',
      description: 'Send automated emails and track engagement',
      icon: Mail,
      available: ['PROFESSIONAL', 'ENTERPRISE'].includes(plan),
      link: '/dashboard/emails',
      category: 'Professional',
      requiredPlan: 'PROFESSIONAL'
    },
    {
      id: 'api',
      title: 'API & Integrations',
      description: 'Connect with external tools and build custom integrations',
      icon: Code,
      available: ['PROFESSIONAL', 'ENTERPRISE'].includes(plan),
      link: '/dashboard/api',
      category: 'Professional',
      requiredPlan: 'PROFESSIONAL'
    },
    {
      id: 'support',
      title: 'Priority Support',
      description: 'Get faster response times and dedicated assistance',
      icon: HelpCircle,
      available: ['PROFESSIONAL', 'ENTERPRISE'].includes(plan),
      link: '/dashboard/support',
      category: 'Professional',
      requiredPlan: 'PROFESSIONAL'
    },
    {
      id: 'advanced-analytics',
      title: 'Advanced Analytics',
      description: 'Deep insights with custom reports and forecasting',
      icon: BarChart3,
      available: plan === 'ENTERPRISE',
      link: '/dashboard/analytics/advanced',
      category: 'Enterprise',
      requiredPlan: 'ENTERPRISE'
    },
    {
      id: 'white-label',
      title: 'White Label Branding',
      description: 'Customize the platform with your own branding',
      icon: Settings,
      available: plan === 'ENTERPRISE',
      link: '/dashboard/settings/branding',
      category: 'Enterprise',
      requiredPlan: 'ENTERPRISE'
    }
  ]

  const getPlanIcon = (plan: string) => {
    switch (plan) {
      case 'ENTERPRISE':
        return Crown
      case 'PROFESSIONAL':
        return Star
      case 'BASIC':
        return Award
      default:
        return Award
    }
  }

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'ENTERPRISE':
        return 'text-purple-600 bg-purple-100 border-purple-200'
      case 'PROFESSIONAL':
        return 'text-blue-600 bg-blue-100 border-blue-200'
      case 'BASIC':
        return 'text-green-600 bg-green-100 border-green-200'
      default:
        return 'text-gray-600 bg-gray-100 border-gray-200'
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Core':
        return 'text-green-600 border-green-200'
      case 'Professional':
        return 'text-blue-600 border-blue-200'
      case 'Enterprise':
        return 'text-purple-600 border-purple-200'
      default:
        return 'text-gray-600 border-gray-200'
    }
  }

  const availableFeatures = features.filter(feature => feature.available)
  const lockedFeatures = features.filter(feature => !feature.available)

  const PlanIcon = getPlanIcon(plan)

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Lightbulb className="h-5 w-5" />
          <span>Feature Discovery</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Current Plan */}
          <div className={`p-4 border rounded-lg ${getPlanColor(plan)}`}>
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-lg ${getPlanColor(plan).split(' ').slice(1).join(' ')}`}>
                <PlanIcon className={`h-5 w-5 ${getPlanColor(plan).split(' ')[0]}`} />
              </div>
              <div>
                <div className="font-medium text-gray-900">
                  {plan} Plan
                </div>
                <div className="text-sm text-gray-600">
                  {availableFeatures.length} features available
                </div>
              </div>
            </div>
          </div>

          {/* Available Features */}
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Available Features</h4>
            <div className="space-y-3">
              {availableFeatures.slice(0, 5).map((feature) => (
                <div key={feature.id} className="group">
                  <Link href={feature.link}>
                    <div className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                      <div className="p-2 bg-gray-100 rounded-lg group-hover:bg-blue-100 transition-colors">
                        <feature.icon className="h-4 w-4 text-gray-600 group-hover:text-blue-600 transition-colors" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <div className="font-medium text-gray-900 text-sm">
                            {feature.title}
                          </div>
                          <Badge className={getCategoryColor(feature.category)} variant="outline" >
                            {feature.category}
                          </Badge>
                        </div>
                        <div className="text-xs text-gray-600 mt-1">
                          {feature.description}
                        </div>
                      </div>
                      <ArrowRight className="h-4 w-4 text-gray-400 group-hover:text-blue-600 transition-colors" />
                    </div>
                  </Link>
                </div>
              ))}
            </div>

            {availableFeatures.length > 5 && (
              <div className="mt-3 text-center">
                <Button variant="ghost" >
                  View All Features ({availableFeatures.length})
                </Button>
              </div>
            )}
          </div>

          {/* Locked Features */}
          {lockedFeatures.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Upgrade to Unlock</h4>
              <div className="space-y-3">
                {lockedFeatures.slice(0, 3).map((feature) => (
                  <div key={feature.id} className="flex items-center space-x-3 p-3 border rounded-lg bg-gray-50">
                    <div className="p-2 bg-gray-200 rounded-lg">
                      <Lock className="h-4 w-4 text-gray-500" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <div className="font-medium text-gray-700 text-sm">
                          {feature.title}
                        </div>
                        <Badge variant="outline" >
                          {feature.requiredPlan}
                        </Badge>
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        {feature.description}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-4">
                <Link href="/dashboard/billing">
                  <Button className="w-full" >
                    <Zap className="h-4 w-4 mr-2" />
                    Upgrade Plan
                  </Button>
                </Link>
              </div>
            </div>
          )}

          {/* Feature Categories */}
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Feature Categories</h4>
            <div className="grid grid-cols-1 gap-3">
              <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center space-x-2 mb-1">
                  <Award className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium text-green-900">Core Features</span>
                </div>
                <div className="text-xs text-green-700">
                  Essential business management tools
                </div>
              </div>

              <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center space-x-2 mb-1">
                  <Star className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-900">Professional Features</span>
                </div>
                <div className="text-xs text-blue-700">
                  Advanced tools for growing businesses
                </div>
              </div>

              <div className="p-3 bg-purple-50 border border-purple-200 rounded-lg">
                <div className="flex items-center space-x-2 mb-1">
                  <Crown className="h-4 w-4 text-purple-600" />
                  <span className="text-sm font-medium text-purple-900">Enterprise Features</span>
                </div>
                <div className="text-xs text-purple-700">
                  Premium features for large organizations
                </div>
              </div>
            </div>
          </div>

          {/* Quick Tips */}
          {isOnboardingCompleted && (
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-start space-x-2">
                <Lightbulb className="h-4 w-4 text-yellow-600 mt-0.5" />
                <div>
                  <div className="text-sm font-medium text-yellow-900">
                    Pro Tip
                  </div>
                  <div className="text-xs text-yellow-700 mt-1">
                    Explore the analytics dashboard to track your business performance and identify growth opportunities.
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
