'use client'

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Server, 
  Zap, 
  Activity,
  Globe,
  Database,
  Cpu,
  HardDrive,
  Wifi,
  CheckCircle,
  AlertTriangle,
  XCircle
} from 'lucide-react'

interface SystemHealthProps {
  metrics: {
    uptime: string
    responseTime: string
    errorRate: string
    activeConnections: number
  }
}

export function SystemHealth({ metrics }: SystemHealthProps) {
  // Mock additional system metrics for demonstration
  const systemMetrics = [
    {
      name: 'API Response Time',
      value: metrics.responseTime,
      status: 'healthy',
      icon: Zap,
      description: 'Average response time'
    },
    {
      name: 'System Uptime',
      value: metrics.uptime,
      status: 'healthy',
      icon: Server,
      description: 'System availability'
    },
    {
      name: 'Error Rate',
      value: metrics.errorRate,
      status: 'healthy',
      icon: Activity,
      description: 'Error percentage'
    },
    {
      name: 'Active Connections',
      value: metrics.activeConnections.toString(),
      status: 'healthy',
      icon: Globe,
      description: 'Current connections'
    }
  ]

  const detailedMetrics = [
    {
      category: 'Database',
      icon: Database,
      metrics: [
        { name: 'Connection Pool', value: '85%', status: 'healthy' },
        { name: 'Query Time', value: '45ms', status: 'healthy' },
        { name: 'Active Queries', value: '23', status: 'healthy' }
      ]
    },
    {
      category: 'Server',
      icon: Cpu,
      metrics: [
        { name: 'CPU Usage', value: '42%', status: 'healthy' },
        { name: 'Memory Usage', value: '68%', status: 'warning' },
        { name: 'Load Average', value: '1.2', status: 'healthy' }
      ]
    },
    {
      category: 'Storage',
      icon: HardDrive,
      metrics: [
        { name: 'Disk Usage', value: '34%', status: 'healthy' },
        { name: 'I/O Operations', value: '156/s', status: 'healthy' },
        { name: 'Free Space', value: '2.1TB', status: 'healthy' }
      ]
    },
    {
      category: 'Network',
      icon: Wifi,
      metrics: [
        { name: 'Bandwidth Usage', value: '23%', status: 'healthy' },
        { name: 'Latency', value: '12ms', status: 'healthy' },
        { name: 'Packet Loss', value: '0.01%', status: 'healthy' }
      ]
    }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return CheckCircle
      case 'warning':
        return AlertTriangle
      case 'critical':
        return XCircle
      default:
        return Activity
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'text-green-600 border-green-200'
      case 'warning':
        return 'text-yellow-600 border-yellow-200'
      case 'critical':
        return 'text-red-600 border-red-200'
      default:
        return 'text-gray-600 border-gray-200'
    }
  }

  const getStatusBgColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'bg-green-100'
      case 'warning':
        return 'bg-yellow-100'
      case 'critical':
        return 'bg-red-100'
      default:
        return 'bg-gray-100'
    }
  }

  return (
    <div className="space-y-6">
      {/* System Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>System Health</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {systemMetrics.map((metric, index) => {
              const StatusIcon = getStatusIcon(metric.status)
              
              return (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${getStatusBgColor(metric.status)}`}>
                      <metric.icon className={`h-4 w-4 ${getStatusColor(metric.status).split(' ')[0]}`} />
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">{metric.name}</div>
                      <div className="text-sm text-gray-600">{metric.description}</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="font-bold text-gray-900">{metric.value}</span>
                    <StatusIcon className={`h-4 w-4 ${getStatusColor(metric.status).split(' ')[0]}`} />
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Detailed Metrics */}
      <Card>
        <CardHeader>
          <CardTitle>Detailed Metrics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {detailedMetrics.map((category, categoryIndex) => (
              <div key={categoryIndex}>
                <div className="flex items-center space-x-2 mb-3">
                  <category.icon className="h-5 w-5 text-gray-600" />
                  <h4 className="font-medium text-gray-900">{category.category}</h4>
                </div>
                <div className="grid grid-cols-1 gap-2">
                  {category.metrics.map((metric, metricIndex) => {
                    const StatusIcon = getStatusIcon(metric.status)
                    
                    return (
                      <div key={metricIndex} className="flex items-center justify-between p-2 border rounded">
                        <span className="text-sm text-gray-600">{metric.name}</span>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium text-gray-900">{metric.value}</span>
                          <StatusIcon className={`h-3 w-3 ${getStatusColor(metric.status).split(' ')[0]}`} />
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* System Status Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Status Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Overall Status</span>
              <Badge className="text-green-600 border-green-200" variant="outline">
                <CheckCircle className="h-3 w-3 mr-1" />
                All Systems Operational
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Last Incident</span>
              <span className="text-sm text-gray-900">None in last 30 days</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Maintenance Window</span>
              <span className="text-sm text-gray-900">Sunday 2:00-4:00 AM UTC</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Next Deployment</span>
              <span className="text-sm text-gray-900">No scheduled deployments</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>System Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-3">
            <button className="p-3 text-left border rounded-lg hover:bg-gray-50 transition-colors">
              <div className="font-medium text-gray-900">View Logs</div>
              <div className="text-sm text-gray-600">System error logs</div>
            </button>
            <button className="p-3 text-left border rounded-lg hover:bg-gray-50 transition-colors">
              <div className="font-medium text-gray-900">Performance</div>
              <div className="text-sm text-gray-600">Detailed metrics</div>
            </button>
            <button className="p-3 text-left border rounded-lg hover:bg-gray-50 transition-colors">
              <div className="font-medium text-gray-900">Monitoring</div>
              <div className="text-sm text-gray-600">Real-time alerts</div>
            </button>
            <button className="p-3 text-left border rounded-lg hover:bg-gray-50 transition-colors">
              <div className="font-medium text-gray-900">Maintenance</div>
              <div className="text-sm text-gray-600">Schedule tasks</div>
            </button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
