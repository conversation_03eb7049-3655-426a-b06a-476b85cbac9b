'use client'

import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { getStatusColor, formatDate } from '@/lib/utils'
import { 
  Mail, 
  Phone, 
  Building, 
  MapPin, 
  Calendar,
  FileText,
  Receipt,

  TrendingUp
} from 'lucide-react'

interface CustomerDetailsProps {
  customer: {
    id: string
    name: string
    email: string | null
    phone: string | null
    company: string | null
    address: string | null
    city: string | null
    state: string | null
    country: string | null
    postalCode: string | null
    notes: string | null
    status: string
    createdAt: Date
    _count: {
      leads: number
      quotations: number
      invoices: number
      contracts: number
    }
  }
}

export function CustomerDetails({ customer }: CustomerDetailsProps) {
  const fullAddress = [
    customer.address,
    customer.city,
    customer.state,
    customer.postalCode,
    customer.country
  ].filter(Boolean).join(', ')

  const stats = [
    {
      label: 'Leads',
      value: customer._count.leads,
      icon: TrendingUp,
      color: 'text-green-600',
    },
    {
      label: 'Quotations',
      value: customer._count.quotations,
      icon: FileText,
 Receipt,
      color: 'text-blue-600',
    },
    {
      label: 'Invoices',
      value: customer._count.invoices,
      icon: Receipt,
      color: 'text-orange-600',
    },
    {
      label: 'Contracts',
      value: customer._count.contracts,
      icon: FileText,
 Receipt,
      color: 'text-purple-600',
    },
  ]

  return (
    <div className="space-y-6">
      {/* Customer Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Customer Information</CardTitle>
            <Badge className={getStatusColor(customer.status)} variant="outline">
              {customer.status}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Contact Information */}
            <div className="space-y-4">
              <h3 className="font-medium text-gray-900">Contact Details</h3>
              
              {customer.email && (
                <div className="flex items-center space-x-3">
                  <Mail className="h-4 w-4 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-600">Email</p>
                    <p className="font-medium">{customer.email}</p>
                  </div>
                </div>
              )}

              {customer.phone && (
                <div className="flex items-center space-x-3">
                  <Phone className="h-4 w-4 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-600">Phone</p>
                    <p className="font-medium">{customer.phone}</p>
                  </div>
                </div>
              )}

              {customer.company && (
                <div className="flex items-center space-x-3">
                  <Building className="h-4 w-4 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-600">Company</p>
                    <p className="font-medium">{customer.company}</p>
                  </div>
                </div>
              )}

              <div className="flex items-center space-x-3">
                <Calendar className="h-4 w-4 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-600">Customer Since</p>
                  <p className="font-medium">{formatDate(customer.createdAt)}</p>
                </div>
              </div>
            </div>

            {/* Address Information */}
            <div className="space-y-4">
              <h3 className="font-medium text-gray-900">Address</h3>
              
              {fullAddress ? (
                <div className="flex items-start space-x-3">
                  <MapPin className="h-4 w-4 text-gray-400 mt-1" />
                  <div>
                    <p className="text-sm text-gray-600">Address</p>
                    <p className="font-medium">{fullAddress}</p>
                  </div>
                </div>
              ) : (
                <p className="text-sm text-gray-500 italic">No address provided</p>
              )}
            </div>
          </div>

          {/* Notes */}
          {customer.notes && (
            <div className="pt-4 border-t">
              <h3 className="font-medium text-gray-900 mb-2">Notes</h3>
              <p className="text-gray-700 whitespace-pre-wrap">{customer.notes}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Statistics */}
      <Card>
        <CardHeader>
          <CardTitle>Activity Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {stats.map((stat, index) => (
              <div key={index} className="text-center p-4 bg-gray-50 rounded-lg">
                <div className={`inline-flex items-center justify-center w-8 h-8 rounded-full bg-white mb-2`}>
                  <stat.icon className={`h-4 w-4 ${stat.color}`} />
                </div>
                <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
                <div className="text-sm text-gray-600">{stat.label}</div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            <Button variant="outline" className="justify-start" asChild>
              <a href={`/dashboard/leads/new?customerId=${customer.id}`}>
                <TrendingUp className="h-4 w-4 mr-2" />
                New Lead
              </a>
            </Button>
            <Button variant="outline" className="justify-start" asChild>
              <a href={`/dashboard/quotations/new?customerId=${customer.id}`}>
                <FileText className="h-4 w-4 mr-2" />
                New Quotation
              </a>
            </Button>
            <Button variant="outline" className="justify-start" asChild>
              <a href={`/dashboard/invoices/new?customerId=${customer.id}`}>
                <Receipt className="h-4 w-4 mr-2" />
                New Invoice
              </a>
            </Button>
            <Button variant="outline" className="justify-start" asChild>
              <a href={`/dashboard/contracts/new?customerId=${customer.id}`}>
                <FileText className="h-4 w-4 mr-2" />
                New Contract
              </a>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
