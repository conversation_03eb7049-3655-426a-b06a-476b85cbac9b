import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { EmailsList } from '@/components/emails/emails-list'
import { EmailStats } from '@/components/emails/email-stats'
import { EmailFilters } from '@/components/emails/email-filters'
import { Button } from '@/components/ui/button'
import { Mail, Send, Settings, Plus, Filter } from 'lucide-react'
import Link from 'next/link'

interface SearchParams {
  type?: string
  status?: string
  recipient?: string
  page?: string
  search?: string
}

export default async function EmailsPage({
  searchParams,
}: {
  searchParams: SearchParams
}) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  const type = searchParams.type || ''
  const status = searchParams.status || ''
  const recipient = searchParams.recipient || ''
  const search = searchParams.search || ''
  const page = parseInt(searchParams.page || '1')
  const limit = 20
  const offset = (page - 1) * limit

  // Build where clause for emails
  const where: any = {
    companyId: session.user.companyId
  }

  if (type) {
    where.type = type
  }

  if (status) {
    where.status = status
  }

  if (recipient) {
    where.OR = [
      { toEmail: { contains: recipient } },
      { toName: { contains: recipient } },
    ]
  }

  if (search) {
    where.OR = [
      { subject: { contains: search } },
      { toEmail: { contains: search } },
      { toName: { contains: search } },
    ]
  }

  // Fetch emails and statistics
  const [emails, totalCount, stats] = await Promise.all([
    prisma.email.findMany({
      where,
      select: {
        id: true,
        subject: true,
        body: true,
        to: true,
        toEmail: true,
        from: true,
        cc: true,
        bcc: true,
        type: true,
        status: true,
        sentAt: true,
        deliveredAt: true,
        openedAt: true,
        clickedAt: true,
        failedAt: true,
        readAt: true,
        sentBy: true,
        createdAt: true,
        updatedAt: true
      },
      orderBy: { createdAt: 'desc' },
      skip: offset,
      take: limit,
    }),
    prisma.email.count({ where }),
    
    // Get email statistics
    Promise.all([
      prisma.email.count({
        where: { companyId: session.user.companyId }
      }),
      prisma.email.count({
        where: { 
          companyId: session.user.companyId,
          status: 'SENT'
        }
      }),
      prisma.email.count({
        where: { 
          companyId: session.user.companyId,
          status: 'DELIVERED'
        }
      }),
      prisma.email.count({
        where: { 
          companyId: session.user.companyId,
          status: 'OPENED'
        }
      }),
      prisma.email.groupBy({
        by: ['type'],
        where: { companyId: session.user.companyId },
        _count: true
      }),
      prisma.email.groupBy({
        by: ['status'],
        where: { companyId: session.user.companyId },
        _count: true
      }),
      // Calculate delivery rate
      prisma.email.aggregate({
        where: { 
          companyId: session.user.companyId,
          status: { in: ['DELIVERED', 'OPENED', 'CLICKED'] }
        },
        _count: true
      }),
      // Calculate open rate
      prisma.email.aggregate({
        where: { 
          companyId: session.user.companyId,
          status: { in: ['OPENED', 'CLICKED'] }
        },
        _count: true
      })
    ])
  ])

  const [
    totalEmails,
    sentEmails,
    deliveredEmails,
    openedEmails,
    typeBreakdown,
    statusBreakdown,
    deliveredCount,
    openedCount
  ] = stats

  const deliveryRate = totalEmails > 0 ? ((deliveredCount._count / totalEmails) * 100).toFixed(1) : '0'
  const openRate = deliveredCount._count > 0 ? ((openedCount._count / deliveredCount._count) * 100).toFixed(1) : '0'

  const emailStats = {
    total: totalEmails,
    sent: sentEmails,
    delivered: deliveredEmails,
    opened: openedEmails,
    deliveryRate: parseFloat(deliveryRate),
    openRate: parseFloat(openRate),
    typeBreakdown,
    statusBreakdown
  }

  const totalPages = Math.ceil(totalCount / limit)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Mail className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Email Management</h1>
            <p className="text-gray-600 mt-1">
              Manage email communications and track delivery performance
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Link href="/dashboard/emails/compose">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Compose Email
            </Button>
          </Link>
          <Link href="/dashboard/emails/templates">
            <Button variant="outline">
              <Mail className="h-4 w-4 mr-2" />
              Templates
            </Button>
          </Link>
          <Link href="/dashboard/emails/settings">
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
          </Link>
        </div>
      </div>

      {/* Statistics */}
      <EmailStats stats={{
        ...emailStats,
        typeBreakdown: emailStats.typeBreakdown.map(item => ({
          type: item.type || 'UNKNOWN',
          _count: item._count
        })),
        statusBreakdown: emailStats.statusBreakdown.map(item => ({
          status: item.status,
          _count: item._count
        }))
      }} />

      {/* Filters */}
      <EmailFilters 
        currentType={type}
        currentStatus={status}
        currentRecipient={recipient}
        currentSearch={search}
      />

      {/* Emails List */}
      <EmailsList 
        emails={emails.map(email => ({
          ...email,
          type: email.type || 'MARKETING',
          toName: email.to,
          toEmail: email.toEmail || email.to || '',
          fromEmail: email.from,
          fromName: email.sentBy || 'System',
          sentBy: email.sentBy ? {
            name: email.sentBy,
            firstName: null,
            lastName: null,
            email: email.from || ''
          } : null,
          bouncedAt: null,
          unsubscribedAt: null,
          spamReportedAt: null,
          metadata: {},
          attachments: []
        }))}
        currentPage={page}
        totalPages={totalPages}
        totalCount={totalCount}
      />
    </div>
  )
}
