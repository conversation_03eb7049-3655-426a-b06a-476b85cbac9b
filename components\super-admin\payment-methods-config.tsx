'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { toast } from 'react-hot-toast'
import {
  CreditCard,
  Smartphone,
  Building,
  Wallet,
  Globe,
  DollarSign,
  Settings,
  Save,
  RefreshCw,
  Loader2,
  Check,
  X,
  Plus,
  Trash2
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs'

const paymentMethodsSchema = z.object({
  global: z.object({
    card: z.boolean(),
    applePay: z.boolean(),
    googlePay: z.boolean(),
  }),
  india: z.object({
    card: z.boolean(),
    upi: z.boolean(),
    netbanking: z.boolean(),
    wallet: z.boolean(),
    emi: z.boolean(),
  }),
  us: z.object({
    card: z.boolean(),
    applePay: z.boolean(),
    googlePay: z.boolean(),
    achDebit: z.boolean(),
    link: z.boolean(),
  }),
  eu: z.object({
    card: z.boolean(),
    sepaDebit: z.boolean(),
    ideal: z.boolean(),
    bancontact: z.boolean(),
    giropay: z.boolean(),
    sofort: z.boolean(),
  }),
  currencies: z.object({
    usd: z.boolean(),
    inr: z.boolean(),
    eur: z.boolean(),
    gbp: z.boolean(),
  }),
  stripeConfig: z.object({
    publishableKey: z.string().optional(),
    webhookSecret: z.string().optional(),
    testMode: z.boolean(),
  })
})

type PaymentMethodsFormData = z.infer<typeof paymentMethodsSchema>

export function PaymentMethodsConfig() {
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue
  } = useForm<PaymentMethodsFormData>({
    resolver: zodResolver(paymentMethodsSchema),
    defaultValues: {
      global: {
        card: true,
        applePay: true,
        googlePay: true,
      },
      india: {
        card: true,
        upi: true,
        netbanking: true,
        wallet: true,
        emi: false,
      },
      us: {
        card: true,
        applePay: true,
        googlePay: true,
        achDebit: false,
        link: false,
      },
      eu: {
        card: true,
        sepaDebit: true,
        ideal: true,
        bancontact: true,
        giropay: false,
        sofort: false,
      },
      currencies: {
        usd: true,
        inr: true,
        eur: true,
        gbp: false,
      },
      stripeConfig: {
        publishableKey: '',
        webhookSecret: '',
        testMode: true,
      }
    }
  })

  const onSubmit = async (data: PaymentMethodsFormData) => {
    setIsSaving(true)
    try {
      const response = await fetch('/api/super-admin/payment-methods', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update payment methods')
      }

      toast.success('Payment methods configuration updated successfully!')
    } catch (error) {
      console.error('Error updating payment methods:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to update payment methods'
      toast.error(errorMessage)
    } finally {
      setIsSaving(false)
    }
  }

  const paymentMethodCategories = [
    {
      id: 'global',
      title: 'Global Payment Methods',
      description: 'Payment methods available worldwide',
      icon: Globe,
      color: 'blue',
      methods: [
        { key: 'card', name: 'Credit/Debit Cards', description: 'Visa, Mastercard, Amex, etc.' },
        { key: 'applePay', name: 'Apple Pay', description: 'Apple Pay digital wallet' },
        { key: 'googlePay', name: 'Google Pay', description: 'Google Pay digital wallet' },
      ]
    },
    {
      id: 'india',
      title: 'India Payment Methods',
      description: 'Payment methods for Indian customers',
      icon: Smartphone,
      color: 'green',
      methods: [
        { key: 'card', name: 'Cards', description: 'Credit/Debit cards including RuPay' },
        { key: 'upi', name: 'UPI', description: 'PhonePe, GPay, Paytm, BHIM UPI' },
        { key: 'netbanking', name: 'Net Banking', description: 'All major Indian banks' },
        { key: 'wallet', name: 'Digital Wallets', description: 'Paytm, PhonePe, Amazon Pay' },
        { key: 'emi', name: 'EMI', description: 'Equated Monthly Installments' },
      ]
    },
    {
      id: 'us',
      title: 'US Payment Methods',
      description: 'Payment methods for US customers',
      icon: DollarSign,
      color: 'purple',
      methods: [
        { key: 'card', name: 'Cards', description: 'Credit/Debit cards' },
        { key: 'applePay', name: 'Apple Pay', description: 'Apple Pay' },
        { key: 'googlePay', name: 'Google Pay', description: 'Google Pay' },
        { key: 'achDebit', name: 'ACH Debit', description: 'Bank account debit' },
        { key: 'link', name: 'Link', description: 'Stripe Link' },
      ]
    },
    {
      id: 'eu',
      title: 'Europe Payment Methods',
      description: 'Payment methods for European customers',
      icon: Building,
      color: 'orange',
      methods: [
        { key: 'card', name: 'Cards', description: 'Credit/Debit cards' },
        { key: 'sepaDebit', name: 'SEPA Debit', description: 'SEPA Direct Debit' },
        { key: 'ideal', name: 'iDEAL', description: 'Netherlands bank transfer' },
        { key: 'bancontact', name: 'Bancontact', description: 'Belgium payment method' },
        { key: 'giropay', name: 'Giropay', description: 'German bank transfer' },
        { key: 'sofort', name: 'Sofort', description: 'European bank transfer' },
      ]
    }
  ]

  const currencies = [
    { key: 'usd', name: 'US Dollar', symbol: '$', code: 'USD' },
    { key: 'inr', name: 'Indian Rupee', symbol: '₹', code: 'INR' },
    { key: 'eur', name: 'Euro', symbol: '€', code: 'EUR' },
    { key: 'gbp', name: 'British Pound', symbol: '£', code: 'GBP' },
  ]

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
      <Tabs defaultValue="methods" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="methods">Payment Methods</TabsTrigger>
          <TabsTrigger value="currencies">Currencies</TabsTrigger>
          <TabsTrigger value="stripe">Stripe Configuration</TabsTrigger>
        </TabsList>

        {/* Payment Methods Tab */}
        <TabsContent value="methods" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {paymentMethodCategories.map((category) => {
              const Icon = category.icon
              return (
                <Card key={category.id}>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Icon className={`h-5 w-5 text-${category.color}-600`} />
                      <span>{category.title}</span>
                    </CardTitle>
                    <p className="text-sm text-gray-600">{category.description}</p>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {category.methods.map((method) => (
                      <div key={method.key} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex-1">
                          <div className="font-medium text-gray-900">{method.name}</div>
                          <div className="text-sm text-gray-600">{method.description}</div>
                        </div>
                        <Switch
                          checked={watch(`${category.id}.${method.key}` as any)}
                          onCheckedChange={(checked) => setValue(`${category.id}.${method.key}` as any, checked)}
                        />
                      </div>
                    ))}
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </TabsContent>

        {/* Currencies Tab */}
        <TabsContent value="currencies" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <DollarSign className="h-5 w-5 text-green-600" />
                <span>Supported Currencies</span>
              </CardTitle>
              <p className="text-sm text-gray-600">Configure which currencies are accepted on your platform</p>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {currencies.map((currency) => (
                  <div key={currency.key} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-bold">{currency.symbol}</span>
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">{currency.name}</div>
                        <div className="text-sm text-gray-600">{currency.code}</div>
                      </div>
                    </div>
                    <Switch
                      checked={watch(`currencies.${currency.key}` as any)}
                      onCheckedChange={(checked) => setValue(`currencies.${currency.key}` as any, checked)}
                    />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Stripe Configuration Tab */}
        <TabsContent value="stripe" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5 text-blue-600" />
                <span>Stripe Configuration</span>
              </CardTitle>
              <p className="text-sm text-gray-600">Configure Stripe payment gateway settings</p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="publishableKey">Publishable Key</Label>
                <Input
                  id="publishableKey"
                  {...register('stripeConfig.publishableKey')}
                  placeholder="pk_test_..."
                  type="password"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="webhookSecret">Webhook Secret</Label>
                <Input
                  id="webhookSecret"
                  {...register('stripeConfig.webhookSecret')}
                  placeholder="whsec_..."
                  type="password"
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Test Mode</Label>
                  <p className="text-sm text-gray-500">Use Stripe test environment</p>
                </div>
                <Switch
                  checked={watch('stripeConfig.testMode')}
                  onCheckedChange={(checked) => setValue('stripeConfig.testMode', checked)}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Save Actions */}
      <div className="flex items-center justify-end space-x-4 p-6 bg-gray-50 rounded-lg">
        <p className="text-sm text-gray-600">Changes will affect all new payment sessions</p>
        <Button type="button" variant="outline" onClick={() => reset()}>
          Reset
        </Button>
        <Button type="submit" disabled={isSaving}>
          {isSaving ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <Save className="h-4 w-4 mr-2" />
          )}
          Save Configuration
        </Button>
      </div>
    </form>
  )
}
