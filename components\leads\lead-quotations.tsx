'use client'

import Link from 'next/link'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { formatDate, formatCurrency, getStatusColor } from '@/lib/utils'
import { 
  FileText,
  ExternalLink,
  Plus,
  Eye,
  Download
} from 'lucide-react'

interface LeadQuotationsProps {
  quotations: Array<{
    id: string
    quotationNumber: string
    title: string
    status: string
    total: number
    validUntil: Date | null
    createdAt: Date
  }>
  leadId: string
}

export function LeadQuotations({ quotations, leadId }: LeadQuotationsProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center">
          <FileText className="h-5 w-5 mr-2" />
          Quotations ({quotations.length})
        </CardTitle>
        <Link href={`/dashboard/quotations/new?leadId=${leadId}`}>
          <Button >
            <Plus className="h-4 w-4 mr-2" />
            New Quotation
          </Button>
        </Link>
      </CardHeader>
      <CardContent>
        {quotations.length === 0 ? (
          <div className="text-center py-8">
            <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500">No quotations yet</p>
            <p className="text-sm text-gray-400 mt-1">
              Create a quotation to send a proposal for this lead
            </p>
            <Link href={`/dashboard/quotations/new?leadId=${leadId}`} className="mt-4 inline-block">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Quotation
              </Button>
            </Link>
          </div>
        ) : (
          <div className="space-y-4">
            {quotations.map((quotation) => (
              <div key={quotation.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <Link 
                        href={`/dashboard/quotations/${quotation.id}`}
                        className="font-medium text-gray-900 hover:text-blue-600 flex items-center"
                      >
                        {quotation.quotationNumber}
                        <ExternalLink className="h-3 w-3 ml-1" />
                      </Link>
                      <Badge className={getStatusColor(quotation.status)} variant="outline">
                        {quotation.status}
                      </Badge>
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-2">{quotation.title}</p>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span className="font-medium text-gray-900">
                          {formatCurrency(quotation.total)}
                        </span>
                        <span>Created: {formatDate(quotation.createdAt)}</span>
                        {quotation.validUntil && (
                          <span>Valid until: {formatDate(quotation.validUntil)}</span>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    <Link href={`/dashboard/quotations/${quotation.id}`}>
                      <Button variant="ghost" >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </Link>
                    <Button variant="ghost" >
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                {/* Progress indicator for quotation status */}
                <div className="mt-3 pt-3 border-t border-gray-100">
                  <div className="flex items-center space-x-2">
                    <div className="flex-1 bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full transition-all duration-300 ${
                          quotation.status === 'DRAFT' ? 'w-1/6 bg-gray-400' :
                          quotation.status === 'SENT' ? 'w-2/6 bg-blue-500' :
                          quotation.status === 'VIEWED' ? 'w-3/6 bg-yellow-500' :
                          quotation.status === 'ACCEPTED' ? 'w-full bg-green-500' :
                          quotation.status === 'REJECTED' ? 'w-full bg-red-500' :
                          'w-1/6 bg-gray-400'
                        }`}
                      />
                    </div>
                    <span className="text-xs text-gray-500 min-w-0">
                      {quotation.status === 'DRAFT' && 'Draft'}
                      {quotation.status === 'SENT' && 'Sent'}
                      {quotation.status === 'VIEWED' && 'Viewed'}
                      {quotation.status === 'ACCEPTED' && 'Accepted'}
                      {quotation.status === 'REJECTED' && 'Rejected'}
                      {quotation.status === 'EXPIRED' && 'Expired'}
                    </span>
                  </div>
                </div>
              </div>
            ))}
            
            {quotations.length >= 10 && (
              <div className="text-center pt-4">
                <Link 
                  href={`/dashboard/quotations?leadId=${leadId}`}
                  className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                >
                  View all quotations for this lead
                </Link>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
