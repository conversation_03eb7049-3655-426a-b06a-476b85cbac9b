import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const customerSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email address').optional().or(z.literal('')),
  phone: z.string().optional(),
  company: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  country: z.string().optional(),
  postalCode: z.string().optional(),
  notes: z.string().optional(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'PROSPECT']),
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const customer = await prisma.customer.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      },
      include: {
        leads: {
          orderBy: { createdAt: 'desc' },
          take: 5,
        },
        quotations: {
          orderBy: { createdAt: 'desc' },
          take: 5,
        },
        invoices: {
          orderBy: { createdAt: 'desc' },
          take: 5,
        },
        contracts: {
          orderBy: { createdAt: 'desc' },
          take: 5,
        },
        activities: {
          include: {
            createdBy: {
              select: { name: true, firstName: true, lastName: true }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
        _count: {
          select: {
            leads: true,
            quotations: true,
            invoices: true,
            contracts: true,
          }
        }
      }
    })

    if (!customer) {
      return NextResponse.json({ error: 'Customer not found' }, { status: 404 })
    }

    return NextResponse.json(customer)
  } catch (error) {
    console.error('Error fetching customer:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = customerSchema.parse(body)

    // Check if customer exists and belongs to the company
    const existingCustomer = await prisma.customer.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      }
    })

    if (!existingCustomer) {
      return NextResponse.json({ error: 'Customer not found' }, { status: 404 })
    }

    // Check if email is being changed and if it conflicts with another customer
    if (validatedData.email && validatedData.email !== existingCustomer.email) {
      const emailConflict = await prisma.customer.findFirst({
        where: {
          email: validatedData.email,
          companyId: session.user.companyId,
          id: { not: params.id },
        }
      })

      if (emailConflict) {
        return NextResponse.json(
          { error: 'Customer with this email already exists' },
          { status: 400 }
        )
      }
    }

    const customer = await prisma.customer.update({
      where: { id: params.id },
      data: validatedData,
      include: {
        _count: {
          select: {
            leads: true,
            quotations: true,
            invoices: true,
            contracts: true,
          }
        }
      }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'NOTE',
        title: 'Customer updated',
        description: `Customer "${customer.name}" was updated`,
        customerId: customer.id,
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    return NextResponse.json(customer)
  } catch (error) {
    console.error('Error updating customer:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if customer exists and belongs to the company
    const customer = await prisma.customer.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      },
      include: {
        _count: {
          select: {
            leads: true,
            quotations: true,
            invoices: true,
            contracts: true,
          }
        }
      }
    })

    if (!customer) {
      return NextResponse.json({ error: 'Customer not found' }, { status: 404 })
    }

    // Check if customer has related records
    const hasRelatedRecords = customer._count.leads > 0 || 
                             customer._count.quotations > 0 || 
                             customer._count.invoices > 0 || 
                             customer._count.contracts > 0

    if (hasRelatedRecords) {
      return NextResponse.json(
        { error: 'Cannot delete customer with existing leads, quotations, invoices, or contracts' },
        { status: 400 }
      )
    }

    await prisma.customer.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: 'Customer deleted successfully' })
  } catch (error) {
    console.error('Error deleting customer:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
