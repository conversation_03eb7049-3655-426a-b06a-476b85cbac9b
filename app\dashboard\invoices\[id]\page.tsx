import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { InvoiceDetails } from '@/components/invoices/invoice-details'
import { InvoiceActivity } from '@/components/invoices/invoice-activity'
import { InvoiceReceipts } from '@/components/invoices/invoice-receipts'
import { InvoicePreview } from '@/components/invoices/invoice-preview'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Edit, Trash2, Send, Download, Copy, CreditCard, Receipt } from 'lucide-react'
import Link from 'next/link'
import { notFound } from 'next/navigation'

export default async function InvoiceDetailPage({
  params,
}: {
  params: { id: string }
}) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  const [invoice, company] = await Promise.all([
    prisma.invoice.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      },
      include: {
        customer: {
          select: { 
            id: true, 
            name: true, 
            email: true, 
            company: true, 
            phone: true,
            address: true,
            city: true,
            state: true,
            country: true,
            postalCode: true
          }
        },
        quotation: {
          select: { id: true, quotationNumber: true, title: true }
        },
        items: {
          include: {
            item: {
              select: { name: true, category: true, unit: true }
            }
          }
        },
        receipts: {
          include: {
            createdBy: {
              select: { name: true, firstName: true, lastName: true }
            }
          },
          orderBy: { createdAt: 'desc' }
        },
        activities: {
          include: {
            createdBy: {
              select: { name: true, firstName: true, lastName: true }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: 15,
        },
        _count: {
          select: {
            items: true,
            activities: true,
            receipts: true,
          }
        }
      }
    }),
    prisma.company.findUnique({
      where: { id: session.user.companyId },
      select: {
        name: true,
        email: true,
        phone: true,
        address: true,
        city: true,
        state: true,
        country: true,
        postalCode: true,
        website: true,
        taxId: true
      }
    })
  ])

  if (!invoice) {
    notFound()
  }

  const isOverdue = invoice.dueDate && 
                   new Date(invoice.dueDate) < new Date() && 
                   !['PAID', 'CANCELLED'].includes(invoice.status)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/invoices">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Invoices
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{invoice.invoiceNumber}</h1>
            <p className="text-gray-600 mt-1">
              {invoice.title}
              {isOverdue && (
                <span className="ml-2 text-red-600 font-medium">• Overdue</span>
              )}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {invoice.status === 'DRAFT' && (
            <>
              <Button variant="outline" className="text-blue-600 hover:text-blue-700">
                <Send className="h-4 w-4 mr-2" />
                Send
              </Button>
              <Link href={`/dashboard/invoices/${invoice.id}/edit`}>
                <Button variant="outline">
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>
              </Link>
            </>
          )}
          {['SENT', 'VIEWED', 'OVERDUE'].includes(invoice.status) && (
            <Button variant="outline" className="text-green-600 hover:text-green-700">
              <CreditCard className="h-4 w-4 mr-2" />
              Mark Paid
            </Button>
          )}
          <Button variant="outline">
            <Copy className="h-4 w-4 mr-2" />
            Duplicate
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Download PDF
          </Button>
          {!['CANCELLED'].includes(invoice.status) && (
            <Link href={`/dashboard/receipts/new?invoiceId=${invoice.id}`}>
              <Button variant="outline" className="text-green-600 hover:text-green-700">
                <Receipt className="h-4 w-4 mr-2" />
                Create Receipt
              </Button>
            </Link>
          )}
          {invoice.status === 'DRAFT' && (
            <Button variant="outline" className="text-red-600 hover:text-red-700">
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Invoice Details */}
        <div className="lg:col-span-2 space-y-6">
          <InvoiceDetails invoice={invoice} />
          <InvoicePreview invoice={{
            ...invoice,
            status: invoice.status as string,
            company: company
          }} company={company} />
        </div>

        {/* Right Column - Receipts & Activity */}
        <div className="space-y-6">
          <InvoiceReceipts
            invoiceId={invoice.id}
            invoiceTotal={invoice.total}
            invoiceStatus={invoice.status}
            receipts={invoice.receipts.map(receipt => ({
              id: receipt.id,
              receiptNumber: receipt.receiptNumber,
              amount: receipt.amount,
              paymentMethod: receipt.paymentMethod || 'Unknown',
              status: receipt.status as string,
              paidAt: receipt.paidAt || receipt.createdAt,
              createdAt: receipt.createdAt,
              createdBy: receipt.createdBy
            }))}
          />
          <InvoiceActivity activities={invoice.activities} />
        </div>
      </div>
    </div>
  )
}
