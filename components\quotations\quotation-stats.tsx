'use client'

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { formatCurrency } from '@/lib/utils'
import { 
  FileText, 
  Send, 
  Eye, 
  CheckCircle, 
  XCircle, 
  Clock,
  DollarSign,
  TrendingUp
} from 'lucide-react'

interface QuotationStatsProps {
  stats: {
    total: number
    draft: number
    sent: number
    viewed: number
    accepted: number
    rejected: number
    expired: number
    totalValue: number
    acceptedValue: number
    pendingValue: number
  }
}

export function QuotationStats({ stats }: QuotationStatsProps) {
  const acceptanceRate = stats.total > 0 ? ((stats.accepted / stats.total) * 100).toFixed(1) : '0'
  const conversionRate = (stats.sent + stats.viewed) > 0 ? 
    ((stats.accepted / (stats.sent + stats.viewed)) * 100).toFixed(1) : '0'

  const statCards = [
    {
      title: 'Total Quotations',
      value: stats.total.toString(),
      icon: FileText,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      description: 'All quotations created'
    },
    {
      title: 'Draft',
      value: stats.draft.toString(),
      icon: FileText,
      color: 'text-gray-600',
      bgColor: 'bg-gray-100',
      description: 'Pending completion'
    },
    {
      title: 'Sent',
      value: stats.sent.toString(),
      icon: Send,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      description: 'Sent to customers'
    },
    {
      title: 'Viewed',
      value: stats.viewed.toString(),
      icon: Eye,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
      description: 'Opened by customers'
    },
    {
      title: 'Accepted',
      value: stats.accepted.toString(),
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      description: 'Successfully accepted'
    },
    {
      title: 'Rejected',
      value: stats.rejected.toString(),
      icon: XCircle,
      color: 'text-red-600',
      bgColor: 'bg-red-100',
      description: 'Declined by customers'
    },
    {
      title: 'Total Value',
      value: formatCurrency(stats.totalValue),
      icon: DollarSign,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      description: 'Total quotation value'
    },
    {
      title: 'Accepted Value',
      value: formatCurrency(stats.acceptedValue),
      icon: TrendingUp,
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-100',
      description: 'Revenue from accepted'
    },
    {
      title: 'Pending Value',
      value: formatCurrency(stats.pendingValue),
      icon: Clock,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      description: 'Awaiting response'
    },
    {
      title: 'Acceptance Rate',
      value: `${acceptanceRate}%`,
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      description: 'Overall acceptance rate'
    },
    {
      title: 'Conversion Rate',
      value: `${conversionRate}%`,
      icon: TrendingUp,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-100',
      description: 'Sent to accepted rate'
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4">
      {statCards.map((stat, index) => (
        <Card key={index} className="hover:shadow-lg transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              {stat.title}
            </CardTitle>
            <div className={`p-2 rounded-lg ${stat.bgColor}`}>
              <stat.icon className={`h-4 w-4 ${stat.color}`} />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">
              {stat.value}
            </div>
            <p className="text-xs text-gray-500 mt-1">
              {stat.description}
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
