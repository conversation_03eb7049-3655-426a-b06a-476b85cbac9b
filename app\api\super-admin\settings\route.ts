import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const settingsSchema = z.object({
  general: z.object({
    platformName: z.string().min(1, 'Platform name is required'),
    platformDescription: z.string().min(1, 'Platform description is required'),
    supportEmail: z.string().email('Invalid email address'),
    companyEmail: z.string().email('Invalid email address'),
    timezone: z.string(),
    language: z.string(),
    maintenanceMode: z.boolean(),
    allowRegistration: z.boolean(),
    defaultUserRole: z.string()
  }).optional(),
  security: z.object({
    requireTwoFactor: z.boolean(),
    passwordMinLength: z.number().min(6).max(32),
    sessionTimeout: z.number().min(1).max(168),
    maxLoginAttempts: z.number().min(3).max(10),
    enableAuditLogs: z.boolean(),
    requireEmailVerification: z.boolean(),
    enableIpWhitelist: z.boolean(),
    forcePasswordReset: z.number().min(30).max(365),
    enableSSOOnly: z.boolean()
  }).optional(),
  features: z.object({
    enableRegistration: z.boolean(),
    enableInvitations: z.boolean(),
    enableApiAccess: z.boolean(),
    enableWebhooks: z.boolean(),
    enableEmailCampaigns: z.boolean(),
    enableAdvancedReporting: z.boolean(),
    enableFileUploads: z.boolean(),
    enableIntegrations: z.boolean(),
    enableCustomBranding: z.boolean(),
    enableMobileApp: z.boolean()
  }).optional(),
  limits: z.object({
    maxUsersPerCompany: z.number().min(1),
    maxStoragePerCompany: z.number().min(1),
    maxApiCallsPerHour: z.number().min(100),
    maxEmailsPerMonth: z.number().min(1000),
    maxFileUploadSize: z.number().min(1).max(100),
    maxCompanies: z.number().min(1),
    maxIntegrationsPerCompany: z.number().min(1)
  }).optional(),
  notifications: z.object({
    enableEmailNotifications: z.boolean(),
    enableSlackNotifications: z.boolean(),
    enableWebhookNotifications: z.boolean(),
    notifyOnNewSignup: z.boolean(),
    notifyOnPaymentFailure: z.boolean(),
    notifyOnSystemErrors: z.boolean(),
    dailyReportEmail: z.string().email().optional(),
    weeklyReportEmail: z.string().email().optional()
  }).optional()
})

// Get platform settings
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.isSuperAdmin && session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // In a real app, these would come from a settings table
    // For now, return default settings
    const settings = {
      general: {
        platformName: 'SaaS Platform',
        platformDescription: 'Enterprise Business Management Platform',
        supportEmail: '<EMAIL>',
        companyEmail: '<EMAIL>',
        timezone: 'UTC',
        language: 'en',
        maintenanceMode: false,
        allowRegistration: true,
        defaultUserRole: 'USER'
      },
      security: {
        requireTwoFactor: false,
        passwordMinLength: 8,
        sessionTimeout: 24,
        maxLoginAttempts: 5,
        enableAuditLogs: true,
        requireEmailVerification: true,
        enableIpWhitelist: false,
        forcePasswordReset: 90,
        enableSSOOnly: false
      },
      features: {
        enableRegistration: true,
        enableInvitations: true,
        enableApiAccess: true,
        enableWebhooks: true,
        enableEmailCampaigns: true,
        enableAdvancedReporting: true,
        enableFileUploads: true,
        enableIntegrations: true,
        enableCustomBranding: false,
        enableMobileApp: false
      },
      limits: {
        maxUsersPerCompany: 100,
        maxStoragePerCompany: 10,
        maxApiCallsPerHour: 1000,
        maxEmailsPerMonth: 10000,
        maxFileUploadSize: 10,
        maxCompanies: 1000,
        maxIntegrationsPerCompany: 10
      },
      notifications: {
        enableEmailNotifications: true,
        enableSlackNotifications: false,
        enableWebhookNotifications: true,
        notifyOnNewSignup: true,
        notifyOnPaymentFailure: true,
        notifyOnSystemErrors: true,
        dailyReportEmail: '<EMAIL>',
        weeklyReportEmail: '<EMAIL>'
      }
    }

    return NextResponse.json(settings)
  } catch (error) {
    console.error('Error fetching settings:', error)
    return NextResponse.json(
      { error: 'Failed to fetch settings' },
      { status: 500 }
    )
  }
}

// Update platform settings
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.isSuperAdmin && session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = settingsSchema.parse(body)

    // In a real app, you would save these to a settings table
    // For now, we'll just validate and return success
    
    // Log the settings update activity
    await prisma.activity.create({
      data: {
        type: 'SYSTEM',
        title: 'Platform settings updated',
        description: 'Platform settings were updated by super admin',
        companyId: session.user.companyId!,
        createdById: session.user.id,
        metadata: {
          updatedSections: Object.keys(validatedData),
          timestamp: new Date().toISOString()
        }
      }
    })

    return NextResponse.json({ 
      success: true, 
      message: 'Settings updated successfully',
      updatedAt: new Date().toISOString()
    })
  } catch (error) {
    console.error('Error updating settings:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Failed to update settings' },
      { status: 500 }
    )
  }
}

// Reset settings to defaults
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.isSuperAdmin && session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // In a real app, you would reset settings in the database
    
    // Log the settings reset activity
    await prisma.activity.create({
      data: {
        type: 'SYSTEM',
        title: 'Platform settings reset',
        description: 'Platform settings were reset to defaults by super admin',
        companyId: session.user.companyId!,
        createdById: session.user.id,
        metadata: {
          action: 'reset_to_defaults',
          timestamp: new Date().toISOString()
        }
      }
    })

    return NextResponse.json({ 
      success: true, 
      message: 'Settings reset to defaults successfully',
      resetAt: new Date().toISOString()
    })
  } catch (error) {
    console.error('Error resetting settings:', error)
    return NextResponse.json(
      { error: 'Failed to reset settings' },
      { status: 500 }
    )
  }
}


