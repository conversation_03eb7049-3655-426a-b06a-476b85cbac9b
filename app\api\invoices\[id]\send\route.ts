import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if invoice exists and belongs to the company
    const invoice = await prisma.invoice.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      },
      include: {
        customer: {
          select: { 
            id: true, 
            name: true, 
            email: true, 
            company: true 
          }
        },
        items: true,
      }
    })

    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 })
    }

    // Check if invoice can be sent
    if (invoice.status !== 'DRAFT') {
      return NextResponse.json(
        { error: 'Only draft invoices can be sent' },
        { status: 400 }
      )
    }

    // Check if customer has email
    if (!invoice.customer?.email) {
      return NextResponse.json(
        { error: 'Customer email is required to send invoice' },
        { status: 400 }
      )
    }

    // Update invoice status to SENT
    const updatedInvoice = await prisma.invoice.update({
      where: { id: params.id },
      data: {
        status: 'SENT',
        sentAt: new Date(),
      },
      include: {
        customer: {
          select: { id: true, name: true, email: true, company: true }
        },
        quotation: {
          select: { id: true, quotationNumber: true, title: true }
        },
        items: {
          include: {
            item: {
              select: { name: true, category: true }
            }
          }
        },
        _count: {
          select: {
            items: true,
            activities: true,
          }
        }
      }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'INVOICE_SENT',
        title: 'Invoice sent',
        description: `Invoice "${invoice.invoiceNumber}" was sent to ${invoice.customer.name}`,
        invoiceId: invoice.id,
        customerId: invoice.customerId,
        quotationId: invoice.quotationId,
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    // TODO: Send email to customer
    // This would integrate with your email service (SendGrid, AWS SES, etc.)
    // For now, we'll just simulate the email sending
    
    try {
      // Simulate email sending
      console.log(`Sending invoice ${invoice.invoiceNumber} to ${invoice.customer.email}`)
      
      // In a real implementation, you would:
      // 1. Generate PDF of the invoice
      // 2. Send email with PDF attachment
      // 3. Include tracking links for viewing
      // 4. Set up payment links if using payment processors
      
    } catch (emailError) {
      console.error('Error sending email:', emailError)
      // Don't fail the request if email fails, just log it
    }

    return NextResponse.json({
      message: 'Invoice sent successfully',
      invoice: updatedInvoice
    })
  } catch (error) {
    console.error('Error sending invoice:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
