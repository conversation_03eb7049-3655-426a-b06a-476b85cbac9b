'use client'

import Link from 'next/link'
import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { formatDate, formatCurrency, getStatusColor } from '@/lib/utils'
import { 
  Eye, 
  Edit, 
  Trash2, 
  Send, 
  Download,
  Copy,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  FileText,
  User,
  Building,
  Calendar,
  DollarSign,
  PenTool,
  Play,
  Clock
} from 'lucide-react'
import toast from 'react-hot-toast'

interface Contract {
  id: string
  contractNumber: string
  title: string
  description: string | null
  status: string
  value: number | null
  startDate: Date | null
  endDate: Date | null
  signedAt: Date | null
  createdAt: Date
  customer: {
    id: string
    name: string
    email: string | null
    company: string | null
  } | null
  quotation: {
    id: string
    quotationNumber: string
    title: string
  } | null
  signatures: Array<{
    id: string
    signedAt: Date
    signedBy: {
      name: string | null
      firstName: string | null
      lastName: string | null
    }
  }>
  _count: {
    signatures: number
    activities: number
  }
}

interface ContractsTableProps {
  contracts: Contract[]
  currentPage: number
  totalPages: number
  totalCount: number
}

export function ContractsTable({ 
  contracts, 
  currentPage, 
  totalPages, 
  totalCount 
}: ContractsTableProps) {
  const [deletingId, setDeletingId] = useState<string | null>(null)
  const [sendingId, setSendingId] = useState<string | null>(null)
  const [executingId, setExecutingId] = useState<string | null>(null)

  const handleDelete = async (contractId: string) => {
    if (!confirm('Are you sure you want to delete this contract?')) {
      return
    }

    setDeletingId(contractId)
    try {
      const response = await fetch(`/api/contracts/${contractId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to delete contract')
      }

      toast.success('Contract deleted successfully')
      window.location.reload()
    } catch (error) {
      toast.error('Failed to delete contract')
    } finally {
      setDeletingId(null)
    }
  }

  const handleSend = async (contractId: string) => {
    setSendingId(contractId)
    try {
      const response = await fetch(`/api/contracts/${contractId}/send`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to send contract')
      }

      toast.success('Contract sent successfully')
      window.location.reload()
    } catch (error) {
      toast.error('Failed to send contract')
    } finally {
      setSendingId(null)
    }
  }

  const handleExecute = async (contractId: string) => {
    setExecutingId(contractId)
    try {
      const response = await fetch(`/api/contracts/${contractId}/execute`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to execute contract')
      }

      toast.success('Contract executed successfully')
      window.location.reload()
    } catch (error) {
      toast.error('Failed to execute contract')
    } finally {
      setExecutingId(null)
    }
  }

  const handleDuplicate = async (contractId: string) => {
    try {
      const response = await fetch(`/api/contracts/${contractId}/duplicate`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to duplicate contract')
      }

      const result = await response.json()
      toast.success('Contract duplicated successfully')
      window.location.href = `/dashboard/contracts/${result.id}/edit`
    } catch (error) {
      toast.error('Failed to duplicate contract')
    }
  }

  const isExpired = (contract: Contract) => {
    return contract.endDate && 
           new Date(contract.endDate) < new Date() && 
           !['CANCELLED', 'EXECUTED'].includes(contract.status)
  }

  const getDaysUntilExpiry = (endDate: Date) => {
    return Math.ceil((new Date(endDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
  }

  if (contracts.length === 0) {
    return (
      <Card>
        <CardContent className="py-12">
          <div className="text-center">
            <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No contracts found</h3>
            <p className="text-gray-500 mb-6">
              Get started by creating your first contract.
            </p>
            <Link href="/dashboard/contracts/new">
              <Button>
                Create Contract
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Contracts ({totalCount})</span>
            <span className="text-sm font-normal text-gray-500">
              Page {currentPage} of {totalPages}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Desktop Table */}
          <div className="hidden md:block overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Contract</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Customer</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Value</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Duration</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Signatures</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">Actions</th>
                </tr>
              </thead>
              <tbody>
                {contracts.map((contract) => (
                  <tr key={contract.id} className="border-b border-gray-100 hover:bg-gray-50">
                    <td className="py-4 px-4">
                      <div>
                        <div className="font-medium text-gray-900">{contract.contractNumber}</div>
                        <div className="text-sm text-gray-600">{contract.title}</div>
                        {contract.quotation && (
                          <div className="text-xs text-blue-600">
                            From: {contract.quotation.quotationNumber}
                          </div>
                        )}
                        <div className="text-xs text-gray-400 mt-1">
                          {contract._count.signatures} signatures • {contract._count.activities} activities
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      {contract.customer ? (
                        <div>
                          <div className="font-medium text-gray-900">{contract.customer.name}</div>
                          {contract.customer.company && (
                            <div className="text-sm text-gray-500">{contract.customer.company}</div>
                          )}
                          {contract.customer.email && (
                            <div className="text-sm text-gray-500">{contract.customer.email}</div>
                          )}
                        </div>
                      ) : (
                        <span className="text-gray-400 italic">No customer</span>
                      )}
                    </td>
                    <td className="py-4 px-4">
                      <div className="space-y-1">
                        <Badge className={getStatusColor(contract.status)} variant="outline">
                          {contract.status}
                        </Badge>
                        {isExpired(contract) && (
                          <div className="flex items-center text-xs text-red-600">
                            <Clock className="h-3 w-3 mr-1" />
                            Expired
                          </div>
                        )}
                        {contract.signedAt && (
                          <div className="text-xs text-green-600">
                            Signed: {formatDate(contract.signedAt)}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      {contract.value ? (
                        <span className="font-medium text-gray-900">
                          {formatCurrency(contract.value)}
                        </span>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </td>
                    <td className="py-4 px-4 text-sm text-gray-600">
                      {contract.startDate && contract.endDate ? (
                        <div>
                          <div>{formatDate(contract.startDate)} -</div>
                          <div className={isExpired(contract) ? 'text-red-600 font-medium' : ''}>
                            {formatDate(contract.endDate)}
                          </div>
                          {contract.endDate && !isExpired(contract) && (
                            <div className="text-xs text-gray-500">
                              {getDaysUntilExpiry(contract.endDate)} days left
                            </div>
                          )}
                        </div>
                      ) : (
                        '-'
                      )}
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium text-gray-900">
                          {contract._count.signatures}
                        </span>
                        {contract._count.signatures > 0 && (
                          <PenTool className="h-3 w-3 text-green-600" />
                        )}
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center justify-end space-x-2">
                        <Link href={`/dashboard/contracts/${contract.id}`}>
                          <Button variant="ghost" >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </Link>
                        {contract.status === 'DRAFT' && (
                          <Link href={`/dashboard/contracts/${contract.id}/edit`}>
                            <Button variant="ghost" >
                              <Edit className="h-4 w-4" />
                            </Button>
                          </Link>
                        )}
                        {contract.status === 'DRAFT' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleSend(contract.id)}
                            disabled={sendingId === contract.id}
                            className="text-blue-600 hover:text-blue-700"
                          >
                            <Send className="h-4 w-4" />
                          </Button>
                        )}
                        {contract.status === 'SIGNED' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleExecute(contract.id)}
                            disabled={executingId === contract.id}
                            className="text-green-600 hover:text-green-700"
                          >
                            <Play className="h-4 w-4" />
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDuplicate(contract.id)}
                          className="text-green-600 hover:text-green-700"
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-gray-600 hover:text-gray-700"
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                        {contract.status === 'DRAFT' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(contract.id)}
                            disabled={deletingId === contract.id}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Mobile Cards */}
          <div className="md:hidden space-y-4">
            {contracts.map((contract) => (
              <Card key={contract.id} className="p-4">
                <div className="space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">{contract.contractNumber}</div>
                      <div className="text-sm text-gray-600 mt-1">{contract.title}</div>
                      {contract.quotation && (
                        <div className="text-xs text-blue-600 mt-1">
                          From: {contract.quotation.quotationNumber}
                        </div>
                      )}
                    </div>
                    <Button variant="ghost" >
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>

                  {contract.customer && (
                    <div className="flex items-center space-x-2">
                      <User className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600">{contract.customer.name}</span>
                      {contract.customer.company && (
                        <>
                          <Building className="h-4 w-4 text-gray-400" />
                          <span className="text-sm text-gray-600">{contract.customer.company}</span>
                        </>
                      )}
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Badge className={getStatusColor(contract.status)} variant="outline">
                        {contract.status}
                      </Badge>
                      {isExpired(contract) && (
                        <Badge variant="destructive" className="text-xs">
                          Expired
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center space-x-1">
                      {contract.value ? (
                        <>
                          <DollarSign className="h-4 w-4 text-gray-400" />
                          <span className="font-medium text-gray-900">
                            {formatCurrency(contract.value)}
                          </span>
                        </>
                      ) : (
                        <span className="text-gray-400">No value</span>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4" />
                      <span>
                        {contract.startDate && contract.endDate 
                          ? `${formatDate(contract.startDate)} - ${formatDate(contract.endDate)}`
                          : 'No duration set'
                        }
                      </span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <PenTool className="h-4 w-4" />
                      <span>{contract._count.signatures} signatures</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-end space-x-2 pt-2 border-t">
                    <Link href={`/dashboard/contracts/${contract.id}`}>
                      <Button variant="ghost" >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </Link>
                    {contract.status === 'DRAFT' && (
                      <>
                        <Link href={`/dashboard/contracts/${contract.id}/edit`}>
                          <Button variant="ghost" >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </Link>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleSend(contract.id)}
                          disabled={sendingId === contract.id}
                          className="text-blue-600"
                        >
                          <Send className="h-4 w-4" />
                        </Button>
                      </>
                    )}
                    {contract.status === 'SIGNED' && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleExecute(contract.id)}
                        disabled={executingId === contract.id}
                        className="text-green-600"
                      >
                        <Play className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            Showing {((currentPage - 1) * 10) + 1} to {Math.min(currentPage * 10, totalCount)} of {totalCount} contracts
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage <= 1}
              asChild
            >
              <Link href={`?page=${currentPage - 1}`}>
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Link>
            </Button>
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage >= totalPages}
              asChild
            >
              <Link href={`?page=${currentPage + 1}`}>
                Next
                <ChevronRight className="h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
