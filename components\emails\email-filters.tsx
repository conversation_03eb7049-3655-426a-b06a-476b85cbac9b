'use client'

import { useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Search, 
  Filter, 
  X,
  Mail,
  Send,
  CheckCircle,
  Eye,
  MousePointer,
  AlertTriangle,
  Clock,
  FileText,
  Receipt,

  Bell,
  TrendingUp
} from 'lucide-react'

interface EmailFiltersProps {
  currentType: string
  currentStatus: string
  currentRecipient: string
  currentSearch: string
}

export function EmailFilters({
  currentType,
  currentStatus,
  currentRecipient,
  currentSearch
}: EmailFiltersProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [searchValue, setSearchValue] = useState(currentSearch)

  const emailTypes = [
    { value: 'QUOTATION', label: 'Quotation', icon: FileText,
 Receipt, color: 'text-blue-600' },
    { value: 'INVOICE', label: 'Invoice', icon: Receipt, color: 'text-green-600' },
    { value: 'CONTRACT', label: 'Contract', icon: FileText,
 Receipt, color: 'text-purple-600' },
    { value: 'RECEIPT', label: 'Receipt', icon: CheckCircle, color: 'text-emerald-600' },
    { value: 'REMINDER', label: 'Reminder', icon: Clock, color: 'text-orange-600' },
    { value: 'NOTIFICATION', label: 'Notification', icon: Bell, color: 'text-indigo-600' },
    { value: 'MARKETING', label: 'Marketing', icon: TrendingUp, color: 'text-pink-600' },
    { value: 'CUSTOM', label: 'Custom', icon: Mail, color: 'text-gray-600' }
  ]

  const statusOptions = [
    { value: 'PENDING', label: 'Pending', icon: Clock, color: 'text-yellow-600' },
    { value: 'SENT', label: 'Sent', icon: Send, color: 'text-blue-600' },
    { value: 'DELIVERED', label: 'Delivered', icon: CheckCircle, color: 'text-green-600' },
    { value: 'OPENED', label: 'Opened', icon: Eye, color: 'text-purple-600' },
    { value: 'CLICKED', label: 'Clicked', icon: MousePointer, color: 'text-indigo-600' },
    { value: 'BOUNCED', label: 'Bounced', icon: AlertTriangle, color: 'text-red-600' },
    { value: 'FAILED', label: 'Failed', icon: X, color: 'text-red-600' }
  ]

  const updateFilters = (key: string, value: string) => {
    const params = new URLSearchParams(searchParams.toString())
    
    if (value) {
      params.set(key, value)
    } else {
      params.delete(key)
    }
    
    // Reset to first page when filters change
    params.delete('page')
    
    router.push(`/dashboard/emails?${params.toString()}`)
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    updateFilters('search', searchValue)
  }

  const clearFilters = () => {
    setSearchValue('')
    router.push('/dashboard/emails')
  }

  const activeFiltersCount = [currentType, currentStatus, currentRecipient, currentSearch]
    .filter(Boolean).length

  return (
    <Card>
      <CardContent className="p-6">
        <div className="space-y-6">
          {/* Search */}
          <div>
            <form onSubmit={handleSearch} className="flex items-center space-x-2">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search emails by subject, recipient..."
                  value={searchValue}
                  onChange={(e) => setSearchValue(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button type="submit">Search</Button>
              {activeFiltersCount > 0 && (
                <Button type="button" variant="outline" onClick={clearFilters}>
                  <X className="h-4 w-4 mr-2" />
                  Clear ({activeFiltersCount})
                </Button>
              )}
            </form>
          </div>

          {/* Active Filters */}
          {activeFiltersCount > 0 && (
            <div className="flex flex-wrap gap-2">
              {currentType && (
                <Badge variant="secondary" className="flex items-center space-x-1">
                  <span>Type: {emailTypes.find(t => t.value === currentType)?.label}</span>
                  <button
                    onClick={() => updateFilters('type', '')}
                    className="ml-1 hover:bg-gray-200 rounded-full p-0.5"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              )}
              {currentStatus && (
                <Badge variant="secondary" className="flex items-center space-x-1">
                  <span>Status: {statusOptions.find(s => s.value === currentStatus)?.label}</span>
                  <button
                    onClick={() => updateFilters('status', '')}
                    className="ml-1 hover:bg-gray-200 rounded-full p-0.5"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              )}
              {currentRecipient && (
                <Badge variant="secondary" className="flex items-center space-x-1">
                  <span>Recipient: "{currentRecipient}"</span>
                  <button
                    onClick={() => updateFilters('recipient', '')}
                    className="ml-1 hover:bg-gray-200 rounded-full p-0.5"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              )}
              {currentSearch && (
                <Badge variant="secondary" className="flex items-center space-x-1">
                  <span>Search: "{currentSearch}"</span>
                  <button
                    onClick={() => {
                      setSearchValue('')
                      updateFilters('search', '')
                    }}
                    className="ml-1 hover:bg-gray-200 rounded-full p-0.5"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              )}
            </div>
          )}

          {/* Filter Options */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Email Types */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                <Filter className="h-4 w-4 mr-2" />
                Email Type
              </h4>
              <div className="space-y-2 max-h-48 overflow-y-auto">
                {emailTypes.map((type) => {
                  const TypeIcon = type.icon
                  const isActive = currentType === type.value
                  
                  return (
                    <button
                      key={type.value}
                      onClick={() => updateFilters('type', isActive ? '' : type.value)}
                      className={`w-full flex items-center space-x-3 p-2 rounded-lg text-left transition-colors ${
                        isActive
                          ? 'bg-blue-100 border-blue-200 border'
                          : 'hover:bg-gray-50 border border-transparent'
                      }`}
                    >
                      <TypeIcon className={`h-4 w-4 ${type.color}`} />
                      <span className={`text-sm ${isActive ? 'font-medium text-blue-900' : 'text-gray-700'}`}>
                        {type.label}
                      </span>
                    </button>
                  )
                })}
              </div>
            </div>

            {/* Status */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                <CheckCircle className="h-4 w-4 mr-2" />
                Delivery Status
              </h4>
              <div className="space-y-2 max-h-48 overflow-y-auto">
                {statusOptions.map((status) => {
                  const StatusIcon = status.icon
                  const isActive = currentStatus === status.value
                  
                  return (
                    <button
                      key={status.value}
                      onClick={() => updateFilters('status', isActive ? '' : status.value)}
                      className={`w-full flex items-center space-x-3 p-2 rounded-lg text-left transition-colors ${
                        isActive
                          ? 'bg-blue-100 border-blue-200 border'
                          : 'hover:bg-gray-50 border border-transparent'
                      }`}
                    >
                      <StatusIcon className={`h-4 w-4 ${status.color}`} />
                      <span className={`text-sm ${isActive ? 'font-medium text-blue-900' : 'text-gray-700'}`}>
                        {status.label}
                      </span>
                    </button>
                  )
                })}
              </div>
            </div>
          </div>

          {/* Quick Filters */}
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Quick Filters</h4>
            <div className="flex flex-wrap gap-2">
              <Button
                variant={currentStatus === 'FAILED' ? 'default' : 'outline'}
                size="sm"
                onClick={() => updateFilters('status', currentStatus === 'FAILED' ? '' : 'FAILED')}
              >
                <AlertTriangle className="h-4 w-4 mr-2" />
                Failed Emails
              </Button>
              <Button
                variant={currentStatus === 'BOUNCED' ? 'default' : 'outline'}
                size="sm"
                onClick={() => updateFilters('status', currentStatus === 'BOUNCED' ? '' : 'BOUNCED')}
              >
                <X className="h-4 w-4 mr-2" />
                Bounced
              </Button>
              <Button
                variant={currentStatus === 'OPENED' ? 'default' : 'outline'}
                size="sm"
                onClick={() => updateFilters('status', currentStatus === 'OPENED' ? '' : 'OPENED')}
              >
                <Eye className="h-4 w-4 mr-2" />
                Opened
              </Button>
              <Button
                variant={currentType === 'QUOTATION' ? 'default' : 'outline'}
                size="sm"
                onClick={() => updateFilters('type', currentType === 'QUOTATION' ? '' : 'QUOTATION')}
              >
                <FileText className="h-4 w-4 mr-2" />
                Quotations
              </Button>
              <Button
                variant={currentType === 'INVOICE' ? 'default' : 'outline'}
                size="sm"
                onClick={() => updateFilters('type', currentType === 'INVOICE' ? '' : 'INVOICE')}
              >
                <Receipt className="h-4 w-4 mr-2" />
                Invoices
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
