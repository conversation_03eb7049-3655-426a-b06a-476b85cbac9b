import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { EmailComposer } from '@/components/emails/email-composer'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Mail } from 'lucide-react'
import Link from 'next/link'

interface SearchParams {
  templateId?: string
  type?: string
  recipientId?: string
  entityId?: string
  entityType?: string
}

export default async function ComposeEmailPage({
  searchParams,
}: {
  searchParams: SearchParams
}) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  // Fetch email templates and customers
  const [templates, customers, company] = await Promise.all([
    prisma.emailTemplate.findMany({
      where: { 
        companyId: session.user.companyId,
        status: 'ACTIVE'
      },
      orderBy: { name: 'asc' }
    }),
    prisma.customer.findMany({
      where: { companyId: session.user.companyId },
      orderBy: { name: 'asc' }
    }),
    prisma.company.findUnique({
      where: { id: session.user.companyId }
    })
  ])

  // Get preselected template
  let preselectedTemplate = null
  if (searchParams.templateId) {
    preselectedTemplate = templates.find(t => t.id === searchParams.templateId)
  }

  // Get preselected recipient
  let preselectedRecipient = null
  if (searchParams.recipientId) {
    preselectedRecipient = customers.find(c => c.id === searchParams.recipientId)
  }

  // Get entity context if provided
  let entityContext = null
  if (searchParams.entityId && searchParams.entityType) {
    try {
      switch (searchParams.entityType) {
        case 'quotation':
          entityContext = await prisma.quotation.findFirst({
            where: { 
              id: searchParams.entityId,
              companyId: session.user.companyId
            },
            include: {
              customer: true,
              items: {
                include: { item: true }
              }
            }
          })
          break
        case 'invoice':
          entityContext = await prisma.invoice.findFirst({
            where: { 
              id: searchParams.entityId,
              companyId: session.user.companyId
            },
            include: {
              customer: true,
              items: {
                include: { item: true }
              }
            }
          })
          break
        case 'contract':
          entityContext = await prisma.contract.findFirst({
            where: { 
              id: searchParams.entityId,
              companyId: session.user.companyId
            },
            include: {
              customer: true
            }
          })
          break
      }
    } catch (error) {
      console.error('Error fetching entity context:', error)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href="/dashboard/emails">
          <Button variant="ghost" >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Emails
          </Button>
        </Link>
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Mail className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Compose Email</h1>
            <p className="text-gray-600 mt-1">
              Create and send professional emails to your customers
            </p>
          </div>
        </div>
      </div>

      {/* Email Composer */}
      <EmailComposer 
        templates={templates}
        customers={customers}
        company={company}
        preselectedTemplate={preselectedTemplate}
        preselectedRecipient={preselectedRecipient}
        entityContext={entityContext}
        entityType={searchParams.entityType}
        defaultType={searchParams.type}
      />
    </div>
  )
}
