'use client'

import Link from 'next/link'
import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { formatDate, formatCurrency, getStatusColor } from '@/lib/utils'
import { 
  Eye, 
  Edit, 
  Trash2, 
  Send, 
  Download,
  Copy,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  FileText,
  User,
  Building,
  Calendar,
  DollarSign
} from 'lucide-react'
import toast from 'react-hot-toast'

interface Quotation {
  id: string
  quotationNumber: string
  title: string
  description: string | null
  status: string
  total: number
  validUntil: Date | null
  createdAt: Date
  customer: {
    id: string
    name: string
    email: string | null
    company: string | null
  } | null
  lead: {
    id: string
    title: string
  } | null
  items: Array<{
    id: string
    name: string
    quantity: number
    unitPrice: number
    item: {
      name: string
      category: string | null
    } | null
  }>
  _count: {
    items: number
    activities: number
  }
}

interface QuotationsTableProps {
  quotations: Quotation[]
  currentPage: number
  totalPages: number
  totalCount: number
}

export function QuotationsTable({ 
  quotations, 
  currentPage, 
  totalPages, 
  totalCount 
}: QuotationsTableProps) {
  const [deletingId, setDeletingId] = useState<string | null>(null)
  const [sendingId, setSendingId] = useState<string | null>(null)

  const handleDelete = async (quotationId: string) => {
    if (!confirm('Are you sure you want to delete this quotation?')) {
      return
    }

    setDeletingId(quotationId)
    try {
      const response = await fetch(`/api/quotations/${quotationId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to delete quotation')
      }

      toast.success('Quotation deleted successfully')
      window.location.reload()
    } catch (error) {
      toast.error('Failed to delete quotation')
    } finally {
      setDeletingId(null)
    }
  }

  const handleSend = async (quotationId: string) => {
    setSendingId(quotationId)
    try {
      const response = await fetch(`/api/quotations/${quotationId}/send`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to send quotation')
      }

      toast.success('Quotation sent successfully')
      window.location.reload()
    } catch (error) {
      toast.error('Failed to send quotation')
    } finally {
      setSendingId(null)
    }
  }

  const handleDuplicate = async (quotationId: string) => {
    try {
      const response = await fetch(`/api/quotations/${quotationId}/duplicate`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to duplicate quotation')
      }

      const result = await response.json()
      toast.success('Quotation duplicated successfully')
      window.location.href = `/dashboard/quotations/${result.id}/edit`
    } catch (error) {
      toast.error('Failed to duplicate quotation')
    }
  }

  if (quotations.length === 0) {
    return (
      <Card>
        <CardContent className="py-12">
          <div className="text-center">
            <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No quotations found</h3>
            <p className="text-gray-500 mb-6">
              Get started by creating your first quotation.
            </p>
            <Link href="/dashboard/quotations/new">
              <Button>
                Create Quotation
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Quotations ({totalCount})</span>
            <span className="text-sm font-normal text-gray-500">
              Page {currentPage} of {totalPages}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Desktop Table */}
          <div className="hidden md:block overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Quotation</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Customer</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Amount</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Valid Until</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Created</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">Actions</th>
                </tr>
              </thead>
              <tbody>
                {quotations.map((quotation) => (
                  <tr key={quotation.id} className="border-b border-gray-100 hover:bg-gray-50">
                    <td className="py-4 px-4">
                      <div>
                        <div className="font-medium text-gray-900">{quotation.quotationNumber}</div>
                        <div className="text-sm text-gray-600">{quotation.title}</div>
                        {quotation.lead && (
                          <div className="text-xs text-blue-600">
                            Lead: {quotation.lead.title}
                          </div>
                        )}
                        <div className="text-xs text-gray-400 mt-1">
                          {quotation._count.items} items • {quotation._count.activities} activities
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      {quotation.customer ? (
                        <div>
                          <div className="font-medium text-gray-900">{quotation.customer.name}</div>
                          {quotation.customer.company && (
                            <div className="text-sm text-gray-500">{quotation.customer.company}</div>
                          )}
                          {quotation.customer.email && (
                            <div className="text-sm text-gray-500">{quotation.customer.email}</div>
                          )}
                        </div>
                      ) : (
                        <span className="text-gray-400 italic">No customer</span>
                      )}
                    </td>
                    <td className="py-4 px-4">
                      <Badge className={getStatusColor(quotation.status)} variant="outline">
                        {quotation.status}
                      </Badge>
                    </td>
                    <td className="py-4 px-4">
                      <span className="font-medium text-gray-900">
                        {formatCurrency(quotation.total)}
                      </span>
                    </td>
                    <td className="py-4 px-4 text-sm text-gray-600">
                      {quotation.validUntil ? formatDate(quotation.validUntil) : '-'}
                    </td>
                    <td className="py-4 px-4 text-sm text-gray-600">
                      {formatDate(quotation.createdAt)}
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center justify-end space-x-2">
                        <Link href={`/dashboard/quotations/${quotation.id}`}>
                          <Button variant="ghost" >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </Link>
                        <Link href={`/dashboard/quotations/${quotation.id}/edit`}>
                          <Button variant="ghost" >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </Link>
                        {quotation.status === 'DRAFT' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleSend(quotation.id)}
                            disabled={sendingId === quotation.id}
                            className="text-blue-600 hover:text-blue-700"
                          >
                            <Send className="h-4 w-4" />
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDuplicate(quotation.id)}
                          className="text-green-600 hover:text-green-700"
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-gray-600 hover:text-gray-700"
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(quotation.id)}
                          disabled={deletingId === quotation.id}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Mobile Cards */}
          <div className="md:hidden space-y-4">
            {quotations.map((quotation) => (
              <Card key={quotation.id} className="p-4">
                <div className="space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">{quotation.quotationNumber}</div>
                      <div className="text-sm text-gray-600 mt-1">{quotation.title}</div>
                      {quotation.lead && (
                        <div className="text-xs text-blue-600 mt-1">
                          Lead: {quotation.lead.title}
                        </div>
                      )}
                    </div>
                    <Button variant="ghost" >
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>

                  {quotation.customer && (
                    <div className="flex items-center space-x-2">
                      <User className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600">{quotation.customer.name}</span>
                      {quotation.customer.company && (
                        <>
                          <Building className="h-4 w-4 text-gray-400" />
                          <span className="text-sm text-gray-600">{quotation.customer.company}</span>
                        </>
                      )}
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Badge className={getStatusColor(quotation.status)} variant="outline">
                        {quotation.status}
                      </Badge>
                    </div>
                    <div className="flex items-center space-x-1">
                      <DollarSign className="h-4 w-4 text-gray-400" />
                      <span className="font-medium text-gray-900">
                        {formatCurrency(quotation.total)}
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4" />
                      <span>{formatDate(quotation.createdAt)}</span>
                    </div>
                    <div>
                      {quotation._count.items} items • {quotation._count.activities} activities
                    </div>
                  </div>

                  <div className="flex items-center justify-end space-x-2 pt-2 border-t">
                    <Link href={`/dashboard/quotations/${quotation.id}`}>
                      <Button variant="ghost" >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </Link>
                    <Link href={`/dashboard/quotations/${quotation.id}/edit`}>
                      <Button variant="ghost" >
                        <Edit className="h-4 w-4" />
                      </Button>
                    </Link>
                    {quotation.status === 'DRAFT' && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSend(quotation.id)}
                        disabled={sendingId === quotation.id}
                        className="text-blue-600"
                      >
                        <Send className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            Showing {((currentPage - 1) * 10) + 1} to {Math.min(currentPage * 10, totalCount)} of {totalCount} quotations
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage <= 1}
              asChild
            >
              <Link href={`?page=${currentPage - 1}`}>
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Link>
            </Button>
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage >= totalPages}
              asChild
            >
              <Link href={`?page=${currentPage + 1}`}>
                Next
                <ChevronRight className="h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
