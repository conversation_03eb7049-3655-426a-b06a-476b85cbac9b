// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// NextAuth.js required models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// Application models
model User {
  id            String    @id @default(cuid())
  name          String?
  firstName     String?
  lastName      String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  password      String?
  role          Role      @default(USER)
  companyId     String?
  lastLoginAt   DateTime?
  isSuperAdmin  Boolean   @default(false)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  accounts   Account[]
  sessions   Session[]
  company    Company?     @relation(fields: [companyId], references: [id])
  ownedCompany Company?   @relation("CompanyOwner")
  
  // Created records
  createdCustomers  Customer[]
  createdLeads      Lead[]
  createdQuotations Quotation[]
  createdInvoices   Invoice[]
  createdContracts  Contract[]
  createdItems      Item[]
  createdItemCategories ItemCategory[]
  createdActivities Activity[]
  createdApiKeys    ApiKey[]
  createdContractTemplates ContractTemplate[]
  createdEmails     Email[]         @relation("EmailCreatedBy")
  createdReceipts   Receipt[]
  createdSupportTickets SupportTicket[] @relation("TicketCreatedBy")
  assignedSupportTickets SupportTicket[] @relation("TicketAssignedTo")
  ticketComments    TicketComment[]
  supportMessages   SupportMessage[] @relation("SupportMessageCreatedBy")
  createdKnowledgeBaseArticles KnowledgeBaseArticle[]
  notifications     Notification[]
  createdNotifications Notification[] @relation("NotificationCreatedBy")
  notificationPreferences NotificationPreference?
  onboardingProgress OnboardingProgress?
  emailTemplates EmailTemplate[]
  quotationTemplates QuotationTemplate[]
  invoiceTemplates InvoiceTemplate[]
  receiptTemplates ReceiptTemplate[]
  reports       Report[]
  webhooks      Webhook[] @relation("WebhookCreatedBy")
  contractSignatures ContractSignature[]

  @@map("users")
}

model Company {
  id                String   @id @default(cuid())
  name              String
  email             String?
  phone             String?
  address           String?
  city              String?
  state             String?
  country           String?
  postalCode        String?
  website           String?
  logo              String?
  industry          String?
  size              String?
  businessType      String?
  taxId             String?
  registrationNumber String?

  // Subscription & Limits
  subscriptionId    String?
  plan              String   @default("STARTER")
  features          Json?
  ownerId           String   @unique
  status            CompanyStatus @default(TRIAL)
  isActive          Boolean  @default(true)
  totalRevenue      Float    @default(0)
  userCount         Int      @default(1)
  customerCount     Int      @default(0)
  quotationCount    Int      @default(0)
  invoiceCount      Int      @default(0)
  contractCount     Int      @default(0)
  lastActivityAt    DateTime?
  maxUsers          Int      @default(5)
  maxCustomers      Int      @default(100)
  maxQuotations     Int      @default(50)
  maxInvoices       Int      @default(50)
  maxContracts      Int      @default(25)
  maxStorage        Int      @default(1000) // MB

  // Settings
  settings          Json?
  branding          Json?
  theme             Json?

  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  trialEndsAt       DateTime?

  // Relations
  owner       User         @relation("CompanyOwner", fields: [ownerId], references: [id])
  users       User[]
  customers   Customer[]
  leads       Lead[]
  quotations  Quotation[]
  invoices    Invoice[]
  contracts   Contract[]
  items       Item[]
  itemCategories ItemCategory[]
  activities  Activity[]
  apiKeys     ApiKey[]
  subscriptions Subscription[]
  payments    Payment[]
  contractTemplates ContractTemplate[]
  emails      Email[]
  receipts    Receipt[]
  supportTickets SupportTicket[]
  knowledgeBaseArticles KnowledgeBaseArticle[]
  notifications Notification[]
  onboardingProgress OnboardingProgress[]
  webhooks      Webhook[]
  emailTemplates EmailTemplate[]
  quotationTemplates QuotationTemplate[]
  invoiceTemplates InvoiceTemplate[]
  receiptTemplates ReceiptTemplate[]
  reports       Report[]

  @@map("companies")
}

model Customer {
  id          String   @id @default(cuid())
  name        String
  email       String?
  phone       String?
  company     String?
  address     String?
  city        String?
  state       String?
  country     String?
  postalCode  String?
  notes       String?
  website     String?
  type        String?
  status      CustomerStatus @default(ACTIVE)
  companyId   String
  createdById String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  company_rel Company     @relation(fields: [companyId], references: [id], onDelete: Cascade)
  createdBy   User        @relation(fields: [createdById], references: [id])
  leads       Lead[]
  quotations  Quotation[]
  invoices    Invoice[]
  contracts   Contract[]
  activities  Activity[]
  emails      Email[]
  receipts    Receipt[]

  @@map("customers")
}

model Lead {
  id          String     @id @default(cuid())
  title       String
  name        String?
  email       String?
  phone       String?
  company     String?
  description String?
  status      LeadStatus @default(NEW)
  priority    Priority   @default(MEDIUM)
  value       Float?
  source      String?
  notes       String?
  customerId  String?
  companyId   String
  createdById String
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // Relations
  company_rel Company     @relation(fields: [companyId], references: [id], onDelete: Cascade)
  createdBy   User        @relation(fields: [createdById], references: [id])
  customer    Customer?   @relation(fields: [customerId], references: [id])
  quotations  Quotation[]
  activities  Activity[]
  emails      Email[]

  @@map("leads")
}

model ItemCategory {
  id          String   @id @default(cuid())
  name        String
  description String?
  color       String?
  icon        String?
  parentId    String?
  isActive    Boolean  @default(true)
  companyId   String
  createdById String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  company     Company       @relation(fields: [companyId], references: [id], onDelete: Cascade)
  createdBy   User          @relation(fields: [createdById], references: [id])
  parent      ItemCategory? @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children    ItemCategory[] @relation("CategoryHierarchy")
  items       Item[]

  @@unique([name, companyId])
  @@map("item_categories")
}

model Item {
  id          String   @id @default(cuid())
  name        String
  description String?
  price       Float
  costPrice   Float?
  type        String?
  category    String?
  categoryId  String?
  sku         String?
  unit        String?
  taxRate     Float    @default(0)
  stockQuantity Int?
  lowStockThreshold Int?
  status      String?
  notes       String?
  isActive    Boolean  @default(true)
  companyId   String
  createdById String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  company_rel      Company         @relation(fields: [companyId], references: [id], onDelete: Cascade)
  createdBy        User            @relation(fields: [createdById], references: [id])
  itemCategory     ItemCategory?   @relation(fields: [categoryId], references: [id])
  quotationItems   QuotationItem[]
  invoiceItems     InvoiceItem[]
  contractItems    ContractItem[]
  activities       Activity[]

  @@map("items")
}

model Quotation {
  id            String          @id @default(cuid())
  quotationNumber String        @unique
  title         String
  description   String?
  status        QuotationStatus @default(DRAFT)
  issueDate     DateTime        @default(now())
  expiryDate    DateTime?
  validUntil    DateTime?
  sentAt        DateTime?
  acceptedAt    DateTime?
  terms         String?
  notes         String?
  paymentTerms  String?
  taxRate       Float           @default(0)
  taxAmount     Float?
  discountType  DiscountType    @default(PERCENTAGE)
  discountValue Float           @default(0)
  discountAmount Float?
  subtotal      Float           @default(0)
  total         Float           @default(0)
  amount        Float           @default(0)
  currency      String          @default("USD")
  customerId    String
  leadId        String?
  templateId    String?
  companyId     String
  createdById   String
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt

  // Relations
  company_rel Company         @relation(fields: [companyId], references: [id], onDelete: Cascade)
  createdBy   User            @relation(fields: [createdById], references: [id])
  customer    Customer        @relation(fields: [customerId], references: [id])
  lead        Lead?           @relation(fields: [leadId], references: [id])
  template    QuotationTemplate? @relation(fields: [templateId], references: [id])
  items       QuotationItem[]
  activities  Activity[]
  contracts   Contract[]
  invoices    Invoice[]
  emails      Email[]

  @@map("quotations")
}

model QuotationItem {
  id          String  @id @default(cuid())
  quotationId String
  itemId      String?
  name        String
  description String
  quantity    Float
  unitPrice   Float
  discount    Float   @default(0)
  taxRate     Float   @default(0)
  total       Float?
  createdAt   DateTime @default(now())

  // Relations
  quotation Quotation @relation(fields: [quotationId], references: [id], onDelete: Cascade)
  item      Item?     @relation(fields: [itemId], references: [id])

  @@map("quotation_items")
}

model Invoice {
  id            String        @id @default(cuid())
  invoiceNumber String        @unique
  title         String
  description   String?
  status        InvoiceStatus @default(DRAFT)
  issueDate     DateTime      @default(now())
  dueDate       DateTime?
  paidAt        DateTime?
  sentAt        DateTime?
  paymentMethod String?
  paymentReference String?
  terms         String?
  notes         String?
  paymentTerms  String?
  taxRate       Float         @default(0)
  taxAmount     Float?
  discountType  DiscountType  @default(PERCENTAGE)
  discountValue Float         @default(0)
  discountAmount Float?
  subtotal      Float         @default(0)
  total         Float         @default(0)
  paidAmount    Float         @default(0)
  customerId    String
  quotationId   String?
  templateId    String?
  companyId     String
  createdById   String
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  company_rel Company       @relation(fields: [companyId], references: [id], onDelete: Cascade)
  createdBy   User          @relation(fields: [createdById], references: [id])
  customer    Customer      @relation(fields: [customerId], references: [id])
  quotation   Quotation?    @relation(fields: [quotationId], references: [id])
  template    InvoiceTemplate? @relation(fields: [templateId], references: [id])
  items       InvoiceItem[]
  activities  Activity[]
  emails      Email[]
  receipts    Receipt[]

  @@map("invoices")
}

model InvoiceItem {
  id        String  @id @default(cuid())
  invoiceId String
  itemId    String?
  name      String
  description String
  quantity  Float
  unitPrice Float
  discount  Float   @default(0)
  taxRate   Float   @default(0)
  total     Float?
  createdAt DateTime @default(now())

  // Relations
  invoice Invoice @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  item    Item?   @relation(fields: [itemId], references: [id])

  @@map("invoice_items")
}

model Contract {
  id            String         @id @default(cuid())
  contractNumber String        @unique
  title         String
  description   String?
  content       String?        @db.Text
  status        ContractStatus @default(DRAFT)
  startDate     DateTime?
  endDate       DateTime?
  terms         String?
  notes         String?
  value         Float?
  signatureRequired Boolean    @default(false)
  autoExecute   Boolean        @default(false)
  sentAt        DateTime?
  signedAt      DateTime?
  executedAt    DateTime?
  templateId    String?
  quotationId   String?
  customerId    String
  companyId     String
  createdById   String
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt

  // Relations
  company_rel Company           @relation(fields: [companyId], references: [id], onDelete: Cascade)
  createdBy   User              @relation(fields: [createdById], references: [id])
  customer    Customer          @relation(fields: [customerId], references: [id])
  quotation   Quotation?        @relation(fields: [quotationId], references: [id])
  template    ContractTemplate? @relation(fields: [templateId], references: [id])
  items       ContractItem[]
  activities  Activity[]
  signatures  ContractSignature[]
  emails      Email[]

  @@map("contracts")
}

model ContractItem {
  id         String  @id @default(cuid())
  contractId String
  itemId     String?
  name       String
  description String
  quantity   Float
  unitPrice  Float
  discount   Float   @default(0)
  taxRate    Float   @default(0)
  total      Float?
  createdAt  DateTime @default(now())

  // Relations
  contract Contract @relation(fields: [contractId], references: [id], onDelete: Cascade)
  item     Item?    @relation(fields: [itemId], references: [id])

  @@map("contract_items")
}

model Activity {
  id          String       @id @default(cuid())
  type        ActivityType
  title       String
  description String?
  metadata    Json?
  status      String?
  customerId  String?
  leadId      String?
  quotationId String?
  invoiceId   String?
  contractId  String?
  itemId      String?
  companyId   String
  createdById String
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt

  // Relations
  company_rel Company    @relation(fields: [companyId], references: [id], onDelete: Cascade)
  createdBy   User       @relation(fields: [createdById], references: [id])
  customer    Customer?  @relation(fields: [customerId], references: [id])
  lead        Lead?      @relation(fields: [leadId], references: [id])
  quotation   Quotation? @relation(fields: [quotationId], references: [id])
  invoice     Invoice?   @relation(fields: [invoiceId], references: [id])
  contract    Contract?  @relation(fields: [contractId], references: [id])
  item        Item?      @relation(fields: [itemId], references: [id])

  @@map("activities")
}

model ApiKey {
  id          String   @id @default(cuid())
  name        String
  description String?
  key         String   @unique
  keyPrefix   String?
  keyHash     String?
  status      String   @default("ACTIVE")
  permissions Json?
  expiresAt   DateTime?
  isActive    Boolean  @default(true)
  lastUsedAt  DateTime?
  companyId   String
  createdById String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  createdBy User    @relation(fields: [createdById], references: [id])

  @@map("api_keys")
}

model ApiRequest {
  id           String   @id @default(cuid())
  apiKeyId     String
  endpoint     String
  method       String
  status       Int
  statusCode   Int?
  responseTime Int?
  userAgent    String?
  ipAddress    String?
  companyId    String
  createdAt    DateTime @default(now())

  @@map("api_requests")
}

model Subscription {
  id              String            @id @default(cuid())
  plan            String
  amount          Float?
  currency        String            @default("USD")
  interval        String?
  status          SubscriptionStatus @default(ACTIVE)
  currentPeriodStart DateTime
  currentPeriodEnd   DateTime
  cancelAtPeriodEnd  Boolean        @default(false)
  cancelledAt     DateTime?
  nextBillingDate DateTime?
  paymentMethod   Json?
  stripeSubscriptionId String?
  stripeCustomerId String?
  companyId       String
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt

  // Relations
  company Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  payments Payment[]

  @@map("subscriptions")
}

model Payment {
  id            String        @id @default(cuid())
  amount        Float
  currency      String        @default("USD")
  status        PaymentStatus @default(PENDING)
  method        String?
  description   String?
  transactionId String?
  stripePaymentIntentId String?
  paidAt        DateTime?
  failedAt      DateTime?
  invoiceUrl    String?
  receiptUrl    String?
  subscriptionId String?
  companyId     String
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  company      Company       @relation(fields: [companyId], references: [id], onDelete: Cascade)
  subscription Subscription? @relation(fields: [subscriptionId], references: [id])

  @@map("payments")
}

model ContractTemplate {
  id          String   @id @default(cuid())
  name        String
  description String?
  content     String   @db.Text
  category    String?
  status      String   @default("ACTIVE")
  variables   Json?
  isActive    Boolean  @default(true)
  companyId   String
  createdById String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  company   Company    @relation(fields: [companyId], references: [id], onDelete: Cascade)
  createdBy User       @relation(fields: [createdById], references: [id])
  contracts Contract[]

  @@map("contract_templates")
}

model ContractSignature {
  id         String   @id @default(cuid())
  contractId String
  signerName String
  signerEmail String
  signature  String   @db.Text
  signatureData String? @db.Text
  signedAt   DateTime @default(now())
  ipAddress  String?
  userAgent  String?
  signedById String?

  // Relations
  contract Contract @relation(fields: [contractId], references: [id], onDelete: Cascade)
  signedBy User?    @relation(fields: [signedById], references: [id])

  @@map("contract_signatures")
}

model Email {
  id          String      @id @default(cuid())
  subject     String
  body        String      @db.Text
  to          String
  toEmail     String?
  from        String
  cc          String?
  bcc         String?
  type        String?
  status      EmailStatus @default(DRAFT)
  sentAt      DateTime?
  deliveredAt DateTime?
  openedAt    DateTime?
  clickedAt   DateTime?
  failedAt    DateTime?
  readAt      DateTime?
  companyId   String
  createdById String
  sentBy      String?
  customerId  String?
  leadId      String?
  quotationId String?
  invoiceId   String?
  contractId  String?
  templateId  String?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  company   Company   @relation(fields: [companyId], references: [id], onDelete: Cascade)
  createdBy User      @relation("EmailCreatedBy", fields: [createdById], references: [id])
  customer  Customer? @relation(fields: [customerId], references: [id])
  lead      Lead?     @relation(fields: [leadId], references: [id])
  quotation Quotation? @relation(fields: [quotationId], references: [id])
  invoice   Invoice?  @relation(fields: [invoiceId], references: [id])
  contract  Contract? @relation(fields: [contractId], references: [id])

  @@map("emails")
}

model Receipt {
  id            String        @id @default(cuid())
  receiptNumber String        @unique
  title         String
  description   String?
  amount        Float
  currency      String        @default("USD")
  status        ReceiptStatus @default(PENDING)
  paymentMethod String?
  referenceNumber String?
  receivedAt    DateTime?
  paidAt        DateTime?
  notes         String?
  invoiceId     String?
  customerId    String?
  templateId    String?
  companyId     String
  createdById   String
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  company   Company   @relation(fields: [companyId], references: [id], onDelete: Cascade)
  createdBy User      @relation(fields: [createdById], references: [id])
  customer  Customer? @relation(fields: [customerId], references: [id])
  invoice   Invoice?  @relation(fields: [invoiceId], references: [id])
  template  ReceiptTemplate? @relation(fields: [templateId], references: [id])

  @@map("receipts")
}

model SupportTicket {
  id          String              @id @default(cuid())
  ticketNumber String             @unique
  title       String
  description String              @db.Text
  status      SupportTicketStatus @default(OPEN)
  priority    TicketPriority      @default(MEDIUM)
  category    String?
  companyId   String
  createdById String
  assignedToId String?
  slaDeadline DateTime?
  resolvedAt  DateTime?
  createdAt   DateTime            @default(now())
  updatedAt   DateTime            @updatedAt

  // Relations
  company    Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  createdBy  User    @relation("TicketCreatedBy", fields: [createdById], references: [id])
  assignedTo User?   @relation("TicketAssignedTo", fields: [assignedToId], references: [id])
  comments   TicketComment[]
  messages   SupportMessage[]

  @@map("support_tickets")
}

model TicketComment {
  id       String   @id @default(cuid())
  content  String   @db.Text
  ticketId String
  userId   String
  createdAt DateTime @default(now())

  // Relations
  ticket SupportTicket @relation(fields: [ticketId], references: [id], onDelete: Cascade)
  user   User          @relation(fields: [userId], references: [id])

  @@map("ticket_comments")
}

model SupportMessage {
  id        String   @id @default(cuid())
  ticketId  String
  content   String   @db.Text
  isInternal Boolean @default(false)
  isFromCustomer Boolean @default(false)
  createdById String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  ticket    SupportTicket @relation(fields: [ticketId], references: [id], onDelete: Cascade)
  createdBy User          @relation("SupportMessageCreatedBy", fields: [createdById], references: [id])

  @@map("support_messages")
}

model KnowledgeBaseArticle {
  id          String   @id @default(cuid())
  title       String
  slug        String   @unique
  content     String   @db.Text
  category    String?
  tags        String?
  isPublished Boolean  @default(false)
  visibility  String   @default("PUBLIC")
  viewCount   Int      @default(0)
  views       Int      @default(0)
  helpful     Int      @default(0)
  notHelpful  Int      @default(0)
  companyId   String?
  createdById String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  company   Company? @relation(fields: [companyId], references: [id], onDelete: Cascade)
  createdBy User     @relation(fields: [createdById], references: [id])

  @@map("knowledge_base_articles")
}

model Notification {
  id        String             @id @default(cuid())
  title     String
  message   String
  type      NotificationType   @default(INFO)
  status    NotificationStatus @default(UNREAD)
  priority  String?            @default("MEDIUM")
  actionUrl String?
  userId    String
  companyId String?
  createdById String?
  metadata  Json?
  readAt    DateTime?
  createdAt DateTime           @default(now())
  updatedAt DateTime           @updatedAt

  // Relations
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  company   Company? @relation(fields: [companyId], references: [id], onDelete: Cascade)
  createdBy User?    @relation("NotificationCreatedBy", fields: [createdById], references: [id])

  @@map("notifications")
}

model NotificationPreference {
  id        String   @id @default(cuid())
  userId    String   @unique
  email     Boolean  @default(true)
  push      Boolean  @default(true)
  sms       Boolean  @default(false)
  preferences Json?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notification_preferences")
}

model OnboardingProgress {
  id        String   @id @default(cuid())
  userId    String   @unique
  companyId String
  step      String
  currentStep String?
  completed Boolean  @default(false)
  completedSteps Json?
  data      Json?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  company Company @relation(fields: [companyId], references: [id], onDelete: Cascade)

  @@map("onboarding_progress")
}

model Webhook {
  id        String   @id @default(cuid())
  name      String
  description String?
  url       String
  events    Json
  status    String   @default("ACTIVE")
  secret    String?
  companyId String
  createdById String
  lastTriggeredAt DateTime?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  company   Company           @relation(fields: [companyId], references: [id], onDelete: Cascade)
  createdBy User              @relation("WebhookCreatedBy", fields: [createdById], references: [id])
  deliveries WebhookDelivery[]

  @@map("webhooks")
}

model WebhookDelivery {
  id         String   @id @default(cuid())
  webhookId  String
  eventType  String
  payload    Json
  status     String   @default("PENDING")
  success    Boolean  @default(false)
  response   String?
  responseTime Int?
  statusCode Int?
  attempts   Int      @default(0)
  lastAttemptAt DateTime?
  nextRetry  DateTime?
  errorMessage String?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Relations
  webhook Webhook @relation(fields: [webhookId], references: [id], onDelete: Cascade)

  @@map("webhook_deliveries")
}

model EmailTemplate {
  id        String   @id @default(cuid())
  name      String
  subject   String
  body      String   @db.Text
  type      String?
  variables Json?
  isActive  Boolean  @default(true)
  status    String   @default("ACTIVE")
  companyId String
  createdById String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  createdBy User    @relation(fields: [createdById], references: [id])

  @@unique([name, companyId], name: "name_companyId")
  @@map("email_templates")
}

model QuotationTemplate {
  id          String   @id @default(cuid())
  name        String
  description String?
  content     String   @db.Text
  category    String?
  status      String   @default("ACTIVE")
  variables   Json?
  isActive    Boolean  @default(true)
  companyId   String
  createdById String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  company    Company    @relation(fields: [companyId], references: [id], onDelete: Cascade)
  createdBy  User       @relation(fields: [createdById], references: [id])
  quotations Quotation[]

  @@unique([name, companyId], name: "name_companyId")
  @@map("quotation_templates")
}

model InvoiceTemplate {
  id          String   @id @default(cuid())
  name        String
  description String?
  content     String   @db.Text
  category    String?
  status      String   @default("ACTIVE")
  variables   Json?
  isActive    Boolean  @default(true)
  companyId   String
  createdById String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  company  Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  createdBy User   @relation(fields: [createdById], references: [id])
  invoices Invoice[]

  @@unique([name, companyId], name: "name_companyId")
  @@map("invoice_templates")
}

model ReceiptTemplate {
  id          String   @id @default(cuid())
  name        String
  description String?
  content     String   @db.Text
  category    String?
  status      String   @default("ACTIVE")
  variables   Json?
  isActive    Boolean  @default(true)
  companyId   String
  createdById String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  company  Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  createdBy User   @relation(fields: [createdById], references: [id])
  receipts Receipt[]

  @@unique([name, companyId], name: "name_companyId")
  @@map("receipt_templates")
}

model Report {
  id        String   @id @default(cuid())
  name      String
  description String?
  type      String
  category  String?
  dataSource String?
  config    Json
  parameters Json?
  data      Json?
  format    String   @default("pdf")
  status    String   @default("DRAFT")
  isPublic  Boolean  @default(false)
  lastGenerated DateTime?
  downloadCount Int      @default(0)
  companyId String
  createdById String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  createdBy User    @relation(fields: [createdById], references: [id])

  @@map("reports")
}

// Enums
enum Role {
  USER
  ADMIN
  OWNER
  SUPER_ADMIN
}

enum CompanyStatus {
  TRIAL
  ACTIVE
  SUSPENDED
  CANCELLED
}

enum CustomerStatus {
  ACTIVE
  INACTIVE
  PROSPECT
}

enum LeadStatus {
  NEW
  CONTACTED
  QUALIFIED
  PROPOSAL
  NEGOTIATION
  CLOSED_WON
  CLOSED_LOST
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum QuotationStatus {
  DRAFT
  SENT
  VIEWED
  ACCEPTED
  REJECTED
  EXPIRED
}

enum InvoiceStatus {
  DRAFT
  SENT
  VIEWED
  PAID
  OVERDUE
  CANCELLED
}

enum ContractStatus {
  DRAFT
  SENT
  SIGNED
  ACTIVE
  EXECUTED
  COMPLETED
  CANCELLED
  EXPIRED
}

enum DiscountType {
  PERCENTAGE
  FIXED
}

enum ActivityType {
  NOTE
  CALL
  EMAIL
  MEETING
  TASK
  QUOTATION_CREATED
  QUOTATION_SENT
  QUOTATION_VIEWED
  QUOTATION_ACCEPTED
  QUOTATION_REJECTED
  INVOICE_CREATED
  INVOICE_SENT
  INVOICE_VIEWED
  INVOICE_PAID
  CONTRACT_CREATED
  CONTRACT_SENT
  CONTRACT_SIGNED
  CONTRACT_EXECUTED
  CUSTOMER_CREATED
  LEAD_CREATED
  LEAD_CONVERTED
  ITEM_CREATED
  COMPANY
  SUPPORT
  ONBOARDING
  WEBHOOK
  API_KEY
  SUBSCRIPTION
}

enum SubscriptionStatus {
  ACTIVE
  CANCELLED
  PAST_DUE
  UNPAID
}

enum PaymentStatus {
  PENDING
  COMPLETED
  SUCCEEDED
  FAILED
  REFUNDED
}

enum EmailStatus {
  DRAFT
  PENDING
  SENT
  DELIVERED
  OPENED
  CLICKED
  FAILED
}

enum ReceiptStatus {
  PENDING
  RECEIVED
  PROCESSED
  CONFIRMED
  CANCELLED
}

enum SupportTicketStatus {
  OPEN
  IN_PROGRESS
  RESOLVED
  CLOSED
}

enum TicketPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum NotificationType {
  INFO
  SUCCESS
  WARNING
  ERROR
  SYSTEM
  EMAIL
  QUOTATION_ACCEPTED
  CONTRACT_SIGNED
  CUSTOMER_CREATED
  LEAD_CONVERTED
  REMINDER
  INVOICE_OVERDUE
  PAYMENT_RECEIVED
  QUOTATION_EXPIRED
  DEADLINE
}

enum NotificationStatus {
  UNREAD
  READ
  ARCHIVED
}
