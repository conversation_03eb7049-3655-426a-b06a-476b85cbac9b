import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { redirect } from 'next/navigation'
import {
  DollarSign,
  TrendingUp,
  BarChart3,
  ArrowUpRight,
  ArrowDownRight,
  CreditCard,
  Receipt,
  Target,
  Calendar,
  Building2,
  Users
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { formatCurrency, formatDate } from '@/lib/utils'

export default async function SuperAdminRevenuePage() {
  const session = await getServerSession(authOptions)

  // Check if user is super admin
  if (!session?.user?.isSuperAdmin && session?.user?.role !== 'SUPER_ADMIN') {
    redirect('/dashboard')
  }

  // Calculate date ranges
  const now = new Date()
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
  const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
  const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0)
  const startOfYear = new Date(now.getFullYear(), 0, 1)
  const startOfLastYear = new Date(now.getFullYear() - 1, 0, 1)
  const endOfLastYear = new Date(now.getFullYear() - 1, 11, 31)

  // Fetch comprehensive revenue data
  const [
    totalRevenue,
    monthlyRevenue,
    lastMonthRevenue,
    yearlyRevenue,
    lastYearRevenue,
    revenueByPlan,
    topRevenueCompanies,
    recentTransactions,
    monthlyGrowth,
    subscriptionMetrics
  ] = await Promise.all([
    // Total revenue
    prisma.invoice.aggregate({
      _sum: { total: true },
      where: { status: 'PAID' }
    }),

    // This month revenue
    prisma.invoice.aggregate({
      _sum: { total: true },
      where: {
        status: 'PAID',
        paidAt: { gte: startOfMonth }
      }
    }),

    // Last month revenue
    prisma.invoice.aggregate({
      _sum: { total: true },
      where: {
        status: 'PAID',
        paidAt: {
          gte: startOfLastMonth,
          lte: endOfLastMonth
        }
      }
    }),

    // This year revenue
    prisma.invoice.aggregate({
      _sum: { total: true },
      where: {
        status: 'PAID',
        paidAt: { gte: startOfYear }
      }
    }),

    // Last year revenue
    prisma.invoice.aggregate({
      _sum: { total: true },
      where: {
        status: 'PAID',
        paidAt: {
          gte: startOfLastYear,
          lte: endOfLastYear
        }
      }
    }),

    // Revenue by plan
    prisma.company.groupBy({
      by: ['plan'],
      _sum: { totalRevenue: true },
      _count: true
    }),

    // Top revenue companies
    prisma.company.findMany({
      take: 10,
      orderBy: { totalRevenue: 'desc' },
      select: {
        id: true,
        name: true,
        plan: true,
        totalRevenue: true,
        userCount: true,
        createdAt: true
      }
    }),

    // Recent transactions
    prisma.invoice.findMany({
      take: 10,
      where: { status: 'PAID' },
      orderBy: { paidAt: 'desc' },
      include: {
        company_rel: {
          select: { name: true, plan: true }
        }
      }
    }),

    // Monthly growth data (last 6 months)
    Promise.all(
      Array.from({ length: 6 }, (_, i) => {
        const monthStart = new Date(now.getFullYear(), now.getMonth() - i, 1)
        const monthEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 0)
        return prisma.invoice.aggregate({
          _sum: { total: true },
          where: {
            status: 'PAID',
            paidAt: {
              gte: monthStart,
              lte: monthEnd
            }
          }
        }).then(result => ({
          month: monthStart.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
          revenue: result._sum.total || 0
        }))
      })
    ),

    // Subscription metrics
    prisma.company.groupBy({
      by: ['plan'],
      _count: true,
      _avg: { totalRevenue: true }
    })
  ])

  // Calculate growth rates
  const monthlyGrowthRate = (lastMonthRevenue._sum.total || 0) > 0
    ? (((monthlyRevenue._sum.total || 0) - (lastMonthRevenue._sum.total || 0)) / (lastMonthRevenue._sum.total || 0) * 100)
    : 0

  const yearlyGrowthRate = (lastYearRevenue._sum.total || 0) > 0
    ? (((yearlyRevenue._sum.total || 0) - (lastYearRevenue._sum.total || 0)) / (lastYearRevenue._sum.total || 0) * 100)
    : 0

  // Revenue statistics
  const revenueStats = [
    {
      title: 'Total Revenue',
      value: formatCurrency(totalRevenue._sum.total || 0),
      change: 'All time',
      changeType: 'neutral' as const,
      icon: DollarSign,
      color: 'green'
    },
    {
      title: 'Monthly Revenue',
      value: formatCurrency(monthlyRevenue._sum.total || 0),
      change: `${monthlyGrowthRate >= 0 ? '+' : ''}${monthlyGrowthRate.toFixed(1)}%`,
      changeType: monthlyGrowthRate >= 0 ? 'positive' : 'negative' as const,
      icon: TrendingUp,
      color: 'blue'
    },
    {
      title: 'Yearly Revenue',
      value: formatCurrency(yearlyRevenue._sum.total || 0),
      change: `${yearlyGrowthRate >= 0 ? '+' : ''}${yearlyGrowthRate.toFixed(1)}%`,
      changeType: yearlyGrowthRate >= 0 ? 'positive' : 'negative' as const,
      icon: BarChart3,
      color: 'purple'
    },
    {
      title: 'Average Deal Size',
      value: formatCurrency((totalRevenue._sum.total || 0) / Math.max(recentTransactions.length, 1)),
      change: 'Per transaction',
      changeType: 'neutral' as const,
      icon: Target,
      color: 'orange'
    }
  ]

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="flex items-center space-x-4">
        <div className="p-3 bg-green-100 rounded-lg">
          <DollarSign className="h-8 w-8 text-green-600" />
        </div>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Revenue Analytics</h1>
          <p className="text-gray-600">Platform-wide financial metrics and insights</p>
        </div>
      </div>

      {/* Revenue Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {revenueStats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {stat.title}
                </CardTitle>
                <Icon className={`h-4 w-4 text-${stat.color}-600`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <div className={`text-xs flex items-center mt-1 ${
                  stat.changeType === 'positive' ? 'text-green-600' :
                  stat.changeType === 'negative' ? 'text-red-600' : 'text-gray-500'
                }`}>
                  {stat.changeType === 'positive' && <ArrowUpRight className="h-3 w-3 mr-1" />}
                  {stat.changeType === 'negative' && <ArrowDownRight className="h-3 w-3 mr-1" />}
                  {stat.change}
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left Column - Charts and Analytics */}
        <div className="lg:col-span-2 space-y-6">
          {/* Revenue by Plan */}
          <Card>
            <CardHeader>
              <CardTitle>Revenue by Subscription Plan</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {revenueByPlan.map((plan) => {
                  const percentage = ((plan._sum.totalRevenue || 0) / (totalRevenue._sum.total || 1)) * 100
                  return (
                    <div key={plan.plan} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                        <div>
                          <p className="font-medium">{plan.plan} Plan</p>
                          <p className="text-sm text-gray-500">{plan._count} companies</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{formatCurrency(plan._sum.totalRevenue || 0)}</p>
                        <p className="text-sm text-gray-500">{percentage.toFixed(1)}%</p>
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>

          {/* Top Revenue Companies */}
          <Card>
            <CardHeader>
              <CardTitle>Top Revenue Companies</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topRevenueCompanies.map((company, index) => (
                  <div key={company.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-green-600">#{index + 1}</span>
                      </div>
                      <div>
                        <p className="font-medium">{company.name}</p>
                        <p className="text-sm text-gray-500">{company.plan} • {company.userCount} users</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{formatCurrency(company.totalRevenue)}</p>
                      <p className="text-sm text-gray-500">
                        Since {formatDate(company.createdAt)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Recent Transactions and Metrics */}
        <div className="space-y-6">
          {/* Subscription Metrics */}
          <Card>
            <CardHeader>
              <CardTitle>Subscription Metrics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {subscriptionMetrics.map((metric) => (
                <div key={metric.plan} className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">{metric.plan}</p>
                    <p className="text-xs text-gray-500">{metric._count} companies</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium">
                      {formatCurrency(metric._avg.totalRevenue || 0)}
                    </p>
                    <p className="text-xs text-gray-500">avg revenue</p>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Recent Transactions */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Transactions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {recentTransactions.map((transaction) => (
                  <div key={transaction.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <Receipt className="h-4 w-4 text-blue-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium">{transaction.company_rel?.name}</p>
                        <p className="text-xs text-gray-500">
                          {transaction.paidAt ? formatDate(transaction.paidAt) : 'Pending'}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">{formatCurrency(transaction.total)}</p>
                      <Badge variant="outline" className="text-xs">
                        {transaction.company_rel?.plan}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Monthly Growth */}
          <Card>
            <CardHeader>
              <CardTitle>Monthly Growth Trend</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {monthlyGrowth.reverse().map((month, index) => (
                  <div key={month.month} className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">{month.month}</span>
                    <span className="text-sm font-medium">{formatCurrency(month.revenue)}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
