import { PrismaClient } from '@prisma/client'
import { hash } from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seed...')

  // Create demo company and user
  const hashedPassword = await hash('password123', 12)
  
  const company = await prisma.company.create({
    data: {
      name: 'Demo Company Ltd.',
      email: '<EMAIL>',
      phone: '+91 9876543210',
      address: '123 Business Street',
      city: 'Mumbai',
      state: 'Maharashtra',
      country: 'India',
      postalCode: '400001',
      website: 'https://democompany.com',
      industry: 'Technology',
      size: '10-50',
      businessType: 'Private Limited',
      taxId: 'GST123456789',
      status: 'TRIAL',
      trialEndsAt: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
      owner: {
        create: {
          firstName: 'Trial',
          lastName: 'User',
          name: 'Trial User',
          email: '<EMAIL>',
          role: 'ADMIN',
        }
      }
    },
    include: {
      owner: true
    }
  })

  console.log('✅ Created company and user')

  // Create sample customers
  const customers = await Promise.all([
    prisma.customer.create({
      data: {
        name: 'Acme Corporation',
        email: '<EMAIL>',
        phone: '+91 9876543210',
        company: 'Acme Corp',
        address: '456 Corporate Ave',
        city: 'Delhi',
        state: 'Delhi',
        country: 'India',
        postalCode: '110001',
        status: 'ACTIVE',
        companyId: company.id,
        createdById: company.ownerId,
      }
    }),
    prisma.customer.create({
      data: {
        name: 'Tech Solutions Pvt Ltd',
        email: '<EMAIL>',
        phone: '+91 9876543211',
        company: 'Tech Solutions',
        address: '789 Tech Park',
        city: 'Bangalore',
        state: 'Karnataka',
        country: 'India',
        postalCode: '560001',
        status: 'ACTIVE',
        companyId: company.id,
        createdById: company.ownerId,
      }
    }),
    prisma.customer.create({
      data: {
        name: 'Global Enterprises',
        email: '<EMAIL>',
        phone: '+91 9876543212',
        company: 'Global Enterprises',
        address: '321 Business District',
        city: 'Chennai',
        state: 'Tamil Nadu',
        country: 'India',
        postalCode: '600001',
        status: 'PROSPECT',
        companyId: company.id,
        createdById: company.ownerId,
      }
    })
  ])

  console.log('✅ Created sample customers')

  // Create sample items
  const items = await Promise.all([
    prisma.item.create({
      data: {
        name: 'Web Development Service',
        description: 'Complete web development with modern technologies',
        price: 50000,
        category: 'Development',
        sku: 'WEB-DEV-001',
        unit: 'Project',
        taxRate: 18,
        companyId: company.id,
        createdById: company.ownerId,
      }
    }),
    prisma.item.create({
      data: {
        name: 'Mobile App Development',
        description: 'Native iOS and Android app development',
        price: 75000,
        category: 'Development',
        sku: 'MOB-DEV-001',
        unit: 'Project',
        taxRate: 18,
        companyId: company.id,
        createdById: company.ownerId,
      }
    }),
    prisma.item.create({
      data: {
        name: 'Consulting Hours',
        description: 'Business and technical consulting services',
        price: 2500,
        category: 'Consulting',
        sku: 'CONS-HR-001',
        unit: 'Hour',
        taxRate: 18,
        companyId: company.id,
        createdById: company.ownerId,
      }
    }),
    prisma.item.create({
      data: {
        name: 'UI/UX Design',
        description: 'User interface and experience design',
        price: 25000,
        category: 'Design',
        sku: 'UIUX-001',
        unit: 'Project',
        taxRate: 18,
        companyId: company.id,
        createdById: company.ownerId,
      }
    }),
    prisma.item.create({
      data: {
        name: 'SEO Optimization',
        description: 'Search engine optimization services',
        price: 15000,
        category: 'Marketing',
        sku: 'SEO-001',
        unit: 'Month',
        taxRate: 18,
        companyId: company.id,
        createdById: company.ownerId,
      }
    })
  ])

  console.log('✅ Created sample items')

  // Create sample leads
  const leads = await Promise.all([
    prisma.lead.create({
      data: {
        title: 'E-commerce Website Development',
        description: 'Complete e-commerce platform with payment integration',
        status: 'NEW',
        priority: 'HIGH',
        value: 150000,
        source: 'Website',
        customerId: customers[0].id,
        companyId: company.id,
        createdById: company.ownerId,
      }
    }),
    prisma.lead.create({
      data: {
        title: 'Mobile App for Delivery Service',
        description: 'On-demand delivery mobile application',
        status: 'CONTACTED',
        priority: 'MEDIUM',
        value: 200000,
        source: 'Referral',
        customerId: customers[1].id,
        companyId: company.id,
        createdById: company.ownerId,
      }
    }),
    prisma.lead.create({
      data: {
        title: 'Digital Marketing Campaign',
        description: 'Complete digital marketing and SEO services',
        status: 'QUALIFIED',
        priority: 'MEDIUM',
        value: 50000,
        source: 'Social Media',
        customerId: customers[2].id,
        companyId: company.id,
        createdById: company.ownerId,
      }
    })
  ])

  console.log('✅ Created sample leads')

  // Create sample quotations
  const quotations = await Promise.all([
    prisma.quotation.create({
      data: {
        quotationNumber: 'QUO-202401-0001',
        title: 'E-commerce Development Proposal',
        description: 'Complete e-commerce website with modern features',
        status: 'SENT',
        validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        terms: 'Payment terms: 50% advance, 50% on completion',
        paymentTerms: 'Net 30 days from invoice date',
        taxRate: 18,
        discountType: 'PERCENTAGE',
        discountValue: 10,
        subtotal: 125000,
        total: 132750,
        customerId: customers[0].id,
        leadId: leads[0].id,
        companyId: company.id,
        createdById: company.ownerId,
        items: {
          create: [
            {
              itemId: items[0].id,
              name: items[0].name,
              description: items[0].description || '',
              quantity: 1,
              unitPrice: items[0].price,
              discount: 0,
              taxRate: 18,
            },
            {
              itemId: items[3].id,
              name: items[3].name,
              description: items[3].description || '',
              quantity: 3,
              unitPrice: items[3].price,
              discount: 0,
              taxRate: 18,
            }
          ]
        }
      }
    }),
    prisma.quotation.create({
      data: {
        quotationNumber: 'QUO-202401-0002',
        title: 'Mobile App Development Quote',
        description: 'Native mobile application development',
        status: 'DRAFT',
        validUntil: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000),
        terms: 'Payment terms: 30% advance, 40% on milestone, 30% on completion',
        paymentTerms: 'Net 15 days from invoice date',
        taxRate: 18,
        discountType: 'FIXED',
        discountValue: 5000,
        subtotal: 75000,
        total: 82650,
        customerId: customers[1].id,
        leadId: leads[1].id,
        companyId: company.id,
        createdById: company.ownerId,
        items: {
          create: [
            {
              itemId: items[1].id,
              name: items[1].name,
              description: items[1].description || '',
              quantity: 1,
              unitPrice: items[1].price,
              discount: 0,
              taxRate: 18,
            }
          ]
        }
      }
    })
  ])

  console.log('✅ Created sample quotations')

  // Create sample invoices
  const invoices = await Promise.all([
    prisma.invoice.create({
      data: {
        invoiceNumber: 'INV-202401-0001',
        title: 'Consulting Services - January',
        description: 'Business consulting services for January 2024',
        status: 'SENT',
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        terms: 'Payment due within 30 days',
        paymentTerms: 'Net 30 days',
        taxRate: 18,
        discountType: 'PERCENTAGE',
        discountValue: 0,
        subtotal: 25000,
        total: 29500,
        paidAmount: 0,
        customerId: customers[0].id,
        companyId: company.id,
        createdById: company.ownerId,
        items: {
          create: [
            {
              itemId: items[2].id,
              name: items[2].name,
              description: items[2].description || '',
              quantity: 10,
              unitPrice: items[2].price,
              discount: 0,
              taxRate: 18,
            }
          ]
        }
      }
    })
  ])

  console.log('✅ Created sample invoices')

  // Create sample contracts
  const contracts = await Promise.all([
    prisma.contract.create({
      data: {
        contractNumber: 'CON-202401-0001',
        title: 'Annual Development Services Contract',
        description: 'Ongoing development and maintenance services',
        status: 'ACTIVE',
        startDate: new Date(),
        endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
        terms: 'Annual contract with monthly billing',
        value: 600000,
        customerId: customers[1].id,
        companyId: company.id,
        createdById: company.ownerId,
        items: {
          create: [
            {
              itemId: items[0].id,
              name: 'Monthly Development Services',
              description: 'Ongoing development and support',
              quantity: 12,
              unitPrice: 50000,
              discount: 0,
              taxRate: 18,
            }
          ]
        }
      }
    })
  ])

  console.log('✅ Created sample contracts')

  // Create sample activities
  await Promise.all([
    prisma.activity.create({
      data: {
        type: 'CUSTOMER_CREATED',
        title: 'New customer added',
        description: `Customer "${customers[0].name}" was added to the system`,
        customerId: customers[0].id,
        companyId: company.id,
        createdById: company.ownerId,
      }
    }),
    prisma.activity.create({
      data: {
        type: 'LEAD_CREATED',
        title: 'New lead created',
        description: `Lead "${leads[0].title}" was created`,
        leadId: leads[0].id,
        customerId: customers[0].id,
        companyId: company.id,
        createdById: company.ownerId,
      }
    }),
    prisma.activity.create({
      data: {
        type: 'QUOTATION_CREATED',
        title: 'Quotation created',
        description: `Quotation "${quotations[0].quotationNumber}" was created`,
        quotationId: quotations[0].id,
        customerId: customers[0].id,
        companyId: company.id,
        createdById: company.ownerId,
      }
    }),
    prisma.activity.create({
      data: {
        type: 'QUOTATION_SENT',
        title: 'Quotation sent to customer',
        description: `Quotation "${quotations[0].quotationNumber}" was sent to ${customers[0].name}`,
        quotationId: quotations[0].id,
        customerId: customers[0].id,
        companyId: company.id,
        createdById: company.ownerId,
      }
    }),
    prisma.activity.create({
      data: {
        type: 'NOTE',
        title: 'Welcome to BusinessSaaS!',
        description: 'Your account has been set up with sample data. You can start exploring the features right away.',
        companyId: company.id,
        createdById: company.ownerId,
      }
    })
  ])

  console.log('✅ Created sample activities')
  console.log('🎉 Database seeded successfully!')
  console.log(`
📧 Demo Login Credentials:
   Email: <EMAIL>
   Password: password123
   
🏢 Company: ${company.name}
👤 User: ${company.owner?.name || 'Trial User'}
  `)
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
