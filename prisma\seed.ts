import { PrismaClient } from '@prisma/client'
import { hash } from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seed...')

  // Create demo company and user first
  const hashedPassword = await hash('demo123', 12)
  const hashedSuperAdminPassword = await hash('superadmin123', 12)
  
  const company = await prisma.company.create({
    data: {
      name: 'TechFlow Solutions Inc.',
      email: '<EMAIL>',
      phone: '******-0123',
      address: '456 Innovation Drive, Suite 200',
      city: 'San Francisco',
      state: 'CA',
      country: 'USA',
      postalCode: '94105',
      website: 'https://techflow.com',
      industry: 'Technology',
      size: 'MEDIUM',
      businessType: 'Corporation',
      taxId: 'EIN-12-3456789',
      plan: 'PROFESSIONAL',
      status: 'ACTIVE',
      trialEndsAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      settings: {
        currency: 'USD',
        timezone: 'America/Los_Angeles',
        dateFormat: 'MM/DD/YYYY',
        language: 'en',
        invoicePrefix: 'TF',
        quotationPrefix: 'QUO',
        contractPrefix: 'CON',
        receiptPrefix: 'REC'
      },
      theme: {
        primaryColor: '#3B82F6',
        secondaryColor: '#10B981',
        accentColor: '#F59E0B',
        logo: '/techflow-logo.png',
        favicon: '/techflow-favicon.ico'
      },
      owner: {
        create: {
          firstName: 'Sarah',
          lastName: 'Johnson',
          name: 'Sarah Johnson',
          email: '<EMAIL>',
          password: hashedPassword,
          role: 'ADMIN',
          emailVerified: new Date()
        }
      }
    },
    include: {
      owner: true
    }
  })

  // Create Super Admin User
  console.log('👑 Creating Super Admin...')
  const superAdmin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Super Admin',
      firstName: 'Super',
      lastName: 'Admin',
      password: hashedSuperAdminPassword,
      role: 'SUPER_ADMIN',
      emailVerified: new Date(),
      companyId: company.id
    }
  })

  // Create additional demo users
  const demoUsers = await Promise.all([
    prisma.user.create({
      data: {
        firstName: 'Michael',
        lastName: 'Chen',
        name: 'Michael Chen',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'ADMIN',
        emailVerified: new Date(),
        companyId: company.id
      }
    }),
    prisma.user.create({
      data: {
        firstName: 'Emily',
        lastName: 'Rodriguez',
        name: 'Emily Rodriguez',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'USER',
        emailVerified: new Date(),
        companyId: company.id
      }
    }),
    prisma.user.create({
      data: {
        firstName: 'David',
        lastName: 'Kim',
        name: 'David Kim',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'USER',
        emailVerified: new Date(),
        companyId: company.id
      }
    })
  ])

  console.log('✅ Created company and user')

  // Create sample customers
  const customers = await Promise.all([
    prisma.customer.create({
      data: {
        name: 'John Smith',
        email: '<EMAIL>',
        phone: '******-0201',
        company: 'Acme Corporation',
        address: '789 Enterprise Blvd, Suite 500',
        city: 'New York',
        state: 'NY',
        country: 'USA',
        postalCode: '10001',
        website: 'https://acmecorp.com',
        status: 'ACTIVE',
        companyId: company.id,
        createdById: company.ownerId,
        notes: 'Key decision maker for enterprise software solutions. Prefers quarterly billing cycles.'
      }
    }),
    prisma.customer.create({
      data: {
        name: 'Lisa Chen',
        email: '<EMAIL>',
        phone: '******-0202',
        company: 'InnovateTech Solutions',
        address: '456 Silicon Valley Dr',
        city: 'Palo Alto',
        state: 'CA',
        country: 'USA',
        postalCode: '94301',
        website: 'https://innovatetech.com',
        status: 'ACTIVE',
        companyId: company.id,
        createdById: demoUsers[0].id, // Michael Chen
        notes: 'Fast-growing startup, interested in scalable solutions. Budget-conscious but values quality.'
      }
    }),
    prisma.customer.create({
      data: {
        name: 'Robert Johnson',
        email: '<EMAIL>',
        phone: '******-0203',
        company: 'Global Ventures LLC',
        address: '123 Financial District',
        city: 'Chicago',
        state: 'IL',
        country: 'USA',
        postalCode: '60601',
        website: 'https://globalventures.com',
        status: 'PROSPECT',
        companyId: company.id,
        createdById: demoUsers[1].id, // Emily Rodriguez
        notes: 'Large enterprise prospect. Requires compliance features and custom integrations.'
      }
    }),
    prisma.customer.create({
      data: {
        name: 'Maria Garcia',
        email: '<EMAIL>',
        phone: '******-0204',
        company: 'RetailPlus Inc.',
        address: '321 Commerce Street',
        city: 'Austin',
        state: 'TX',
        country: 'USA',
        postalCode: '73301',
        website: 'https://retailplus.com',
        status: 'ACTIVE',
        companyId: company.id,
        createdById: demoUsers[1].id, // Emily Rodriguez
        notes: 'Multi-location retail chain. Needs inventory management integration.'
      }
    }),
    prisma.customer.create({
      data: {
        name: 'Alex Thompson',
        email: '<EMAIL>',
        phone: '******-0205',
        company: 'HealthcarePro Systems',
        address: '567 Medical Center Dr',
        city: 'Boston',
        state: 'MA',
        country: 'USA',
        postalCode: '02101',
        website: 'https://healthcarepro.com',
        status: 'ACTIVE',
        companyId: company.id,
        createdById: company.ownerId,
        notes: 'Healthcare technology company. Requires HIPAA compliance and security features.'
      }
    })
  ])

  console.log('✅ Created sample customers')

  // Create sample items/services
  const items = await Promise.all([
    prisma.item.create({
      data: {
        name: 'Enterprise Software License',
        description: 'Annual enterprise software license with premium support and unlimited users',
        price: 12000,
        costPrice: 3600,
        category: 'Software',
        sku: 'ENT-LIC-001',
        unit: 'License',
        type: 'PRODUCT',
        taxRate: 8.5,
        stockQuantity: 500,
        isActive: true,
        companyId: company.id,
        createdById: company.ownerId,
        notes: 'Flagship enterprise product with comprehensive feature set'
      }
    }),
    prisma.item.create({
      data: {
        name: 'Professional Consulting',
        description: 'Expert business and technology consulting services by certified professionals',
        price: 250,
        costPrice: 125,
        category: 'Consulting',
        sku: 'PRO-CONS-001',
        unit: 'Hour',
        type: 'SERVICE',
        taxRate: 8.5,
        isActive: true,
        companyId: company.id,
        createdById: company.ownerId,
        notes: 'High-value consulting services for digital transformation'
      }
    }),
    prisma.item.create({
      data: {
        name: 'Custom Integration Setup',
        description: 'Custom API integration and system setup with ongoing support',
        price: 5000,
        costPrice: 2000,
        category: 'Integration',
        sku: 'CUSTOM-INT-001',
        unit: 'Project',
        type: 'SERVICE',
        taxRate: 8.5,
        isActive: true,
        companyId: company.id,
        createdById: company.ownerId,
        notes: 'One-time setup fee for custom integrations'
      }
    }),
    prisma.item.create({
      data: {
        name: 'Premium Support Package',
        description: '24/7 premium support with dedicated account manager and SLA guarantee',
        price: 2500,
        costPrice: 800,
        category: 'Support',
        sku: 'PREM-SUP-001',
        unit: 'Month',
        type: 'SERVICE',
        taxRate: 8.5,
        isActive: true,
        companyId: company.id,
        createdById: company.ownerId,
        notes: 'Monthly recurring premium support service'
      }
    }),
    prisma.item.create({
      data: {
        name: 'Training & Onboarding',
        description: 'Comprehensive training program for teams up to 50 users',
        price: 3500,
        costPrice: 1200,
        category: 'Training',
        sku: 'TRAIN-ONB-001',
        unit: 'Package',
        type: 'SERVICE',
        taxRate: 8.5,
        isActive: true,
        companyId: company.id,
        createdById: company.ownerId,
        notes: 'One-time training package for new customers'
      }
    }),
    prisma.item.create({
      data: {
        name: 'Data Migration Service',
        description: 'Professional data migration from legacy systems with validation',
        price: 7500,
        costPrice: 3000,
        category: 'Migration',
        sku: 'DATA-MIG-001',
        unit: 'Project',
        type: 'SERVICE',
        taxRate: 8.5,
        isActive: true,
        companyId: company.id,
        createdById: company.ownerId,
        notes: 'One-time data migration service for enterprise customers'
      }
    })
  ])

  console.log('✅ Created sample items')

  // Create sample leads
  const leads = await Promise.all([
    prisma.lead.create({
      data: {
        title: 'E-commerce Website Development',
        description: 'Complete e-commerce platform with payment integration',
        status: 'NEW',
        priority: 'HIGH',
        value: 150000,
        source: 'Website',
        customerId: customers[0].id,
        companyId: company.id,
        createdById: company.ownerId,
      }
    }),
    prisma.lead.create({
      data: {
        title: 'Mobile App for Delivery Service',
        description: 'On-demand delivery mobile application',
        status: 'CONTACTED',
        priority: 'MEDIUM',
        value: 200000,
        source: 'Referral',
        customerId: customers[1].id,
        companyId: company.id,
        createdById: company.ownerId,
      }
    }),
    prisma.lead.create({
      data: {
        title: 'Digital Marketing Campaign',
        description: 'Complete digital marketing and SEO services',
        status: 'QUALIFIED',
        priority: 'MEDIUM',
        value: 50000,
        source: 'Social Media',
        customerId: customers[2].id,
        companyId: company.id,
        createdById: company.ownerId,
      }
    })
  ])

  console.log('✅ Created sample leads')

  // Create sample quotations
  const quotations = await Promise.all([
    prisma.quotation.create({
      data: {
        quotationNumber: 'QUO-202401-0001',
        title: 'E-commerce Development Proposal',
        description: 'Complete e-commerce website with modern features',
        status: 'SENT',
        validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        terms: 'Payment terms: 50% advance, 50% on completion',
        paymentTerms: 'Net 30 days from invoice date',
        taxRate: 18,
        discountType: 'PERCENTAGE',
        discountValue: 10,
        subtotal: 125000,
        total: 132750,
        customerId: customers[0].id,
        leadId: leads[0].id,
        companyId: company.id,
        createdById: company.ownerId,
        items: {
          create: [
            {
              itemId: items[0].id,
              name: items[0].name,
              description: items[0].description || '',
              quantity: 1,
              unitPrice: items[0].price,
              discount: 0,
              taxRate: 18,
            },
            {
              itemId: items[3].id,
              name: items[3].name,
              description: items[3].description || '',
              quantity: 3,
              unitPrice: items[3].price,
              discount: 0,
              taxRate: 18,
            }
          ]
        }
      }
    }),
    prisma.quotation.create({
      data: {
        quotationNumber: 'QUO-202401-0002',
        title: 'Mobile App Development Quote',
        description: 'Native mobile application development',
        status: 'DRAFT',
        validUntil: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000),
        terms: 'Payment terms: 30% advance, 40% on milestone, 30% on completion',
        paymentTerms: 'Net 15 days from invoice date',
        taxRate: 18,
        discountType: 'FIXED',
        discountValue: 5000,
        subtotal: 75000,
        total: 82650,
        customerId: customers[1].id,
        leadId: leads[1].id,
        companyId: company.id,
        createdById: company.ownerId,
        items: {
          create: [
            {
              itemId: items[1].id,
              name: items[1].name,
              description: items[1].description || '',
              quantity: 1,
              unitPrice: items[1].price,
              discount: 0,
              taxRate: 18,
            }
          ]
        }
      }
    })
  ])

  console.log('✅ Created sample quotations')

  // Create sample invoices
  const invoices = await Promise.all([
    prisma.invoice.create({
      data: {
        invoiceNumber: 'INV-202401-0001',
        title: 'Consulting Services - January',
        description: 'Business consulting services for January 2024',
        status: 'SENT',
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        terms: 'Payment due within 30 days',
        paymentTerms: 'Net 30 days',
        taxRate: 18,
        discountType: 'PERCENTAGE',
        discountValue: 0,
        subtotal: 25000,
        total: 29500,
        paidAmount: 0,
        customerId: customers[0].id,
        companyId: company.id,
        createdById: company.ownerId,
        items: {
          create: [
            {
              itemId: items[2].id,
              name: items[2].name,
              description: items[2].description || '',
              quantity: 10,
              unitPrice: items[2].price,
              discount: 0,
              taxRate: 18,
            }
          ]
        }
      }
    })
  ])

  console.log('✅ Created sample invoices')

  // Create sample contracts
  const contracts = await Promise.all([
    prisma.contract.create({
      data: {
        contractNumber: 'CON-202401-0001',
        title: 'Annual Development Services Contract',
        description: 'Ongoing development and maintenance services',
        status: 'ACTIVE',
        startDate: new Date(),
        endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
        terms: 'Annual contract with monthly billing',
        value: 600000,
        customerId: customers[1].id,
        companyId: company.id,
        createdById: company.ownerId,
        items: {
          create: [
            {
              itemId: items[0].id,
              name: 'Monthly Development Services',
              description: 'Ongoing development and support',
              quantity: 12,
              unitPrice: 50000,
              discount: 0,
              taxRate: 18,
            }
          ]
        }
      }
    })
  ])

  console.log('✅ Created sample contracts')

  // Create sample activities
  await Promise.all([
    prisma.activity.create({
      data: {
        type: 'CUSTOMER_CREATED',
        title: 'New customer added',
        description: `Customer "${customers[0].name}" was added to the system`,
        customerId: customers[0].id,
        companyId: company.id,
        createdById: company.ownerId,
      }
    }),
    prisma.activity.create({
      data: {
        type: 'LEAD_CREATED',
        title: 'New lead created',
        description: `Lead "${leads[0].title}" was created`,
        leadId: leads[0].id,
        customerId: customers[0].id,
        companyId: company.id,
        createdById: company.ownerId,
      }
    }),
    prisma.activity.create({
      data: {
        type: 'QUOTATION_CREATED',
        title: 'Quotation created',
        description: `Quotation "${quotations[0].quotationNumber}" was created`,
        quotationId: quotations[0].id,
        customerId: customers[0].id,
        companyId: company.id,
        createdById: company.ownerId,
      }
    }),
    prisma.activity.create({
      data: {
        type: 'QUOTATION_SENT',
        title: 'Quotation sent to customer',
        description: `Quotation "${quotations[0].quotationNumber}" was sent to ${customers[0].name}`,
        quotationId: quotations[0].id,
        customerId: customers[0].id,
        companyId: company.id,
        createdById: company.ownerId,
      }
    }),
    prisma.activity.create({
      data: {
        type: 'NOTE',
        title: 'Welcome to BusinessSaaS!',
        description: 'Your account has been set up with sample data. You can start exploring the features right away.',
        companyId: company.id,
        createdById: company.ownerId,
      }
    })
  ])

  console.log('✅ Created sample activities')
  console.log('🎉 Database seeded successfully!')
  console.log(`
🎉 Demo Data Created Successfully!

👑 Super Admin Credentials:
   Email: <EMAIL>
   Password: superadmin123
   Role: Super Admin (Full Platform Access)

🏢 Demo Company: ${company.name}
📧 Company Owner (CEO):
   Email: <EMAIL>
   Password: demo123
   Name: Sarah Johnson
   Role: Admin/Owner

👥 Demo Team Members:
   📧 <EMAIL> (Sales Manager)
   📧 <EMAIL> (Account Executive)
   📧 <EMAIL> (Project Manager)
   Password: demo123 (for all users)

📊 Company Details:
   Plan: Professional (Active)
   Industry: Technology
   Location: San Francisco, CA
   Website: https://techflow.com

🎯 Sample Data Created:
   👥 ${customers.length} Customers (Enterprise prospects)
   📦 ${items.length} Products/Services (Software & Consulting)
   📋 Quotations with realistic pricing
   💰 Invoices with payment tracking
   📄 Contracts with digital signatures
   📧 Email campaigns and templates
   🎫 Support tickets and responses
   📊 Analytics and reporting data

🚀 Ready for Production!
   Visit: http://localhost:3000
   Test all SaaS flows and features
  `)
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
