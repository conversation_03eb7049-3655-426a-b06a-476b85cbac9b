'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { formatDate, getStatusColor } from '@/lib/utils'
import { 
  PenTool, 
  CheckCircle, 
  XCircle,
  User,
  Calendar,
  MessageSquare,
  Send,
  Download
} from 'lucide-react'
import toast from 'react-hot-toast'

interface ContractSignatureProps {
  contract: {
    id: string
    contractNumber: string
    title: string
    status: string
    customer: {
      name: string
      email: string | null
    }
    signatures: Array<{
      id: string
      signedAt: Date
      signedBy: string
      ipAddress: string | null
      userAgent: string | null
      signatureData: string | null
    }>
    createdAt: Date
  }
}

export function ContractSignature({ contract }: ContractSignatureProps) {
  const [isProcessing, setIsProcessing] = useState(false)
  const [signatureData, setSignatureData] = useState('')
  const [signerName, setSignerName] = useState(contract.customer.name)
  const [signerEmail, setSignerEmail] = useState(contract.customer.email || '')
  const [comments, setComments] = useState('')

  const handleSign = async () => {
    if (!signatureData.trim()) {
      toast.error('Please provide a signature')
      return
    }

    if (!signerName.trim()) {
      toast.error('Please provide signer name')
      return
    }

    setIsProcessing(true)
    try {
      const response = await fetch(`/api/contracts/${contract.id}/sign`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          signatureData,
          signerName,
          signerEmail,
          comments,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to sign contract')
      }

      toast.success('Contract signed successfully')
      window.location.reload()
    } catch (error) {
      console.error('Error signing contract:', error)
      toast.error('Failed to sign contract')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleReject = async () => {
    if (!comments.trim()) {
      toast.error('Please provide a reason for rejection')
      return
    }

    setIsProcessing(true)
    try {
      const response = await fetch(`/api/contracts/${contract.id}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          comments,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to reject contract')
      }

      toast.success('Contract rejected')
      window.location.reload()
    } catch (error) {
      console.error('Error rejecting contract:', error)
      toast.error('Failed to reject contract')
    } finally {
      setIsProcessing(false)
    }
  }

  const isSignable = contract.status === 'SENT' || contract.status === 'VIEWED'
  const isSigned = contract.status === 'SIGNED'
  const isRejected = contract.status === 'REJECTED'

  return (
    <div className="space-y-6">
      {/* Contract Status */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <PenTool className="h-5 w-5" />
              <span>Contract Signature</span>
            </CardTitle>
            <Badge className={getStatusColor(contract.status)} variant="outline">
              {contract.status}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-gray-600">
            <p>Contract: {contract.title}</p>
            <p>Contract Number: {contract.contractNumber}</p>
            <p>Created: {formatDate(contract.createdAt)}</p>
          </div>
        </CardContent>
      </Card>

      {/* Existing Signatures */}
      {contract.signatures.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <span>Signatures</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {contract.signatures.map((signature) => (
                <div key={signature.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <User className="h-4 w-4 text-gray-400" />
                      <span className="font-medium">{signature.signedBy}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-500">
                      <Calendar className="h-4 w-4" />
                      <span>{formatDate(signature.signedAt)}</span>
                    </div>
                  </div>
                  {signature.signatureData && (
                    <div className="mt-2">
                      <p className="text-sm text-gray-600 mb-2">Digital Signature:</p>
                      <div className="bg-gray-50 p-2 rounded border text-sm font-mono">
                        {signature.signatureData.substring(0, 100)}...
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Signature Form */}
      {isSignable && (
        <Card>
          <CardHeader>
            <CardTitle>Sign Contract</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="signerName">Full Name</Label>
                <Input
                  id="signerName"
                  value={signerName}
                  onChange={(e) => setSignerName(e.target.value)}
                  placeholder="Enter your full name"
                />
              </div>
              <div>
                <Label htmlFor="signerEmail">Email Address</Label>
                <Input
                  id="signerEmail"
                  type="email"
                  value={signerEmail}
                  onChange={(e) => setSignerEmail(e.target.value)}
                  placeholder="Enter your email address"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="signature">Digital Signature</Label>
              <Textarea
                id="signature"
                value={signatureData}
                onChange={(e) => setSignatureData(e.target.value)}
                placeholder="Type your full name as your digital signature"
                rows={3}
              />
              <p className="text-xs text-gray-500 mt-1">
                By typing your name above, you agree to electronically sign this contract.
              </p>
            </div>

            <div>
              <Label htmlFor="comments">Comments (Optional)</Label>
              <Textarea
                id="comments"
                value={comments}
                onChange={(e) => setComments(e.target.value)}
                placeholder="Add any comments or notes"
                rows={3}
              />
            </div>

            <div className="flex items-center space-x-4 pt-4">
              <Button
                onClick={handleSign}
                disabled={isProcessing}
                className="flex items-center space-x-2"
              >
                <CheckCircle className="h-4 w-4" />
                <span>{isProcessing ? 'Signing...' : 'Sign Contract'}</span>
              </Button>
              <Button
                variant="outline"
                onClick={handleReject}
                disabled={isProcessing}
                className="flex items-center space-x-2"
              >
                <XCircle className="h-4 w-4" />
                <span>{isProcessing ? 'Processing...' : 'Reject Contract'}</span>
              </Button>
            </div>

            <div className="text-xs text-gray-500 pt-2 border-t">
              <p>
                By signing this contract electronically, you acknowledge that:
              </p>
              <ul className="list-disc list-inside mt-1 space-y-1">
                <li>You have read and understood the terms of this contract</li>
                <li>You agree to be bound by the terms and conditions</li>
                <li>Your electronic signature has the same legal effect as a handwritten signature</li>
                <li>You are authorized to enter into this agreement</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Contract Signed */}
      {isSigned && (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="pt-6">
            <div className="text-center">
              <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-green-900 mb-2">
                Contract Signed Successfully
              </h3>
              <p className="text-green-700">
                This contract has been electronically signed and is now legally binding.
              </p>
              <div className="mt-4">
                <Button variant="outline" >
                  <Download className="h-4 w-4 mr-2" />
                  Download Signed Contract
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Contract Rejected */}
      {isRejected && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="text-center">
              <XCircle className="h-12 w-12 text-red-600 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-red-900 mb-2">
                Contract Rejected
              </h3>
              <p className="text-red-700">
                This contract has been rejected and is no longer active.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
