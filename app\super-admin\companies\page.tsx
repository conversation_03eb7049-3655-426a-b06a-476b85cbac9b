import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { CompaniesManagement } from '@/components/super-admin/companies-management'
import { CompaniesStats } from '@/components/super-admin/companies-stats'
import { CompaniesFilters } from '@/components/super-admin/companies-filters'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Building2, Plus } from 'lucide-react'
import Link from 'next/link'
import { redirect } from 'next/navigation'

interface SearchParams {
  plan?: string
  status?: string
  search?: string
  page?: string
}

export default async function SuperAdminCompaniesPage({
  searchParams,
}: {
  searchParams: SearchParams
}) {
  const session = await getServerSession(authOptions)
  
  // Check if user is super admin
  if (!session?.user?.isSuperAdmin) {
    redirect('/dashboard')
  }

  const plan = searchParams.plan || ''
  const status = searchParams.status || ''
  const search = searchParams.search || ''
  const page = parseInt(searchParams.page || '1')
  const limit = 20
  const offset = (page - 1) * limit

  // Build where clause for companies
  const where: any = {}

  if (plan) {
    where.plan = plan
  }

  if (status === 'active') {
    where.users = {
      some: {
        lastLoginAt: { gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
      }
    }
  } else if (status === 'inactive') {
    where.OR = [
      {
        users: {
          none: {}
        }
      },
      {
        users: {
          every: {
            OR: [
              { lastLoginAt: null },
              { lastLoginAt: { lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } }
            ]
          }
        }
      }
    ]
  }

  if (search) {
    where.OR = [
      { name: { contains: search } },
      { email: { contains: search } },
      { domain: { contains: search } },
    ]
  }

  // Fetch companies and statistics
  const [companies, totalCount, stats] = await Promise.all([
    prisma.company.findMany({
      where,
      include: {
        _count: {
          select: {
            users: true,
            customers: true,
            quotations: true,
            invoices: true,
            contracts: true
          }
        },
        users: {
          where: { role: 'OWNER' },
          select: { name: true, firstName: true, lastName: true, email: true, lastLoginAt: true },
          take: 1
        },
        invoices: {
          where: { status: 'PAID' },
          select: { total: true }
        }
      },
      orderBy: { createdAt: 'desc' },
      skip: offset,
      take: limit,
    }),
    prisma.company.count({ where }),
    
    // Get company statistics
    Promise.all([
      prisma.company.count(),
      prisma.company.count({
        where: {
          users: {
            some: {
              lastLoginAt: { gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
            }
          }
        }
      }),
      prisma.company.groupBy({
        by: ['plan'],
        _count: true
      }),
      prisma.company.aggregate({
        _avg: {
          createdAt: true
        }
      }),
      // Revenue statistics
      prisma.company.findMany({
        include: {
          invoices: {
            where: { status: 'PAID' },
            select: { total: true }
          }
        }
      })
    ])
  ])

  const [
    totalCompanies,
    activeCompanies,
    planBreakdown,
    avgCreatedAt,
    companiesWithRevenue
  ] = stats

  // Calculate total revenue
  const totalRevenue = companiesWithRevenue.reduce((sum, company) => 
    sum + company.invoices.reduce((companySum, invoice) => companySum + invoice.total, 0), 0
  )

  // Process companies with revenue data
  const processedCompanies = companies.map(company => ({
    ...company,
    totalRevenue: company.invoices.reduce((sum, invoice) => sum + invoice.total, 0),
    owner: company.users[0] || null,
    isActive: company._count.users > 0 && company.users.some(user => 
      user.lastLoginAt && user.lastLoginAt > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    )
  }))

  const companiesStats = {
    total: totalCompanies,
    active: activeCompanies,
    inactive: totalCompanies - activeCompanies,
    totalRevenue,
    planBreakdown
  }

  const totalPages = Math.ceil(totalCount / limit)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link href="/super-admin">
                <Button variant="ghost" >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Dashboard
                </Button>
              </Link>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Building2 className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Companies Management</h1>
                  <p className="text-sm text-gray-600">Manage all platform companies</p>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Company
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-6">
          {/* Statistics */}
          <CompaniesStats stats={companiesStats} />

          {/* Filters */}
          <CompaniesFilters 
            currentPlan={plan}
            currentStatus={status}
            currentSearch={search}
          />

          {/* Companies List */}
          <CompaniesManagement 
            companies={processedCompanies}
            currentPage={page}
            totalPages={totalPages}
            totalCount={totalCount}
          />
        </div>
      </div>
    </div>
  )
}
