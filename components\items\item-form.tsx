'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Save, X, Package, ShoppingCart, Wrench } from 'lucide-react'
import toast from 'react-hot-toast'

const itemSchema = z.object({
  name: z.string().min(1, 'Item name is required'),
  description: z.string().optional(),
  sku: z.string().optional(),
  type: z.enum(['PRODUCT', 'SERVICE']),
  category: z.string().optional(),
  categoryId: z.string().optional(),
  price: z.number().min(0, 'Price must be non-negative'),
  costPrice: z.number().min(0).optional(),
  unit: z.string().optional(),
  stockQuantity: z.number().min(0).optional(),
  lowStockThreshold: z.number().min(0).optional(),
  status: z.enum(['ACTIVE', 'INACTIVE']).default('ACTIVE'),
  taxRate: z.number().min(0).max(100).default(18),
  notes: z.string().optional(),
})

type ItemFormData = z.infer<typeof itemSchema>

interface ItemFormProps {
  mode: 'create' | 'edit'
  item?: any
  categories: any[]
  suggestedSku?: string
}

export function ItemForm({ 
  mode, 
  item, 
  categories,
  suggestedSku
}: ItemFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const router = useRouter()

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
  } = useForm<ItemFormData>({
    resolver: zodResolver(itemSchema),
    defaultValues: item ? {
      name: item.name || '',
      description: item.description || '',
      sku: item.sku || '',
      type: item.type || 'PRODUCT',
      category: item.category || '',
      categoryId: item.categoryId || '',
      price: item.price || 0,
      costPrice: item.costPrice || 0,
      unit: item.unit || '',
      stockQuantity: item.stockQuantity || 0,
      lowStockThreshold: item.lowStockThreshold || 10,
      status: item.status || 'ACTIVE',
      taxRate: item.taxRate || 18,
      notes: item.notes || '',
    } : {
      name: '',
      description: '',
      sku: suggestedSku || '',
      type: 'PRODUCT',
      category: '',
      categoryId: '',
      price: 0,
      costPrice: 0,
      unit: '',
      stockQuantity: 0,
      lowStockThreshold: 10,
      status: 'ACTIVE',
      taxRate: 18,
      notes: '',
    }
  })

  const watchedType = watch('type')
  const watchedPrice = watch('price')
  const watchedCostPrice = watch('costPrice')

  // Calculate profit margin
  const profitMargin = watchedPrice && watchedCostPrice 
    ? (((watchedPrice - watchedCostPrice) / watchedPrice) * 100).toFixed(1)
    : '0'

  const onSubmit = async (data: ItemFormData) => {
    setIsLoading(true)
    setError('')

    try {
      const url = mode === 'create' ? '/api/items' : `/api/items/${item.id}`
      const method = mode === 'create' ? 'POST' : 'PUT'

      // Clean data based on type
      const submissionData = {
        ...data,
        // Remove stock-related fields for services
        stockQuantity: data.type === 'SERVICE' ? null : data.stockQuantity,
        lowStockThreshold: data.type === 'SERVICE' ? null : data.lowStockThreshold,
        // Convert empty strings to null
        sku: data.sku || null,
        category: data.category || null,
        description: data.description || null,
        unit: data.unit || null,
        costPrice: data.costPrice || null,
        notes: data.notes || null,
      }

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submissionData),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to save item')
      }

      const result = await response.json()
      
      toast.success(mode === 'create' ? 'Item created successfully!' : 'Item updated successfully!')
      
      if (mode === 'create') {
        router.push(`/dashboard/items/${result.id}`)
      } else {
        router.push('/dashboard/items')
      }
    } catch (error) {
      console.error('Error saving item:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to save item'
      setError(errorMessage)
      toast.error(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    if (mode === 'create') {
      reset()
    } else {
      router.push('/dashboard/items')
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Item Name *</Label>
              <Input
                id="name"
                {...register('name')}
                placeholder="Enter item name"
                disabled={isLoading}
              />
              {errors.name && (
                <p className="text-sm text-red-600 mt-1">{errors.name.message}</p>
              )}
            </div>
            <div>
              <Label htmlFor="sku">SKU</Label>
              <Input
                id="sku"
                {...register('sku')}
                placeholder="SKU-2401-0001"
                disabled={isLoading}
              />
            </div>
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <textarea
              id="description"
              {...register('description')}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Describe the item..."
              disabled={isLoading}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="type">Type *</Label>
              <select
                id="type"
                {...register('type')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isLoading}
              >
                <option value="PRODUCT">
                  📦 Product (Physical item)
                </option>
                <option value="SERVICE">
                  🔧 Service (Intangible offering)
                </option>
              </select>
            </div>
            <div>
              <Label htmlFor="categoryId">Category</Label>
              <select
                id="categoryId"
                {...register('categoryId')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isLoading}
              >
                <option value="">Select a category</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.parent ? `${category.parent.name} > ` : ''}{category.name}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <Label htmlFor="status">Status</Label>
              <select
                id="status"
                {...register('status')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isLoading}
              >
                <option value="ACTIVE">Active</option>
                <option value="INACTIVE">Inactive</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Pricing Information */}
      <Card>
        <CardHeader>
          <CardTitle>Pricing Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="price">Selling Price (₹) *</Label>
              <Input
                id="price"
                type="number"
                step="0.01"
                min="0"
                {...register('price', { valueAsNumber: true })}
                placeholder="0.00"
                disabled={isLoading}
              />
              {errors.price && (
                <p className="text-sm text-red-600 mt-1">{errors.price.message}</p>
              )}
            </div>
            <div>
              <Label htmlFor="costPrice">Cost Price (₹)</Label>
              <Input
                id="costPrice"
                type="number"
                step="0.01"
                min="0"
                {...register('costPrice', { valueAsNumber: true })}
                placeholder="0.00"
                disabled={isLoading}
              />
            </div>
            <div>
              <Label htmlFor="unit">Unit</Label>
              <Input
                id="unit"
                {...register('unit')}
                placeholder="e.g., piece, hour, kg"
                disabled={isLoading}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="taxRate">Tax Rate (%)</Label>
              <Input
                id="taxRate"
                type="number"
                step="0.01"
                min="0"
                max="100"
                {...register('taxRate', { valueAsNumber: true })}
                placeholder="18"
                disabled={isLoading}
              />
            </div>
            {watchedPrice > 0 && watchedCostPrice && watchedCostPrice > 0 && (
              <div className="flex items-end">
                <div className="p-3 bg-green-50 rounded-md w-full">
                  <p className="text-sm text-green-600">Profit Margin</p>
                  <p className="font-medium text-green-800">{profitMargin}%</p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Inventory Information (Products only) */}
      {watchedType === 'PRODUCT' && (
        <Card>
          <CardHeader>
            <CardTitle>Inventory Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="stockQuantity">Current Stock</Label>
                <Input
                  id="stockQuantity"
                  type="number"
                  min="0"
                  {...register('stockQuantity', { valueAsNumber: true })}
                  placeholder="0"
                  disabled={isLoading}
                />
              </div>
              <div>
                <Label htmlFor="lowStockThreshold">Low Stock Alert</Label>
                <Input
                  id="lowStockThreshold"
                  type="number"
                  min="0"
                  {...register('lowStockThreshold', { valueAsNumber: true })}
                  placeholder="10"
                  disabled={isLoading}
                />
                <p className="text-sm text-gray-500 mt-1">
                  Alert when stock falls below this level
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Additional Information */}
      <Card>
        <CardHeader>
          <CardTitle>Additional Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div>
            <Label htmlFor="notes">Notes</Label>
            <textarea
              id="notes"
              {...register('notes')}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Additional notes about this item..."
              disabled={isLoading}
            />
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex items-center justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={handleCancel}
          disabled={isLoading}
        >
          <X className="h-4 w-4 mr-2" />
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              {mode === 'create' ? 'Creating...' : 'Updating...'}
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              {mode === 'create' ? 'Create Item' : 'Update Item'}
            </>
          )}
        </Button>
      </div>
    </form>
  )
}
