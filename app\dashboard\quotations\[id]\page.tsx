import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { QuotationDetails } from '@/components/quotations/quotation-details'
import { QuotationActivity } from '@/components/quotations/quotation-activity'
import { QuotationPreview } from '@/components/quotations/quotation-preview'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Edit, Trash2, Send, Download, Copy, FileText } from 'lucide-react'
import Link from 'next/link'
import { notFound } from 'next/navigation'

export default async function QuotationDetailPage({
  params,
}: {
  params: { id: string }
}) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  const [quotation, company] = await Promise.all([
    prisma.quotation.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      },
      include: {
        customer: {
          select: { 
            id: true, 
            name: true, 
            email: true, 
            company: true, 
            phone: true,
            address: true,
            city: true,
            state: true,
            country: true,
            postalCode: true
          }
        },
        lead: {
          select: { id: true, title: true, value: true }
        },
        items: {
          include: {
            item: {
              select: { name: true, category: true, unit: true }
            }
          }
        },
        activities: {
          include: {
            createdBy: {
              select: { name: true, firstName: true, lastName: true }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: 15,
        },
        _count: {
          select: {
            items: true,
            activities: true,
          }
        }
      }
    }),
    prisma.company.findUnique({
      where: { id: session.user.companyId },
      select: {
        name: true,
        email: true,
        phone: true,
        address: true,
        city: true,
        state: true,
        country: true,
        postalCode: true,
        website: true,
        taxId: true
      }
    })
  ])

  if (!quotation) {
    notFound()
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/quotations">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Quotations
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{quotation.quotationNumber}</h1>
            <p className="text-gray-600 mt-1">
              {quotation.title}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {quotation.status === 'DRAFT' && (
            <>
              <Button variant="outline" className="text-blue-600 hover:text-blue-700">
                <Send className="h-4 w-4 mr-2" />
                Send
              </Button>
              <Link href={`/dashboard/quotations/${quotation.id}/edit`}>
                <Button variant="outline">
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>
              </Link>
            </>
          )}
          <Button variant="outline">
            <Copy className="h-4 w-4 mr-2" />
            Duplicate
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Download PDF
          </Button>
          {quotation.lead && (
            <Link href={`/dashboard/invoices/new?quotationId=${quotation.id}&customerId=${quotation.customerId}`}>
              <Button variant="outline">
                <FileText className="h-4 w-4 mr-2" />
                Create Invoice
              </Button>
            </Link>
          )}
          {quotation.status === 'DRAFT' && (
            <Button variant="outline" className="text-red-600 hover:text-red-700">
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Quotation Details */}
        <div className="lg:col-span-2 space-y-6">
          <QuotationDetails quotation={quotation} />
          <QuotationPreview quotation={{
            ...quotation,
            status: quotation.status as string,
            company: company || {
              name: 'Unknown Company',
              email: null,
              phone: null,
              address: null,
              city: null,
              state: null,
              country: null,
              postalCode: null
            }
          }} company={company} />
        </div>

        {/* Right Column - Activity */}
        <div>
          <QuotationActivity activities={quotation.activities} />
        </div>
      </div>
    </div>
  )
}
