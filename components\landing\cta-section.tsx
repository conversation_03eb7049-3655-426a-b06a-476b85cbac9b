'use client'

import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowRight,
  Rocket,
  Star,
  CheckCircle,
  Zap,
  Users,
  TrendingUp
} from 'lucide-react'

export function CTASection() {
  const benefits = [
    'No credit card required',
    '14-day free trial',
    'Setup in 5 minutes',
    'Cancel anytime',
    'Full feature access',
    '24/7 support included'
  ]

  const stats = [
    { icon: Users, value: '10,000+', label: 'Happy customers' },
    { icon: Star, value: '4.9/5', label: 'Customer rating' },
    { icon: TrendingUp, value: '300%', label: 'Average ROI' }
  ]

  return (
    <section className="py-20 px-4 bg-gradient-to-br from-blue-600 via-purple-600 to-blue-800 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-grid-white/10 [mask-image:linear-gradient(0deg,transparent,rgba(255,255,255,0.1))]" />
      <div className="absolute top-0 left-1/4 w-96 h-96 bg-white/10 rounded-full blur-3xl" />
      <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-white/10 rounded-full blur-3xl" />
      
      <div className="container mx-auto relative">
        <div className="max-w-4xl mx-auto text-center text-white">
          {/* Badge */}
          <div className="mb-8">
            <Badge className="bg-white/20 text-white border-white/30 px-4 py-2">
              <Rocket className="h-4 w-4 mr-2" />
              Ready to Transform Your Business?
            </Badge>
          </div>

          {/* Main Headline */}
          <h2 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
            Start Your Free Trial
            <span className="block text-3xl md:text-5xl mt-2 opacity-90">
              and See Results in Days
            </span>
          </h2>

          {/* Subheadline */}
          <p className="text-xl md:text-2xl opacity-90 mb-8 leading-relaxed max-w-3xl mx-auto">
            Join thousands of businesses that have streamlined their operations 
            and increased revenue with BusinessSaaS. No risk, no commitment.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Link href="/auth/signup">
              <Button 
                size="lg" 
                className="text-lg px-8 py-4 bg-white text-blue-600 hover:bg-gray-100 shadow-lg hover:shadow-xl transition-all font-semibold"
              >
                Start Free Trial
                <Zap className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Button 
              variant="outline" 
              size="lg" 
              className="text-lg px-8 py-4 border-white text-white hover:bg-white hover:text-blue-600 transition-all font-semibold"
            >
              Schedule Demo
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </div>

          {/* Benefits List */}
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-12 max-w-3xl mx-auto">
            {benefits.map((benefit, index) => (
              <div key={index} className="flex items-center space-x-2 text-sm opacity-90">
                <CheckCircle className="h-4 w-4 text-green-300 flex-shrink-0" />
                <span>{benefit}</span>
              </div>
            ))}
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-2xl mx-auto mb-12">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center mx-auto mb-3">
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
                <div className="text-2xl font-bold mb-1">{stat.value}</div>
                <div className="text-sm opacity-80">{stat.label}</div>
              </div>
            ))}
          </div>

          {/* Urgency/Scarcity */}
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 max-w-2xl mx-auto">
            <div className="flex items-center justify-center space-x-2 mb-3">
              <Star className="h-5 w-5 text-yellow-300" />
              <span className="font-semibold">Limited Time Offer</span>
              <Star className="h-5 w-5 text-yellow-300" />
            </div>
            <p className="text-lg mb-4">
              Get 2 months free when you upgrade to Professional during your trial
            </p>
            <div className="text-sm opacity-80">
              Offer expires in 7 days • No code required • Automatically applied
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Wave */}
      <div className="absolute bottom-0 left-0 right-0">
        <svg 
          viewBox="0 0 1200 120" 
          preserveAspectRatio="none" 
          className="relative block w-full h-16 fill-white"
        >
          <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" 
          opacity=".25"
        />
        <path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" 
          opacity=".5"
        />
        <path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" 
        />
        </svg>
      </div>
    </section>
  )
}
