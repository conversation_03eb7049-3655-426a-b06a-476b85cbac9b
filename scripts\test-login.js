#!/usr/bin/env node

/**
 * Test login functionality with demo credentials
 */

const axios = require('axios')

const BASE_URL = 'http://localhost:3000'

const testCredentials = [
  {
    name: 'Super Admin',
    email: '<EMAIL>',
    password: 'superadmin123'
  },
  {
    name: '<PERSON> (CEO)',
    email: '<EMAIL>',
    password: 'demo123'
  },
  {
    name: '<PERSON> (Sales Manager)',
    email: 'micha<PERSON>.<EMAIL>',
    password: 'demo123'
  },
  {
    name: '<PERSON> (Account Executive)',
    email: '<EMAIL>',
    password: 'demo123'
  },
  {
    name: '<PERSON> (Project Manager)',
    email: '<EMAIL>',
    password: 'demo123'
  }
]

async function testLogin(credentials) {
  try {
    console.log(`\n🧪 Testing login for: ${credentials.name}`)
    console.log(`📧 Email: ${credentials.email}`)
    
    // Test the auth endpoint
    const response = await axios.post(`${BASE_URL}/api/auth/callback/credentials`, {
      email: credentials.email,
      password: credentials.password,
      csrfToken: 'test-token'
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      validateStatus: function (status) {
        return status < 500 // Accept any status less than 500
      }
    })
    
    if (response.status === 200) {
      console.log(`✅ Login successful for ${credentials.name}`)
      return true
    } else {
      console.log(`❌ Login failed for ${credentials.name} - Status: ${response.status}`)
      return false
    }
  } catch (error) {
    console.log(`❌ Login error for ${credentials.name}:`, error.message)
    return false
  }
}

async function runTests() {
  console.log('🚀 Testing Demo User Login Functionality')
  console.log('=' * 50)
  
  let successCount = 0
  let totalCount = testCredentials.length
  
  for (const credentials of testCredentials) {
    const success = await testLogin(credentials)
    if (success) successCount++
    
    // Wait a bit between tests
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
  
  console.log('\n' + '=' * 50)
  console.log('📊 Test Results Summary:')
  console.log(`✅ Successful logins: ${successCount}/${totalCount}`)
  console.log(`❌ Failed logins: ${totalCount - successCount}/${totalCount}`)
  
  if (successCount === totalCount) {
    console.log('\n🎉 All login tests passed! Authentication is working correctly.')
  } else {
    console.log('\n⚠️  Some login tests failed. Please check the authentication configuration.')
  }
  
  console.log('\n🌐 Access the platform at: http://localhost:3000')
  console.log('🔐 Use any of the demo credentials above to login')
}

if (require.main === module) {
  runTests().catch(console.error)
}

module.exports = { testLogin, testCredentials }
