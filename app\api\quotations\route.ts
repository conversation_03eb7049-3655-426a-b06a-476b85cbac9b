import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const quotationItemSchema = z.object({
  itemId: z.string().optional(),
  name: z.string().min(1, 'Item name is required'),
  description: z.string().optional(),
  quantity: z.number().min(0.01, 'Quantity must be greater than 0'),
  unitPrice: z.number().min(0, 'Unit price must be non-negative'),
  discount: z.number().min(0).max(100).default(0),
  taxRate: z.number().min(0).max(100).default(18),
})

const quotationSchema = z.object({
  quotationNumber: z.string().min(1, 'Quotation number is required'),
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  customerId: z.string().min(1, 'Customer is required'),
  leadId: z.string().optional(),
  validUntil: z.string().optional().transform((val) => val ? new Date(val) : undefined),
  terms: z.string().optional(),
  paymentTerms: z.string().optional(),
  taxRate: z.number().min(0).max(100).default(18),
  discountType: z.enum(['PERCENTAGE', 'FIXED']).default('PERCENTAGE'),
  discountValue: z.number().min(0).default(0),
  subtotal: z.number().min(0),
  total: z.number().min(0),
  items: z.array(quotationItemSchema).min(1, 'At least one item is required'),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || ''
    const customerId = searchParams.get('customerId') || ''
    const leadId = searchParams.get('leadId') || ''
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = (page - 1) * limit

    // Build where clause
    const where: any = {
      companyId: session.user.companyId,
    }

    if (search) {
      where.OR = [
        { quotationNumber: { contains: search } },
        { title: { contains: search } },
        { description: { contains: search } },
        { customer: { name: { contains: search } } },
      ]
    }

    if (status) {
      where.status = status
    }

    if (customerId) {
      where.customerId = customerId
    }

    if (leadId) {
      where.leadId = leadId
    }

    const [quotations, totalCount] = await Promise.all([
      prisma.quotation.findMany({
        where,
        include: {
          customer: {
            select: { id: true, name: true, email: true, company: true }
          },
          lead: {
            select: { id: true, title: true }
          },
          items: {
            include: {
              item: {
                select: { name: true, category: true }
              }
            }
          },
          _count: {
            select: {
              items: true,
              activities: true,
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip: offset,
        take: limit,
      }),
      prisma.quotation.count({ where })
    ])

    return NextResponse.json({
      quotations,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      }
    })
  } catch (error) {
    console.error('Error fetching quotations:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = quotationSchema.parse(body)

    // Verify customer belongs to the company
    const customer = await prisma.customer.findFirst({
      where: {
        id: validatedData.customerId,
        companyId: session.user.companyId,
      }
    })

    if (!customer) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 400 }
      )
    }

    // Verify lead belongs to the company (if provided)
    if (validatedData.leadId) {
      const lead = await prisma.lead.findFirst({
        where: {
          id: validatedData.leadId,
          companyId: session.user.companyId,
        }
      })

      if (!lead) {
        return NextResponse.json(
          { error: 'Lead not found' },
          { status: 400 }
        )
      }
    }

    // Check if quotation number already exists
    const existingQuotation = await prisma.quotation.findFirst({
      where: {
        quotationNumber: validatedData.quotationNumber,
        companyId: session.user.companyId,
      }
    })

    if (existingQuotation) {
      return NextResponse.json(
        { error: 'Quotation number already exists' },
        { status: 400 }
      )
    }

    const quotation = await prisma.quotation.create({
      data: {
        quotationNumber: validatedData.quotationNumber,
        title: validatedData.title,
        description: validatedData.description,
        status: 'DRAFT',
        validUntil: validatedData.validUntil,
        terms: validatedData.terms,
        paymentTerms: validatedData.paymentTerms,
        taxRate: validatedData.taxRate,
        discountType: validatedData.discountType,
        discountValue: validatedData.discountValue,
        subtotal: validatedData.subtotal,
        total: validatedData.total,
        customerId: validatedData.customerId,
        leadId: validatedData.leadId,
        companyId: session.user.companyId,
        createdById: session.user.id,
        items: {
          create: validatedData.items.map(item => ({
            itemId: item.itemId,
            name: item.name,
            description: item.description || '',
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            discount: item.discount,
            taxRate: item.taxRate,
          }))
        }
      },
      include: {
        customer: {
          select: { id: true, name: true, email: true, company: true }
        },
        lead: {
          select: { id: true, title: true }
        },
        items: {
          include: {
            item: {
              select: { name: true, category: true }
            }
          }
        },
        _count: {
          select: {
            items: true,
            activities: true,
          }
        }
      }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'QUOTATION_CREATED',
        title: 'Quotation created',
        description: `Quotation "${quotation.quotationNumber}" was created`,
        quotationId: quotation.id,
        customerId: quotation.customerId,
        leadId: quotation.leadId,
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    return NextResponse.json(quotation, { status: 201 })
  } catch (error) {
    console.error('Error creating quotation:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
