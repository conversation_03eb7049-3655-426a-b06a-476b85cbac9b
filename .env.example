# Database
DATABASE_URL="mysql://username:password@localhost:3306/nextjs_saas_production"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# Email SMTP Configuration (Optional - for production)
EMAIL_SERVER_HOST="smtp.gmail.com"
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="your-app-password"
EMAIL_FROM="<EMAIL>"
EMAIL_FROM_NAME="Your Company Name"

# Mailgun Email Service (Alternative to SMTP)
MAILGUN_API_KEY="your-mailgun-api-key"
MAILGUN_DOMAIN="mg.yourdomain.com"
MAILGUN_REGION="us"

# OpenAI Configuration
OPENAI_API_KEY="your-openai-api-key"
OPENAI_MODEL="gpt-3.5-turbo"
OPENAI_MAX_TOKENS="1000"
OPENAI_TEMPERATURE="0.7"
OPENAI_ORG_ID="your-organization-id"

# Grok AI Configuration (X.AI)
GROK_API_KEY="your-grok-api-key"
GROK_MODEL="grok-beta"
GROK_MAX_TOKENS="1000"
GROK_TEMPERATURE="0.7"

# Stripe Payment Processing
STRIPE_PUBLISHABLE_KEY="pk_test_your_stripe_publishable_key"
STRIPE_SECRET_KEY="sk_test_your_stripe_secret_key"
STRIPE_WEBHOOK_SECRET="whsec_your_webhook_secret"

# File Upload (Optional)
UPLOAD_DIR="./uploads"
MAX_FILE_SIZE="5242880"

# App Configuration
APP_NAME="Business Management SaaS"
APP_URL="http://localhost:3000"
COMPANY_NAME="Your Company"
SUPPORT_EMAIL="<EMAIL>"
