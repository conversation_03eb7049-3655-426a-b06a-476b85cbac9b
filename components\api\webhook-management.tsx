'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { formatDate } from '@/lib/utils'
import { 
  Webhook, 
  Plus,
  Edit,
  Trash2,
  TestTube,
  CheckCircle,
  XCircle,
  AlertTriangle,
  RefreshCw,
  ExternalLink,
  Activity
} from 'lucide-react'
import toast from 'react-hot-toast'

interface WebhookManagementProps {
  webhooks: Array<{
    id: string
    name: string
    url: string
    events: string[]
    status: string
    secret: string | null
    lastTriggeredAt: Date | null
    createdAt: Date
  }>
  companyId: string
}

export function WebhookManagement({ webhooks, companyId }: WebhookManagementProps) {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [newWebhook, setNewWebhook] = useState({
    name: '',
    url: '',
    events: [] as string[],
    description: ''
  })

  const availableEvents = [
    { value: 'customer.created', label: 'Customer Created' },
    { value: 'customer.updated', label: 'Customer Updated' },
    { value: 'customer.deleted', label: 'Customer Deleted' },
    { value: 'quotation.created', label: 'Quotation Created' },
    { value: 'quotation.updated', label: 'Quotation Updated' },
    { value: 'quotation.sent', label: 'Quotation Sent' },
    { value: 'quotation.accepted', label: 'Quotation Accepted' },
    { value: 'invoice.created', label: 'Invoice Created' },
    { value: 'invoice.updated', label: 'Invoice Updated' },
    { value: 'invoice.sent', label: 'Invoice Sent' },
    { value: 'invoice.paid', label: 'Invoice Paid' },
    { value: 'contract.created', label: 'Contract Created' },
    { value: 'contract.updated', label: 'Contract Updated' },
    { value: 'contract.signed', label: 'Contract Signed' },
    { value: 'payment.succeeded', label: 'Payment Succeeded' },
    { value: 'payment.failed', label: 'Payment Failed' }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return CheckCircle
      case 'INACTIVE':
        return XCircle
      case 'ERROR':
        return AlertTriangle
      default:
        return XCircle
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'text-green-600 border-green-200'
      case 'INACTIVE':
        return 'text-gray-600 border-gray-200'
      case 'ERROR':
        return 'text-red-600 border-red-200'
      default:
        return 'text-gray-600 border-gray-200'
    }
  }

  const handleCreateWebhook = async () => {
    if (!newWebhook.name.trim()) {
      toast.error('Webhook name is required')
      return
    }

    if (!newWebhook.url.trim()) {
      toast.error('Webhook URL is required')
      return
    }

    if (!isValidUrl(newWebhook.url)) {
      toast.error('Please enter a valid URL')
      return
    }

    if (newWebhook.events.length === 0) {
      toast.error('At least one event is required')
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch('/api/webhooks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newWebhook.name,
          url: newWebhook.url,
          events: newWebhook.events,
          description: newWebhook.description
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to create webhook')
      }

      toast.success('Webhook created successfully')
      
      // Reset form and close dialog
      setNewWebhook({
        name: '',
        url: '',
        events: [],
        description: ''
      })
      setIsCreateDialogOpen(false)
      
      // Refresh the page to show new webhook
      window.location.reload()
    } catch (error) {
      toast.error('Failed to create webhook')
    } finally {
      setIsLoading(false)
    }
  }

  const handleTestWebhook = async (webhookId: string) => {
    try {
      const response = await fetch(`/api/webhooks/${webhookId}/test`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to test webhook')
      }

      toast.success('Test webhook sent successfully')
    } catch (error) {
      toast.error('Failed to test webhook')
    }
  }

  const handleToggleWebhookStatus = async (webhookId: string, currentStatus: string) => {
    const newStatus = currentStatus === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE'
    
    try {
      const response = await fetch(`/api/webhooks/${webhookId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      })

      if (!response.ok) {
        throw new Error('Failed to update webhook status')
      }

      toast.success(`Webhook ${newStatus.toLowerCase()} successfully`)
      window.location.reload()
    } catch (error) {
      toast.error('Failed to update webhook status')
    }
  }

  const handleDeleteWebhook = async (webhookId: string) => {
    if (!confirm('Are you sure you want to delete this webhook? This action cannot be undone.')) {
      return
    }

    try {
      const response = await fetch(`/api/webhooks/${webhookId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to delete webhook')
      }

      toast.success('Webhook deleted successfully')
      window.location.reload()
    } catch (error) {
      toast.error('Failed to delete webhook')
    }
  }

  const isValidUrl = (url: string) => {
    try {
      new URL(url)
      return url.startsWith('http://') || url.startsWith('https://')
    } catch {
      return false
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Webhook className="h-5 w-5" />
            <span>Webhooks</span>
          </CardTitle>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Webhook
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Create New Webhook</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="webhookName">Name</Label>
                  <Input
                    id="webhookName"
                    placeholder="My Webhook"
                    value={newWebhook.name}
                    onChange={(e) => setNewWebhook({ ...newWebhook, name: e.target.value })}
                  />
                </div>
                
                <div>
                  <Label htmlFor="webhookUrl">URL</Label>
                  <Input
                    id="webhookUrl"
                    placeholder="https://example.com/webhook"
                    value={newWebhook.url}
                    onChange={(e) => setNewWebhook({ ...newWebhook, url: e.target.value })}
                  />
                </div>

                <div>
                  <Label htmlFor="webhookDescription">Description (Optional)</Label>
                  <Textarea
                    id="webhookDescription"
                    placeholder="Description of what this webhook does"
                    value={newWebhook.description}
                    onChange={(e) => setNewWebhook({ ...newWebhook, description: e.target.value })}
                  />
                </div>

                <div>
                  <Label>Events</Label>
                  <div className="grid grid-cols-1 gap-2 mt-2 max-h-40 overflow-y-auto">
                    {availableEvents.map((event) => (
                      <label key={event.value} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={newWebhook.events.includes(event.value)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setNewWebhook({
                                ...newWebhook,
                                events: [...newWebhook.events, event.value]
                              })
                            } else {
                              setNewWebhook({
                                ...newWebhook,
                                events: newWebhook.events.filter(e => e !== event.value)
                              })
                            }
                          }}
                          className="rounded"
                        />
                        <span className="text-sm">{event.label}</span>
                      </label>
                    ))}
                  </div>
                </div>

                <div className="flex justify-end space-x-2">
                  <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreateWebhook} disabled={isLoading}>
                    {isLoading ? (
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Plus className="h-4 w-4 mr-2" />
                    )}
                    Create Webhook
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {webhooks.length > 0 ? (
          <div className="space-y-4">
            {webhooks.map((webhook) => {
              const StatusIcon = getStatusIcon(webhook.status)
              
              return (
                <div key={webhook.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div>
                        <h4 className="font-medium text-gray-900">{webhook.name}</h4>
                        <div className="flex items-center space-x-2 mt-1">
                          <code className="bg-gray-100 px-2 py-1 rounded text-sm font-mono">
                            {webhook.url}
                          </code>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => window.open(webhook.url, '_blank')}
                          >
                            <ExternalLink className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge className={getStatusColor(webhook.status)} variant="outline">
                        <StatusIcon className="h-3 w-3 mr-1" />
                        {webhook.status}
                      </Badge>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex flex-wrap gap-1">
                      {webhook.events.map((event) => (
                        <Badge key={event} variant="secondary" className="text-xs">
                          {event}
                        </Badge>
                      ))}
                    </div>

                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <div className="flex items-center space-x-4">
                        <span>Created: {formatDate(webhook.createdAt)}</span>
                        {webhook.lastTriggeredAt && (
                          <span>Last triggered: {formatDate(webhook.lastTriggeredAt)}</span>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center justify-between pt-2">
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleTestWebhook(webhook.id)}
                        >
                          <TestTube className="h-4 w-4 mr-1" />
                          Test
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleToggleWebhookStatus(webhook.id, webhook.status)}
                        >
                          {webhook.status === 'ACTIVE' ? 'Deactivate' : 'Activate'}
                        </Button>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteWebhook(webhook.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        ) : (
          <div className="text-center py-8">
            <Webhook className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="font-medium text-gray-900 mb-2">No Webhooks</h3>
            <p className="text-gray-500 mb-6">
              Create your first webhook to receive real-time notifications about events.
            </p>
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create Webhook
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
