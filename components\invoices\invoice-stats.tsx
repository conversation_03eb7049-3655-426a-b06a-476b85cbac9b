'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { formatCurrency } from '@/lib/utils'
import { 
  Receipt, 
  Send, 
  Eye, 
  CheckCircle, 
  AlertTriangle, 
  XCircle,
  DollarSign,
  TrendingUp,
  Clock,
  CreditCard
} from 'lucide-react'

interface InvoiceStatsProps {
  stats: {
    total: number
    draft: number
    sent: number
    viewed: number
    paid: number
    overdue: number
    cancelled: number
    totalValue: number
    paidValue: number
    pendingValue: number
    overdueValue: number
  }
}

export function InvoiceStats({ stats }: InvoiceStatsProps) {
  const paymentRate = stats.total > 0 ? ((stats.paid / stats.total) * 100).toFixed(1) : '0'
  const overdueRate = stats.total > 0 ? ((stats.overdue / stats.total) * 100).toFixed(1) : '0'
  const collectionRate = (stats.paidValue + stats.pendingValue) > 0 ? 
    ((stats.paidValue / (stats.paidValue + stats.pendingValue)) * 100).toFixed(1) : '0'

  const statCards = [
    {
      title: 'Total Invoices',
      value: stats.total.toString(),
      icon: Receipt,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      description: 'All invoices created'
    },
    {
      title: 'Draft',
      value: stats.draft.toString(),
      icon: Receipt,
      color: 'text-gray-600',
      bgColor: 'bg-gray-100',
      description: 'Pending completion'
    },
    {
      title: 'Sent',
      value: stats.sent.toString(),
      icon: Send,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      description: 'Sent to customers'
    },
    {
      title: 'Viewed',
      value: stats.viewed.toString(),
      icon: Eye,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
      description: 'Opened by customers'
    },
    {
      title: 'Paid',
      value: stats.paid.toString(),
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      description: 'Successfully paid'
    },
    {
      title: 'Overdue',
      value: stats.overdue.toString(),
      icon: AlertTriangle,
      color: 'text-red-600',
      bgColor: 'bg-red-100',
      description: 'Past due date'
    },
    {
      title: 'Total Value',
      value: formatCurrency(stats.totalValue),
      icon: DollarSign,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      description: 'Total invoice value'
    },
    {
      title: 'Paid Amount',
      value: formatCurrency(stats.paidValue),
      icon: CreditCard,
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-100',
      description: 'Revenue collected'
    },
    {
      title: 'Pending Amount',
      value: formatCurrency(stats.pendingValue),
      icon: Clock,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      description: 'Awaiting payment'
    },
    {
      title: 'Overdue Amount',
      value: formatCurrency(stats.overdueValue),
      icon: AlertTriangle,
      color: 'text-red-600',
      bgColor: 'bg-red-100',
      description: 'Past due payments'
    },
    {
      title: 'Payment Rate',
      value: `${paymentRate}%`,
      icon: TrendingUp,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      description: 'Invoices paid on time'
    },
    {
      title: 'Collection Rate',
      value: `${collectionRate}%`,
      icon: CheckCircle,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-100',
      description: 'Revenue collection rate'
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4">
      {statCards.map((stat, index) => (
        <Card key={index} className="hover:shadow-lg transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              {stat.title}
            </CardTitle>
            <div className={`p-2 rounded-lg ${stat.bgColor}`}>
              <stat.icon className={`h-4 w-4 ${stat.color}`} />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">
              {stat.value}
            </div>
            <p className="text-xs text-gray-500 mt-1">
              {stat.description}
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
