import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if quotation exists and belongs to the company
    const originalQuotation = await prisma.quotation.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      },
      include: {
        items: true,
      }
    })

    if (!originalQuotation) {
      return NextResponse.json({ error: 'Quotation not found' }, { status: 404 })
    }

    // Generate new quotation number
    const lastQuotation = await prisma.quotation.findFirst({
      where: { companyId: session.user.companyId },
      orderBy: { createdAt: 'desc' },
      select: { quotationNumber: true }
    })

    const newQuotationNumber = generateQuotationNumber(lastQuotation?.quotationNumber)

    // Create duplicate quotation
    const duplicatedQuotation = await prisma.quotation.create({
      data: {
        quotationNumber: newQuotationNumber,
        title: `${originalQuotation.title} (Copy)`,
        description: originalQuotation.description,
        status: 'DRAFT',
        validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        terms: originalQuotation.terms,
        paymentTerms: originalQuotation.paymentTerms,
        taxRate: originalQuotation.taxRate,
        discountType: originalQuotation.discountType,
        discountValue: originalQuotation.discountValue,
        subtotal: originalQuotation.subtotal,
        total: originalQuotation.total,
        customerId: originalQuotation.customerId,
        leadId: originalQuotation.leadId,
        companyId: session.user.companyId,
        createdById: session.user.id,
        items: {
          create: originalQuotation.items.map(item => ({
            itemId: item.itemId,
            name: item.name,
            description: item.description,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            discount: item.discount,
            taxRate: item.taxRate,
          }))
        }
      },
      include: {
        customer: {
          select: { id: true, name: true, email: true, company: true }
        },
        lead: {
          select: { id: true, title: true }
        },
        items: {
          include: {
            item: {
              select: { name: true, category: true }
            }
          }
        },
        _count: {
          select: {
            items: true,
            activities: true,
          }
        }
      }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'QUOTATION_CREATED',
        title: 'Quotation duplicated',
        description: `Quotation "${duplicatedQuotation.quotationNumber}" was created as a copy of "${originalQuotation.quotationNumber}"`,
        quotationId: duplicatedQuotation.id,
        customerId: duplicatedQuotation.customerId,
        leadId: duplicatedQuotation.leadId,
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    return NextResponse.json(duplicatedQuotation, { status: 201 })
  } catch (error) {
    console.error('Error duplicating quotation:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function generateQuotationNumber(lastNumber?: string): string {
  const currentYear = new Date().getFullYear()
  const currentMonth = String(new Date().getMonth() + 1).padStart(2, '0')
  
  let nextNumber = 1
  
  if (lastNumber) {
    const match = lastNumber.match(/QUO-(\d{4})(\d{2})-(\d{4})/)
    if (match) {
      const [, year, month, num] = match
      if (year === currentYear.toString() && month === currentMonth) {
        nextNumber = parseInt(num) + 1
      }
    }
  }
  
  return `QUO-${currentYear}${currentMonth}-${String(nextNumber).padStart(4, '0')}`
}
