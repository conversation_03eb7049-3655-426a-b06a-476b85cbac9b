'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { formatCurrency } from '@/lib/utils'
import { 
  Target, 
  TrendingUp, 
  Users, 
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  DollarSign
} from 'lucide-react'

interface SalesMetricsProps {
  data: {
    quotationStats: Array<{
      status: string
      _count: number
      _sum: { total: number | null }
    }>
    invoiceStats: Array<{
      status: string
      _count: number
      _sum: { total: number | null }
    }>
    contractStats: Array<{
      status: string
      _count: number
      _sum: { value: number | null }
    }>
    period: string
  }
}

export function SalesMetrics({ data }: SalesMetricsProps) {
  // Calculate quotation metrics
  const quotationMetrics = {
    total: data.quotationStats.reduce((sum, stat) => sum + stat._count, 0),
    totalValue: data.quotationStats.reduce((sum, stat) => sum + (stat._sum.total || 0), 0),
    draft: data.quotationStats.find(s => s.status === 'DRAFT')?._count || 0,
    sent: data.quotationStats.find(s => s.status === 'SENT')?._count || 0,
    viewed: data.quotationStats.find(s => s.status === 'VIEWED')?._count || 0,
    accepted: data.quotationStats.find(s => s.status === 'ACCEPTED')?._count || 0,
    rejected: data.quotationStats.find(s => s.status === 'REJECTED')?._count || 0,
    expired: data.quotationStats.find(s => s.status === 'EXPIRED')?._count || 0,
  }

  // Calculate invoice metrics
  const invoiceMetrics = {
    total: data.invoiceStats.reduce((sum, stat) => sum + stat._count, 0),
    totalValue: data.invoiceStats.reduce((sum, stat) => sum + (stat._sum.total || 0), 0),
    draft: data.invoiceStats.find(s => s.status === 'DRAFT')?._count || 0,
    sent: data.invoiceStats.find(s => s.status === 'SENT')?._count || 0,
    viewed: data.invoiceStats.find(s => s.status === 'VIEWED')?._count || 0,
    paid: data.invoiceStats.find(s => s.status === 'PAID')?._count || 0,
    overdue: data.invoiceStats.find(s => s.status === 'OVERDUE')?._count || 0,
    cancelled: data.invoiceStats.find(s => s.status === 'CANCELLED')?._count || 0,
  }

  // Calculate contract metrics
  const contractMetrics = {
    total: data.contractStats.reduce((sum, stat) => sum + stat._count, 0),
    totalValue: data.contractStats.reduce((sum, stat) => sum + (stat._sum.value || 0), 0),
    draft: data.contractStats.find(s => s.status === 'DRAFT')?._count || 0,
    sent: data.contractStats.find(s => s.status === 'SENT')?._count || 0,
    viewed: data.contractStats.find(s => s.status === 'VIEWED')?._count || 0,
    signed: data.contractStats.find(s => s.status === 'SIGNED')?._count || 0,
    executed: data.contractStats.find(s => s.status === 'EXECUTED')?._count || 0,
    expired: data.contractStats.find(s => s.status === 'EXPIRED')?._count || 0,
    cancelled: data.contractStats.find(s => s.status === 'CANCELLED')?._count || 0,
  }

  // Calculate conversion rates
  const quotationConversionRate = quotationMetrics.total > 0 
    ? ((quotationMetrics.accepted / quotationMetrics.total) * 100).toFixed(1)
    : '0'

  const invoicePaymentRate = invoiceMetrics.total > 0
    ? ((invoiceMetrics.paid / invoiceMetrics.total) * 100).toFixed(1)
    : '0'

  const contractSignatureRate = contractMetrics.total > 0
    ? (((contractMetrics.signed + contractMetrics.executed) / contractMetrics.total) * 100).toFixed(1)
    : '0'

  const overallConversionRate = quotationMetrics.total > 0
    ? ((invoiceMetrics.paid / quotationMetrics.total) * 100).toFixed(1)
    : '0'

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Target className="h-5 w-5 text-blue-600" />
          <span>Sales Performance</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Key Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="flex items-center justify-center mb-1">
              <TrendingUp className="h-4 w-4 text-blue-600" />
            </div>
            <p className="text-lg font-bold text-blue-800">{quotationConversionRate}%</p>
            <p className="text-xs text-blue-600">Quote Conversion</p>
          </div>
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <div className="flex items-center justify-center mb-1">
              <DollarSign className="h-4 w-4 text-green-600" />
            </div>
            <p className="text-lg font-bold text-green-800">{invoicePaymentRate}%</p>
            <p className="text-xs text-green-600">Payment Rate</p>
          </div>
          <div className="text-center p-3 bg-purple-50 rounded-lg">
            <div className="flex items-center justify-center mb-1">
              <CheckCircle className="h-4 w-4 text-purple-600" />
            </div>
            <p className="text-lg font-bold text-purple-800">{contractSignatureRate}%</p>
            <p className="text-xs text-purple-600">Signature Rate</p>
          </div>
          <div className="text-center p-3 bg-orange-50 rounded-lg">
            <div className="flex items-center justify-center mb-1">
              <Target className="h-4 w-4 text-orange-600" />
            </div>
            <p className="text-lg font-bold text-orange-800">{overallConversionRate}%</p>
            <p className="text-xs text-orange-600">Overall Conversion</p>
          </div>
        </div>

        {/* Quotations Breakdown */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">Quotations</h4>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Total Quotations</span>
              <span className="font-medium">{quotationMetrics.total}</span>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <span className="text-xs text-gray-600">Draft</span>
                <Badge variant="outline" className="text-gray-600">{quotationMetrics.draft}</Badge>
              </div>
              <div className="flex items-center justify-between p-2 bg-blue-50 rounded">
                <span className="text-xs text-blue-600">Sent</span>
                <Badge variant="outline" className="text-blue-600">{quotationMetrics.sent}</Badge>
              </div>
              <div className="flex items-center justify-between p-2 bg-yellow-50 rounded">
                <span className="text-xs text-yellow-600">Viewed</span>
                <Badge variant="outline" className="text-yellow-600">{quotationMetrics.viewed}</Badge>
              </div>
              <div className="flex items-center justify-between p-2 bg-green-50 rounded">
                <span className="text-xs text-green-600">Accepted</span>
                <Badge variant="outline" className="text-green-600">{quotationMetrics.accepted}</Badge>
              </div>
              <div className="flex items-center justify-between p-2 bg-red-50 rounded">
                <span className="text-xs text-red-600">Rejected</span>
                <Badge variant="outline" className="text-red-600">{quotationMetrics.rejected}</Badge>
              </div>
              <div className="flex items-center justify-between p-2 bg-orange-50 rounded">
                <span className="text-xs text-orange-600">Expired</span>
                <Badge variant="outline" className="text-orange-600">{quotationMetrics.expired}</Badge>
              </div>
            </div>
            <div className="text-right">
              <span className="text-sm font-medium text-gray-900">
                Total Value: {formatCurrency(quotationMetrics.totalValue)}
              </span>
            </div>
          </div>
        </div>

        {/* Invoices Breakdown */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">Invoices</h4>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Total Invoices</span>
              <span className="font-medium">{invoiceMetrics.total}</span>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <span className="text-xs text-gray-600">Draft</span>
                <Badge variant="outline" className="text-gray-600">{invoiceMetrics.draft}</Badge>
              </div>
              <div className="flex items-center justify-between p-2 bg-blue-50 rounded">
                <span className="text-xs text-blue-600">Sent</span>
                <Badge variant="outline" className="text-blue-600">{invoiceMetrics.sent}</Badge>
              </div>
              <div className="flex items-center justify-between p-2 bg-yellow-50 rounded">
                <span className="text-xs text-yellow-600">Viewed</span>
                <Badge variant="outline" className="text-yellow-600">{invoiceMetrics.viewed}</Badge>
              </div>
              <div className="flex items-center justify-between p-2 bg-green-50 rounded">
                <span className="text-xs text-green-600">Paid</span>
                <Badge variant="outline" className="text-green-600">{invoiceMetrics.paid}</Badge>
              </div>
              <div className="flex items-center justify-between p-2 bg-red-50 rounded">
                <span className="text-xs text-red-600">Overdue</span>
                <Badge variant="outline" className="text-red-600">{invoiceMetrics.overdue}</Badge>
              </div>
              <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <span className="text-xs text-gray-600">Cancelled</span>
                <Badge variant="outline" className="text-gray-600">{invoiceMetrics.cancelled}</Badge>
              </div>
            </div>
            <div className="text-right">
              <span className="text-sm font-medium text-gray-900">
                Total Value: {formatCurrency(invoiceMetrics.totalValue)}
              </span>
            </div>
          </div>
        </div>

        {/* Contracts Breakdown */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">Contracts</h4>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Total Contracts</span>
              <span className="font-medium">{contractMetrics.total}</span>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <span className="text-xs text-gray-600">Draft</span>
                <Badge variant="outline" className="text-gray-600">{contractMetrics.draft}</Badge>
              </div>
              <div className="flex items-center justify-between p-2 bg-blue-50 rounded">
                <span className="text-xs text-blue-600">Sent</span>
                <Badge variant="outline" className="text-blue-600">{contractMetrics.sent}</Badge>
              </div>
              <div className="flex items-center justify-between p-2 bg-yellow-50 rounded">
                <span className="text-xs text-yellow-600">Viewed</span>
                <Badge variant="outline" className="text-yellow-600">{contractMetrics.viewed}</Badge>
              </div>
              <div className="flex items-center justify-between p-2 bg-green-50 rounded">
                <span className="text-xs text-green-600">Signed</span>
                <Badge variant="outline" className="text-green-600">{contractMetrics.signed}</Badge>
              </div>
              <div className="flex items-center justify-between p-2 bg-emerald-50 rounded">
                <span className="text-xs text-emerald-600">Executed</span>
                <Badge variant="outline" className="text-emerald-600">{contractMetrics.executed}</Badge>
              </div>
              <div className="flex items-center justify-between p-2 bg-red-50 rounded">
                <span className="text-xs text-red-600">Expired</span>
                <Badge variant="outline" className="text-red-600">{contractMetrics.expired}</Badge>
              </div>
            </div>
            <div className="text-right">
              <span className="text-sm font-medium text-gray-900">
                Total Value: {formatCurrency(contractMetrics.totalValue)}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
