import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get or create onboarding progress
    let onboardingProgress = await prisma.onboardingProgress.findFirst({
      where: { 
        companyId: session.user.companyId,
        userId: session.user.id 
      }
    })

    if (!onboardingProgress) {
      onboardingProgress = await prisma.onboardingProgress.create({
        data: {
          companyId: session.user.companyId,
          userId: session.user.id,
          step: 'complete',
          currentStep: 'complete',
          completedSteps: ['welcome', 'company-setup', 'first-customer', 'first-quotation', 'explore-features'],
          completed: true
        }
      })
    } else {
      // Update existing progress
      const completedSteps = (onboardingProgress.completedSteps as string[]) || []
      const allSteps = ['welcome', 'company-setup', 'first-customer', 'first-quotation', 'explore-features', 'complete']
      
      // Add any missing steps
      allSteps.forEach(step => {
        if (!completedSteps.includes(step)) {
          completedSteps.push(step)
        }
      })

      onboardingProgress = await prisma.onboardingProgress.update({
        where: { id: onboardingProgress.id },
        data: {
          step: 'complete',
          currentStep: 'complete',
          completedSteps,
          completed: true
        }
      })
    }

    // Create completion activity log
    await prisma.activity.create({
      data: {
        type: 'ONBOARDING',
        title: 'Onboarding completed',
        description: 'User completed the onboarding process',
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    // Send welcome email (in real implementation)
    await sendWelcomeEmail(session.user.id, session.user.companyId)

    // Create initial notification for completed onboarding
    await prisma.notification.create({
      data: {
        title: 'Welcome to BusinessSaaS! 🎉',
        message: 'Congratulations on completing your onboarding! You\'re now ready to manage your business with our powerful tools.',
        type: 'SUCCESS',
        companyId: session.user.companyId,
        userId: session.user.id,
        readAt: null
      }
    })

    // Create helpful tips notifications
    const tips = [
      {
        title: 'Pro Tip: Explore Analytics',
        message: 'Check out your analytics dashboard to track business performance and identify growth opportunities.',
        delay: 1000 * 60 * 60 * 24 // 1 day
      },
      {
        title: 'Don\'t Forget: Set Up Billing',
        message: 'Consider upgrading your plan to unlock advanced features and grow your business faster.',
        delay: 1000 * 60 * 60 * 24 * 3 // 3 days
      },
      {
        title: 'Feature Spotlight: API Integration',
        message: 'Did you know you can integrate BusinessSaaS with your existing tools using our powerful API?',
        delay: 1000 * 60 * 60 * 24 * 7 // 7 days
      }
    ]

    // Schedule tip notifications (in real implementation, use a job queue)
    for (const tip of tips) {
      setTimeout(async () => {
        try {
          await prisma.notification.create({
            data: {
              title: tip.title,
              message: tip.message,
              type: 'INFO',
              companyId: session.user.companyId,
              userId: session.user.id,
              readAt: null
            }
          })
        } catch (error) {
          console.error('Error creating tip notification:', error)
        }
      }, tip.delay)
    }

    return NextResponse.json({ 
      success: true, 
      progress: onboardingProgress,
      message: 'Onboarding completed successfully!'
    })
  } catch (error) {
    console.error('Error completing onboarding:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function sendWelcomeEmail(userId: string, companyId: string) {
  try {
    // Get user and company information
    const [user, company] = await Promise.all([
      prisma.user.findUnique({
        where: { id: userId },
        select: { email: true, firstName: true, lastName: true }
      }),
      prisma.company.findUnique({
        where: { id: companyId },
        select: { name: true, plan: true }
      })
    ])

    if (!user || !company) return

    const userName = user.firstName && user.lastName 
      ? `${user.firstName} ${user.lastName}` 
      : user.email

    // In a real implementation, send email using your email service
    const emailContent = {
      to: user.email,
      subject: `Welcome to BusinessSaaS, ${userName}! 🎉`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px 20px; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 28px;">Welcome to BusinessSaaS!</h1>
            <p style="color: white; margin: 10px 0 0 0; opacity: 0.9;">You're all set up and ready to go</p>
          </div>
          
          <div style="padding: 40px 20px;">
            <h2 style="color: #333; margin-bottom: 20px;">Hi ${userName},</h2>
            
            <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
              Congratulations on completing your onboarding! You've successfully set up <strong>${company.name}</strong> 
              on the <strong>${company.plan}</strong> plan and you're now ready to streamline your business operations.
            </p>
            
            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 30px 0;">
              <h3 style="color: #333; margin-top: 0;">What's Next?</h3>
              <ul style="color: #666; line-height: 1.8; padding-left: 20px;">
                <li>Explore your dashboard and familiarize yourself with the features</li>
                <li>Add more customers and start creating quotations</li>
                <li>Check out the analytics to track your business performance</li>
                <li>Consider upgrading your plan to unlock advanced features</li>
              </ul>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.NEXTAUTH_URL}/dashboard" 
                 style="background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; display: inline-block;">
                Go to Dashboard
              </a>
            </div>
            
            <div style="border-top: 1px solid #eee; padding-top: 20px; margin-top: 30px;">
              <h3 style="color: #333; margin-bottom: 15px;">Need Help?</h3>
              <p style="color: #666; line-height: 1.6; margin-bottom: 15px;">
                Our support team is here to help you succeed:
              </p>
              <ul style="color: #666; line-height: 1.6; padding-left: 20px;">
                <li><a href="${process.env.NEXTAUTH_URL}/dashboard/support/knowledge-base" style="color: #667eea;">Browse our Knowledge Base</a></li>
                <li><a href="${process.env.NEXTAUTH_URL}/dashboard/support" style="color: #667eea;">Contact Support</a></li>
                <li>Email us at <a href="mailto:<EMAIL>" style="color: #667eea;"><EMAIL></a></li>
              </ul>
            </div>
          </div>
          
          <div style="background: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #eee;">
            <p style="color: #999; margin: 0; font-size: 14px;">
              Thanks for choosing BusinessSaaS!<br>
              The BusinessSaaS Team
            </p>
          </div>
        </div>
      `
    }

    // Log the email (in real implementation, actually send it)
    console.log('Welcome email would be sent to:', user.email)
    console.log('Email content:', emailContent.subject)

    // Create email record
    await prisma.email.create({
      data: {
        to: user.email,
        subject: emailContent.subject,
        body: emailContent.html,
        from: user.email,
        status: 'SENT',
        type: 'WELCOME',
        companyId,
        createdById: userId
      }
    })
  } catch (error) {
    console.error('Error sending welcome email:', error)
  }
}

// Utility function to reset onboarding (for testing)
async function resetOnboarding(companyId: string, userId: string) {
  try {
    await prisma.onboardingProgress.deleteMany({
      where: { companyId, userId }
    })

    return { success: true, message: 'Onboarding reset successfully' }
  } catch (error) {
    console.error('Error resetting onboarding:', error)
    throw error
  }
}

// Utility function to get onboarding completion stats
async function getOnboardingStats() {
  try {
    const [total, completed, byStep] = await Promise.all([
      prisma.onboardingProgress.count(),
      prisma.onboardingProgress.count({
        where: { completed: true }
      }),
      prisma.onboardingProgress.groupBy({
        by: ['currentStep'],
        _count: true
      })
    ])

    const completionRate = total > 0 ? (completed / total) * 100 : 0

    return {
      total,
      completed,
      completionRate: Math.round(completionRate * 100) / 100,
      byStep
    }
  } catch (error) {
    console.error('Error getting onboarding stats:', error)
    return {
      total: 0,
      completed: 0,
      completionRate: 0,
      byStep: []
    }
  }
}
