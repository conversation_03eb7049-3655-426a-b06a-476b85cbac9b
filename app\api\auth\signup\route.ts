import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { hash } from 'bcryptjs'
import { z } from 'zod'

const signupSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  companyName: z.string().min(1, 'Company name is required'),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validatedData = signupSchema.parse(body)
    
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: validatedData.email }
    })
    
    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 400 }
      )
    }
    
    // Hash password
    const hashedPassword = await hash(validatedData.password, 12)
    
    // Create user and company in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create company first
      const company = await tx.company.create({
        data: {
          name: validatedData.companyName,
          status: 'TRIAL',
          trialEndsAt: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
          owner: {
            create: {
              firstName: validatedData.firstName,
              lastName: validatedData.lastName,
              name: `${validatedData.firstName} ${validatedData.lastName}`,
              email: validatedData.email,
              password: hashedPassword,
              role: 'ADMIN',
            }
          }
        },
        include: {
          owner: true
        }
      })
      
      return company
    })
    
    // Create initial sample data
    await createSampleData(result.id, result.ownerId)
    
    return NextResponse.json(
      { 
        message: 'Account created successfully',
        userId: result.ownerId,
        companyId: result.id
      },
      { status: 201 }
    )
    
  } catch (error) {
    console.error('Signup error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function createSampleData(companyId: string, userId: string) {
  try {
    // Create sample customer
    const customer = await prisma.customer.create({
      data: {
        name: 'Sample Customer',
        email: '<EMAIL>',
        phone: '+91 **********',
        company: 'Sample Company Ltd.',
        address: '123 Business Street',
        city: 'Mumbai',
        state: 'Maharashtra',
        country: 'India',
        postalCode: '400001',
        companyId,
        createdById: userId,
      }
    })
    
    // Create sample items
    const items = await Promise.all([
      prisma.item.create({
        data: {
          name: 'Web Development Service',
          description: 'Professional web development services',
          price: 50000,
          category: 'Services',
          unit: 'Project',
          companyId,
          createdById: userId,
        }
      }),
      prisma.item.create({
        data: {
          name: 'Mobile App Development',
          description: 'Native mobile application development',
          price: 75000,
          category: 'Services',
          unit: 'Project',
          companyId,
          createdById: userId,
        }
      }),
      prisma.item.create({
        data: {
          name: 'Consulting Hours',
          description: 'Business consulting and strategy',
          price: 2500,
          category: 'Consulting',
          unit: 'Hour',
          companyId,
          createdById: userId,
        }
      })
    ])
    
    // Create sample lead
    const lead = await prisma.lead.create({
      data: {
        title: 'Website Redesign Project',
        description: 'Complete website redesign for better user experience',
        status: 'NEW',
        priority: 'HIGH',
        value: 100000,
        source: 'Website',
        customerId: customer.id,
        companyId,
        createdById: userId,
      }
    })
    
    // Create sample quotation
    const quotation = await prisma.quotation.create({
      data: {
        quotationNumber: 'QUO-202401-0001',
        title: 'Website Development Proposal',
        description: 'Complete website development with modern design',
        status: 'DRAFT',
        validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
        terms: 'Payment terms: 50% advance, 50% on completion',
        paymentTerms: 'Net 30 days',
        taxRate: 18,
        discountType: 'PERCENTAGE',
        discountValue: 0,
        subtotal: 50000,
        total: 59000,
        customerId: customer.id,
        leadId: lead.id,
        companyId,
        createdById: userId,
        items: {
          create: [
            {
              itemId: items[0].id,
              name: items[0].name,
              description: items[0].description,
              quantity: 1,
              unitPrice: items[0].price,
              discount: 0,
              taxRate: 18,
            }
          ]
        }
      }
    })
    
    // Create welcome activity
    await prisma.activity.create({
      data: {
        type: 'NOTE',
        title: 'Welcome to BusinessSaaS!',
        description: 'Your account has been created successfully. Sample data has been added to help you get started.',
        companyId,
        createdById: userId,
      }
    })
    
    console.log('Sample data created successfully')
  } catch (error) {
    console.error('Error creating sample data:', error)
    // Don't throw error as account creation was successful
  }
}
