'use client'

import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  ChevronDown,
  ChevronUp,
  HelpCircle,
  MessageSquare,
  ArrowRight
} from 'lucide-react'

export function FAQSection() {
  const [openItems, setOpenItems] = useState<number[]>([0])

  const toggleItem = (index: number) => {
    setOpenItems(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    )
  }

  const faqs = [
    {
      category: 'Getting Started',
      question: 'How quickly can I get started with BusinessSaaS?',
      answer: 'You can get started immediately with our 14-day free trial. Our guided onboarding process takes just 5-10 minutes to complete, and you can start creating quotations and managing customers right away. No credit card required for the trial.'
    },
    {
      category: 'Features',
      question: 'What types of documents can I create?',
      answer: 'BusinessSaaS supports creating professional quotations, invoices, contracts, receipts, and custom business documents. All documents are fully customizable with your branding, terms, and conditions. You can also create templates for recurring document types.'
    },
    {
      category: 'Pricing',
      question: 'Can I change my plan at any time?',
      answer: 'Yes, you can upgrade or downgrade your plan at any time. When you upgrade, you get immediate access to new features. When you downgrade, changes take effect at your next billing cycle. There are no cancellation fees or long-term contracts.'
    },
    {
      category: 'Integration',
      question: 'Does BusinessSaaS integrate with other tools?',
      answer: 'Yes, we offer integrations with popular tools like Stripe for payments, email marketing platforms, accounting software, and CRM systems. Our Professional and Enterprise plans include API access for custom integrations.'
    },
    {
      category: 'Security',
      question: 'How secure is my data?',
      answer: 'We take security seriously. All data is encrypted in transit and at rest using industry-standard encryption. We\'re SOC 2 compliant, perform regular security audits, and maintain 99.9% uptime. Your data is backed up daily and stored in secure, geographically distributed data centers.'
    },
    {
      category: 'Support',
      question: 'What kind of support do you provide?',
      answer: 'Support varies by plan: Basic includes email support with 24-hour response time. Professional adds priority support and phone support during business hours. Enterprise includes 24/7 support with a dedicated account manager and 1-hour response time for critical issues.'
    },
    {
      category: 'Migration',
      question: 'Can you help me migrate from my current system?',
      answer: 'Absolutely! We provide free migration assistance for all plans. Our team can help you import your existing customer data, templates, and documents. For Enterprise customers, we offer white-glove migration service with dedicated support throughout the process.'
    },
    {
      category: 'Billing',
      question: 'What payment methods do you accept?',
      answer: 'We accept all major credit cards (Visa, MasterCard, American Express), PayPal, and bank transfers for annual plans. Enterprise customers can also pay by invoice. All payments are processed securely through Stripe.'
    },
    {
      category: 'Features',
      question: 'Is there a limit on the number of customers or documents?',
      answer: 'Limits vary by plan: Basic allows up to 50 quotations and invoices per month. Professional increases this to 500 per month. Enterprise plans have unlimited documents. All plans include unlimited customer storage.'
    },
    {
      category: 'Mobile',
      question: 'Can I use BusinessSaaS on mobile devices?',
      answer: 'Yes! BusinessSaaS is fully responsive and works great on tablets and smartphones. You can create, edit, and send documents from anywhere. We also offer mobile apps for iOS and Android with offline capabilities.'
    }
  ]

  const categories = [...new Set(faqs.map(faq => faq.category))]

  return (
    <section id="faq" className="py-20 px-4 bg-white">
      <div className="container mx-auto max-w-4xl">
        {/* Section Header */}
        <div className="text-center mb-16">
          <Badge variant="outline" className="mb-4">
            <HelpCircle className="h-4 w-4 mr-2" />
            Frequently Asked Questions
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Got Questions?
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent block">
              We Have Answers
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
            Find answers to the most common questions about BusinessSaaS. 
            Can't find what you're looking for? Our support team is here to help.
          </p>
        </div>

        {/* FAQ Categories */}
        <div className="flex flex-wrap justify-center gap-2 mb-12">
          {categories.map((category, index) => (
            <Badge key={index} variant="secondary" className="px-3 py-1">
              {category}
            </Badge>
          ))}
        </div>

        {/* FAQ Items */}
        <div className="space-y-4 mb-12">
          {faqs.map((faq, index) => {
            const isOpen = openItems.includes(index)
            
            return (
              <Card key={index} className="border shadow-sm hover:shadow-md transition-shadow">
                <CardContent className="p-0">
                  <button
                    className="w-full text-left p-6 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset"
                    onClick={() => toggleItem(index)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-1">
                          <Badge variant="outline" className="text-xs">
                            {faq.category}
                          </Badge>
                        </div>
                        <h3 className="text-lg font-semibold text-gray-900 pr-8">
                          {faq.question}
                        </h3>
                      </div>
                      <div className="flex-shrink-0">
                        {isOpen ? (
                          <ChevronUp className="h-5 w-5 text-gray-500" />
                        ) : (
                          <ChevronDown className="h-5 w-5 text-gray-500" />
                        )}
                      </div>
                    </div>
                  </button>
                  
                  {isOpen && (
                    <div className="px-6 pb-6">
                      <div className="border-t pt-4">
                        <p className="text-gray-600 leading-relaxed">
                          {faq.answer}
                        </p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Contact Support CTA */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8 text-center">
          <MessageSquare className="h-12 w-12 text-blue-600 mx-auto mb-4" />
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Still Have Questions?
          </h3>
          <p className="text-gray-600 mb-6 max-w-md mx-auto">
            Our friendly support team is here to help. Get in touch and we'll get back to you as soon as possible.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="outline" className="bg-white">
              Browse Help Center
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
            <Button className="bg-blue-600 hover:bg-blue-700">
              Contact Support
              <MessageSquare className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Quick Links */}
        <div className="mt-12 grid md:grid-cols-3 gap-6 text-center">
          <div className="p-4">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <HelpCircle className="h-6 w-6 text-blue-600" />
            </div>
            <h4 className="font-semibold text-gray-900 mb-2">Help Center</h4>
            <p className="text-sm text-gray-600 mb-3">
              Comprehensive guides and tutorials
            </p>
            <Button variant="ghost" >
              Visit Help Center
            </Button>
          </div>
          
          <div className="p-4">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <MessageSquare className="h-6 w-6 text-green-600" />
            </div>
            <h4 className="font-semibold text-gray-900 mb-2">Live Chat</h4>
            <p className="text-sm text-gray-600 mb-3">
              Get instant help from our team
            </p>
            <Button variant="ghost" >
              Start Chat
            </Button>
          </div>
          
          <div className="p-4">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <ArrowRight className="h-6 w-6 text-purple-600" />
            </div>
            <h4 className="font-semibold text-gray-900 mb-2">Schedule Demo</h4>
            <p className="text-sm text-gray-600 mb-3">
              See BusinessSaaS in action
            </p>
            <Button variant="ghost" >
              Book Demo
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
