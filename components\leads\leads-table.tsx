'use client'

import Link from 'next/link'
import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { formatDate, formatCurrency, getStatusColor } from '@/lib/utils'
import { 
  Eye, 
  Edit, 
  Trash2, 
  Mail, 
  Phone, 
  Building,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  DollarSign,
  Calendar,
  User
} from 'lucide-react'
import toast from 'react-hot-toast'

interface Lead {
  id: string
  title: string
  description: string | null
  status: string
  priority: string
  value: number | null
  source: string | null
  createdAt: Date
  customer: {
    id: string
    name: string
    email: string | null
    company: string | null
  } | null
  _count: {
    quotations: number
    activities: number
  }
}

interface LeadsTableProps {
  leads: Lead[]
  currentPage: number
  totalPages: number
  totalCount: number
}

export function LeadsTable({ 
  leads, 
  currentPage, 
  totalPages, 
  totalCount 
}: LeadsTableProps) {
  const [deletingId, setDeletingId] = useState<string | null>(null)

  const handleDelete = async (leadId: string) => {
    if (!confirm('Are you sure you want to delete this lead?')) {
      return
    }

    setDeletingId(leadId)
    try {
      const response = await fetch(`/api/leads/${leadId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to delete lead')
      }

      toast.success('Lead deleted successfully')
      window.location.reload()
    } catch (error) {
      toast.error('Failed to delete lead')
    } finally {
      setDeletingId(null)
    }
  }

  if (leads.length === 0) {
    return (
      <Card>
        <CardContent className="py-12">
          <div className="text-center">
            <Building className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No leads found</h3>
            <p className="text-gray-500 mb-6">
              Get started by adding your first lead.
            </p>
            <Link href="/dashboard/leads/new">
              <Button>
                Add Lead
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Leads ({totalCount})</span>
            <span className="text-sm font-normal text-gray-500">
              Page {currentPage} of {totalPages}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Desktop Table */}
          <div className="hidden md:block overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Lead</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Customer</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Priority</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Value</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Source</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Created</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">Actions</th>
                </tr>
              </thead>
              <tbody>
                {leads.map((lead) => (
                  <tr key={lead.id} className="border-b border-gray-100 hover:bg-gray-50">
                    <td className="py-4 px-4">
                      <div>
                        <div className="font-medium text-gray-900">{lead.title}</div>
                        {lead.description && (
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {lead.description}
                          </div>
                        )}
                        <div className="text-xs text-gray-400 mt-1">
                          {lead._count.quotations} quotations • {lead._count.activities} activities
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      {lead.customer ? (
                        <div>
                          <div className="font-medium text-gray-900">{lead.customer.name}</div>
                          {lead.customer.company && (
                            <div className="text-sm text-gray-500">{lead.customer.company}</div>
                          )}
                          {lead.customer.email && (
                            <div className="text-sm text-gray-500">{lead.customer.email}</div>
                          )}
                        </div>
                      ) : (
                        <span className="text-gray-400 italic">No customer</span>
                      )}
                    </td>
                    <td className="py-4 px-4">
                      <Badge className={getStatusColor(lead.status)} variant="outline">
                        {lead.status.replace('_', ' ')}
                      </Badge>
                    </td>
                    <td className="py-4 px-4">
                      <Badge className={getStatusColor(lead.priority)} variant="outline">
                        {lead.priority}
                      </Badge>
                    </td>
                    <td className="py-4 px-4">
                      {lead.value ? (
                        <span className="font-medium text-gray-900">
                          {formatCurrency(lead.value)}
                        </span>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </td>
                    <td className="py-4 px-4 text-sm text-gray-600">
                      {lead.source || '-'}
                    </td>
                    <td className="py-4 px-4 text-sm text-gray-600">
                      {formatDate(lead.createdAt)}
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center justify-end space-x-2">
                        <Link href={`/dashboard/leads/${lead.id}`}>
                          <Button variant="ghost" >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </Link>
                        <Link href={`/dashboard/leads/${lead.id}/edit`}>
                          <Button variant="ghost" >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </Link>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(lead.id)}
                          disabled={deletingId === lead.id}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Mobile Cards */}
          <div className="md:hidden space-y-4">
            {leads.map((lead) => (
              <Card key={lead.id} className="p-4">
                <div className="space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">{lead.title}</div>
                      {lead.description && (
                        <div className="text-sm text-gray-500 mt-1 line-clamp-2">
                          {lead.description}
                        </div>
                      )}
                    </div>
                    <Button variant="ghost" >
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>

                  {lead.customer && (
                    <div className="flex items-center space-x-2">
                      <User className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600">{lead.customer.name}</span>
                      {lead.customer.company && (
                        <>
                          <Building className="h-4 w-4 text-gray-400" />
                          <span className="text-sm text-gray-600">{lead.customer.company}</span>
                        </>
                      )}
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Badge className={getStatusColor(lead.status)} variant="outline">
                        {lead.status.replace('_', ' ')}
                      </Badge>
                      <Badge className={getStatusColor(lead.priority)} variant="outline">
                        {lead.priority}
                      </Badge>
                    </div>
                    {lead.value && (
                      <div className="flex items-center space-x-1">
                        <DollarSign className="h-4 w-4 text-gray-400" />
                        <span className="font-medium text-gray-900">
                          {formatCurrency(lead.value)}
                        </span>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4" />
                      <span>{formatDate(lead.createdAt)}</span>
                    </div>
                    <div>
                      {lead._count.quotations} quotations • {lead._count.activities} activities
                    </div>
                  </div>

                  <div className="flex items-center justify-end space-x-2 pt-2 border-t">
                    <Link href={`/dashboard/leads/${lead.id}`}>
                      <Button variant="ghost" >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </Link>
                    <Link href={`/dashboard/leads/${lead.id}/edit`}>
                      <Button variant="ghost" >
                        <Edit className="h-4 w-4" />
                      </Button>
                    </Link>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            Showing {((currentPage - 1) * 10) + 1} to {Math.min(currentPage * 10, totalCount)} of {totalCount} leads
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage <= 1}
              asChild
            >
              <Link href={`?page=${currentPage - 1}`}>
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Link>
            </Button>
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage >= totalPages}
              asChild
            >
              <Link href={`?page=${currentPage + 1}`}>
                Next
                <ChevronRight className="h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
