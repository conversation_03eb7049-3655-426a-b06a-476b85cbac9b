'use client'

import Link from 'next/link'
import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { formatDate, getStatusColor } from '@/lib/utils'
import { 
  Eye, 
  Edit, 
  Trash2, 
  Copy,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  FileText,
  Calendar,
  User,
  Tag,
  CheckCircle,
  Edit3,
  Archive,
  TrendingUp
} from 'lucide-react'
import toast from 'react-hot-toast'

interface TemplateData {
  id: string
  name: string
  description: string | null
  category: string | null
  status: string
  content: string
  variables: any
  createdAt: Date
  updatedAt: Date
  createdBy: {
    name: string | null
    firstName: string | null
    lastName: string | null
  } | null
  _count: {
    contracts: number
  }
}

interface TemplatesTableProps {
  templates: TemplateData[]
  currentPage: number
  totalPages: number
  totalCount: number
}

export function TemplatesTable({ 
  templates, 
  currentPage, 
  totalPages, 
  totalCount 
}: TemplatesTableProps) {
  const [deletingId, setDeletingId] = useState<string | null>(null)

  const handleDelete = async (templateId: string) => {
    if (!confirm('Are you sure you want to delete this template?')) {
      return
    }

    setDeletingId(templateId)
    try {
      const response = await fetch(`/api/contract-templates/${templateId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete template')
      }

      toast.success('Template deleted successfully')
      window.location.reload()
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete template'
      toast.error(errorMessage)
    } finally {
      setDeletingId(null)
    }
  }

  const handleDuplicate = async (templateId: string) => {
    try {
      const response = await fetch(`/api/contract-templates/${templateId}/duplicate`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to duplicate template')
      }

      const result = await response.json()
      toast.success('Template duplicated successfully')
      window.location.href = `/dashboard/contract-templates/${result.id}/edit`
    } catch (error) {
      toast.error('Failed to duplicate template')
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return CheckCircle
      case 'DRAFT':
        return Edit3
      case 'ARCHIVED':
        return Archive
      default:
        return FileText
    }
  }

  const getUserName = (user: any) => {
    if (!user) return 'System'
    if (user.name) return user.name
    if (user.firstName && user.lastName) return `${user.firstName} ${user.lastName}`
    if (user.firstName) return user.firstName
    return 'Unknown User'
  }

  if (templates.length === 0) {
    return (
      <Card>
        <CardContent className="py-12">
          <div className="text-center">
            <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No templates found</h3>
            <p className="text-gray-500 mb-6">
              Get started by creating your first contract template.
            </p>
            <Link href="/dashboard/contract-templates/new">
              <Button>
                Create Template
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Templates ({totalCount})</span>
            <span className="text-sm font-normal text-gray-500">
              Page {currentPage} of {totalPages}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Desktop Table */}
          <div className="hidden md:block overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Template</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Category</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Usage</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Created</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Updated</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">Actions</th>
                </tr>
              </thead>
              <tbody>
                {templates.map((template) => {
                  const StatusIcon = getStatusIcon(template.status)
                  
                  return (
                    <tr key={template.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-4 px-4">
                        <div>
                          <div className="font-medium text-gray-900">{template.name}</div>
                          {template.description && (
                            <div className="text-sm text-gray-600 mt-1 truncate max-w-xs">
                              {template.description}
                            </div>
                          )}
                          <div className="text-xs text-gray-500 mt-1">
                            by {getUserName(template.createdBy)}
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        {template.category ? (
                          <div className="flex items-center space-x-2">
                            <Tag className="h-4 w-4 text-gray-400" />
                            <span className="text-sm text-gray-900">{template.category}</span>
                          </div>
                        ) : (
                          <span className="text-gray-400 text-sm">No category</span>
                        )}
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center space-x-2">
                          <StatusIcon className={`h-4 w-4 ${getStatusColor(template.status).replace('border-', 'text-').replace('text-', 'text-')}`} />
                          <Badge className={getStatusColor(template.status)} variant="outline">
                            {template.status}
                          </Badge>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center space-x-2">
                          <TrendingUp className="h-4 w-4 text-blue-600" />
                          <span className="font-medium text-gray-900">
                            {template._count.contracts}
                          </span>
                          <span className="text-sm text-gray-600">contracts</span>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="text-sm text-gray-900">
                          {formatDate(template.createdAt)}
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="text-sm text-gray-900">
                          {formatDate(template.updatedAt)}
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center justify-end space-x-2">
                          <Link href={`/dashboard/contract-templates/${template.id}`}>
                            <Button variant="ghost" >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </Link>
                          <Link href={`/dashboard/contract-templates/${template.id}/edit`}>
                            <Button variant="ghost" >
                              <Edit className="h-4 w-4" />
                            </Button>
                          </Link>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDuplicate(template.id)}
                            className="text-green-600 hover:text-green-700"
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(template.id)}
                            disabled={deletingId === template.id}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>

          {/* Mobile Cards */}
          <div className="md:hidden space-y-4">
            {templates.map((template) => {
              const StatusIcon = getStatusIcon(template.status)
              
              return (
                <Card key={template.id} className="p-4">
                  <div className="space-y-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">{template.name}</div>
                        {template.description && (
                          <div className="text-sm text-gray-600 mt-1">
                            {template.description}
                          </div>
                        )}
                      </div>
                      <Button variant="ghost" >
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <StatusIcon className={`h-4 w-4 ${getStatusColor(template.status).replace('border-', 'text-').replace('text-', 'text-')}`} />
                        <Badge className={getStatusColor(template.status)} variant="outline">
                          {template.status}
                        </Badge>
                      </div>
                      <div className="flex items-center space-x-2">
                        <TrendingUp className="h-4 w-4 text-blue-600" />
                        <span className="text-sm font-medium text-gray-900">
                          {template._count.contracts} contracts
                        </span>
                      </div>
                    </div>

                    {template.category && (
                      <div className="flex items-center space-x-2">
                        <Tag className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-600">{template.category}</span>
                      </div>
                    )}

                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <div className="flex items-center space-x-2">
                        <User className="h-4 w-4" />
                        <span>{getUserName(template.createdBy)}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4" />
                        <span>{formatDate(template.createdAt)}</span>
                      </div>
                    </div>

                    <div className="flex items-center justify-end space-x-2 pt-2 border-t">
                      <Link href={`/dashboard/contract-templates/${template.id}`}>
                        <Button variant="ghost" >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </Link>
                      <Link href={`/dashboard/contract-templates/${template.id}/edit`}>
                        <Button variant="ghost" >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </Link>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDuplicate(template.id)}
                        className="text-green-600"
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </Card>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            Showing {((currentPage - 1) * 10) + 1} to {Math.min(currentPage * 10, totalCount)} of {totalCount} templates
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage <= 1}
              asChild
            >
              <Link href={`?page=${currentPage - 1}`}>
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Link>
            </Button>
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage >= totalPages}
              asChild
            >
              <Link href={`?page=${currentPage + 1}`}>
                Next
                <ChevronRight className="h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
