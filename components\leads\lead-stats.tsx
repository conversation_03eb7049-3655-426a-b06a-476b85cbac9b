'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { formatCurrency } from '@/lib/utils'
import {
  TrendingUp,
  Users,
  Phone,
  CheckCircle,
  FileText,
  UserCheck,
  Trophy,
  XCircle,
  DollarSign
} from 'lucide-react'

interface LeadStatsProps {
  stats: {
    total: number
    new: number
    contacted: number
    qualified: number
    proposal: number
    negotiation: number
    closedWon: number
    closedLost: number
    totalValue: number
    wonValue: number
  }
}

export function LeadStats({ stats }: LeadStatsProps) {
  const conversionRate = stats.total > 0 ? ((stats.closedWon / stats.total) * 100).toFixed(1) : '0'
  const winRate = (stats.closedWon + stats.closedLost) > 0 ? 
    ((stats.closedWon / (stats.closedWon + stats.closedLost)) * 100).toFixed(1) : '0'

  const statCards = [
    {
      title: 'Total Leads',
      value: stats.total.toString(),
      icon: TrendingUp,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      description: 'All leads in pipeline'
    },
    {
      title: 'New Leads',
      value: stats.new.toString(),
      icon: Users,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      description: 'Awaiting contact'
    },
    {
      title: 'In Progress',
      value: (stats.contacted + stats.qualified + stats.proposal + stats.negotiation).toString(),
      icon: Phone,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
      description: 'Active opportunities'
    },
    {
      title: 'Closed Won',
      value: stats.closedWon.toString(),
      icon: Trophy,
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-100',
      description: 'Successfully converted'
    },
    {
      title: 'Pipeline Value',
      value: formatCurrency(stats.totalValue),
      icon: DollarSign,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      description: 'Total opportunity value'
    },
    {
      title: 'Won Value',
      value: formatCurrency(stats.wonValue),
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      description: 'Revenue from won deals'
    },
    {
      title: 'Conversion Rate',
      value: `${conversionRate}%`,
      icon: UserCheck,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-100',
      description: 'Leads to customers'
    },
    {
      title: 'Win Rate',
      value: `${winRate}%`,
      icon: FileText,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      description: 'Won vs lost deals'
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statCards.map((stat, index) => (
        <Card key={index} className="hover:shadow-lg transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              {stat.title}
            </CardTitle>
            <div className={`p-2 rounded-lg ${stat.bgColor}`}>
              <stat.icon className={`h-4 w-4 ${stat.color}`} />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">
              {stat.value}
            </div>
            <p className="text-xs text-gray-500 mt-1">
              {stat.description}
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
