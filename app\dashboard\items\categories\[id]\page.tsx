import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { notFound } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Tag,
  ArrowLeft,
  Edit,
  Copy,
  Trash2,
  Package,
  Calendar,
  User,
  FolderOpen
} from 'lucide-react'

interface PageProps {
  params: {
    id: string
  }
}

export default async function CategoryViewPage({ params }: PageProps) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  const category = await prisma.itemCategory.findFirst({
    where: {
      id: params.id,
      companyId: session.user.companyId
    },
    include: {
      createdBy: {
        select: { name: true, firstName: true, lastName: true }
      },
      parent: {
        select: { id: true, name: true, color: true }
      },
      children: {
        select: { id: true, name: true, color: true, _count: { select: { items: true } } }
      },
      items: {
        select: { 
          id: true, 
          name: true, 
          price: true, 
          type: true, 
          status: true,
          stockQuantity: true 
        },
        take: 10,
        orderBy: { createdAt: 'desc' }
      },
      _count: {
        select: {
          items: true,
          children: true
        }
      }
    }
  })

  if (!category) {
    notFound()
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/items/categories">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Categories
            </Button>
          </Link>
          <div className="p-2 bg-blue-100 rounded-lg">
            <Tag className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <div className="flex items-center space-x-3">
              {category.color && (
                <div 
                  className="w-4 h-4 rounded-full"
                  style={{ backgroundColor: category.color }}
                />
              )}
              <h1 className="text-3xl font-bold text-gray-900">{category.name}</h1>
            </div>
            <p className="text-gray-600 mt-1">{category.description}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Link href={`/dashboard/items/categories/${category.id}/edit`}>
            <Button>
              <Edit className="h-4 w-4 mr-2" />
              Edit Category
            </Button>
          </Link>
          <Button variant="outline">
            <Copy className="h-4 w-4 mr-2" />
            Duplicate
          </Button>
          <Button variant="outline" className="text-red-600 hover:text-red-700">
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Category Info */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Category Information</CardTitle>
                <Badge variant={category.isActive ? 'default' : 'secondary'}>
                  {category.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {category.parent && (
                <div>
                  <label className="text-sm font-medium text-gray-600">Parent Category</label>
                  <div className="flex items-center space-x-2 mt-1">
                    {category.parent.color && (
                      <div 
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: category.parent.color }}
                      />
                    )}
                    <Link 
                      href={`/dashboard/items/categories/${category.parent.id}`}
                      className="text-blue-600 hover:text-blue-800"
                    >
                      {category.parent.name}
                    </Link>
                  </div>
                </div>
              )}
              
              {category.description && (
                <div>
                  <label className="text-sm font-medium text-gray-600">Description</label>
                  <p className="text-lg">{category.description}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Subcategories */}
          {category.children.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Subcategories ({category.children.length})</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {category.children.map((child) => (
                    <Link 
                      key={child.id}
                      href={`/dashboard/items/categories/${child.id}`}
                      className="block p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-sm transition-all"
                    >
                      <div className="flex items-center space-x-3">
                        {child.color && (
                          <div 
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: child.color }}
                          />
                        )}
                        <div>
                          <p className="font-medium">{child.name}</p>
                          <p className="text-sm text-gray-500">{child._count.items} items</p>
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Items in Category */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Items in Category ({category._count.items})</CardTitle>
                {category._count.items > 10 && (
                  <Link href={`/dashboard/items?category=${category.id}`}>
                    <Button variant="outline" size="sm">View All</Button>
                  </Link>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {category.items.length > 0 ? (
                <div className="space-y-3">
                  {category.items.map((item) => (
                    <Link 
                      key={item.id}
                      href={`/dashboard/items/${item.id}`}
                      className="block p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-sm transition-all"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <Package className="h-5 w-5 text-gray-400" />
                          <div>
                            <p className="font-medium">{item.name}</p>
                            <div className="flex items-center space-x-2 text-sm text-gray-500">
                              <Badge variant="outline">{item.type}</Badge>
                              <span>${item.price}</span>
                              {item.stockQuantity !== null && (
                                <span>Stock: {item.stockQuantity}</span>
                              )}
                            </div>
                          </div>
                        </div>
                        <Badge variant={item.status === 'ACTIVE' ? 'default' : 'secondary'}>
                          {item.status}
                        </Badge>
                      </div>
                    </Link>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No items in this category</h3>
                  <p className="text-gray-600 mb-4">Start by adding items to this category.</p>
                  <Link href="/dashboard/items/new">
                    <Button>Add Item</Button>
                  </Link>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Category Details */}
          <Card>
            <CardHeader>
              <CardTitle>Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-3">
                <Calendar className="h-4 w-4 text-gray-400" />
                <div>
                  <p className="text-sm font-medium">Created</p>
                  <p className="text-sm text-gray-600">
                    {new Date(category.createdAt).toLocaleDateString()}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <User className="h-4 w-4 text-gray-400" />
                <div>
                  <p className="text-sm font-medium">Created By</p>
                  <p className="text-sm text-gray-600">
                    {category.createdBy?.name || 'Unknown'}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Package className="h-4 w-4 text-gray-400" />
                <div>
                  <p className="text-sm font-medium">Items Count</p>
                  <p className="text-sm text-gray-600">
                    {category._count.items} items
                  </p>
                </div>
              </div>

              {category._count.children > 0 && (
                <div className="flex items-center space-x-3">
                  <FolderOpen className="h-4 w-4 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium">Subcategories</p>
                    <p className="text-sm text-gray-600">
                      {category._count.children} subcategories
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Link href={`/dashboard/items/categories/${category.id}/edit`}>
                <Button className="w-full" variant="outline">
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Category
                </Button>
              </Link>
              <Button className="w-full" variant="outline">
                <Copy className="h-4 w-4 mr-2" />
                Duplicate Category
              </Button>
              <Link href={`/dashboard/items?category=${category.id}`}>
                <Button className="w-full" variant="outline">
                  <Package className="h-4 w-4 mr-2" />
                  View All Items
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
