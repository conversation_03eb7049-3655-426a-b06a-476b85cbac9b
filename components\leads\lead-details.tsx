'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { getStatusColor, formatDate, formatCurrency } from '@/lib/utils'
import { 
  User, 
  Building, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar,
  DollarSign,
  Target,
  TrendingUp,
  FileText,
  Receipt,

  Edit
} from 'lucide-react'
import toast from 'react-hot-toast'

interface LeadDetailsProps {
  lead: {
    id: string
    title: string
    description: string | null
    status: string
    priority: string
    value: number | null
    source: string | null
    createdAt: Date
    customer: {
      id: string
      name: string
      email: string | null
      company: string | null
      phone: string | null
      address: string | null
      city: string | null
      state: string | null
      country: string | null
    } | null
    _count: {
      quotations: number
      activities: number
    }
  }
}

export function LeadDetails({ lead }: LeadDetailsProps) {
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false)

  const handleStatusChange = async (newStatus: string) => {
    setIsUpdatingStatus(true)
    try {
      const response = await fetch(`/api/leads/${lead.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      })

      if (!response.ok) {
        throw new Error('Failed to update lead status')
      }

      toast.success('Lead status updated successfully')
      window.location.reload()
    } catch (error) {
      toast.error('Failed to update lead status')
    } finally {
      setIsUpdatingStatus(false)
    }
  }

  const fullAddress = lead.customer ? [
    lead.customer.address,
    lead.customer.city,
    lead.customer.state,
    lead.customer.country
  ].filter(Boolean).join(', ') : null

  const statusOptions = [
    { value: 'NEW', label: 'New' },
    { value: 'CONTACTED', label: 'Contacted' },
    { value: 'QUALIFIED', label: 'Qualified' },
    { value: 'PROPOSAL', label: 'Proposal' },
    { value: 'NEGOTIATION', label: 'Negotiation' },
    { value: 'CLOSED_WON', label: 'Closed Won' },
    { value: 'CLOSED_LOST', label: 'Closed Lost' },
  ]

  return (
    <div className="space-y-6">
      {/* Lead Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Lead Information</CardTitle>
            <Link href={`/dashboard/leads/${lead.id}/edit`}>
              <Button variant="outline" >
                <Edit className="h-4 w-4 mr-2" />
                Edit Lead
              </Button>
            </Link>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Lead Information */}
            <div className="space-y-4">
              <h3 className="font-medium text-gray-900">Lead Details</h3>
              
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-gray-600">Title</p>
                  <p className="font-medium text-gray-900">{lead.title}</p>
                </div>

                {lead.description && (
                  <div>
                    <p className="text-sm text-gray-600">Description</p>
                    <p className="text-gray-900 whitespace-pre-wrap">{lead.description}</p>
                  </div>
                )}

                <div className="flex items-center space-x-4">
                  <div>
                    <p className="text-sm text-gray-600">Status</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <Badge className={getStatusColor(lead.status)} variant="outline">
                        {lead.status.replace('_', ' ')}
                      </Badge>
                      <select
                        value={lead.status}
                        onChange={(e) => handleStatusChange(e.target.value)}
                        disabled={isUpdatingStatus}
                        className="text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        {statusOptions.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm text-gray-600">Priority</p>
                    <Badge className={getStatusColor(lead.priority)} variant="outline">
                      {lead.priority}
                    </Badge>
                  </div>
                </div>

                {lead.value && (
                  <div className="flex items-center space-x-3">
                    <DollarSign className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">Estimated Value</p>
                      <p className="font-medium text-gray-900">{formatCurrency(lead.value)}</p>
                    </div>
                  </div>
                )}

                {lead.source && (
                  <div className="flex items-center space-x-3">
                    <Target className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">Source</p>
                      <p className="font-medium text-gray-900">{lead.source}</p>
                    </div>
                  </div>
                )}

                <div className="flex items-center space-x-3">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-600">Created</p>
                    <p className="font-medium text-gray-900">{formatDate(lead.createdAt)}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Customer Information */}
            <div className="space-y-4">
              <h3 className="font-medium text-gray-900">Customer Information</h3>
              
              {lead.customer ? (
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <User className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">Name</p>
                      <Link 
                        href={`/dashboard/customers/${lead.customer.id}`}
                        className="font-medium text-blue-600 hover:text-blue-800"
                      >
                        {lead.customer.name}
                      </Link>
                    </div>
                  </div>

                  {lead.customer.company && (
                    <div className="flex items-center space-x-3">
                      <Building className="h-4 w-4 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-600">Company</p>
                        <p className="font-medium text-gray-900">{lead.customer.company}</p>
                      </div>
                    </div>
                  )}

                  {lead.customer.email && (
                    <div className="flex items-center space-x-3">
                      <Mail className="h-4 w-4 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-600">Email</p>
                        <a 
                          href={`mailto:${lead.customer.email}`}
                          className="font-medium text-blue-600 hover:text-blue-800"
                        >
                          {lead.customer.email}
                        </a>
                      </div>
                    </div>
                  )}

                  {lead.customer.phone && (
                    <div className="flex items-center space-x-3">
                      <Phone className="h-4 w-4 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-600">Phone</p>
                        <a 
                          href={`tel:${lead.customer.phone}`}
                          className="font-medium text-blue-600 hover:text-blue-800"
                        >
                          {lead.customer.phone}
                        </a>
                      </div>
                    </div>
                  )}

                  {fullAddress && (
                    <div className="flex items-start space-x-3">
                      <MapPin className="h-4 w-4 text-gray-400 mt-1" />
                      <div>
                        <p className="text-sm text-gray-600">Address</p>
                        <p className="font-medium text-gray-900">{fullAddress}</p>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <p className="text-gray-500 italic">No customer assigned</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Activity Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Activity Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 mb-2">
                <FileText className="h-4 w-4 text-blue-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900">{lead._count.quotations}</div>
              <div className="text-sm text-gray-600">Quotations</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-green-100 mb-2">
                <TrendingUp className="h-4 w-4 text-green-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900">{lead._count.activities}</div>
              <div className="text-sm text-gray-600">Activities</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-purple-100 mb-2">
                <Calendar className="h-4 w-4 text-purple-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900">
                {Math.ceil((new Date().getTime() - new Date(lead.createdAt).getTime()) / (1000 * 60 * 60 * 24))}
              </div>
              <div className="text-sm text-gray-600">Days Old</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            <Button variant="outline" className="justify-start" asChild>
              <Link href={`/dashboard/quotations/new?leadId=${lead.id}&customerId=${lead.customer?.id}`}>
                <FileText className="h-4 w-4 mr-2" />
                Create Quotation
              </Link>
            </Button>
            <Button variant="outline" className="justify-start" asChild>
              <Link href={`/dashboard/invoices/new?customerId=${lead.customer?.id}`}>
                <Receipt className="h-4 w-4 mr-2" />
                Create Invoice
              </Link>
            </Button>
            <Button variant="outline" className="justify-start" asChild>
              <Link href={`/dashboard/contracts/new?customerId=${lead.customer?.id}`}>
                <FileText className="h-4 w-4 mr-2" />
                Create Contract
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
