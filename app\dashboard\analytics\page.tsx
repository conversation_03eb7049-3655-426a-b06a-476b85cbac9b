import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { DashboardOverview } from '@/components/dashboard/dashboard-overview'
import { RevenueChart } from '@/components/dashboard/revenue-chart'
import { SalesMetrics } from '@/components/dashboard/sales-metrics'
import { TopCustomers } from '@/components/dashboard/top-customers'
import { TopItems } from '@/components/dashboard/top-items'
import { PipelineOverview } from '@/components/dashboard/pipeline-overview'
import { FinancialSummary } from '@/components/dashboard/financial-summary'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Calendar, 
  Download, 
  Filter, 
  RefreshCw, 
  BarChart3,
  TrendingUp,
  DollarSign,
  Users,
  FileText,
  Target
} from 'lucide-react'
import Link from 'next/link'

interface SearchParams {
  period?: string
  startDate?: string
  endDate?: string
}

export default async function AnalyticsPage({
  searchParams,
}: {
  searchParams: SearchParams
}) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  // Date range calculation
  const period = searchParams.period || '30d'
  const now = new Date()
  let startDate: Date
  let endDate: Date = now

  switch (period) {
    case '7d':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      break
    case '30d':
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      break
    case '90d':
      startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
      break
    case '1y':
      startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
      break
    case 'custom':
      startDate = searchParams.startDate ? new Date(searchParams.startDate) : new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      endDate = searchParams.endDate ? new Date(searchParams.endDate) : now
      break
    default:
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
  }

  // Fetch comprehensive analytics data
  const [
    // Overview metrics
    totalCustomers,
    totalLeads,
    totalQuotations,
    totalInvoices,
    totalContracts,
    totalItems,
    
    // Financial data
    revenueData,
    quotationStats,
    invoiceStats,
    contractStats,
    
    // Top performers
    topCustomers,
    topItems,
    
    // Pipeline data
    leadsByStatus,
    quotationsByStatus,
    invoicesByStatus,
    contractsByStatus,
    
    // Additional analytics
    itemsByCategory,
    customersByStatus,
    revenueByMonth
  ] = await Promise.all([
    // Overview counts
    prisma.customer.count({ where: { companyId: session.user.companyId } }),
    prisma.lead.count({ where: { companyId: session.user.companyId } }),
    prisma.quotation.count({ where: { companyId: session.user.companyId } }),
    prisma.invoice.count({ where: { companyId: session.user.companyId } }),
    prisma.contract.count({ where: { companyId: session.user.companyId } }),
    prisma.item.count({ where: { companyId: session.user.companyId } }),
    
    // Financial aggregations
    prisma.invoice.aggregate({
      where: {
        companyId: session.user.companyId,
        status: 'PAID',
        paidAt: { gte: startDate, lte: endDate }
      },
      _sum: { total: true },
      _count: true
    }),
    
    // Status breakdowns
    prisma.quotation.groupBy({
      by: ['status'],
      where: {
        companyId: session.user.companyId,
        createdAt: { gte: startDate, lte: endDate }
      },
      _count: true,
      _sum: { total: true }
    }),
    
    prisma.invoice.groupBy({
      by: ['status'],
      where: {
        companyId: session.user.companyId,
        createdAt: { gte: startDate, lte: endDate }
      },
      _count: true,
      _sum: { total: true }
    }),
    
    prisma.contract.groupBy({
      by: ['status'],
      where: {
        companyId: session.user.companyId,
        createdAt: { gte: startDate, lte: endDate }
      },
      _count: true,
      _sum: { value: true }
    }),
    
    // Top customers by revenue
    prisma.customer.findMany({
      where: { companyId: session.user.companyId },
      include: {
        invoices: {
          where: {
            status: 'PAID',
            paidAt: { gte: startDate, lte: endDate }
          },
          select: { total: true }
        },
        quotations: {
          where: {
            createdAt: { gte: startDate, lte: endDate }
          },
          select: { total: true }
        }
      },
      take: 10
    }),
    
    // Top items by usage
    prisma.item.findMany({
      where: { companyId: session.user.companyId },
      include: {
        quotationItems: {
          where: {
            quotation: {
              createdAt: { gte: startDate, lte: endDate }
            }
          }
        },
        invoiceItems: {
          where: {
            invoice: {
              createdAt: { gte: startDate, lte: endDate }
            }
          }
        }
      },
      take: 10
    }),
    
    // Pipeline data
    prisma.lead.groupBy({
      by: ['status'],
      where: { companyId: session.user.companyId },
      _count: true
    }),
    
    prisma.quotation.groupBy({
      by: ['status'],
      where: { companyId: session.user.companyId },
      _count: true
    }),
    
    prisma.invoice.groupBy({
      by: ['status'],
      where: { companyId: session.user.companyId },
      _count: true
    }),
    
    prisma.contract.groupBy({
      by: ['status'],
      where: { companyId: session.user.companyId },
      _count: true
    }),
    
    // Additional analytics
    prisma.item.groupBy({
      by: ['category'],
      where: { 
        companyId: session.user.companyId,
        category: { not: null }
      },
      _count: true
    }),
    
    prisma.customer.groupBy({
      by: ['status'],
      where: { companyId: session.user.companyId },
      _count: true
    }),
    
    // Revenue by month (simplified)
    []
  ])

  // Process data for components
  const overviewData = {
    totalCustomers,
    totalLeads,
    totalQuotations,
    totalInvoices,
    totalContracts,
    totalItems,
    totalRevenue: revenueData._sum.total || 0,
    paidInvoices: revenueData._count
  }

  const salesData = {
    quotationStats,
    invoiceStats,
    contractStats,
    period,
    startDate,
    endDate
  }

  const pipelineData = {
    leads: leadsByStatus,
    quotations: quotationsByStatus,
    invoices: invoicesByStatus,
    contracts: contractsByStatus
  }

  const trendsData = {
    revenue: revenueByMonth,
    quotations: [],
    invoices: [],
    contracts: []
  }

  // Process top customers
  const processedTopCustomers = topCustomers
    .map(customer => ({
      ...customer,
      totalRevenue: customer.invoices.reduce((sum, inv) => sum + inv.total, 0),
      totalQuotations: customer.quotations.length,
      quotationValue: customer.quotations.reduce((sum, quot) => sum + quot.total, 0)
    }))
    .sort((a, b) => b.totalRevenue - a.totalRevenue)
    .slice(0, 5)

  // Process top items
  const processedTopItems = topItems
    .map(item => ({
      ...item,
      type: item.type || 'PRODUCT',
      totalUsage: item.quotationItems.length + item.invoiceItems.length,
      quotationUsage: item.quotationItems.length,
      invoiceUsage: item.invoiceItems.length
    }))
    .sort((a, b) => b.totalUsage - a.totalUsage)
    .slice(0, 5)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
          <p className="text-gray-600 mt-1">
            Comprehensive business intelligence and performance metrics
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Link href="/dashboard">
            <Button variant="outline">
              ← Back to Dashboard
            </Button>
          </Link>
          <select
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            defaultValue={period}
            name="period"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
            <option value="custom">Custom range</option>
          </select>
          <Button variant="outline">
            <Calendar className="h-4 w-4 mr-2" />
            Date Range
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Download Report
          </Button>
          <Button variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Key Performance Indicators */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="h-5 w-5 text-blue-600" />
            <span>Key Performance Indicators</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <DollarSign className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-green-800">
                {((revenueData._sum.total || 0) / 1000).toFixed(1)}K
              </div>
              <div className="text-sm text-green-600">Revenue (₹)</div>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <Users className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-blue-800">{totalCustomers}</div>
              <div className="text-sm text-blue-600">Total Customers</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <FileText className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-purple-800">{totalQuotations}</div>
              <div className="text-sm text-purple-600">Total Quotations</div>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <BarChart3 className="h-8 w-8 text-orange-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-orange-800">
                {totalQuotations > 0 ? ((revenueData._count / totalQuotations) * 100).toFixed(1) : '0'}%
              </div>
              <div className="text-sm text-orange-600">Conversion Rate</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Overview Cards */}
      <DashboardOverview data={overviewData} />

      {/* Financial Summary */}
      <FinancialSummary data={salesData} />

      {/* Charts and Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <RevenueChart data={trendsData} />
        <SalesMetrics data={salesData} />
      </div>

      {/* Pipeline and Performance */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <PipelineOverview data={pipelineData} />
        <div className="space-y-6">
          <TopCustomers customers={processedTopCustomers} />
          <TopItems items={processedTopItems} />
        </div>
      </div>

      {/* Additional Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Item Categories */}
        <Card>
          <CardHeader>
            <CardTitle>Items by Category</CardTitle>
          </CardHeader>
          <CardContent>
            {itemsByCategory.length > 0 ? (
              <div className="space-y-3">
                {itemsByCategory.map((category, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">{category.category}</span>
                    <Badge variant="outline">{category._count}</Badge>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500">No categories found</p>
            )}
          </CardContent>
        </Card>

        {/* Customer Status */}
        <Card>
          <CardHeader>
            <CardTitle>Customers by Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {customersByStatus.map((status, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">{status.status}</span>
                  <Badge variant="outline">{status._count}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
