#!/usr/bin/env node

/**
 * Fix API route exports that are causing TypeScript errors
 */

const fs = require('fs')
const path = require('path')

const apiRoutes = [
  'app/api/notifications/preferences/route.ts',
  'app/api/notifications/route.ts',
  'app/api/onboarding/complete/route.ts',
  'app/api/onboarding/progress/route.ts',
  'app/api/support/tickets/route.ts',
  'app/api/webhooks/route.ts'
]

function fixApiRoute(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${filePath}`)
      return
    }

    let content = fs.readFileSync(filePath, 'utf8')
    let modified = false

    // Find export functions that are not HTTP methods
    const httpMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS']
    const lines = content.split('\n')
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i]
      
      // Check if line starts with "export async function" or "export function"
      if (line.match(/^export\s+(async\s+)?function\s+(\w+)/)) {
        const match = line.match(/^export\s+(async\s+)?function\s+(\w+)/)
        if (match) {
          const functionName = match[2]
          
          // If it's not an HTTP method, remove the export
          if (!httpMethods.includes(functionName)) {
            lines[i] = line.replace(/^export\s+/, '')
            modified = true
            console.log(`✅ Fixed export in ${filePath}: ${functionName}`)
          }
        }
      }
    }

    if (modified) {
      fs.writeFileSync(filePath, lines.join('\n'))
      console.log(`📝 Updated: ${filePath}`)
    } else {
      console.log(`✅ No changes needed: ${filePath}`)
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message)
  }
}

console.log('🔧 Fixing API route exports...\n')

apiRoutes.forEach(route => {
  console.log(`Processing: ${route}`)
  fixApiRoute(route)
  console.log('')
})

console.log('🎉 API route export fixes complete!')
