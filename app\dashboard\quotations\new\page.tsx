import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { QuotationForm } from '@/components/quotations/quotation-form'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'

interface SearchParams {
  customerId?: string
  leadId?: string
}

export default async function NewQuotationPage({
  searchParams,
}: {
  searchParams: SearchParams
}) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  // Fetch required data
  const [customers, leads, items, company] = await Promise.all([
    prisma.customer.findMany({
      where: { 
        companyId: session.user.companyId,
        status: { in: ['ACTIVE', 'PROSPECT'] }
      },
      select: { 
        id: true, 
        name: true, 
        email: true, 
        company: true,
        address: true,
        city: true,
        state: true,
        country: true,
        postalCode: true
      },
      orderBy: { name: 'asc' },
    }),
    prisma.lead.findMany({
      where: { 
        companyId: session.user.companyId,
        status: { not: 'CLOSED_LOST' }
      },
      select: { 
        id: true, 
        title: true, 
        customerId: true,
        value: true
      },
      orderBy: { createdAt: 'desc' },
    }),
    prisma.item.findMany({
      where: { companyId: session.user.companyId },
      select: { 
        id: true, 
        name: true, 
        description: true,
        price: true,
        category: true,
        unit: true,
        taxRate: true
      },
      orderBy: { name: 'asc' },
    }),
    prisma.company.findUnique({
      where: { id: session.user.companyId },
      select: {
        name: true,
        email: true,
        phone: true,
        address: true,
        city: true,
        state: true,
        country: true,
        postalCode: true,
        website: true,
        taxId: true
      }
    })
  ])

  const preselectedCustomerId = searchParams.customerId
  const preselectedLeadId = searchParams.leadId

  // Generate next quotation number
  const lastQuotation = await prisma.quotation.findFirst({
    where: { companyId: session.user.companyId },
    orderBy: { createdAt: 'desc' },
    select: { quotationNumber: true }
  })

  const nextQuotationNumber = generateQuotationNumber(lastQuotation?.quotationNumber)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href="/dashboard/quotations">
          <Button variant="ghost" >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Quotations
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Create New Quotation</h1>
          <p className="text-gray-600 mt-1">
            Create a professional quotation for your customer
          </p>
        </div>
      </div>

      {/* Quotation Form */}
      <QuotationForm 
        mode="create" 
        customers={customers}
        leads={leads}
        items={items}
        company={company}
        preselectedCustomerId={preselectedCustomerId}
        preselectedLeadId={preselectedLeadId}
        quotationNumber={nextQuotationNumber}
      />
    </div>
  )
}

function generateQuotationNumber(lastNumber?: string): string {
  const currentYear = new Date().getFullYear()
  const currentMonth = String(new Date().getMonth() + 1).padStart(2, '0')
  
  let nextNumber = 1
  
  if (lastNumber) {
    const match = lastNumber.match(/QUO-(\d{4})(\d{2})-(\d{4})/)
    if (match) {
      const [, year, month, num] = match
      if (year === currentYear.toString() && month === currentMonth) {
        nextNumber = parseInt(num) + 1
      }
    }
  }
  
  return `QUO-${currentYear}${currentMonth}-${String(nextNumber).padStart(4, '0')}`
}
