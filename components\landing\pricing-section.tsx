'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { 
  Check, 
  Star,
  Crown,
  Award,
  ArrowRight,
  Zap,
  Users,
  FileText,
  BarChart3,
  Shield,
  Headphones
} from 'lucide-react'
import Link from 'next/link'

export function PricingSection() {
  const [isYearly, setIsYearly] = useState(false)

  const plans = [
    {
      name: 'Basic',
      icon: Award,
      description: 'Perfect for small businesses getting started',
      monthlyPrice: 29,
      yearlyPrice: 290,
      savings: 20,
      popular: false,
      features: [
        'Up to 5 users',
        '50 quotations per month',
        '50 invoices per month',
        '10 contracts per month',
        'Basic analytics',
        'Email support',
        '1GB storage',
        'Standard templates'
      ],
      limitations: [
        'No API access',
        'No custom branding',
        'No priority support'
      ],
      cta: 'Start Free Trial',
      color: 'border-gray-200'
    },
    {
      name: 'Professional',
      icon: Star,
      description: 'Best for growing businesses with advanced needs',
      monthlyPrice: 79,
      yearlyPrice: 790,
      savings: 25,
      popular: true,
      features: [
        'Up to 25 users',
        '500 quotations per month',
        '500 invoices per month',
        '100 contracts per month',
        'Advanced analytics',
        'Priority email support',
        'Phone support (business hours)',
        '10GB storage',
        'Custom templates',
        'API access (5 keys)',
        'Email automation',
        'Webhook integrations',
        'Custom branding',
        'Advanced reporting'
      ],
      limitations: [],
      cta: 'Start Free Trial',
      color: 'border-blue-500 ring-2 ring-blue-500 ring-opacity-20'
    },
    {
      name: 'Enterprise',
      icon: Crown,
      description: 'For large organizations with custom requirements',
      monthlyPrice: 199,
      yearlyPrice: 1990,
      savings: 30,
      popular: false,
      features: [
        'Up to 100 users',
        'Unlimited quotations',
        'Unlimited invoices',
        'Unlimited contracts',
        'Full analytics suite',
        '24/7 phone & email support',
        'Dedicated account manager',
        '100GB storage',
        'Custom templates & branding',
        'Full API access (20 keys)',
        'Advanced automation',
        'Custom integrations',
        'White-label options',
        'Advanced security',
        'Compliance tools',
        'Custom training',
        'SLA guarantee'
      ],
      limitations: [],
      cta: 'Contact Sales',
      color: 'border-purple-500'
    }
  ]

  const getPrice = (plan: any) => {
    const price = isYearly ? plan.yearlyPrice : plan.monthlyPrice
    const period = isYearly ? 'year' : 'month'
    return { price, period }
  }

  const getSavings = (plan: any) => {
    if (!isYearly) return null
    const monthlyCost = plan.monthlyPrice * 12
    const savings = monthlyCost - plan.yearlyPrice
    const percentage = Math.round((savings / monthlyCost) * 100)
    return { amount: savings, percentage }
  }

  return (
    <section id="pricing" className="py-20 px-4 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="container mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <Badge variant="outline" className="mb-4">
            <Zap className="h-4 w-4 mr-2" />
            Simple Pricing
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Choose the Perfect Plan
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent block">
              for Your Business
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-8">
            Start with a 14-day free trial. No credit card required. Upgrade or downgrade at any time.
          </p>

          {/* Billing Toggle */}
          <div className="flex items-center justify-center space-x-4">
            <span className={`text-sm ${!isYearly ? 'text-gray-900 font-medium' : 'text-gray-500'}`}>
              Monthly
            </span>
            <Switch
              checked={isYearly}
              onCheckedChange={setIsYearly}
              className="data-[state=checked]:bg-blue-600"
            />
            <span className={`text-sm ${isYearly ? 'text-gray-900 font-medium' : 'text-gray-500'}`}>
              Yearly
            </span>
            {isYearly && (
              <Badge className="bg-green-100 text-green-800 border-green-200">
                Save up to 30%
              </Badge>
            )}
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {plans.map((plan, index) => {
            const { price, period } = getPrice(plan)
            const savings = getSavings(plan)
            const PlanIcon = plan.icon

            return (
              <Card 
                key={index} 
                className={`relative ${plan.color} ${plan.popular ? 'scale-105 shadow-2xl' : 'shadow-lg'} hover:shadow-xl transition-all duration-300`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-blue-600 text-white px-4 py-1">
                      <Star className="h-3 w-3 mr-1" />
                      Most Popular
                    </Badge>
                  </div>
                )}

                <CardHeader className="text-center pb-8">
                  <div className={`w-16 h-16 mx-auto rounded-2xl flex items-center justify-center mb-4 ${
                    plan.name === 'Basic' ? 'bg-gray-100' :
                    plan.name === 'Professional' ? 'bg-blue-100' :
                    'bg-purple-100'
                  }`}>
                    <PlanIcon className={`h-8 w-8 ${
                      plan.name === 'Basic' ? 'text-gray-600' :
                      plan.name === 'Professional' ? 'text-blue-600' :
                      'text-purple-600'
                    }`} />
                  </div>
                  
                  <CardTitle className="text-2xl mb-2">{plan.name}</CardTitle>
                  <CardDescription className="text-gray-600 mb-6">
                    {plan.description}
                  </CardDescription>
                  
                  <div className="space-y-2">
                    <div className="flex items-baseline justify-center">
                      <span className="text-5xl font-bold text-gray-900">${price}</span>
                      <span className="text-gray-600 ml-2">/{period}</span>
                    </div>
                    {savings && (
                      <div className="text-sm text-green-600 font-medium">
                        Save ${savings.amount} ({savings.percentage}% off)
                      </div>
                    )}
                  </div>
                </CardHeader>

                <CardContent className="space-y-6">
                  {/* Features List */}
                  <div className="space-y-3">
                    {plan.features.map((feature, idx) => (
                      <div key={idx} className="flex items-start space-x-3">
                        <Check className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                        <span className="text-gray-700 text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>

                  {/* CTA Button */}
                  <div className="pt-6">
                    {plan.name === 'Enterprise' ? (
                      <Button 
                        className="w-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800"
                        size="lg"
                      >
                        {plan.cta}
                        <ArrowRight className="ml-2 h-5 w-5" />
                      </Button>
                    ) : (
                      <Link href="/auth/signup" className="block">
                        <Button 
                          className={`w-full ${plan.popular 
                            ? 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800' 
                            : 'bg-gray-900 hover:bg-gray-800'
                          }`}
                          size="lg"
                        >
                          {plan.cta}
                          <ArrowRight className="ml-2 h-5 w-5" />
                        </Button>
                      </Link>
                    )}
                  </div>

                  {/* Additional Info */}
                  <div className="text-center text-sm text-gray-500">
                    {plan.name !== 'Enterprise' && '14-day free trial included'}
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Feature Comparison */}
        <div className="mt-20">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              Compare All Features
            </h3>
            <p className="text-lg text-gray-600">
              See what's included in each plan
            </p>
          </div>

          <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="text-left py-4 px-6 font-semibold text-gray-900">Features</th>
                    <th className="text-center py-4 px-6 font-semibold text-gray-900">Basic</th>
                    <th className="text-center py-4 px-6 font-semibold text-gray-900">Professional</th>
                    <th className="text-center py-4 px-6 font-semibold text-gray-900">Enterprise</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {[
                    { feature: 'Users', basic: '5', pro: '25', enterprise: '100' },
                    { feature: 'Quotations/month', basic: '50', pro: '500', enterprise: 'Unlimited' },
                    { feature: 'Storage', basic: '1GB', pro: '10GB', enterprise: '100GB' },
                    { feature: 'API Access', basic: '✗', pro: '✓', enterprise: '✓' },
                    { feature: 'Custom Branding', basic: '✗', pro: '✓', enterprise: '✓' },
                    { feature: 'Priority Support', basic: '✗', pro: '✓', enterprise: '✓' },
                    { feature: 'Dedicated Manager', basic: '✗', pro: '✗', enterprise: '✓' }
                  ].map((row, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="py-4 px-6 font-medium text-gray-900">{row.feature}</td>
                      <td className="py-4 px-6 text-center text-gray-600">{row.basic}</td>
                      <td className="py-4 px-6 text-center text-gray-600">{row.pro}</td>
                      <td className="py-4 px-6 text-center text-gray-600">{row.enterprise}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* Enterprise CTA */}
        <div className="mt-20 bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl p-8 md:p-12 text-center text-white">
          <Crown className="h-16 w-16 mx-auto mb-6 opacity-80" />
          <h3 className="text-3xl font-bold mb-4">
            Need a Custom Solution?
          </h3>
          <p className="text-xl opacity-90 mb-8 max-w-2xl mx-auto">
            Our Enterprise plan can be customized to meet your specific requirements. 
            Contact our sales team to discuss your needs.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="secondary" className="bg-white text-purple-600 hover:bg-gray-100">
              <Headphones className="mr-2 h-5 w-5" />
              Schedule a Call
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-purple-600">
              Request Demo
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
