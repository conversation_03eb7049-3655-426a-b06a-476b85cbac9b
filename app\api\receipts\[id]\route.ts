import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const receiptSchema = z.object({
  receiptNumber: z.string().min(1, 'Receipt number is required'),
  invoiceId: z.string().min(1, 'Invoice is required'),
  amount: z.number().min(0.01, 'Amount must be greater than 0'),
  paymentMethod: z.enum(['CASH', 'BANK_TRANSFER', 'CREDIT_CARD', 'DEBIT_CARD', 'UPI', 'CHEQUE', 'OTHER']),
  paidAt: z.string().min(1, 'Payment date is required'),
  status: z.enum(['PENDING', 'CONFIRMED', 'CANCELLED']).default('CONFIRMED'),
  notes: z.string().optional().nullable(),
  referenceNumber: z.string().optional().nullable(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const receipt = await prisma.receipt.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      },
      include: {
        invoice: {
          include: {
            customer: {
              select: { id: true, name: true, email: true, company: true, phone: true }
            }
          }
        },
        createdBy: {
          select: { name: true, firstName: true, lastName: true }
        }
      }
    })

    if (!receipt) {
      return NextResponse.json({ error: 'Receipt not found' }, { status: 404 })
    }

    return NextResponse.json(receipt)
  } catch (error) {
    console.error('Error fetching receipt:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = receiptSchema.parse(body)

    // Check if receipt exists and belongs to the company
    const existingReceipt = await prisma.receipt.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      },
      include: {
        invoice: true
      }
    })

    if (!existingReceipt) {
      return NextResponse.json({ error: 'Receipt not found' }, { status: 404 })
    }

    // Check if receipt number conflicts with another receipt (if changed)
    if (validatedData.receiptNumber !== existingReceipt.receiptNumber) {
      const conflictingReceipt = await prisma.receipt.findFirst({
        where: {
          receiptNumber: validatedData.receiptNumber,
          companyId: session.user.companyId,
          id: { not: params.id },
        }
      })

      if (conflictingReceipt) {
        return NextResponse.json(
          { error: 'Receipt number already exists' },
          { status: 400 }
        )
      }
    }

    // Verify invoice exists and belongs to company (if changed)
    if (validatedData.invoiceId !== existingReceipt.invoiceId) {
      const invoice = await prisma.invoice.findFirst({
        where: {
          id: validatedData.invoiceId,
          companyId: session.user.companyId,
        }
      })

      if (!invoice) {
        return NextResponse.json(
          { error: 'Invoice not found' },
          { status: 404 }
        )
      }
    }

    // Update receipt in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Update the receipt
      const receipt = await tx.receipt.update({
        where: { id: params.id },
        data: {
          receiptNumber: validatedData.receiptNumber,
          invoiceId: validatedData.invoiceId,
          amount: validatedData.amount,
          paymentMethod: validatedData.paymentMethod,
          paidAt: new Date(validatedData.paidAt),
          status: validatedData.status,
          notes: validatedData.notes,
          referenceNumber: validatedData.referenceNumber,
        },
        include: {
          invoice: {
            include: {
              customer: {
                select: { id: true, name: true, email: true, company: true }
              }
            }
          },
          createdBy: {
            select: { name: true, firstName: true, lastName: true }
          }
        }
      })

      // Update invoice status based on receipt status and amount
      const invoice = await tx.invoice.findUnique({
        where: { id: validatedData.invoiceId }
      })

      if (invoice) {
        if (validatedData.status === 'CONFIRMED' && validatedData.amount >= invoice.total) {
          // Mark invoice as paid if receipt is confirmed and amount is sufficient
          await tx.invoice.update({
            where: { id: validatedData.invoiceId },
            data: { 
              status: 'PAID',
              paidAt: new Date(validatedData.paidAt)
            }
          })
        } else if (existingReceipt.status === 'CONFIRMED' && validatedData.status !== 'CONFIRMED') {
          // If receipt was confirmed but now isn't, check if invoice should be unpaid
          const otherConfirmedReceipts = await tx.receipt.findMany({
            where: {
              invoiceId: validatedData.invoiceId,
              status: 'CONFIRMED',
              id: { not: params.id }
            }
          })

          const totalOtherPayments = otherConfirmedReceipts.reduce((sum, r) => sum + r.amount, 0)
          
          if (totalOtherPayments < invoice.total) {
            await tx.invoice.update({
              where: { id: validatedData.invoiceId },
              data: { 
                status: 'SENT',
                paidAt: null
              }
            })
          }
        }
      }

      return receipt
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'NOTE',
        title: 'Payment receipt updated',
        description: `Receipt ${result.receiptNumber} was updated`,
        invoiceId: result.invoiceId,
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    return NextResponse.json(result)
  } catch (error) {
    console.error('Error updating receipt:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if receipt exists and belongs to the company
    const receipt = await prisma.receipt.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      },
      include: {
        invoice: true
      }
    })

    if (!receipt) {
      return NextResponse.json({ error: 'Receipt not found' }, { status: 404 })
    }

    // Delete receipt in a transaction
    await prisma.$transaction(async (tx) => {
      // Delete the receipt
      await tx.receipt.delete({
        where: { id: params.id }
      })

      // Check if invoice should be marked as unpaid
      if (receipt.status === 'CONFIRMED' && receipt.invoice) {
        const remainingReceipts = await tx.receipt.findMany({
          where: {
            invoiceId: receipt.invoiceId,
            status: 'CONFIRMED'
          }
        })

        const totalRemainingPayments = remainingReceipts.reduce((sum, r) => sum + r.amount, 0)
        
        if (totalRemainingPayments < receipt.invoice.total) {
          await tx.invoice.update({
            where: { id: receipt.invoiceId! },
            data: { 
              status: 'SENT',
              paidAt: null
            }
          })
        }
      }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'NOTE',
        title: 'Payment receipt deleted',
        description: `Receipt ${receipt.receiptNumber} was deleted`,
        invoiceId: receipt.invoiceId,
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    return NextResponse.json({ message: 'Receipt deleted successfully' })
  } catch (error) {
    console.error('Error deleting receipt:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
