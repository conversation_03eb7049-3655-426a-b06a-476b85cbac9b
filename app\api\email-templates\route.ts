import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const templateSchema = z.object({
  name: z.string().min(1, 'Template name is required'),
  subject: z.string().min(1, 'Subject is required'),
  body: z.string().min(1, 'Body content is required'),
  type: z.enum([
    'QUOTATION',
    'INVOICE',
    'CONTRACT',
    'RECEIPT',
    'REMINDER',
    'NOTIFICATION',
    'MARKETING',
    'CUSTOM'
  ]),
  status: z.enum(['ACTIVE', 'INACTIVE', 'DRAFT']),
  variables: z.array(z.string()).optional()
})

// Get email templates
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search') || ''
    const type = searchParams.get('type') || ''
    const status = searchParams.get('status') || ''
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = (page - 1) * limit

    // Build where clause
    const where: any = {
      companyId: session.user.companyId,
    }

    if (search) {
      where.OR = [
        { name: { contains: search } },
        { subject: { contains: search } },
        { body: { contains: search } },
      ]
    }

    if (type) {
      where.type = type
    }

    if (status) {
      where.status = status
    }

    // Fetch templates
    const [templates, totalCount] = await Promise.all([
      prisma.emailTemplate.findMany({
        where,
        include: {
          createdBy: {
            select: { name: true, firstName: true, lastName: true }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip: offset,
        take: limit,
      }),
      prisma.emailTemplate.count({ where })
    ])

    return NextResponse.json({
      templates,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching email templates:', error)
    return NextResponse.json(
      { error: 'Failed to fetch email templates' },
      { status: 500 }
    )
  }
}

// Create email template
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = templateSchema.parse(body)

    // Create template
    const template = await prisma.emailTemplate.create({
      data: {
        ...validatedData,
        companyId: session.user.companyId,
        createdById: session.user.id,
      },
      include: {
        createdBy: {
          select: { name: true, firstName: true, lastName: true }
        }
      }
    })

    // Log activity
    await prisma.activity.create({
      data: {
        type: 'NOTE',
        title: 'Email template created',
        description: `Email template "${template.name}" was created`,
        companyId: session.user.companyId,
        createdById: session.user.id,
        metadata: {
          templateId: template.id,
          templateName: template.name,
          templateType: template.type
        }
      }
    })

    return NextResponse.json(template, { status: 201 })
  } catch (error) {
    console.error('Error creating email template:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Failed to create email template' },
      { status: 500 }
    )
  }
}

// Update email template
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const templateId = searchParams.get('id')

    if (!templateId) {
      return NextResponse.json({ error: 'Template ID is required' }, { status: 400 })
    }

    const body = await request.json()
    const validatedData = templateSchema.parse(body)

    // Check if template exists and belongs to company
    const existingTemplate = await prisma.emailTemplate.findFirst({
      where: {
        id: templateId,
        companyId: session.user.companyId
      }
    })

    if (!existingTemplate) {
      return NextResponse.json({ error: 'Template not found' }, { status: 404 })
    }

    // Update template
    const template = await prisma.emailTemplate.update({
      where: { id: templateId },
      data: {
        ...validatedData,
        updatedAt: new Date()
      },
      include: {
        createdBy: {
          select: { name: true, firstName: true, lastName: true }
        }
      }
    })

    // Log activity
    await prisma.activity.create({
      data: {
        type: 'EMAIL_TEMPLATE',
        title: 'Email template updated',
        description: `Email template "${template.name}" was updated`,
        companyId: session.user.companyId,
        createdById: session.user.id,
        metadata: {
          templateId: template.id,
          templateName: template.name,
          templateType: template.type
        }
      }
    })

    return NextResponse.json(template)
  } catch (error) {
    console.error('Error updating email template:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Failed to update email template' },
      { status: 500 }
    )
  }
}

// Delete email template
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const templateId = searchParams.get('id')

    if (!templateId) {
      return NextResponse.json({ error: 'Template ID is required' }, { status: 400 })
    }

    // Check if template exists and belongs to company
    const existingTemplate = await prisma.emailTemplate.findFirst({
      where: {
        id: templateId,
        companyId: session.user.companyId
      }
    })

    if (!existingTemplate) {
      return NextResponse.json({ error: 'Template not found' }, { status: 404 })
    }

    // Delete template
    await prisma.emailTemplate.delete({
      where: { id: templateId }
    })

    // Log activity
    await prisma.activity.create({
      data: {
        type: 'EMAIL_TEMPLATE',
        title: 'Email template deleted',
        description: `Email template "${existingTemplate.name}" was deleted`,
        companyId: session.user.companyId,
        createdById: session.user.id,
        metadata: {
          templateId: existingTemplate.id,
          templateName: existingTemplate.name,
          templateType: existingTemplate.type
        }
      }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting email template:', error)
    return NextResponse.json(
      { error: 'Failed to delete email template' },
      { status: 500 }
    )
  }
}
