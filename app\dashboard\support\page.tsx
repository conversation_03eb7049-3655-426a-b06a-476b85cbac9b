import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { SupportOverview } from '@/components/support/support-overview'
import { TicketList } from '@/components/support/ticket-list'
import { CreateTicketDialog } from '@/components/support/create-ticket-dialog'
import { KnowledgeBaseWidget } from '@/components/support/knowledge-base-widget'
import { SupportStats } from '@/components/support/support-stats'
import { Button } from '@/components/ui/button'
import { HelpCircle, Plus, MessageSquare, BookOpen } from 'lucide-react'
import Link from 'next/link'

export default async function SupportPage() {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  // Fetch support tickets and related data
  const [tickets, company, supportStats] = await Promise.all([
    prisma.supportTicket.findMany({
      where: { companyId: session.user.companyId },
      include: {
        createdBy: {
          select: { name: true, firstName: true, lastName: true, email: true }
        },
        assignedTo: {
          select: { name: true, firstName: true, lastName: true, email: true }
        },
        _count: {
          select: { messages: true }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 20
    }),
    prisma.company.findUnique({
      where: { id: session.user.companyId }
    }),
    // Get support statistics
    Promise.all([
      prisma.supportTicket.count({
        where: { 
          companyId: session.user.companyId,
          status: 'OPEN'
        }
      }),
      prisma.supportTicket.count({
        where: { 
          companyId: session.user.companyId,
          status: 'IN_PROGRESS'
        }
      }),
      prisma.supportTicket.count({
        where: { 
          companyId: session.user.companyId,
          status: 'RESOLVED'
        }
      }),
      prisma.supportTicket.count({
        where: { 
          companyId: session.user.companyId,
          createdAt: { gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
        }
      }),
      // Average response time (mock data for now)
      Promise.resolve('2.5 hours'),
      // Customer satisfaction (mock data for now)
      Promise.resolve(4.8)
    ])
  ])

  const [openTickets, inProgressTickets, resolvedTickets, recentTickets, avgResponseTime, satisfaction] = supportStats

  const supportData = {
    openTickets,
    inProgressTickets,
    resolvedTickets,
    recentTickets,
    avgResponseTime,
    satisfaction,
    totalTickets: tickets.length
  }

  // Get knowledge base articles (top 5)
  const knowledgeBaseArticles = await prisma.knowledgeBaseArticle.findMany({
    where: { 
      status: 'PUBLISHED',
      OR: [
        { visibility: 'PUBLIC' },
        { visibility: 'CUSTOMERS' }
      ]
    },
    orderBy: { views: 'desc' },
    take: 5,
    select: {
      id: true,
      title: true,
      slug: true,
      category: true,
      views: true,
      helpful: true,
      notHelpful: true
    }
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="p-2 bg-blue-100 rounded-lg">
            <HelpCircle className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Support Center</h1>
            <p className="text-gray-600 mt-1">
              Get help, manage tickets, and access resources
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Link href="/dashboard/support/knowledge-base">
            <Button variant="outline">
              <BookOpen className="h-4 w-4 mr-2" />
              Knowledge Base
            </Button>
          </Link>
          <Link href="/dashboard/support/chat">
            <Button variant="outline">
              <MessageSquare className="h-4 w-4 mr-2" />
              Live Chat
            </Button>
          </Link>
          <CreateTicketDialog>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Ticket
            </Button>
          </CreateTicketDialog>
        </div>
      </div>

      {/* Support Statistics */}
      <SupportStats stats={supportData} />

      {/* Support Overview */}
      <SupportOverview 
        company={company}
        stats={supportData}
        plan={company?.plan || 'BASIC'}
      />

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Tickets List */}
        <div className="lg:col-span-2">
          <TicketList 
            tickets={tickets}
            companyId={session.user.companyId}
          />
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Knowledge Base Widget */}
          <KnowledgeBaseWidget articles={knowledgeBaseArticles} />

          {/* Quick Actions */}
          <div className="bg-white rounded-lg border p-6">
            <h3 className="font-medium text-gray-900 mb-4">Quick Actions</h3>
            <div className="space-y-3">
              <Link href="/dashboard/support/knowledge-base">
                <Button variant="ghost" className="w-full justify-start">
                  <BookOpen className="h-4 w-4 mr-2" />
                  Browse Knowledge Base
                </Button>
              </Link>
              <Link href="/dashboard/support/chat">
                <Button variant="ghost" className="w-full justify-start">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Start Live Chat
                </Button>
              </Link>
              <CreateTicketDialog>
                <Button variant="ghost" className="w-full justify-start">
                  <Plus className="h-4 w-4 mr-2" />
                  Submit New Ticket
                </Button>
              </CreateTicketDialog>
            </div>
          </div>

          {/* Support Plan Info */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200 p-6">
            <h3 className="font-medium text-gray-900 mb-2">Your Support Plan</h3>
            <div className="text-sm text-gray-600 mb-4">
              {company?.plan === 'ENTERPRISE' ? (
                <>
                  <div className="font-medium text-blue-800">Enterprise Support</div>
                  <ul className="mt-2 space-y-1">
                    <li>• 24/7 phone & email support</li>
                    <li>• Dedicated account manager</li>
                    <li>• Priority response (1 hour)</li>
                    <li>• Custom integrations</li>
                  </ul>
                </>
              ) : company?.plan === 'PROFESSIONAL' ? (
                <>
                  <div className="font-medium text-blue-800">Professional Support</div>
                  <ul className="mt-2 space-y-1">
                    <li>• Priority email support</li>
                    <li>• Response within 4 hours</li>
                    <li>• Phone support (business hours)</li>
                    <li>• Advanced troubleshooting</li>
                  </ul>
                </>
              ) : (
                <>
                  <div className="font-medium text-blue-800">Basic Support</div>
                  <ul className="mt-2 space-y-1">
                    <li>• Email support</li>
                    <li>• Response within 24 hours</li>
                    <li>• Knowledge base access</li>
                    <li>• Community forum</li>
                  </ul>
                </>
              )}
            </div>
            {company?.plan !== 'ENTERPRISE' && (
              <Link href="/dashboard/billing">
                <Button size="sm" className="w-full">
                  Upgrade for Better Support
                </Button>
              </Link>
            )}
          </div>

          {/* Contact Information */}
          <div className="bg-white rounded-lg border p-6">
            <h3 className="font-medium text-gray-900 mb-4">Contact Information</h3>
            <div className="space-y-3 text-sm">
              <div>
                <div className="font-medium text-gray-700">Email Support</div>
                <div className="text-gray-600"><EMAIL></div>
              </div>
              {(company?.plan === 'PROFESSIONAL' || company?.plan === 'ENTERPRISE') && (
                <div>
                  <div className="font-medium text-gray-700">Phone Support</div>
                  <div className="text-gray-600">+****************</div>
                  <div className="text-xs text-gray-500">
                    {company?.plan === 'ENTERPRISE' ? '24/7 Available' : 'Mon-Fri 9AM-6PM EST'}
                  </div>
                </div>
              )}
              <div>
                <div className="font-medium text-gray-700">Emergency Contact</div>
                <div className="text-gray-600"><EMAIL></div>
                <div className="text-xs text-gray-500">Critical issues only</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
