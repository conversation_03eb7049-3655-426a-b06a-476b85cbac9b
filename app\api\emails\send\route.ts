import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const sendEmailSchema = z.object({
  type: z.enum([
    'QUOTATION',
    'INVOICE', 
    'CONTRACT',
    'RECEIPT',
    'REMINDER',
    'NOTIFICATION',
    'MARKETING',
    'CUSTOM'
  ]),
  toEmail: z.string().email('Invalid email address'),
  toName: z.string().optional(),
  subject: z.string().min(1, 'Subject is required'),
  content: z.string().min(1, 'Content is required'),
  templateId: z.string().optional(),
  entityId: z.string().optional(),
  entityType: z.string().optional(),
  metadata: z.any().optional()
})

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = sendEmailSchema.parse(body)

    // Get company information for from fields
    const company = await prisma.company.findUnique({
      where: { id: session.user.companyId }
    })

    if (!company) {
      return NextResponse.json({ error: 'Company not found' }, { status: 404 })
    }

    // Create email record
    const email = await prisma.email.create({
      data: {
        type: validatedData.type,
        subject: validatedData.subject,
        body: validatedData.content,
        to: validatedData.toEmail,
        toEmail: validatedData.toEmail,
        from: company.email || session.user.email || '',
        status: 'PENDING',
        companyId: session.user.companyId,
        createdById: session.user.id,
      },
      include: {
        createdBy: {
          select: { name: true, firstName: true, lastName: true, email: true }
        }
      }
    })

    // Send the email (simulate for now)
    await sendEmailAsync(email.id)

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'EMAIL',
        title: 'Email sent',
        description: `Email "${email.subject}" sent to ${email.toEmail}`,
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    return NextResponse.json(email, { status: 201 })
  } catch (error) {
    console.error('Error sending email:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Async function to handle email sending
async function sendEmailAsync(emailId: string) {
  try {
    // Update status to sending
    await prisma.email.update({
      where: { id: emailId },
      data: { status: 'PENDING' }
    })

    // TODO: Integrate with actual email service (SendGrid, AWS SES, etc.)
    // For now, simulate email sending
    
    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Update email status to sent
    await prisma.email.update({
      where: { id: emailId },
      data: {
        status: 'SENT',
        sentAt: new Date()
      }
    })

    // Simulate delivery tracking
    setTimeout(async () => {
      try {
        await prisma.email.update({
          where: { id: emailId },
          data: {
            status: 'DELIVERED',
            deliveredAt: new Date()
          }
        })

        // Simulate email opening (30% chance)
        if (Math.random() < 0.3) {
          setTimeout(async () => {
            try {
              await prisma.email.update({
                where: { id: emailId },
                data: {
                  status: 'OPENED',
                  openedAt: new Date()
                }
              })

              // Simulate link clicking (10% chance if opened)
              if (Math.random() < 0.1) {
                setTimeout(async () => {
                  try {
                    await prisma.email.update({
                      where: { id: emailId },
                      data: {
                        status: 'CLICKED',
                        clickedAt: new Date()
                      }
                    })
                  } catch (error) {
                    console.error('Error updating email click status:', error)
                  }
                }, Math.random() * 30000) // Random delay up to 30 seconds
              }
            } catch (error) {
              console.error('Error updating email open status:', error)
            }
          }, Math.random() * 60000) // Random delay up to 1 minute
        }
      } catch (error) {
        console.error('Error updating email delivery status:', error)
      }
    }, Math.random() * 10000) // Random delay up to 10 seconds

  } catch (error) {
    console.error('Error in email sending process:', error)
    
    // Mark as failed
    try {
      await prisma.email.update({
        where: { id: emailId },
        data: {
          status: 'FAILED',
          failedAt: new Date()
        }
      })
    } catch (updateError) {
      console.error('Error updating email failure status:', updateError)
    }
  }
}

// Real email service integration would look like this:
/*
async function sendEmailWithService(emailData: any) {
  // Example with SendGrid
  const sgMail = require('@sendgrid/mail')
  sgMail.setApiKey(process.env.SENDGRID_API_KEY)

  const msg = {
    to: emailData.toEmail,
    from: emailData.fromEmail,
    subject: emailData.subject,
    text: emailData.content,
    html: emailData.content.replace(/\n/g, '<br>'),
  }

  try {
    await sgMail.send(msg)
    return { success: true }
  } catch (error) {
    console.error('SendGrid error:', error)
    throw error
  }
}

// Example with AWS SES
async function sendEmailWithAWS(emailData: any) {
  const AWS = require('aws-sdk')
  const ses = new AWS.SES({ region: 'us-east-1' })

  const params = {
    Destination: {
      ToAddresses: [emailData.toEmail]
    },
    Message: {
      Body: {
        Text: { Data: emailData.content }
      },
      Subject: { Data: emailData.subject }
    },
    Source: emailData.fromEmail
  }

  try {
    const result = await ses.sendEmail(params).promise()
    return { success: true, messageId: result.MessageId }
  } catch (error) {
    console.error('AWS SES error:', error)
    throw error
  }
}
*/
