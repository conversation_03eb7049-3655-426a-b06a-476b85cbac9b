import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { redirect } from 'next/navigation'
import {
  BarChart3,
  TrendingUp,
  Pie<PERSON>hart,
  Users,
  Building2,
  Activity,
  FileText,
  Mail,
  CreditCard,
  Calendar,
  Clock,
  Target,
  Zap,
  Globe
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { formatDate } from '@/lib/utils'

export default async function SuperAdminAnalyticsPage() {
  const session = await getServerSession(authOptions)

  // Check if user is super admin
  if (!session?.user?.isSuperAdmin && session?.user?.role !== 'SUPER_ADMIN') {
    redirect('/dashboard')
  }

  // Calculate date ranges
  const now = new Date()
  const startOfWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
  const startOfYear = new Date(now.getFullYear(), 0, 1)

  // Fetch comprehensive analytics data
  const [
    // User analytics
    totalUsers,
    activeUsersWeek,
    activeUsersMonth,
    usersByRole,
    userGrowthData,

    // Company analytics
    totalCompanies,
    companiesByPlan,
    companiesByStatus,

    // Activity analytics
    totalActivities,
    activitiesByType,
    recentActivities,

    // Content analytics
    totalInvoices,
    totalQuotations,
    totalContracts,
    totalEmails,

    // Feature usage
    featureUsage,

    // Geographic data
    companiesByLocation
  ] = await Promise.all([
    // User queries
    prisma.user.count(),
    prisma.user.count({ where: { lastLoginAt: { gte: startOfWeek } } }),
    prisma.user.count({ where: { lastLoginAt: { gte: startOfMonth } } }),
    prisma.user.groupBy({
      by: ['role'],
      _count: true
    }),
    Promise.all(
      Array.from({ length: 12 }, (_, i) => {
        const monthStart = new Date(now.getFullYear(), now.getMonth() - i, 1)
        const monthEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 0)
        return prisma.user.count({
          where: {
            createdAt: {
              gte: monthStart,
              lte: monthEnd
            }
          }
        }).then(count => ({
          month: monthStart.toLocaleDateString('en-US', { month: 'short' }),
          users: count
        }))
      })
    ),

    // Company queries
    prisma.company.count(),
    prisma.company.groupBy({
      by: ['plan'],
      _count: true
    }),
    prisma.company.groupBy({
      by: ['isActive'],
      _count: true
    }),

    // Activity queries
    prisma.activity.count(),
    prisma.activity.groupBy({
      by: ['type'],
      _count: true
    }),
    prisma.activity.findMany({
      take: 10,
      orderBy: { createdAt: 'desc' },
      include: {
        company_rel: { select: { name: true } },
        createdBy: { select: { name: true, firstName: true, lastName: true } }
      }
    }),

    // Content queries
    prisma.invoice.count(),
    prisma.quotation.count(),
    prisma.contract.count(),
    prisma.email.count(),

    // Feature usage (mock data for now)
    Promise.resolve([
      { feature: 'Invoicing', usage: 85, trend: 'up' },
      { feature: 'Quotations', usage: 72, trend: 'up' },
      { feature: 'Contracts', usage: 68, trend: 'stable' },
      { feature: 'Email Campaigns', usage: 45, trend: 'down' },
      { feature: 'Customer Management', usage: 92, trend: 'up' },
      { feature: 'Reporting', usage: 38, trend: 'up' }
    ]),

    // Geographic data (mock for now)
    Promise.resolve([
      { country: 'United States', companies: 45, percentage: 35 },
      { country: 'Canada', companies: 28, percentage: 22 },
      { country: 'United Kingdom', companies: 18, percentage: 14 },
      { country: 'Australia', companies: 15, percentage: 12 },
      { country: 'Germany', companies: 12, percentage: 9 },
      { country: 'Others', companies: 10, percentage: 8 }
    ])
  ])

  // Calculate engagement metrics
  const weeklyEngagement = (activeUsersWeek / totalUsers * 100).toFixed(1)
  const monthlyEngagement = (activeUsersMonth / totalUsers * 100).toFixed(1)

  // Platform usage statistics
  const usageStats = [
    {
      title: 'Total Users',
      value: totalUsers.toLocaleString(),
      subtitle: `${weeklyEngagement}% active this week`,
      icon: Users,
      color: 'blue'
    },
    {
      title: 'Total Companies',
      value: totalCompanies.toLocaleString(),
      subtitle: 'Across all plans',
      icon: Building2,
      color: 'green'
    },
    {
      title: 'Platform Activities',
      value: totalActivities.toLocaleString(),
      subtitle: 'All time actions',
      icon: Activity,
      color: 'purple'
    },
    {
      title: 'Content Created',
      value: (totalInvoices + totalQuotations + totalContracts + totalEmails).toLocaleString(),
      subtitle: 'Documents & emails',
      icon: FileText,
      color: 'orange'
    }
  ]

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="flex items-center space-x-4">
        <div className="p-3 bg-indigo-100 rounded-lg">
          <BarChart3 className="h-8 w-8 text-indigo-600" />
        </div>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Platform Analytics</h1>
          <p className="text-gray-600">Comprehensive platform metrics and insights</p>
        </div>
      </div>

      {/* Usage Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {usageStats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {stat.title}
                </CardTitle>
                <Icon className={`h-4 w-4 text-${stat.color}-600`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <p className="text-xs text-gray-500 mt-1">{stat.subtitle}</p>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Main Analytics Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left Column - Charts and Trends */}
        <div className="lg:col-span-2 space-y-6">
          {/* User Growth Trend */}
          <Card>
            <CardHeader>
              <CardTitle>User Growth Trend (Last 12 Months)</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {userGrowthData.reverse().map((month, index) => (
                  <div key={month.month} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="text-sm font-medium">{month.month}</span>
                    <div className="flex items-center space-x-3">
                      <div className="w-32 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${Math.min((month.users / Math.max(...userGrowthData.map(m => m.users))) * 100, 100)}%` }}
                        ></div>
                      </div>
                      <span className="text-sm text-gray-600 w-12 text-right">{month.users}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Feature Usage Analytics */}
          <Card>
            <CardHeader>
              <CardTitle>Feature Usage Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {featureUsage.map((feature) => (
                  <div key={feature.feature} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-indigo-500 rounded-full"></div>
                      <span className="font-medium">{feature.feature}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-24 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-indigo-600 h-2 rounded-full"
                          style={{ width: `${feature.usage}%` }}
                        ></div>
                      </div>
                      <span className="text-sm text-gray-600 w-12 text-right">{feature.usage}%</span>
                      <Badge
                        variant="outline"
                        className={
                          feature.trend === 'up' ? 'text-green-600 border-green-200' :
                          feature.trend === 'down' ? 'text-red-600 border-red-200' :
                          'text-gray-600 border-gray-200'
                        }
                      >
                        {feature.trend === 'up' ? '↗' : feature.trend === 'down' ? '↘' : '→'}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Activity Types Breakdown */}
          <Card>
            <CardHeader>
              <CardTitle>Platform Activity Breakdown</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                {activitiesByType.map((activity) => (
                  <div key={activity.type} className="p-4 bg-gray-50 rounded-lg">
                    <div className="text-2xl font-bold">{activity._count.toLocaleString()}</div>
                    <div className="text-sm text-gray-600">{activity.type.replace('_', ' ')}</div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Breakdowns and Insights */}
        <div className="space-y-6">
          {/* User Role Distribution */}
          <Card>
            <CardHeader>
              <CardTitle>User Role Distribution</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {usersByRole.map((role) => (
                <div key={role.role} className="flex items-center justify-between">
                  <span className="text-sm font-medium">{role.role.replace('_', ' ')}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: `${(role._count / totalUsers) * 100}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-gray-600 w-8 text-right">{role._count}</span>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Company Plan Distribution */}
          <Card>
            <CardHeader>
              <CardTitle>Company Plan Distribution</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {companiesByPlan.map((plan) => (
                <div key={plan.plan} className="flex items-center justify-between">
                  <span className="text-sm font-medium">{plan.plan}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-green-600 h-2 rounded-full"
                        style={{ width: `${(plan._count / totalCompanies) * 100}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-gray-600 w-8 text-right">{plan._count}</span>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Geographic Distribution */}
          <Card>
            <CardHeader>
              <CardTitle>Geographic Distribution</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {companiesByLocation.map((location) => (
                <div key={location.country} className="flex items-center justify-between">
                  <span className="text-sm font-medium">{location.country}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-purple-600 h-2 rounded-full"
                        style={{ width: `${location.percentage}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-gray-600 w-8 text-right">{location.companies}</span>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Content Statistics */}
          <Card>
            <CardHeader>
              <CardTitle>Content Statistics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <CreditCard className="h-4 w-4 text-blue-600" />
                  <span className="text-sm">Invoices</span>
                </div>
                <span className="text-sm font-medium">{totalInvoices.toLocaleString()}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <FileText className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Quotations</span>
                </div>
                <span className="text-sm font-medium">{totalQuotations.toLocaleString()}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <FileText className="h-4 w-4 text-purple-600" />
                  <span className="text-sm">Contracts</span>
                </div>
                <span className="text-sm font-medium">{totalContracts.toLocaleString()}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Mail className="h-4 w-4 text-orange-600" />
                  <span className="text-sm">Emails</span>
                </div>
                <span className="text-sm font-medium">{totalEmails.toLocaleString()}</span>
              </div>
            </CardContent>
          </Card>

          {/* Engagement Metrics */}
          <Card>
            <CardHeader>
              <CardTitle>Engagement Metrics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm">Weekly Active Users</span>
                <Badge variant="outline" className="text-green-600">
                  {weeklyEngagement}%
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Monthly Active Users</span>
                <Badge variant="outline" className="text-blue-600">
                  {monthlyEngagement}%
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Platform Uptime</span>
                <Badge variant="outline" className="text-green-600">
                  99.9%
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
