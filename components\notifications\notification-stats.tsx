'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card'
import { 
  Bell, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  Mail,
  MessageSquare,
  Calendar,
  DollarSign,
  FileText,
  Users
} from 'lucide-react'

interface NotificationStatsProps {
  stats: {
    total: number
    unread: number
    highPriority: number
    typeBreakdown: Array<{
      type: string
      _count: number
    }>
    statusBreakdown: Array<{
      status: string
      _count: number
    }>
  }
}

export function NotificationStats({ stats }: NotificationStatsProps) {
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'INVOICE_OVERDUE':
      case 'PAYMENT_RECEIVED':
        return DollarSign
      case 'QUOTATION_ACCEPTED':
      case 'QUOTATION_EXPIRED':
      case 'CONTRACT_SIGNED':
        return FileText
      case 'CUSTOMER_CREATED':
      case 'LEAD_CONVERTED':
        return Users
      case 'REMINDER':
      case 'DEADLINE':
        return Calendar
      case 'EMAIL':
        return Mail
      case 'SYSTEM':
        return Bell
      default:
        return MessageSquare
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'INVOICE_OVERDUE':
        return 'text-red-600 bg-red-100'
      case 'PAYMENT_RECEIVED':
        return 'text-green-600 bg-green-100'
      case 'QUOTATION_ACCEPTED':
      case 'CONTRACT_SIGNED':
        return 'text-blue-600 bg-blue-100'
      case 'QUOTATION_EXPIRED':
        return 'text-orange-600 bg-orange-100'
      case 'CUSTOMER_CREATED':
      case 'LEAD_CONVERTED':
        return 'text-purple-600 bg-purple-100'
      case 'REMINDER':
      case 'DEADLINE':
        return 'text-yellow-600 bg-yellow-100'
      case 'EMAIL':
        return 'text-indigo-600 bg-indigo-100'
      case 'SYSTEM':
        return 'text-gray-600 bg-gray-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const formatTypeName = (type: string) => {
    return type.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    ).join(' ')
  }

  const overviewCards = [
    {
      title: 'Total Notifications',
      value: stats.total.toString(),
      icon: Bell,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      description: 'All notifications'
    },
    {
      title: 'Unread',
      value: stats.unread.toString(),
      icon: Clock,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      description: 'Require attention'
    },
    {
      title: 'High Priority',
      value: stats.highPriority.toString(),
      icon: AlertTriangle,
      color: 'text-red-600',
      bgColor: 'bg-red-100',
      description: 'Urgent items'
    },
    {
      title: 'Read',
      value: (stats.total - stats.unread).toString(),
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      description: 'Completed'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {overviewCards.map((card, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {card.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${card.bgColor}`}>
                <card.icon className={`h-4 w-4 ${card.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">
                {card.value}
              </div>
              <p className="text-xs text-gray-500 mt-1">
                {card.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Breakdown Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Type Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle>Notifications by Type</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats.typeBreakdown.map((item, index) => {
                const TypeIcon = getTypeIcon(item.type)
                const percentage = stats.total > 0 ? ((item._count / stats.total) * 100).toFixed(1) : '0'
                
                return (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded-lg ${getTypeColor(item.type)}`}>
                        <TypeIcon className="h-4 w-4" />
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">
                          {formatTypeName(item.type)}
                        </div>
                        <div className="text-sm text-gray-600">
                          {percentage}% of total
                        </div>
                      </div>
                    </div>
                    <div className="text-lg font-bold text-gray-900">
                      {item._count}
                    </div>
                  </div>
                )
              })}
              {stats.typeBreakdown.length === 0 && (
                <div className="text-center py-6 text-gray-500">
                  No notifications by type
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Status Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle>Notifications by Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats.statusBreakdown.map((item, index) => {
                const percentage = stats.total > 0 ? ((item._count / stats.total) * 100).toFixed(1) : '0'
                const isUnread = item.status === 'UNREAD'
                
                return (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded-lg ${isUnread ? 'bg-orange-100' : 'bg-green-100'}`}>
                        {isUnread ? (
                          <Clock className="h-4 w-4 text-orange-600" />
                        ) : (
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        )}
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">
                          {item.status === 'UNREAD' ? 'Unread' : 'Read'}
                        </div>
                        <div className="text-sm text-gray-600">
                          {percentage}% of total
                        </div>
                      </div>
                    </div>
                    <div className="text-lg font-bold text-gray-900">
                      {item._count}
                    </div>
                  </div>
                )
              })}
              {stats.statusBreakdown.length === 0 && (
                <div className="text-center py-6 text-gray-500">
                  No notifications by status
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <Bell className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="font-medium text-gray-900">Enable Notifications</div>
              <div className="text-sm text-gray-600 mt-1">
                Get real-time alerts for important events
              </div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <Mail className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <div className="font-medium text-gray-900">Email Preferences</div>
              <div className="text-sm text-gray-600 mt-1">
                Configure email notification settings
              </div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <Calendar className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <div className="font-medium text-gray-900">Schedule Reminders</div>
              <div className="text-sm text-gray-600 mt-1">
                Set up automatic reminders for tasks
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
