'use client'

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { formatCurrency } from '@/lib/utils'
import { 
  Users, 
  TrendingUp, 
  FileText,
  Receipt,

  DollarSign,
  Clock
} from 'lucide-react'

interface DashboardStatsProps {
  stats: {
    customers: number
    leads: number
    quotations: number
    invoices: number
    contracts: number
    revenue: number
    pendingRevenue: number
  }
}

export function DashboardStats({ stats }: DashboardStatsProps) {
  const statCards = [
    {
      title: 'Total Customers',
      value: stats.customers.toString(),
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      change: '+12%',
      changeType: 'positive' as const
    },
    {
      title: 'Active Leads',
      value: stats.leads.toString(),
      icon: TrendingUp,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      change: '+8%',
      changeType: 'positive' as const
    },
    {
      title: 'Quotations',
      value: stats.quotations.toString(),
      icon: FileText,
 Receipt,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      change: '+15%',
      changeType: 'positive' as const
    },
    {
      title: 'Invoices',
      value: stats.invoices.toString(),
      icon: Receipt,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      change: '+5%',
      changeType: 'positive' as const
    },
    {
      title: 'Contracts',
      value: stats.contracts.toString(),
      icon: FileText,
 Receipt,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-100',
      change: '+20%',
      changeType: 'positive' as const
    },
    {
      title: 'Total Revenue',
      value: formatCurrency(stats.revenue),
      icon: DollarSign,
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-100',
      change: '+25%',
      changeType: 'positive' as const
    },
    {
      title: 'Pending Revenue',
      value: formatCurrency(stats.pendingRevenue),
      icon: Clock,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
      change: '+10%',
      changeType: 'positive' as const
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statCards.map((stat, index) => (
        <Card key={index} className="hover:shadow-lg transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              {stat.title}
            </CardTitle>
            <div className={`p-2 rounded-lg ${stat.bgColor}`}>
              <stat.icon className={`h-4 w-4 ${stat.color}`} />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">
              {stat.value}
            </div>
            <div className="flex items-center mt-2">
              <span className={`text-xs font-medium ${
                stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
              }`}>
                {stat.change}
              </span>
              <span className="text-xs text-gray-500 ml-1">
                from last month
              </span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
