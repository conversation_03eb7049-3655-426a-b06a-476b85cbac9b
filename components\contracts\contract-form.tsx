'use client'

import { useState, useEffect } from 'react'
import { use<PERSON>out<PERSON> } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Save, X, Plus, FileText, Sparkles } from 'lucide-react'
import toast from 'react-hot-toast'
import Link from 'next/link'

const contractSchema = z.object({
  contractNumber: z.string().min(1, 'Contract number is required'),
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  customerId: z.string().min(1, 'Customer is required'),
  quotationId: z.string().optional(),
  templateId: z.string().optional(),
  content: z.string().min(1, 'Contract content is required'),
  value: z.number().min(0).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  terms: z.string().optional(),
  signatureRequired: z.boolean().default(true),
  autoExecute: z.boolean().default(false),
})

type ContractFormData = z.infer<typeof contractSchema>

interface Customer {
  id: string
  name: string
  email: string | null
  company: string | null
  address: string | null
  city: string | null
  state: string | null
  country: string | null
  postalCode: string | null
}

interface Quotation {
  id: string
  quotationNumber: string
  title: string
  customerId: string
  customer: {
    id: string
    name: string
    company: string | null
  }
}

interface Template {
  id: string
  name: string
  description: string | null
  content: string
  category: string | null
}

interface Company {
  name: string | null
  email: string | null
  phone: string | null
  address: string | null
  city: string | null
  state: string | null
  country: string | null
  postalCode: string | null
  website: string | null
  taxId: string | null
}

interface ContractFormProps {
  mode: 'create' | 'edit'
  contract?: any
  customers: Customer[]
  quotations: Quotation[]
  templates: Template[]
  company: Company | null
  preselectedCustomerId?: string
  preselectedQuotationId?: string
  preselectedQuotation?: Quotation | null
  contractNumber?: string
}

export function ContractForm({ 
  mode, 
  contract, 
  customers, 
  quotations, 
  templates, 
  company,
  preselectedCustomerId,
  preselectedQuotationId,
  preselectedQuotation,
  contractNumber
}: ContractFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [isGeneratingContent, setIsGeneratingContent] = useState(false)
  const router = useRouter()

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
  } = useForm<ContractFormData>({
    resolver: zodResolver(contractSchema),
    defaultValues: contract ? {
      contractNumber: contract.contractNumber || '',
      title: contract.title || '',
      description: contract.description || '',
      customerId: contract.customerId || '',
      quotationId: contract.quotationId || '',
      templateId: '',
      content: contract.content || '',
      value: contract.value || 0,
      startDate: contract.startDate ? new Date(contract.startDate).toISOString().split('T')[0] : '',
      endDate: contract.endDate ? new Date(contract.endDate).toISOString().split('T')[0] : '',
      terms: contract.terms || '',
      signatureRequired: contract.signatureRequired ?? true,
      autoExecute: contract.autoExecute ?? false,
    } : {
      contractNumber: contractNumber || '',
      title: preselectedQuotation?.title ? `Contract for ${preselectedQuotation.title}` : '',
      description: '',
      customerId: preselectedCustomerId || preselectedQuotation?.customerId || '',
      quotationId: preselectedQuotationId || '',
      templateId: '',
      content: '',
      value: 0,
      startDate: new Date().toISOString().split('T')[0],
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 1 year from now
      terms: 'This contract is governed by the laws of [Jurisdiction]. Any disputes shall be resolved through binding arbitration.',
      signatureRequired: true,
      autoExecute: false,
    }
  })

  const watchedCustomerId = watch('customerId')
  const watchedQuotationId = watch('quotationId')
  const watchedTemplateId = watch('templateId')

  // Filter quotations by selected customer
  const filteredQuotations = quotations.filter(quotation => 
    !watchedCustomerId || quotation.customerId === watchedCustomerId
  )

  // Load template content when template is selected
  useEffect(() => {
    if (watchedTemplateId && mode === 'create') {
      const selectedTemplate = templates.find(t => t.id === watchedTemplateId)
      if (selectedTemplate) {
        setValue('content', selectedTemplate.content)
      }
    }
  }, [watchedTemplateId, templates, setValue, mode])

  // Load quotation details when quotation is selected
  useEffect(() => {
    if (watchedQuotationId && mode === 'create') {
      const selectedQuotation = quotations.find(q => q.id === watchedQuotationId)
      if (selectedQuotation) {
        // Update title if not set
        if (!watch('title')) {
          setValue('title', `Contract for ${selectedQuotation.title}`)
        }
      }
    }
  }, [watchedQuotationId, quotations, setValue, watch, mode])

  const onSubmit = async (data: ContractFormData) => {
    setIsLoading(true)
    setError('')

    try {
      const url = mode === 'create' ? '/api/contracts' : `/api/contracts/${contract.id}`
      const method = mode === 'create' ? 'POST' : 'PUT'

      // Prepare data for submission
      const submissionData = {
        ...data,
        value: data.value || null,
        startDate: data.startDate ? new Date(data.startDate) : null,
        endDate: data.endDate ? new Date(data.endDate) : null,
      }

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submissionData),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to save contract')
      }

      const result = await response.json()
      
      toast.success(mode === 'create' ? 'Contract created successfully!' : 'Contract updated successfully!')
      
      if (mode === 'create') {
        router.push(`/dashboard/contracts/${result.id}`)
      } else {
        router.push('/dashboard/contracts')
      }
    } catch (error) {
      console.error('Error saving contract:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to save contract'
      setError(errorMessage)
      toast.error(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    if (mode === 'create') {
      reset()
    } else {
      router.push('/dashboard/contracts')
    }
  }

  const generateAIContent = async () => {
    const selectedCustomer = customers.find(c => c.id === watch('customerId'))
    const selectedQuotation = quotations.find(q => q.id === watch('quotationId'))
    
    if (!selectedCustomer) {
      toast.error('Please select a customer first')
      return
    }

    setIsGeneratingContent(true)
    try {
      const response = await fetch('/api/contracts/generate-content', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customer: selectedCustomer,
          quotation: selectedQuotation,
          title: watch('title'),
          value: watch('value'),
          startDate: watch('startDate'),
          endDate: watch('endDate'),
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to generate content')
      }

      const result = await response.json()
      
      if (result.content) setValue('content', result.content)
      if (result.terms) setValue('terms', result.terms)
      
      toast.success('AI content generated successfully!')
    } catch (error) {
      toast.error('Failed to generate AI content')
    } finally {
      setIsGeneratingContent(false)
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Contract Information</CardTitle>
            <Button
              type="button"
              variant="outline"
              onClick={generateAIContent}
              disabled={isGeneratingContent || !watch('customerId')}
            >
              {isGeneratingContent ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Sparkles className="h-4 w-4 mr-2" />
                  Generate AI Content
                </>
              )}
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="contractNumber">Contract Number *</Label>
              <Input
                id="contractNumber"
                {...register('contractNumber')}
                placeholder="CON-202401-0001"
                disabled={isLoading}
              />
              {errors.contractNumber && (
                <p className="text-sm text-red-600 mt-1">{errors.contractNumber.message}</p>
              )}
            </div>
            <div>
              <Label htmlFor="value">Contract Value (₹)</Label>
              <Input
                id="value"
                type="number"
                step="0.01"
                min="0"
                {...register('value', { valueAsNumber: true })}
                placeholder="0.00"
                disabled={isLoading}
              />
            </div>
          </div>

          <div>
            <Label htmlFor="title">Title *</Label>
            <Input
              id="title"
              {...register('title')}
              placeholder="Contract title"
              disabled={isLoading}
            />
            {errors.title && (
              <p className="text-sm text-red-600 mt-1">{errors.title.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <textarea
              id="description"
              {...register('description')}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Describe the contract..."
              disabled={isLoading}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="startDate">Start Date</Label>
              <Input
                id="startDate"
                type="date"
                {...register('startDate')}
                disabled={isLoading}
              />
            </div>
            <div>
              <Label htmlFor="endDate">End Date</Label>
              <Input
                id="endDate"
                type="date"
                {...register('endDate')}
                disabled={isLoading}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Customer and Quotation Information */}
      <Card>
        <CardHeader>
          <CardTitle>Customer & Quotation Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <Label htmlFor="customerId">Customer *</Label>
                <Link href="/dashboard/customers/new">
                  <Button type="button" variant="outline" >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Customer
                  </Button>
                </Link>
              </div>
              <select
                id="customerId"
                {...register('customerId')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isLoading}
              >
                <option value="">Select a customer</option>
                {customers.map((customer) => (
                  <option key={customer.id} value={customer.id}>
                    {customer.name} {customer.company && `(${customer.company})`}
                  </option>
                ))}
              </select>
              {errors.customerId && (
                <p className="text-sm text-red-600 mt-1">{errors.customerId.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="quotationId">Related Quotation (Optional)</Label>
              <select
                id="quotationId"
                {...register('quotationId')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isLoading}
              >
                <option value="">Select a quotation</option>
                {filteredQuotations.map((quotation) => (
                  <option key={quotation.id} value={quotation.id}>
                    {quotation.quotationNumber} - {quotation.title}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Show selected customer details */}
          {watch('customerId') && (
            <div className="p-3 bg-gray-50 rounded-md">
              {(() => {
                const selectedCustomer = customers.find(c => c.id === watch('customerId'))
                if (!selectedCustomer) return null

                return (
                  <div>
                    <p className="font-medium text-gray-900">{selectedCustomer.name}</p>
                    {selectedCustomer.company && (
                      <p className="text-sm text-gray-600">{selectedCustomer.company}</p>
                    )}
                    {selectedCustomer.email && (
                      <p className="text-sm text-gray-600">{selectedCustomer.email}</p>
                    )}
                    {selectedCustomer.address && (
                      <p className="text-sm text-gray-600">
                        {[selectedCustomer.address, selectedCustomer.city, selectedCustomer.state, selectedCustomer.country]
                          .filter(Boolean).join(', ')}
                      </p>
                    )}
                  </div>
                )
              })()}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Template Selection */}
      {templates.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Contract Template (Optional)</CardTitle>
          </CardHeader>
          <CardContent>
            <div>
              <Label htmlFor="templateId">Select Template</Label>
              <select
                id="templateId"
                {...register('templateId')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isLoading}
              >
                <option value="">Select a template</option>
                {templates.map((template) => (
                  <option key={template.id} value={template.id}>
                    {template.name} {template.category && `(${template.category})`}
                  </option>
                ))}
              </select>
              <p className="text-sm text-gray-500 mt-1">
                Selecting a template will populate the contract content below
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Contract Content */}
      <Card>
        <CardHeader>
          <CardTitle>Contract Content</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="content">Contract Content *</Label>
            <textarea
              id="content"
              {...register('content')}
              rows={15}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
              placeholder="Enter the contract content here..."
              disabled={isLoading}
            />
            {errors.content && (
              <p className="text-sm text-red-600 mt-1">{errors.content.message}</p>
            )}
            <p className="text-sm text-gray-500 mt-1">
              Use placeholders like [CUSTOMER_NAME], [COMPANY_NAME], [START_DATE], [END_DATE], [VALUE] which will be automatically replaced
            </p>
          </div>

          <div>
            <Label htmlFor="terms">Terms & Conditions</Label>
            <textarea
              id="terms"
              {...register('terms')}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Additional terms and conditions..."
              disabled={isLoading}
            />
          </div>
        </CardContent>
      </Card>

      {/* Contract Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Contract Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="signatureRequired"
              {...register('signatureRequired')}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              disabled={isLoading}
            />
            <Label htmlFor="signatureRequired" className="text-sm">
              Require digital signature from customer
            </Label>
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="autoExecute"
              {...register('autoExecute')}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              disabled={isLoading}
            />
            <Label htmlFor="autoExecute" className="text-sm">
              Automatically execute contract when signed
            </Label>
          </div>

          <div className="p-3 bg-blue-50 rounded-md">
            <h4 className="font-medium text-blue-900 mb-2">Digital Signature Features</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Secure digital signatures with timestamp</li>
              <li>• Email notifications for signature requests</li>
              <li>• Automatic status updates when signed</li>
              <li>• Legal compliance and audit trail</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex items-center justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={handleCancel}
          disabled={isLoading}
        >
          <X className="h-4 w-4 mr-2" />
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              {mode === 'create' ? 'Creating...' : 'Updating...'}
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              {mode === 'create' ? 'Create Contract' : 'Update Contract'}
            </>
          )}
        </Button>
      </div>
    </form>
  )
}
