import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the original template
    const originalTemplate = await prisma.contractTemplate.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      }
    })

    if (!originalTemplate) {
      return NextResponse.json({ error: 'Template not found' }, { status: 404 })
    }

    // Generate a unique name for the duplicate
    let duplicateName = `${originalTemplate.name} (Copy)`
    let counter = 1
    
    while (true) {
      const existingTemplate = await prisma.contractTemplate.findFirst({
        where: {
          name: duplicateName,
          companyId: session.user.companyId,
        }
      })
      
      if (!existingTemplate) {
        break
      }
      
      counter++
      duplicateName = `${originalTemplate.name} (Copy ${counter})`
    }

    // Create the duplicate template
    const duplicateTemplate = await prisma.contractTemplate.create({
      data: {
        name: duplicateName,
        description: originalTemplate.description,
        category: originalTemplate.category,
        status: 'DRAFT', // Always create duplicates as draft
        content: originalTemplate.content,
        variables: originalTemplate.variables || null,
        companyId: session.user.companyId,
        createdById: session.user.id,
      },
      include: {
        createdBy: {
          select: { name: true, firstName: true, lastName: true }
        },
        _count: {
          select: {
            contracts: true
          }
        }
      }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'NOTE',
        title: 'Contract template duplicated',
        description: `Template "${originalTemplate.name}" was duplicated as "${duplicateName}"`,
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    return NextResponse.json(duplicateTemplate, { status: 201 })
  } catch (error) {
    console.error('Error duplicating contract template:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
