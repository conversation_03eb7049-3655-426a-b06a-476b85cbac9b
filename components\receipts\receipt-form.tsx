'use client'

import { useState, useEffect } from 'react'
import { use<PERSON>out<PERSON> } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { formatCurrency } from '@/lib/utils'
import { 
  Loader2, 
  Save, 
  X, 
  Receipt,
  DollarSign,
  Calendar,
  CreditCard,
  FileText,
  User
} from 'lucide-react'
import toast from 'react-hot-toast'

const receiptSchema = z.object({
  receiptNumber: z.string().min(1, 'Receipt number is required'),
  invoiceId: z.string().min(1, 'Invoice is required'),
  amount: z.number().min(0.01, 'Amount must be greater than 0'),
  paymentMethod: z.enum(['CASH', 'BANK_TRANSFER', 'CREDIT_CARD', 'DEBIT_CARD', 'UPI', 'CHEQUE', 'OTHER']),
  paidAt: z.string().min(1, 'Payment date is required'),
  status: z.enum(['PENDING', 'CONFIRMED', 'CANCELLED']).default('CONFIRMED'),
  notes: z.string().optional(),
  referenceNumber: z.string().optional(),
})

type ReceiptFormData = z.infer<typeof receiptSchema>

interface ReceiptFormProps {
  mode: 'create' | 'edit'
  receipt?: any
  availableInvoices: Array<{
    id: string
    invoiceNumber: string
    total: number
    customer: {
      id: string
      name: string
      company: string | null
    } | null
  }>
  selectedInvoice?: any
  suggestedReceiptNumber?: string
}

export function ReceiptForm({ 
  mode, 
  receipt, 
  availableInvoices,
  selectedInvoice,
  suggestedReceiptNumber
}: ReceiptFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [selectedInvoiceData, setSelectedInvoiceData] = useState(selectedInvoice)
  const router = useRouter()

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
  } = useForm<ReceiptFormData>({
    resolver: zodResolver(receiptSchema),
    defaultValues: receipt ? {
      receiptNumber: receipt.receiptNumber || '',
      invoiceId: receipt.invoiceId || '',
      amount: receipt.amount || 0,
      paymentMethod: receipt.paymentMethod || 'BANK_TRANSFER',
      paidAt: receipt.paidAt ? new Date(receipt.paidAt).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
      status: receipt.status || 'CONFIRMED',
      notes: receipt.notes || '',
      referenceNumber: receipt.referenceNumber || '',
    } : {
      receiptNumber: suggestedReceiptNumber || '',
      invoiceId: selectedInvoice?.id || '',
      amount: selectedInvoice?.total || 0,
      paymentMethod: 'BANK_TRANSFER',
      paidAt: new Date().toISOString().split('T')[0],
      status: 'CONFIRMED',
      notes: '',
      referenceNumber: '',
    }
  })

  const watchedInvoiceId = watch('invoiceId')
  const watchedAmount = watch('amount')

  // Update selected invoice when invoice changes
  useEffect(() => {
    if (watchedInvoiceId) {
      const invoice = availableInvoices.find(inv => inv.id === watchedInvoiceId)
      setSelectedInvoiceData(invoice)
      if (invoice && mode === 'create') {
        setValue('amount', invoice.total)
      }
    }
  }, [watchedInvoiceId, availableInvoices, setValue, mode])

  const onSubmit = async (data: ReceiptFormData) => {
    setIsLoading(true)
    setError('')

    try {
      const url = mode === 'create' ? '/api/receipts' : `/api/receipts/${receipt.id}`
      const method = mode === 'create' ? 'POST' : 'PUT'

      const submissionData = {
        ...data,
        paidAt: new Date(data.paidAt).toISOString(),
        referenceNumber: data.referenceNumber || null,
        notes: data.notes || null,
      }

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submissionData),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to save receipt')
      }

      const result = await response.json()
      
      toast.success(mode === 'create' ? 'Receipt created successfully!' : 'Receipt updated successfully!')
      
      if (mode === 'create') {
        router.push(`/dashboard/receipts/${result.id}`)
      } else {
        router.push('/dashboard/receipts')
      }
    } catch (error) {
      console.error('Error saving receipt:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to save receipt'
      setError(errorMessage)
      toast.error(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    if (mode === 'create') {
      reset()
    } else {
      router.push('/dashboard/receipts')
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Receipt Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Receipt className="h-5 w-5" />
            <span>Receipt Information</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="receiptNumber">Receipt Number *</Label>
              <Input
                id="receiptNumber"
                {...register('receiptNumber')}
                placeholder="RCP-2401-0001"
                disabled={isLoading}
              />
              {errors.receiptNumber && (
                <p className="text-sm text-red-600 mt-1">{errors.receiptNumber.message}</p>
              )}
            </div>
            <div>
              <Label htmlFor="status">Status</Label>
              <select
                id="status"
                {...register('status')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isLoading}
              >
                <option value="PENDING">Pending</option>
                <option value="CONFIRMED">Confirmed</option>
                <option value="CANCELLED">Cancelled</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Invoice Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>Invoice Details</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="invoiceId">Select Invoice *</Label>
            <select
              id="invoiceId"
              {...register('invoiceId')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={isLoading}
            >
              <option value="">Select an invoice</option>
              {availableInvoices.map((invoice) => (
                <option key={invoice.id} value={invoice.id}>
                  {invoice.invoiceNumber} - {formatCurrency(invoice.total)}
                  {invoice.customer && ` (${invoice.customer.name})`}
                </option>
              ))}
            </select>
            {errors.invoiceId && (
              <p className="text-sm text-red-600 mt-1">{errors.invoiceId.message}</p>
            )}
          </div>

          {selectedInvoiceData && (
            <div className="p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">Invoice Summary</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-blue-700">Invoice Number:</p>
                  <p className="font-medium text-blue-900">{selectedInvoiceData.invoiceNumber}</p>
                </div>
                <div>
                  <p className="text-blue-700">Total Amount:</p>
                  <p className="font-medium text-blue-900">{formatCurrency(selectedInvoiceData.total)}</p>
                </div>
                {selectedInvoiceData.customer && (
                  <>
                    <div>
                      <p className="text-blue-700">Customer:</p>
                      <p className="font-medium text-blue-900">{selectedInvoiceData.customer.name}</p>
                    </div>
                    {selectedInvoiceData.customer.company && (
                      <div>
                        <p className="text-blue-700">Company:</p>
                        <p className="font-medium text-blue-900">{selectedInvoiceData.customer.company}</p>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Payment Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <DollarSign className="h-5 w-5" />
            <span>Payment Information</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="amount">Amount Received (₹) *</Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                min="0"
                {...register('amount', { valueAsNumber: true })}
                placeholder="0.00"
                disabled={isLoading}
              />
              {errors.amount && (
                <p className="text-sm text-red-600 mt-1">{errors.amount.message}</p>
              )}
              {selectedInvoiceData && watchedAmount !== selectedInvoiceData.total && (
                <p className="text-sm text-orange-600 mt-1">
                  Amount differs from invoice total ({formatCurrency(selectedInvoiceData.total)})
                </p>
              )}
            </div>
            <div>
              <Label htmlFor="paymentMethod">Payment Method *</Label>
              <select
                id="paymentMethod"
                {...register('paymentMethod')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isLoading}
              >
                <option value="CASH">Cash</option>
                <option value="BANK_TRANSFER">Bank Transfer</option>
                <option value="CREDIT_CARD">Credit Card</option>
                <option value="DEBIT_CARD">Debit Card</option>
                <option value="UPI">UPI</option>
                <option value="CHEQUE">Cheque</option>
                <option value="OTHER">Other</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="paidAt">Payment Date *</Label>
              <Input
                id="paidAt"
                type="date"
                {...register('paidAt')}
                disabled={isLoading}
              />
              {errors.paidAt && (
                <p className="text-sm text-red-600 mt-1">{errors.paidAt.message}</p>
              )}
            </div>
            <div>
              <Label htmlFor="referenceNumber">Reference Number</Label>
              <Input
                id="referenceNumber"
                {...register('referenceNumber')}
                placeholder="Transaction ID, Cheque number, etc."
                disabled={isLoading}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Additional Information */}
      <Card>
        <CardHeader>
          <CardTitle>Additional Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div>
            <Label htmlFor="notes">Notes</Label>
            <textarea
              id="notes"
              {...register('notes')}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Additional notes about this payment..."
              disabled={isLoading}
            />
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex items-center justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={handleCancel}
          disabled={isLoading}
        >
          <X className="h-4 w-4 mr-2" />
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              {mode === 'create' ? 'Creating...' : 'Updating...'}
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              {mode === 'create' ? 'Create Receipt' : 'Update Receipt'}
            </>
          )}
        </Button>
      </div>
    </form>
  )
}
