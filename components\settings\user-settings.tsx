'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { toast } from 'react-hot-toast'
import {
  User,
  Shield,
  Globe,
  Save,
  Loader2,
  Camera,
  Key,
  Smartphone
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs'

const userSettingsSchema = z.object({
  profile: z.object({
    name: z.string().min(1, 'Name is required'),
    email: z.string().email('Invalid email address'),
    timezone: z.string(),
    language: z.string(),
  }),
  security: z.object({
    currentPassword: z.string().optional(),
    newPassword: z.string().optional(),
    confirmPassword: z.string().optional(),
    twoFactorEnabled: z.boolean(),
  }),
  preferences: z.object({
    emailNotifications: z.boolean(),
    pushNotifications: z.boolean(),
    marketingEmails: z.boolean(),
    securityAlerts: z.boolean(),
  })
}).refine((data) => {
  if (data.security.newPassword && data.security.newPassword !== data.security.confirmPassword) {
    return false
  }
  return true
}, {
  message: "Passwords don't match",
  path: ["security", "confirmPassword"]
})

type UserSettingsFormData = z.infer<typeof userSettingsSchema>

interface UserSettingsProps {
  userId: string
  initialSettings: {
    profile: {
      name: string
      email: string
      role: string
      timezone: string
      language: string
      avatar: string
    }
    company: {
      name: string
      plan: string
      status: string
      userCount: number
      subscription: any
    }
    notifications: {
      email: boolean
      push: boolean
      sms: boolean
      marketing: boolean
      security: boolean
      updates: boolean
    }
  }
}

export function UserSettings({ userId, initialSettings }: UserSettingsProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('profile')

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue
  } = useForm<UserSettingsFormData>({
    resolver: zodResolver(userSettingsSchema),
    defaultValues: {
      profile: {
        name: initialSettings.profile.name,
        email: initialSettings.profile.email,
        timezone: initialSettings.profile.timezone,
        language: initialSettings.profile.language,
      },
      security: {
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
        twoFactorEnabled: false,
      },
      preferences: {
        emailNotifications: initialSettings.notifications.email,
        pushNotifications: initialSettings.notifications.push,
        marketingEmails: initialSettings.notifications.marketing,
        securityAlerts: initialSettings.notifications.security,
      }
    }
  })

  const onSubmit = async (data: UserSettingsFormData) => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/user/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update settings')
      }

      toast.success('Settings updated successfully!')
    } catch (error) {
      console.error('Error updating settings:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to update settings'
      toast.error(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const timezones = [
    'UTC',
    'America/New_York',
    'America/Los_Angeles',
    'Europe/London',
    'Europe/Paris',
    'Asia/Tokyo',
    'Asia/Kolkata',
    'Australia/Sydney'
  ]

  const languages = [
    { code: 'en', name: 'English' },
    { code: 'es', name: 'Spanish' },
    { code: 'fr', name: 'French' },
    { code: 'de', name: 'German' },
    { code: 'hi', name: 'Hindi' },
    { code: 'ja', name: 'Japanese' }
  ]

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="profile">Profile</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="preferences">Preferences</TabsTrigger>
        </TabsList>

        {/* Profile Tab */}
        <TabsContent value="profile" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Personal Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <User className="h-5 w-5 text-blue-600" />
                  <span>Personal Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="name">Full Name</Label>
                  <Input
                    id="name"
                    {...register('profile.name')}
                    placeholder="Enter your full name"
                  />
                  {errors.profile?.name && (
                    <p className="text-sm text-red-600">{errors.profile.name.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email Address</Label>
                  <Input
                    id="email"
                    type="email"
                    {...register('profile.email')}
                    placeholder="Enter your email"
                  />
                  {errors.profile?.email && (
                    <p className="text-sm text-red-600">{errors.profile.email.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="timezone">Timezone</Label>
                  <Select value={watch('profile.timezone')} onValueChange={(value) => setValue('profile.timezone', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select timezone" />
                    </SelectTrigger>
                    <SelectContent>
                      {timezones.map((tz) => (
                        <SelectItem key={tz} value={tz}>{tz}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="language">Language</Label>
                  <Select value={watch('profile.language')} onValueChange={(value) => setValue('profile.language', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select language" />
                    </SelectTrigger>
                    <SelectContent>
                      {languages.map((lang) => (
                        <SelectItem key={lang.code} value={lang.code}>{lang.name}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* Company Information */}
            <Card>
              <CardHeader>
                <CardTitle>Company Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Company</span>
                  <span className="text-sm text-gray-600">{initialSettings.company.name}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Role</span>
                  <Badge variant="outline">{initialSettings.profile.role}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Plan</span>
                  <Badge>{initialSettings.company.plan}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Status</span>
                  <Badge variant={initialSettings.company.status === 'ACTIVE' ? 'default' : 'secondary'}>
                    {initialSettings.company.status}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Team Size</span>
                  <span className="text-sm text-gray-600">{initialSettings.company.userCount} users</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Security Tab */}
        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-green-600" />
                <span>Security Settings</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="currentPassword">Current Password</Label>
                <Input
                  id="currentPassword"
                  type="password"
                  {...register('security.currentPassword')}
                  placeholder="Enter current password"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="newPassword">New Password</Label>
                <Input
                  id="newPassword"
                  type="password"
                  {...register('security.newPassword')}
                  placeholder="Enter new password"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Confirm New Password</Label>
                <Input
                  id="confirmPassword"
                  type="password"
                  {...register('security.confirmPassword')}
                  placeholder="Confirm new password"
                />
                {errors.security?.confirmPassword && (
                  <p className="text-sm text-red-600">{errors.security.confirmPassword.message}</p>
                )}
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Two-Factor Authentication</Label>
                  <p className="text-sm text-gray-500">Add an extra layer of security</p>
                </div>
                <Switch
                  checked={watch('security.twoFactorEnabled')}
                  onCheckedChange={(checked) => setValue('security.twoFactorEnabled', checked)}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Preferences Tab */}
        <TabsContent value="preferences" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Notification Preferences</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Email Notifications</Label>
                  <p className="text-sm text-gray-500">Receive notifications via email</p>
                </div>
                <Switch
                  checked={watch('preferences.emailNotifications')}
                  onCheckedChange={(checked) => setValue('preferences.emailNotifications', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Push Notifications</Label>
                  <p className="text-sm text-gray-500">Receive push notifications in browser</p>
                </div>
                <Switch
                  checked={watch('preferences.pushNotifications')}
                  onCheckedChange={(checked) => setValue('preferences.pushNotifications', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Marketing Emails</Label>
                  <p className="text-sm text-gray-500">Receive product updates and offers</p>
                </div>
                <Switch
                  checked={watch('preferences.marketingEmails')}
                  onCheckedChange={(checked) => setValue('preferences.marketingEmails', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Security Alerts</Label>
                  <p className="text-sm text-gray-500">Important security notifications</p>
                </div>
                <Switch
                  checked={watch('preferences.securityAlerts')}
                  onCheckedChange={(checked) => setValue('preferences.securityAlerts', checked)}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Save Actions */}
      <div className="flex items-center justify-end space-x-4 p-6 bg-gray-50 rounded-lg">
        <p className="text-sm text-gray-600">Changes will be saved to your account</p>
        <Button type="button" variant="outline" onClick={() => reset()}>
          Reset
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <Save className="h-4 w-4 mr-2" />
          )}
          Save Settings
        </Button>
      </div>
    </form>
  )
}
