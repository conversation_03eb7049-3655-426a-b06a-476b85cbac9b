'use client'

import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { cn } from '@/lib/utils'
import {
  LayoutDashboard,
  Users,
  FileText,
  Receipt,

  Package,
  TrendingUp,
  Settings,
  HelpCircle,
  ChevronDown,
  ChevronRight,
  BarChart3,
  Bell,
  Mail,
  CreditCard,
  Code,
  Rocket
} from 'lucide-react'
import { useState } from 'react'

const navigation = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: LayoutDashboard,
  },
  {
    name: 'Customers',
    href: '/dashboard/customers',
    icon: Users,
  },
  {
    name: 'Leads',
    href: '/dashboard/leads',
    icon: TrendingUp,
  },
  {
    name: 'Quotations',
    href: '/dashboard/quotations',
    icon: FileText,
 Receipt,
    children: [
      { name: 'All Quotations', href: '/dashboard/quotations' },
      { name: 'Create New', href: '/dashboard/quotations/new' },
      { name: 'Templates', href: '/dashboard/quotations/templates' },
    ]
  },
  {
    name: 'Invoices',
    href: '/dashboard/invoices',
    icon: Receipt,
    children: [
      { name: 'All Invoices', href: '/dashboard/invoices' },
      { name: 'Create New', href: '/dashboard/invoices/new' },
      { name: 'Recurring', href: '/dashboard/invoices/recurring' },
    ]
  },
  {
    name: 'Contracts',
    href: '/dashboard/contracts',
    icon: FileText,
 Receipt,
    children: [
      { name: 'All Contracts', href: '/dashboard/contracts' },
      { name: 'Create New', href: '/dashboard/contracts/new' },
      { name: 'Templates', href: '/dashboard/contract-templates' },
    ]
  },
  {
    name: 'Items',
    href: '/dashboard/items',
    icon: Package,
  },
  {
    name: 'Receipts',
    href: '/dashboard/receipts',
    icon: Receipt,
    children: [
      { name: 'All Receipts', href: '/dashboard/receipts' },
      { name: 'Create New', href: '/dashboard/receipts/new' },
    ]
  },
  {
    name: 'Reports',
    href: '/dashboard/reports',
    icon: BarChart3,
    children: [
      { name: 'All Reports', href: '/dashboard/reports' },
      { name: 'Report Builder', href: '/dashboard/reports/builder' },
      { name: 'Analytics', href: '/dashboard/analytics' },
    ]
  },
  {
    name: 'Notifications',
    href: '/dashboard/notifications',
    icon: Bell,
    children: [
      { name: 'All Notifications', href: '/dashboard/notifications' },
      { name: 'Settings', href: '/dashboard/notifications/settings' },
    ]
  },
  {
    name: 'Emails',
    href: '/dashboard/emails',
    icon: Mail,
    children: [
      { name: 'All Emails', href: '/dashboard/emails' },
      { name: 'Compose', href: '/dashboard/emails/compose' },
      { name: 'Templates', href: '/dashboard/emails/templates' },
      { name: 'Settings', href: '/dashboard/emails/settings' },
    ]
  },
  {
    name: 'Billing',
    href: '/dashboard/billing',
    icon: CreditCard,
    children: [
      { name: 'Overview', href: '/dashboard/billing' },
      { name: 'Subscription', href: '/dashboard/billing/subscription' },
      { name: 'Payment History', href: '/dashboard/billing/payments' },
      { name: 'Settings', href: '/dashboard/billing/settings' },
    ]
  },
  {
    name: 'API',
    href: '/dashboard/api',
    icon: Code,
    children: [
      { name: 'Overview', href: '/dashboard/api' },
      { name: 'API Keys', href: '/dashboard/api/keys' },
      { name: 'Webhooks', href: '/dashboard/api/webhooks' },
      { name: 'Documentation', href: '/dashboard/api/docs' },
      { name: 'Settings', href: '/dashboard/api/settings' },
    ]
  },
  {
    name: 'Support',
    href: '/dashboard/support',
    icon: HelpCircle,
    children: [
      { name: 'Support Center', href: '/dashboard/support' },
      { name: 'My Tickets', href: '/dashboard/support/tickets' },
      { name: 'Knowledge Base', href: '/dashboard/support/knowledge-base' },
      { name: 'Live Chat', href: '/dashboard/support/chat' },
      { name: 'Contact', href: '/dashboard/support/contact' },
    ]
  },
]

// Quick access items (shown at bottom of sidebar)
export const quickAccessItems = [
  {
    name: 'Getting Started',
    href: '/dashboard/onboarding',
    icon: Rocket,
    description: 'Complete your setup and learn the platform'
  },
]

const secondaryNavigation = [
  {
    name: 'Settings',
    href: '/dashboard/settings',
    icon: Settings,
  },
  {
    name: 'Help & Support',
    href: '/dashboard/help',
    icon: HelpCircle,
  },
]

export function DashboardNav() {
  const pathname = usePathname()
  const [expandedItems, setExpandedItems] = useState<string[]>([])

  const toggleExpanded = (name: string) => {
    setExpandedItems(prev => 
      prev.includes(name) 
        ? prev.filter(item => item !== name)
        : [...prev, name]
    )
  }

  const isActive = (href: string) => {
    if (href === '/dashboard') {
      return pathname === href
    }
    return pathname.startsWith(href)
  }

  const isChildActive = (children: any[]) => {
    return children.some(child => pathname.startsWith(child.href))
  }

  return (
    <nav className="w-64 bg-white border-r border-gray-200 min-h-screen">
      <div className="p-6">
        {/* Main Navigation */}
        <div className="space-y-1">
          {navigation.map((item) => {
            const hasChildren = item.children && item.children.length > 0
            const isExpanded = expandedItems.includes(item.name)
            const isItemActive = isActive(item.href)
            const hasActiveChild = hasChildren && isChildActive(item.children)

            return (
              <div key={item.name}>
                <div className="flex items-center">
                  <Link
                    href={item.href}
                    className={cn(
                      'flex items-center flex-1 px-3 py-2 text-sm font-medium rounded-lg transition-colors',
                      isItemActive || hasActiveChild
                        ? 'bg-blue-50 text-blue-700'
                        : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                    )}
                  >
                    <item.icon className="h-5 w-5 mr-3" />
                    {item.name}
                  </Link>
                  
                  {hasChildren && (
                    <button
                      onClick={() => toggleExpanded(item.name)}
                      className={cn(
                        'p-1 rounded hover:bg-gray-100 transition-colors',
                        isItemActive || hasActiveChild ? 'text-blue-700' : 'text-gray-400'
                      )}
                    >
                      {isExpanded ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </button>
                  )}
                </div>

                {/* Sub Navigation */}
                {hasChildren && isExpanded && (
                  <div className="ml-8 mt-1 space-y-1">
                    {item.children.map((child) => (
                      <Link
                        key={child.name}
                        href={child.href}
                        className={cn(
                          'block px-3 py-2 text-sm rounded-lg transition-colors',
                          pathname === child.href
                            ? 'bg-blue-50 text-blue-700 font-medium'
                            : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                        )}
                      >
                        {child.name}
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            )
          })}
        </div>

        {/* Divider */}
        <div className="my-6 border-t border-gray-200" />

        {/* Secondary Navigation */}
        <div className="space-y-1">
          {secondaryNavigation.map((item) => (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                'flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors',
                isActive(item.href)
                  ? 'bg-blue-50 text-blue-700'
                  : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
              )}
            >
              <item.icon className="h-5 w-5 mr-3" />
              {item.name}
            </Link>
          ))}
        </div>

        {/* Trial Status */}
        <div className="mt-8 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <span className="text-yellow-600 text-sm font-bold">!</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-yellow-800">
                Trial Period
              </p>
              <p className="text-xs text-yellow-700">
                12 days remaining
              </p>
            </div>
          </div>
          <div className="mt-3">
            <Link
              href="/dashboard/billing"
              className="text-xs text-yellow-800 hover:text-yellow-900 font-medium underline"
            >
              Upgrade now
            </Link>
          </div>
        </div>
      </div>
    </nav>
  )
}
