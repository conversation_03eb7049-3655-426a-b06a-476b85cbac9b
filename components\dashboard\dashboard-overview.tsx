'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { formatCurrency } from '@/lib/utils'
import { 
  Users, 
  UserPlus, 
  FileText,
  Receipt,

  Package,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Minus
} from 'lucide-react'

interface DashboardOverviewProps {
  data: {
    totalCustomers: number
    totalLeads: number
    totalQuotations: number
    totalInvoices: number
    totalContracts: number
    totalItems: number
    totalRevenue: number
    paidInvoices: number
  }
}

export function DashboardOverview({ data }: DashboardOverviewProps) {
  const overviewCards = [
    {
      title: 'Total Customers',
      value: data.totalCustomers.toString(),
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      description: 'Active customers',
      trend: null
    },
    {
      title: 'Active Leads',
      value: data.totalLeads.toString(),
      icon: UserPlus,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      description: 'Potential customers',
      trend: null
    },
    {
      title: 'Total Quotations',
      value: data.totalQuotations.toString(),
      icon: FileText,
 Receipt,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      description: 'All quotations',
      trend: null
    },
    {
      title: 'Total Invoices',
      value: data.totalInvoices.toString(),
      icon: Receipt,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      description: 'All invoices',
      trend: null
    },
    {
      title: 'Active Contracts',
      value: data.totalContracts.toString(),
      icon: FileText,
 Receipt,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-100',
      description: 'All contracts',
      trend: null
    },
    {
      title: 'Catalog Items',
      value: data.totalItems.toString(),
      icon: Package,
      color: 'text-cyan-600',
      bgColor: 'bg-cyan-100',
      description: 'Products & services',
      trend: null
    },
    {
      title: 'Total Revenue',
      value: formatCurrency(data.totalRevenue),
      icon: DollarSign,
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-100',
      description: 'Paid invoices',
      trend: null
    },
    {
      title: 'Paid Invoices',
      value: data.paidInvoices.toString(),
      icon: Receipt,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      description: 'Successfully paid',
      trend: null
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {overviewCards.map((card, index) => (
        <Card key={index} className="hover:shadow-lg transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              {card.title}
            </CardTitle>
            <div className={`p-2 rounded-lg ${card.bgColor}`}>
              <card.icon className={`h-4 w-4 ${card.color}`} />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">
              {card.value}
            </div>
            <p className="text-xs text-gray-500 mt-1">
              {card.description}
            </p>
            {card.trend && (
              <div className="flex items-center mt-2">
                {card.trend > 0 ? (
                  <TrendingUp className="h-3 w-3 text-green-600 mr-1" />
                ) : card.trend < 0 ? (
                  <TrendingDown className="h-3 w-3 text-red-600 mr-1" />
                ) : (
                  <Minus className="h-3 w-3 text-gray-400 mr-1" />
                )}
                <span className={`text-xs ${
                  card.trend > 0 ? 'text-green-600' : 
                  card.trend < 0 ? 'text-red-600' : 'text-gray-400'
                }`}>
                  {card.trend > 0 ? '+' : ''}{card.trend}%
                </span>
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
