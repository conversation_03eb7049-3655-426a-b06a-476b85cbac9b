'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { formatCurrency, formatDate } from '@/lib/utils'
import { 
  Receipt, 
  Download,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  CreditCard,
  Calendar,
  DollarSign,
  FileText
} from 'lucide-react'

interface PaymentHistoryProps {
  payments: Array<{
    id: string
    amount: number
    currency: string
    status: string
    description: string | null
    invoiceUrl: string | null
    receiptUrl: string | null
    createdAt: Date
    paidAt: Date | null
    failedAt: Date | null
  }>
}

export function PaymentHistory({ payments }: PaymentHistoryProps) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'SUCCEEDED':
        return CheckCircle
      case 'FAILED':
        return XCircle
      case 'PENDING':
        return Clock
      case 'REFUNDED':
        return AlertTriangle
      default:
        return Clock
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'SUCCEEDED':
        return 'text-green-600 border-green-200'
      case 'FAILED':
        return 'text-red-600 border-red-200'
      case 'PENDING':
        return 'text-yellow-600 border-yellow-200'
      case 'REFUNDED':
        return 'text-orange-600 border-orange-200'
      default:
        return 'text-gray-600 border-gray-200'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'SUCCEEDED':
        return 'Paid'
      case 'FAILED':
        return 'Failed'
      case 'PENDING':
        return 'Pending'
      case 'REFUNDED':
        return 'Refunded'
      default:
        return status
    }
  }

  const handleDownloadInvoice = async (paymentId: string, invoiceUrl: string | null) => {
    if (invoiceUrl) {
      window.open(invoiceUrl, '_blank')
    } else {
      // Generate invoice if not available
      try {
        const response = await fetch(`/api/billing/payments/${paymentId}/invoice`)
        if (response.ok) {
          const blob = await response.blob()
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = `invoice-${paymentId}.pdf`
          document.body.appendChild(a)
          a.click()
          window.URL.revokeObjectURL(url)
          document.body.removeChild(a)
        }
      } catch (error) {
        console.error('Error downloading invoice:', error)
      }
    }
  }

  const handleDownloadReceipt = async (paymentId: string, receiptUrl: string | null) => {
    if (receiptUrl) {
      window.open(receiptUrl, '_blank')
    } else {
      // Generate receipt if not available
      try {
        const response = await fetch(`/api/billing/payments/${paymentId}/receipt`)
        if (response.ok) {
          const blob = await response.blob()
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = `receipt-${paymentId}.pdf`
          document.body.appendChild(a)
          a.click()
          window.URL.revokeObjectURL(url)
          document.body.removeChild(a)
        }
      } catch (error) {
        console.error('Error downloading receipt:', error)
      }
    }
  }

  // Calculate payment statistics
  const totalPaid = payments
    .filter(p => p.status === 'SUCCEEDED')
    .reduce((sum, p) => sum + p.amount, 0)
  
  const successfulPayments = payments.filter(p => p.status === 'SUCCEEDED').length
  const failedPayments = payments.filter(p => p.status === 'FAILED').length
  const pendingPayments = payments.filter(p => p.status === 'PENDING').length

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Receipt className="h-5 w-5" />
            <span>Payment History</span>
          </CardTitle>
          <Button variant="outline" >
            <Download className="h-4 w-4 mr-2" />
            Download All
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {payments.length > 0 ? (
          <div className="space-y-6">
            {/* Payment Statistics */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <DollarSign className="h-6 w-6 text-green-600 mx-auto mb-2" />
                <div className="text-lg font-bold text-green-800">
                  {formatCurrency(totalPaid)}
                </div>
                <div className="text-sm text-green-600">Total Paid</div>
              </div>
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <CheckCircle className="h-6 w-6 text-blue-600 mx-auto mb-2" />
                <div className="text-lg font-bold text-blue-800">
                  {successfulPayments}
                </div>
                <div className="text-sm text-blue-600">Successful</div>
              </div>
              <div className="text-center p-4 bg-red-50 rounded-lg">
                <XCircle className="h-6 w-6 text-red-600 mx-auto mb-2" />
                <div className="text-lg font-bold text-red-800">
                  {failedPayments}
                </div>
                <div className="text-sm text-red-600">Failed</div>
              </div>
              <div className="text-center p-4 bg-yellow-50 rounded-lg">
                <Clock className="h-6 w-6 text-yellow-600 mx-auto mb-2" />
                <div className="text-lg font-bold text-yellow-800">
                  {pendingPayments}
                </div>
                <div className="text-sm text-yellow-600">Pending</div>
              </div>
            </div>

            {/* Payment List */}
            <div className="space-y-3">
              {payments.map((payment) => {
                const StatusIcon = getStatusIcon(payment.status)
                
                return (
                  <div 
                    key={payment.id} 
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center space-x-4">
                      <div className={`p-2 rounded-lg ${getStatusColor(payment.status).includes('green') ? 'bg-green-100' : 
                        getStatusColor(payment.status).includes('red') ? 'bg-red-100' :
                        getStatusColor(payment.status).includes('yellow') ? 'bg-yellow-100' :
                        getStatusColor(payment.status).includes('orange') ? 'bg-orange-100' : 'bg-gray-100'}`}>
                        <StatusIcon className={`h-4 w-4 ${getStatusColor(payment.status).split(' ')[0]}`} />
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">
                          {formatCurrency(payment.amount)}
                        </div>
                        <div className="text-sm text-gray-600">
                          {payment.description || 'Subscription payment'}
                        </div>
                        <div className="flex items-center space-x-4 text-xs text-gray-500 mt-1">
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-3 w-3" />
                            <span>{formatDate(payment.createdAt)}</span>
                          </div>
                          {payment.paidAt && (
                            <div className="flex items-center space-x-1">
                              <CheckCircle className="h-3 w-3" />
                              <span>Paid: {formatDate(payment.paidAt)}</span>
                            </div>
                          )}
                          {payment.failedAt && (
                            <div className="flex items-center space-x-1">
                              <XCircle className="h-3 w-3" />
                              <span>Failed: {formatDate(payment.failedAt)}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <Badge className={getStatusColor(payment.status)} variant="outline">
                        {getStatusText(payment.status)}
                      </Badge>
                      
                      {payment.status === 'SUCCEEDED' && (
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDownloadInvoice(payment.id, payment.invoiceUrl)}
                          >
                            <FileText className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDownloadReceipt(payment.id, payment.receiptUrl)}
                          >
                            <Receipt className="h-4 w-4" />
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>

            {/* Load More */}
            {payments.length >= 10 && (
              <div className="text-center pt-4 border-t">
                <Button variant="outline">
                  Load More Payments
                </Button>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-8">
            <Receipt className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="font-medium text-gray-900 mb-2">No Payment History</h3>
            <p className="text-gray-500 mb-6">
              Your payment history will appear here once you make your first payment.
            </p>
            <Button>
              <CreditCard className="h-4 w-4 mr-2" />
              Upgrade Plan
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
