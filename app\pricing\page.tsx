import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Check, Star, Zap, Crown } from 'lucide-react'
import Link from 'next/link'

const plans = [
  {
    name: 'Starter',
    description: 'Perfect for small businesses getting started',
    price: 29,
    period: 'month',
    icon: Star,
    popular: false,
    features: [
      'Up to 100 customers',
      '50 quotations per month',
      '25 invoices per month',
      'Basic email support',
      'Standard templates',
      '1 user account',
      'Basic reporting',
      'Mobile app access'
    ],
    limitations: [
      'No API access',
      'No custom branding',
      'No advanced integrations'
    ]
  },
  {
    name: 'Professional',
    description: 'Ideal for growing businesses with advanced needs',
    price: 79,
    period: 'month',
    icon: Zap,
    popular: true,
    features: [
      'Up to 1,000 customers',
      'Unlimited quotations',
      'Unlimited invoices',
      'Priority email & chat support',
      'Custom templates',
      'Up to 5 user accounts',
      'Advanced reporting & analytics',
      'Mobile app access',
      'API access',
      'Custom branding',
      'Email campaigns',
      'Contract management',
      'Webhook integrations'
    ],
    limitations: [
      'No white-label solution',
      'Standard integrations only'
    ]
  },
  {
    name: 'Enterprise',
    description: 'For large organizations requiring maximum flexibility',
    price: 199,
    period: 'month',
    icon: Crown,
    popular: false,
    features: [
      'Unlimited customers',
      'Unlimited quotations',
      'Unlimited invoices',
      '24/7 phone & email support',
      'Fully custom templates',
      'Unlimited user accounts',
      'Enterprise reporting & analytics',
      'Mobile app access',
      'Full API access',
      'White-label solution',
      'Advanced email campaigns',
      'Advanced contract management',
      'Custom integrations',
      'Dedicated account manager',
      'Custom training',
      'SLA guarantee',
      'Advanced security features'
    ],
    limitations: []
  }
]

export default function PricingPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">BusinessSaaS</h1>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/auth/signin">
                <Button variant="ghost">Sign In</Button>
              </Link>
              <Link href="/auth/signup">
                <Button>Get Started</Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 sm:text-5xl md:text-6xl">
            Simple, Transparent Pricing
          </h1>
          <p className="mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">
            Choose the perfect plan for your business. Start with a 14-day free trial, no credit card required.
          </p>
        </div>

        {/* Pricing Cards */}
        <div className="mt-16 grid grid-cols-1 gap-8 lg:grid-cols-3">
          {plans.map((plan) => {
            const Icon = plan.icon
            return (
              <Card key={plan.name} className={`relative ${plan.popular ? 'ring-2 ring-blue-500 shadow-lg' : ''}`}>
                {plan.popular && (
                  <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-blue-500">
                    Most Popular
                  </Badge>
                )}
                
                <CardHeader className="text-center">
                  <div className="flex justify-center mb-4">
                    <Icon className={`h-12 w-12 ${plan.popular ? 'text-blue-500' : 'text-gray-400'}`} />
                  </div>
                  <CardTitle className="text-2xl font-bold">{plan.name}</CardTitle>
                  <CardDescription className="text-gray-500">
                    {plan.description}
                  </CardDescription>
                  <div className="mt-4">
                    <span className="text-4xl font-bold text-gray-900">${plan.price}</span>
                    <span className="text-gray-500">/{plan.period}</span>
                  </div>
                </CardHeader>

                <CardContent>
                  <ul className="space-y-3">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <Check className="h-5 w-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>

                <CardFooter>
                  <Link href="/auth/signup" className="w-full">
                    <Button 
                      className={`w-full ${plan.popular ? 'bg-blue-500 hover:bg-blue-600' : ''}`}
                      variant={plan.popular ? 'default' : 'outline'}
                    >
                      Start Free Trial
                    </Button>
                  </Link>
                </CardFooter>
              </Card>
            )
          })}
        </div>

        {/* FAQ Section */}
        <div className="mt-20">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
            Frequently Asked Questions
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Can I change plans anytime?
              </h3>
              <p className="text-gray-600">
                Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Is there a free trial?
              </h3>
              <p className="text-gray-600">
                Yes, all plans come with a 14-day free trial. No credit card required to start.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                What payment methods do you accept?
              </h3>
              <p className="text-gray-600">
                We accept all major credit cards, PayPal, and bank transfers for Enterprise plans.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Can I cancel anytime?
              </h3>
              <p className="text-gray-600">
                Yes, you can cancel your subscription at any time. No long-term contracts or cancellation fees.
              </p>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="mt-20 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Ready to get started?
          </h2>
          <p className="text-xl text-gray-600 mb-8">
            Join thousands of businesses already using our platform to grow their revenue.
          </p>
          <Link href="/auth/signup">
            <Button size="lg" className="bg-blue-500 hover:bg-blue-600 text-white px-8 py-3">
              Start Your Free Trial Today
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}
