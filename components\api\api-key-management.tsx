'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { formatDate } from '@/lib/utils'
import { 
  Key, 
  Plus,
  Copy,
  Eye,
  EyeOff,
  Edit,
  Trash2,
  MoreHorizontal,
  CheckCircle,
  XCircle,
  AlertTriangle,
  RefreshCw
} from 'lucide-react'
import toast from 'react-hot-toast'

interface ApiKeyManagementProps {
  apiKeys: Array<{
    id: string
    name: string
    description: string | null
    keyPrefix: string
    status: string
    permissions: string[]
    lastUsedAt: Date | null
    expiresAt: Date | null
    createdAt: Date
  }>
  companyId: string
  plan: string
}

export function ApiKeyManagement({ apiKeys, companyId, plan }: ApiKeyManagementProps) {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [visibleKeys, setVisibleKeys] = useState<Set<string>>(new Set())
  const [newKey, setNewKey] = useState({
    name: '',
    description: '',
    permissions: [] as string[],
    expiresIn: '365' // days
  })

  const availablePermissions = [
    { value: 'customers:read', label: 'Read Customers' },
    { value: 'customers:write', label: 'Write Customers' },
    { value: 'quotations:read', label: 'Read Quotations' },
    { value: 'quotations:write', label: 'Write Quotations' },
    { value: 'invoices:read', label: 'Read Invoices' },
    { value: 'invoices:write', label: 'Write Invoices' },
    { value: 'contracts:read', label: 'Read Contracts' },
    { value: 'contracts:write', label: 'Write Contracts' },
    { value: 'webhooks:read', label: 'Read Webhooks' },
    { value: 'webhooks:write', label: 'Write Webhooks' },
    { value: 'analytics:read', label: 'Read Analytics' }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return CheckCircle
      case 'INACTIVE':
        return XCircle
      case 'EXPIRED':
        return AlertTriangle
      default:
        return XCircle
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'text-green-600 border-green-200'
      case 'INACTIVE':
        return 'text-gray-600 border-gray-200'
      case 'EXPIRED':
        return 'text-red-600 border-red-200'
      default:
        return 'text-gray-600 border-gray-200'
    }
  }

  const handleCreateApiKey = async () => {
    if (!newKey.name.trim()) {
      toast.error('API key name is required')
      return
    }

    if (newKey.permissions.length === 0) {
      toast.error('At least one permission is required')
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch('/api/api-keys', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newKey.name,
          description: newKey.description,
          permissions: newKey.permissions,
          expiresIn: parseInt(newKey.expiresIn)
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to create API key')
      }

      const result = await response.json()
      
      // Show the full API key in an alert (only time it's shown)
      toast.success('API key created successfully')
      
      // Reset form and close dialog
      setNewKey({
        name: '',
        description: '',
        permissions: [],
        expiresIn: '365'
      })
      setIsCreateDialogOpen(false)
      
      // Refresh the page to show new key
      window.location.reload()
    } catch (error) {
      toast.error('Failed to create API key')
    } finally {
      setIsLoading(false)
    }
  }

  const handleToggleKeyVisibility = (keyId: string) => {
    const newVisibleKeys = new Set(visibleKeys)
    if (newVisibleKeys.has(keyId)) {
      newVisibleKeys.delete(keyId)
    } else {
      newVisibleKeys.add(keyId)
    }
    setVisibleKeys(newVisibleKeys)
  }

  const handleCopyKey = async (keyPrefix: string) => {
    try {
      await navigator.clipboard.writeText(`${keyPrefix}...`)
      toast.success('API key prefix copied to clipboard')
    } catch (error) {
      toast.error('Failed to copy API key')
    }
  }

  const handleRevokeKey = async (keyId: string) => {
    if (!confirm('Are you sure you want to revoke this API key? This action cannot be undone.')) {
      return
    }

    try {
      const response = await fetch(`/api/api-keys/${keyId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to revoke API key')
      }

      toast.success('API key revoked successfully')
      window.location.reload()
    } catch (error) {
      toast.error('Failed to revoke API key')
    }
  }

  const handleToggleKeyStatus = async (keyId: string, currentStatus: string) => {
    const newStatus = currentStatus === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE'
    
    try {
      const response = await fetch(`/api/api-keys/${keyId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      })

      if (!response.ok) {
        throw new Error('Failed to update API key status')
      }

      toast.success(`API key ${newStatus.toLowerCase()} successfully`)
      window.location.reload()
    } catch (error) {
      toast.error('Failed to update API key status')
    }
  }

  const getKeyLimitForPlan = (plan: string) => {
    switch (plan) {
      case 'PROFESSIONAL':
        return 5
      case 'ENTERPRISE':
        return 20
      default:
        return 0
    }
  }

  const keyLimit = getKeyLimitForPlan(plan)
  const canCreateMore = apiKeys.length < keyLimit

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Key className="h-5 w-5" />
            <span>API Keys</span>
          </CardTitle>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button disabled={!canCreateMore}>
                <Plus className="h-4 w-4 mr-2" />
                Create API Key
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Create New API Key</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="keyName">Name</Label>
                  <Input
                    id="keyName"
                    placeholder="My API Key"
                    value={newKey.name}
                    onChange={(e) => setNewKey({ ...newKey, name: e.target.value })}
                  />
                </div>
                
                <div>
                  <Label htmlFor="keyDescription">Description (Optional)</Label>
                  <Textarea
                    id="keyDescription"
                    placeholder="Description of what this key is used for"
                    value={newKey.description}
                    onChange={(e) => setNewKey({ ...newKey, description: e.target.value })}
                  />
                </div>

                <div>
                  <Label>Permissions</Label>
                  <div className="grid grid-cols-1 gap-2 mt-2 max-h-40 overflow-y-auto">
                    {availablePermissions.map((permission) => (
                      <label key={permission.value} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={newKey.permissions.includes(permission.value)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setNewKey({
                                ...newKey,
                                permissions: [...newKey.permissions, permission.value]
                              })
                            } else {
                              setNewKey({
                                ...newKey,
                                permissions: newKey.permissions.filter(p => p !== permission.value)
                              })
                            }
                          }}
                          className="rounded"
                        />
                        <span className="text-sm">{permission.label}</span>
                      </label>
                    ))}
                  </div>
                </div>

                <div>
                  <Label htmlFor="expiresIn">Expires In</Label>
                  <Select value={newKey.expiresIn} onValueChange={(value) => setNewKey({ ...newKey, expiresIn: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="30">30 days</SelectItem>
                      <SelectItem value="90">90 days</SelectItem>
                      <SelectItem value="180">180 days</SelectItem>
                      <SelectItem value="365">1 year</SelectItem>
                      <SelectItem value="730">2 years</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex justify-end space-x-2">
                  <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreateApiKey} disabled={isLoading}>
                    {isLoading ? (
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Plus className="h-4 w-4 mr-2" />
                    )}
                    Create Key
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {!canCreateMore && (
          <Alert className="mb-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              You've reached the maximum number of API keys for your {plan} plan ({keyLimit} keys). 
              Upgrade to Enterprise for more API keys.
            </AlertDescription>
          </Alert>
        )}

        {apiKeys.length > 0 ? (
          <div className="space-y-4">
            {apiKeys.map((apiKey) => {
              const StatusIcon = getStatusIcon(apiKey.status)
              const isExpired = apiKey.expiresAt && new Date(apiKey.expiresAt) < new Date()
              const isVisible = visibleKeys.has(apiKey.id)
              
              return (
                <div key={apiKey.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div>
                        <h4 className="font-medium text-gray-900">{apiKey.name}</h4>
                        {apiKey.description && (
                          <p className="text-sm text-gray-600">{apiKey.description}</p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge className={getStatusColor(isExpired ? 'EXPIRED' : apiKey.status)} variant="outline">
                        <StatusIcon className="h-3 w-3 mr-1" />
                        {isExpired ? 'EXPIRED' : apiKey.status}
                      </Badge>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Label className="text-xs text-gray-500">API Key:</Label>
                      <div className="flex items-center space-x-2 flex-1">
                        <code className="bg-gray-100 px-2 py-1 rounded text-sm font-mono">
                          {isVisible ? `${apiKey.keyPrefix}...` : '••••••••••••••••'}
                        </code>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleToggleKeyVisibility(apiKey.id)}
                        >
                          {isVisible ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleCopyKey(apiKey.keyPrefix)}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <div className="flex items-center space-x-4">
                        <span>Created: {formatDate(apiKey.createdAt)}</span>
                        {apiKey.lastUsedAt && (
                          <span>Last used: {formatDate(apiKey.lastUsedAt)}</span>
                        )}
                        {apiKey.expiresAt && (
                          <span>Expires: {formatDate(apiKey.expiresAt)}</span>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex flex-wrap gap-1">
                        {apiKey.permissions.map((permission) => (
                          <Badge key={permission} variant="secondary" className="text-xs">
                            {permission}
                          </Badge>
                        ))}
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        {!isExpired && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleToggleKeyStatus(apiKey.id, apiKey.status)}
                          >
                            {apiKey.status === 'ACTIVE' ? 'Deactivate' : 'Activate'}
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRevokeKey(apiKey.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        ) : (
          <div className="text-center py-8">
            <Key className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="font-medium text-gray-900 mb-2">No API Keys</h3>
            <p className="text-gray-500 mb-6">
              Create your first API key to start integrating with our API.
            </p>
            {canCreateMore && (
              <Button onClick={() => setIsCreateDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create API Key
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
