import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ItemsTable } from '@/components/items/items-table'
import { ItemStats } from '@/components/items/item-stats'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Plus, Search, Filter, Package, Tags, Import, Download } from 'lucide-react'
import Link from 'next/link'

interface SearchParams {
  search?: string
  category?: string
  status?: string
  type?: string
  page?: string
}

export default async function ItemsPage({
  searchParams,
}: {
  searchParams: SearchParams
}) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  const search = searchParams.search || ''
  const category = searchParams.category || ''
  const status = searchParams.status || ''
  const type = searchParams.type || ''
  const page = parseInt(searchParams.page || '1')
  const limit = 10
  const offset = (page - 1) * limit

  // Build where clause
  const where: any = {
    companyId: session.user.companyId,
  }

  if (search) {
    where.OR = [
      { name: { contains: search } },
      { description: { contains: search } },
      { sku: { contains: search } },
      { category: { contains: search } },
    ]
  }

  if (category) {
    where.category = category
  }

  if (status) {
    where.status = status
  }

  if (type) {
    where.type = type
  }

  // Fetch items and stats
  const [items, totalCount, stats, categories] = await Promise.all([
    prisma.item.findMany({
      where,
      include: {
        _count: {
          select: {
            quotationItems: true,
            invoiceItems: true,
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      skip: offset,
      take: limit,
    }),
    prisma.item.count({ where }),
    prisma.item.groupBy({
      by: ['status', 'type'],
      where: { companyId: session.user.companyId },
      _count: true,
      _avg: { price: true },
      _sum: { stockQuantity: true },
    }),
    prisma.item.findMany({
      where: { companyId: session.user.companyId },
      select: { category: true },
      distinct: ['category'],
      orderBy: { category: 'asc' },
    }).then(items => items.map(item => item.category).filter(Boolean))
  ])

  const totalPages = Math.ceil(totalCount / limit)

  // Calculate stats
  const itemStats = {
    total: totalCount,
    active: stats.filter(s => s.status === 'ACTIVE').reduce((sum, s) => sum + s._count, 0),
    inactive: stats.filter(s => s.status === 'INACTIVE').reduce((sum, s) => sum + s._count, 0),
    products: stats.filter(s => s.type === 'PRODUCT').reduce((sum, s) => sum + s._count, 0),
    services: stats.filter(s => s.type === 'SERVICE').reduce((sum, s) => sum + s._count, 0),
    lowStock: 0, // Will be calculated from items with low stock
    totalValue: stats.reduce((sum, s) => sum + ((s._avg.price || 0) * s._count), 0),
    totalStock: stats.reduce((sum, s) => sum + (s._sum.stockQuantity || 0), 0),
    avgPrice: stats.length > 0 ? stats.reduce((sum, s) => sum + (s._avg.price || 0), 0) / stats.length : 0,
  }

  // Calculate low stock items
  const lowStockItems = await prisma.item.count({
    where: {
      companyId: session.user.companyId,
      type: 'PRODUCT',
      status: 'ACTIVE',
      stockQuantity: { lte: prisma.item.fields.lowStockThreshold }
    }
  })
  itemStats.lowStock = lowStockItems

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Items Catalog</h1>
          <p className="text-gray-600 mt-1">
            Manage your products and services
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Link href="/dashboard/items/categories">
            <Button variant="outline">
              <Tags className="h-4 w-4 mr-2" />
              Categories
            </Button>
          </Link>
          <Button variant="outline">
            <Import className="h-4 w-4 mr-2" />
            Import
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Download
          </Button>
          <Link href="/dashboard/items/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Item
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats */}
      <ItemStats stats={itemStats} />

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search items..."
              className="pl-10"
              defaultValue={search}
              name="search"
            />
          </div>
        </div>
        <div className="flex gap-2">
          <select
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            defaultValue={type}
            name="type"
          >
            <option value="">All Types</option>
            <option value="PRODUCT">Products</option>
            <option value="SERVICE">Services</option>
          </select>
          <select
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            defaultValue={status}
            name="status"
          >
            <option value="">All Status</option>
            <option value="ACTIVE">Active</option>
            <option value="INACTIVE">Inactive</option>
          </select>
          {categories.length > 0 && (
            <select
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              defaultValue={category}
              name="category"
            >
              <option value="">All Categories</option>
              {categories.map((cat) => (
                <option key={cat} value={cat}>
                  {cat}
                </option>
              ))}
            </select>
          )}
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
        </div>
      </div>

      {/* Items Table */}
      <ItemsTable 
        items={items}
        currentPage={page}
        totalPages={totalPages}
        totalCount={totalCount}
      />
    </div>
  )
}
