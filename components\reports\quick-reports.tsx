'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  Download,
  FileText,
  BarChart3,
  TrendingUp,
  Users,
  DollarSign,
  Calendar,
  Target,
  Package,
  Receipt,
  Clock,
  AlertTriangle,
  Plus
} from 'lucide-react'
import toast from 'react-hot-toast'

interface QuickReportsProps {
  companyId: string
}

export function QuickReports({ companyId }: QuickReportsProps) {
  const [generatingReport, setGeneratingReport] = useState<string | null>(null)

  const handleGenerateReport = async (reportType: string, reportName: string) => {
    setGeneratingReport(reportType)
    try {
      const response = await fetch(`/api/reports/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: reportType,
          format: 'pdf'
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to generate report')
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${reportName.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
      
      toast.success(`${reportName} generated successfully`)
    } catch (error) {
      toast.error(`Failed to generate ${reportName}`)
    } finally {
      setGeneratingReport(null)
    }
  }

  const quickReports = [
    {
      id: 'sales-summary',
      title: 'Sales Summary',
      description: 'Overview of sales performance and revenue',
      icon: BarChart3,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      category: 'Sales'
    },
    {
      id: 'customer-analysis',
      title: 'Customer Analysis',
      description: 'Customer demographics and behavior insights',
      icon: Users,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      category: 'Customer'
    },
    {
      id: 'financial-overview',
      title: 'Financial Overview',
      description: 'Revenue, expenses, and profitability analysis',
      icon: DollarSign,
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-100',
      category: 'Financial'
    },
    {
      id: 'quotation-performance',
      title: 'Quotation Performance',
      description: 'Quotation conversion rates and trends',
      icon: FileText,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      category: 'Sales'
    },
    {
      id: 'invoice-aging',
      title: 'Invoice Aging',
      description: 'Outstanding invoices and payment analysis',
      icon: Receipt,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      category: 'Financial'
    },
    {
      id: 'contract-status',
      title: 'Contract Status',
      description: 'Contract lifecycle and execution tracking',
      icon: FileText,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-100',
      category: 'Operations'
    },
    {
      id: 'monthly-trends',
      title: 'Monthly Trends',
      description: 'Month-over-month performance comparison',
      icon: TrendingUp,
      color: 'text-pink-600',
      bgColor: 'bg-pink-100',
      category: 'Analytics'
    },
    {
      id: 'item-performance',
      title: 'Item Performance',
      description: 'Product and service usage analytics',
      icon: Package,
      color: 'text-cyan-600',
      bgColor: 'bg-cyan-100',
      category: 'Operations'
    },
    {
      id: 'overdue-items',
      title: 'Overdue Items',
      description: 'Overdue invoices and pending actions',
      icon: AlertTriangle,
      color: 'text-red-600',
      bgColor: 'bg-red-100',
      category: 'Alerts'
    }
  ]

  const reportCategories = [
    { name: 'Sales', count: quickReports.filter(r => r.category === 'Sales').length },
    { name: 'Financial', count: quickReports.filter(r => r.category === 'Financial').length },
    { name: 'Customer', count: quickReports.filter(r => r.category === 'Customer').length },
    { name: 'Operations', count: quickReports.filter(r => r.category === 'Operations').length },
    { name: 'Analytics', count: quickReports.filter(r => r.category === 'Analytics').length },
    { name: 'Alerts', count: quickReports.filter(r => r.category === 'Alerts').length }
  ]

  return (
    <div className="space-y-6">
      {/* Quick Reports Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Quick Reports</h3>
          <p className="text-sm text-gray-600">Generate instant reports with one click</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" >
            <Calendar className="h-4 w-4 mr-2" />
            Schedule Reports
          </Button>
          <Button variant="outline" >
            <Target className="h-4 w-4 mr-2" />
            Custom Report
          </Button>
        </div>
      </div>

      {/* Report Categories */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        {reportCategories.map((category, index) => (
          <Card key={index} className="text-center hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="text-lg font-bold text-gray-900">{category.count}</div>
              <div className="text-sm text-gray-600">{category.name}</div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Reports Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {quickReports.map((report) => (
          <Card key={report.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${report.bgColor}`}>
                    <report.icon className={`h-5 w-5 ${report.color}`} />
                  </div>
                  <div>
                    <CardTitle className="text-base">{report.title}</CardTitle>
                    <div className="text-xs text-gray-500 mt-1">{report.category}</div>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                {report.description}
              </p>
              <div className="flex items-center space-x-2">
                <Button
                  size="sm"
                  onClick={() => handleGenerateReport(report.id, report.title)}
                  disabled={generatingReport === report.id}
                  className="flex-1"
                >
                  {generatingReport === report.id ? (
                    <>
                      <Clock className="h-4 w-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Download className="h-4 w-4 mr-2" />
                      Generate PDF
                    </>
                  )}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleGenerateReport(`${report.id}-excel`, report.title)}
                  disabled={generatingReport === `${report.id}-excel`}
                >
                  Excel
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Scheduled Reports */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Scheduled Reports</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Calendar className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Scheduled Reports</h3>
            <p className="text-gray-500 mb-4">
              Set up automatic report generation to receive regular business insights.
            </p>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Schedule Your First Report
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Report Templates */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>Report Templates</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors cursor-pointer">
              <FileText className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm font-medium text-gray-600">Executive Summary</p>
              <p className="text-xs text-gray-500 mt-1">High-level business overview</p>
            </div>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors cursor-pointer">
              <BarChart3 className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm font-medium text-gray-600">Detailed Analytics</p>
              <p className="text-xs text-gray-500 mt-1">In-depth performance analysis</p>
            </div>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors cursor-pointer">
              <TrendingUp className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm font-medium text-gray-600">Trend Report</p>
              <p className="text-xs text-gray-500 mt-1">Historical trend analysis</p>
            </div>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors cursor-pointer">
              <Target className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm font-medium text-gray-600">Custom Report</p>
              <p className="text-xs text-gray-500 mt-1">Build your own report</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
