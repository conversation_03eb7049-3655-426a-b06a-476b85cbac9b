import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { BillingOverview } from '@/components/billing/billing-overview'
import { SubscriptionDetails } from '@/components/billing/subscription-details'
import { PaymentHistory } from '@/components/billing/payment-history'
import { PlanComparison } from '@/components/billing/plan-comparison'
import { Button } from '@/components/ui/button'
import { CreditCard, Download, Settings } from 'lucide-react'
import Link from 'next/link'

export default async function BillingPage() {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  // Fetch company with subscription details
  const company = await prisma.company.findUnique({
    where: { id: session.user.companyId },
    include: {
      subscriptions: {
        include: {
          payments: {
            orderBy: { createdAt: 'desc' },
            take: 10
          }
        }
      },
      _count: {
        select: {
          users: true,
          customers: true,
          quotations: true,
          invoices: true,
          contracts: true
        }
      }
    }
  })

  if (!company) {
    return <div>Error: Company not found</div>
  }

  // Calculate usage metrics
  const currentDate = new Date()
  const currentMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)
  
  const [monthlyUsage, yearlyUsage] = await Promise.all([
    // Monthly usage
    Promise.all([
      prisma.quotation.count({
        where: { 
          companyId: company.id,
          createdAt: { gte: currentMonth }
        }
      }),
      prisma.invoice.count({
        where: { 
          companyId: company.id,
          createdAt: { gte: currentMonth }
        }
      }),
      prisma.contract.count({
        where: { 
          companyId: company.id,
          createdAt: { gte: currentMonth }
        }
      }),
      prisma.email.count({
        where: { 
          companyId: company.id,
          createdAt: { gte: currentMonth }
        }
      })
    ]),
    // Yearly usage
    Promise.all([
      prisma.quotation.count({
        where: { 
          companyId: company.id,
          createdAt: { gte: new Date(currentDate.getFullYear(), 0, 1) }
        }
      }),
      prisma.invoice.count({
        where: { 
          companyId: company.id,
          createdAt: { gte: new Date(currentDate.getFullYear(), 0, 1) }
        }
      }),
      prisma.contract.count({
        where: { 
          companyId: company.id,
          createdAt: { gte: new Date(currentDate.getFullYear(), 0, 1) }
        }
      }),
      prisma.email.count({
        where: { 
          companyId: company.id,
          createdAt: { gte: new Date(currentDate.getFullYear(), 0, 1) }
        }
      })
    ])
  ])

  const [monthlyQuotations, monthlyInvoices, monthlyContracts, monthlyEmails] = monthlyUsage
  const [yearlyQuotations, yearlyInvoices, yearlyContracts, yearlyEmails] = yearlyUsage

  const usageData = {
    monthly: {
      quotations: monthlyQuotations,
      invoices: monthlyInvoices,
      contracts: monthlyContracts,
      emails: monthlyEmails,
      users: company._count.users
    },
    yearly: {
      quotations: yearlyQuotations,
      invoices: yearlyInvoices,
      contracts: yearlyContracts,
      emails: yearlyEmails,
      users: company._count.users
    },
    total: {
      customers: company._count.customers,
      quotations: company._count.quotations,
      invoices: company._count.invoices,
      contracts: company._count.contracts,
      users: company._count.users
    }
  }

  // Define plan limits
  const planLimits = {
    BASIC: {
      users: 5,
      quotations: 50,
      invoices: 50,
      contracts: 10,
      emails: 100,
      storage: '1GB',
      support: 'Email'
    },
    PROFESSIONAL: {
      users: 25,
      quotations: 500,
      invoices: 500,
      contracts: 100,
      emails: 1000,
      storage: '10GB',
      support: 'Priority Email'
    },
    ENTERPRISE: {
      users: 100,
      quotations: -1, // Unlimited
      invoices: -1,
      contracts: -1,
      emails: -1,
      storage: '100GB',
      support: 'Phone & Email'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="p-2 bg-green-100 rounded-lg">
            <CreditCard className="h-6 w-6 text-green-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Billing & Subscription</h1>
            <p className="text-gray-600 mt-1">
              Manage your subscription, billing, and usage
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Download Invoice
          </Button>
          <Link href="/dashboard/billing/settings">
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Billing Settings
            </Button>
          </Link>
        </div>
      </div>

      {/* Billing Overview */}
      <BillingOverview 
        company={company}
        subscription={company.subscriptions?.[0]}
        usage={usageData}
        limits={planLimits[company.plan as keyof typeof planLimits]}
      />

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Subscription Details */}
        <div className="lg:col-span-2 space-y-6">
          <SubscriptionDetails 
            company={company}
            subscription={company.subscriptions?.[0]}
            usage={usageData}
            limits={planLimits[company.plan as keyof typeof planLimits]}
          />
          
          <PaymentHistory 
            payments={company.subscriptions?.[0]?.payments?.map(payment => ({
              id: payment.id,
              amount: payment.amount,
              currency: payment.currency,
              status: payment.status,
              description: payment.description,
              invoiceUrl: payment.invoiceUrl,
              receiptUrl: payment.receiptUrl,
              createdAt: payment.createdAt,
              paidAt: payment.paidAt,
              failedAt: payment.failedAt
            })) || []}
          />
        </div>

        {/* Plan Comparison */}
        <div>
          <PlanComparison 
            currentPlan={company.plan}
            planLimits={planLimits}
            usage={usageData}
          />
        </div>
      </div>
    </div>
  )
}
