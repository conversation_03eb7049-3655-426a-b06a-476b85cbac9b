import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ReceiptForm } from '@/components/receipts/receipt-form'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'

interface SearchParams {
  invoiceId?: string
}

export default async function NewReceiptPage({
  searchParams,
}: {
  searchParams: SearchParams
}) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  // Fetch available invoices that can have receipts
  const availableInvoices = await prisma.invoice.findMany({
    where: { 
      companyId: session.user.companyId,
      status: { in: ['SENT', 'VIEWED', 'OVERDUE'] } // Only invoices that can be paid
    },
    include: {
      customer: {
        select: { id: true, name: true, company: true }
      }
    },
    orderBy: { createdAt: 'desc' }
  })

  // If invoiceId is provided, get the specific invoice
  let selectedInvoice = null
  if (searchParams.invoiceId) {
    selectedInvoice = await prisma.invoice.findFirst({
      where: {
        id: searchParams.invoiceId,
        companyId: session.user.companyId
      },
      include: {
        customer: {
          select: { id: true, name: true, company: true, email: true }
        }
      }
    })
  }

  // Generate next receipt number
  const lastReceipt = await prisma.receipt.findFirst({
    where: { companyId: session.user.companyId },
    orderBy: { createdAt: 'desc' },
    select: { receiptNumber: true }
  })

  const nextReceiptNumber = generateReceiptNumber(lastReceipt?.receiptNumber)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href="/dashboard/receipts">
          <Button variant="ghost" >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Receipts
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Create Payment Receipt</h1>
          <p className="text-gray-600 mt-1">
            Record a payment received for an invoice
          </p>
        </div>
      </div>

      {/* Receipt Form */}
      <ReceiptForm 
        mode="create" 
        availableInvoices={availableInvoices}
        selectedInvoice={selectedInvoice}
        suggestedReceiptNumber={nextReceiptNumber}
      />
    </div>
  )
}

function generateReceiptNumber(lastReceiptNumber?: string | null): string {
  const currentYear = new Date().getFullYear().toString().slice(-2)
  const currentMonth = String(new Date().getMonth() + 1).padStart(2, '0')
  
  let nextNumber = 1
  
  if (lastReceiptNumber) {
    const match = lastReceiptNumber.match(/RCP-(\d{2})(\d{2})-(\d{4})/)
    if (match) {
      const [, year, month, num] = match
      if (year === currentYear && month === currentMonth) {
        nextNumber = parseInt(num) + 1
      }
    }
  }
  
  return `RCP-${currentYear}${currentMonth}-${String(nextNumber).padStart(4, '0')}`
}
