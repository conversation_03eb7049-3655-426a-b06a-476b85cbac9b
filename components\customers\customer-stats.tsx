'use client'

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Users, UserCheck, UserX, UserPlus } from 'lucide-react'

interface CustomerStatsProps {
  stats: {
    total: number
    active: number
    inactive: number
    prospects: number
  }
}

export function CustomerStats({ stats }: CustomerStatsProps) {
  const statCards = [
    {
      title: 'Total Customers',
      value: stats.total.toString(),
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      title: 'Active Customers',
      value: stats.active.toString(),
      icon: UserCheck,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      title: 'Prospects',
      value: stats.prospects.toString(),
      icon: UserPlus,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
    },
    {
      title: 'Inactive',
      value: stats.inactive.toString(),
      icon: UserX,
      color: 'text-gray-600',
      bgColor: 'bg-gray-100',
    },
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statCards.map((stat, index) => (
        <Card key={index} className="hover:shadow-lg transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              {stat.title}
            </CardTitle>
            <div className={`p-2 rounded-lg ${stat.bgColor}`}>
              <stat.icon className={`h-4 w-4 ${stat.color}`} />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">
              {stat.value}
            </div>
            <div className="flex items-center mt-2">
              <span className="text-xs text-gray-500">
                {index === 0 && 'Total registered customers'}
                {index === 1 && 'Currently active customers'}
                {index === 2 && 'Potential customers'}
                {index === 3 && 'Inactive customers'}
              </span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
