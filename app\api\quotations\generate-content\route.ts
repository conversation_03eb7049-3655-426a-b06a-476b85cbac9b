import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { customer, lead, items } = body

    // For now, we'll generate content based on templates
    // In a real implementation, you would integrate with OpenAI or another AI service
    
    const generatedContent = generateQuotationContent(customer, lead, items)

    return NextResponse.json(generatedContent)
  } catch (error) {
    console.error('Error generating AI content:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function generateQuotationContent(customer: any, lead: any, items: any[]) {
  const customerName = customer?.name || 'Valued Customer'
  const companyName = customer?.company || customerName
  const leadTitle = lead?.title || 'Business Opportunity'
  
  // Generate title
  const title = lead 
    ? `Proposal for ${leadTitle} - ${companyName}`
    : `Business Proposal for ${companyName}`

  // Generate description
  let description = `Dear ${customerName},\n\n`
  description += `Thank you for your interest in our services. We are pleased to present this comprehensive proposal for ${companyName}.\n\n`
  
  if (lead) {
    description += `This proposal addresses your requirements for "${leadTitle}" and outlines our recommended solution.\n\n`
  }
  
  if (items.length > 0) {
    description += `Our proposal includes the following services:\n`
    items.forEach((item: any, index: number) => {
      if (item.name) {
        description += `${index + 1}. ${item.name}`
        if (item.description) {
          description += ` - ${item.description}`
        }
        description += '\n'
      }
    })
    description += '\n'
  }
  
  description += `We believe this solution will provide significant value to your organization and help you achieve your business objectives.\n\n`
  description += `Please review the detailed breakdown below and feel free to contact us with any questions or clarifications.\n\n`
  description += `We look forward to the opportunity to work with you.`

  // Generate terms
  const terms = `Terms & Conditions:

1. Validity: This quotation is valid for 30 days from the date of issue.

2. Payment Terms: 
   - 50% advance payment upon acceptance of this quotation
   - 50% balance payment upon completion of work

3. Scope of Work: The services outlined in this quotation are based on the requirements discussed. Any additional work outside this scope will be quoted separately.

4. Timeline: Work will commence within 5 business days of receiving the advance payment. Estimated completion time will be communicated upon project initiation.

5. Intellectual Property: All deliverables will be the property of the client upon full payment.

6. Cancellation: Either party may cancel this agreement with 7 days written notice. Payments for completed work will be due immediately.

7. Warranty: We provide a 30-day warranty on all deliverables for any defects in workmanship.

8. Limitation of Liability: Our liability is limited to the total value of this quotation.

By accepting this quotation, you agree to these terms and conditions.`

  return {
    title,
    description,
    terms
  }
}

// Alternative implementation with OpenAI (commented out)
/*
async function generateAIContent(customer: any, lead: any, items: any[]) {
  const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  })

  const prompt = `Generate a professional quotation content for:
Customer: ${customer?.name} (${customer?.company})
Lead: ${lead?.title || 'General business proposal'}
Items: ${items.map(item => item.name).join(', ')}

Please generate:
1. A professional title
2. A detailed description (2-3 paragraphs)
3. Professional terms and conditions

Make it sound professional and tailored to the customer.`

  try {
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "You are a professional business proposal writer. Generate clear, professional, and persuasive quotation content."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      max_tokens: 1000,
      temperature: 0.7,
    })

    const content = completion.choices[0]?.message?.content || ''
    
    // Parse the AI response to extract title, description, and terms
    // This would need proper parsing logic based on the AI response format
    
    return {
      title: "AI Generated Title",
      description: content,
      terms: "AI Generated Terms"
    }
  } catch (error) {
    console.error('OpenAI API error:', error)
    throw error
  }
}
*/
