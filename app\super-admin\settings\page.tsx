import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { redirect } from 'next/navigation'
import { SettingsForm } from '@/components/super-admin/settings-form'
import { IntegrationStatus } from '@/components/super-admin/integration-status'

export default async function SuperAdminSettingsPage() {
  const session = await getServerSession(authOptions)

  // Check if user is super admin
  if (!session?.user?.isSuperAdmin && session?.user?.role !== 'SUPER_ADMIN') {
    redirect('/dashboard')
  }

  // Fetch current settings from API
  async function fetchSettings() {
    try {
      const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/super-admin/settings`, {
        headers: {
          'Cookie': session ? `next-auth.session-token=${session}` : ''
        }
      })
      if (response.ok) {
        return await response.json()
      }
    } catch (error) {
      console.error('Failed to fetch settings:', error)
    }

    // Fallback to default settings
    return {
      general: {
        platformName: 'SaaS Platform',
        platformDescription: 'Enterprise Business Management Platform',
        supportEmail: '<EMAIL>',
        companyEmail: '<EMAIL>',
        timezone: 'UTC',
        language: 'en',
        maintenanceMode: false,
        allowRegistration: true,
        defaultUserRole: 'USER'
      },
      security: {
        requireTwoFactor: false,
        passwordMinLength: 8,
        sessionTimeout: 24,
        maxLoginAttempts: 5,
        enableAuditLogs: true,
        requireEmailVerification: true,
        enableIpWhitelist: false,
        forcePasswordReset: 90,
        enableSSOOnly: false
      },
      features: {
        enableRegistration: true,
        enableInvitations: true,
        enableApiAccess: true,
        enableWebhooks: true,
        enableEmailCampaigns: true,
        enableAdvancedReporting: true,
        enableFileUploads: true,
        enableIntegrations: true,
        enableCustomBranding: false,
        enableMobileApp: false
      },
      limits: {
        maxUsersPerCompany: 100,
        maxStoragePerCompany: 10,
        maxApiCallsPerHour: 1000,
        maxEmailsPerMonth: 10000,
        maxFileUploadSize: 10,
        maxCompanies: 1000,
        maxIntegrationsPerCompany: 10
      },
      notifications: {
        enableEmailNotifications: true,
        enableSlackNotifications: false,
        enableWebhookNotifications: true,
        notifyOnNewSignup: true,
        notifyOnPaymentFailure: true,
        notifyOnSystemErrors: true,
        dailyReportEmail: '<EMAIL>',
        weeklyReportEmail: '<EMAIL>'
      }
    }
  }

  const platformSettings = await fetchSettings()

  return (
    <div className="space-y-8">
      {/* Interactive Settings Form */}
      <SettingsForm initialSettings={platformSettings} />

      {/* Dynamic Integrations Status */}
      <IntegrationStatus />


    </div>
  )
}
