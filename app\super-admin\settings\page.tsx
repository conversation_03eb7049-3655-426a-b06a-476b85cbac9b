import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { redirect } from 'next/navigation'
import { SettingsForm } from '@/components/super-admin/settings-form'
import {
  Settings,
  CreditCard,
  Mail,
  Bell,
  Zap,
  Key
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

export default async function SuperAdminSettingsPage() {
  const session = await getServerSession(authOptions)

  // Check if user is super admin
  if (!session?.user?.isSuperAdmin && session?.user?.role !== 'SUPER_ADMIN') {
    redirect('/dashboard')
  }

  // Fetch current settings (in a real app, these would come from a settings table)
  const platformSettings = {
    general: {
      platformName: 'SaaS Platform',
      platformDescription: 'Enterprise Business Management Platform',
      supportEmail: '<EMAIL>',
      timezone: 'UTC',
      language: 'en',
      maintenanceMode: false
    },
    security: {
      requireTwoFactor: false,
      passwordMinLength: 8,
      sessionTimeout: 24,
      maxLoginAttempts: 5,
      enableAuditLogs: true,
      requireEmailVerification: true
    },
    features: {
      enableRegistration: true,
      enableInvitations: true,
      enableApiAccess: true,
      enableWebhooks: true,
      enableEmailCampaigns: true,
      enableAdvancedReporting: true
    },
    limits: {
      maxUsersPerCompany: 100,
      maxStoragePerCompany: 10,
      maxApiCallsPerHour: 1000,
      maxEmailsPerMonth: 10000
    }
  }

  return (
    <div className="space-y-8">
      {/* Interactive Settings Form */}
      <SettingsForm initialSettings={platformSettings} />



      {/* Integrations Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Key className="h-5 w-5 text-orange-600" />
            <span>Third-Party Integrations</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <CreditCard className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="font-medium">Stripe</p>
                  <p className="text-sm text-gray-500">Payment processing</p>
                </div>
              </div>
              <Badge variant="outline" className="text-green-600 border-green-200">
                Connected
              </Badge>
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-orange-600" />
                <div>
                  <p className="font-medium">Mailgun</p>
                  <p className="text-sm text-gray-500">Email delivery</p>
                </div>
              </div>
              <Badge variant="outline" className="text-green-600 border-green-200">
                Connected
              </Badge>
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <Bell className="h-5 w-5 text-purple-600" />
                <div>
                  <p className="font-medium">Slack</p>
                  <p className="text-sm text-gray-500">Notifications</p>
                </div>
              </div>
              <Badge variant="outline" className="text-gray-600 border-gray-200">
                Disconnected
              </Badge>
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <Zap className="h-5 w-5 text-yellow-600" />
                <div>
                  <p className="font-medium">Webhooks</p>
                  <p className="text-sm text-gray-500">Event notifications</p>
                </div>
              </div>
              <Badge variant="outline" className="text-green-600 border-green-200">
                Active
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>


    </div>
  )
}
