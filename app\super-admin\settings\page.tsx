import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { redirect } from 'next/navigation'
import {
  Settings,
  Cog,
  Shield,
  Globe,
  Mail,
  Database,
  Key,
  Bell,
  Users,
  Building2,
  CreditCard,
  Zap,
  Lock,
  Eye,
  Save,
  RefreshCw
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

export default async function SuperAdminSettingsPage() {
  const session = await getServerSession(authOptions)

  // Check if user is super admin
  if (!session?.user?.isSuperAdmin && session?.user?.role !== 'SUPER_ADMIN') {
    redirect('/dashboard')
  }

  // Platform settings (in a real app, these would come from a settings table)
  const platformSettings = {
    general: {
      platformName: 'SaaS Platform',
      platformDescription: 'Enterprise Business Management Platform',
      supportEmail: '<EMAIL>',
      timezone: 'UTC',
      language: 'en',
      maintenanceMode: false
    },
    security: {
      requireTwoFactor: false,
      passwordMinLength: 8,
      sessionTimeout: 24, // hours
      maxLoginAttempts: 5,
      enableAuditLogs: true,
      requireEmailVerification: true
    },
    features: {
      enableRegistration: true,
      enableInvitations: true,
      enableApiAccess: true,
      enableWebhooks: true,
      enableEmailCampaigns: true,
      enableAdvancedReporting: true
    },
    limits: {
      maxUsersPerCompany: 100,
      maxStoragePerCompany: 10, // GB
      maxApiCallsPerHour: 1000,
      maxEmailsPerMonth: 10000
    },
    integrations: {
      stripeEnabled: true,
      mailgunEnabled: true,
      slackEnabled: false,
      webhooksEnabled: true
    }
  }

  const settingSections = [
    {
      id: 'general',
      title: 'General Settings',
      description: 'Basic platform configuration',
      icon: Globe,
      color: 'blue'
    },
    {
      id: 'security',
      title: 'Security & Authentication',
      description: 'Security policies and authentication settings',
      icon: Shield,
      color: 'red'
    },
    {
      id: 'features',
      title: 'Feature Flags',
      description: 'Enable or disable platform features',
      icon: Zap,
      color: 'purple'
    },
    {
      id: 'limits',
      title: 'Platform Limits',
      description: 'Resource limits and quotas',
      icon: Database,
      color: 'green'
    },
    {
      id: 'integrations',
      title: 'Integrations',
      description: 'Third-party service integrations',
      icon: Key,
      color: 'orange'
    }
  ]

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="p-3 bg-gray-100 rounded-lg">
            <Settings className="h-8 w-8 text-gray-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Platform Settings</h1>
            <p className="text-gray-600">Configure platform-wide settings and preferences</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Reset to Defaults
          </Button>
          <Button>
            <Save className="h-4 w-4 mr-2" />
            Save Changes
          </Button>
        </div>
      </div>

      {/* Settings Navigation */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        {settingSections.map((section) => {
          const Icon = section.icon
          return (
            <Card key={section.id} className="cursor-pointer hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg bg-${section.color}-100`}>
                    <Icon className={`h-5 w-5 text-${section.color}-600`} />
                  </div>
                  <div>
                    <h3 className="font-medium text-sm">{section.title}</h3>
                    <p className="text-xs text-gray-500">{section.description}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Settings Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* General Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Globe className="h-5 w-5 text-blue-600" />
              <span>General Settings</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="platformName">Platform Name</Label>
              <Input
                id="platformName"
                defaultValue={platformSettings.general.platformName}
                placeholder="Enter platform name"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="platformDescription">Platform Description</Label>
              <Textarea
                id="platformDescription"
                defaultValue={platformSettings.general.platformDescription}
                placeholder="Enter platform description"
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="supportEmail">Support Email</Label>
              <Input
                id="supportEmail"
                type="email"
                defaultValue={platformSettings.general.supportEmail}
                placeholder="<EMAIL>"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="timezone">Default Timezone</Label>
              <Select defaultValue={platformSettings.general.timezone}>
                <SelectTrigger>
                  <SelectValue placeholder="Select timezone" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="UTC">UTC</SelectItem>
                  <SelectItem value="America/New_York">Eastern Time</SelectItem>
                  <SelectItem value="America/Los_Angeles">Pacific Time</SelectItem>
                  <SelectItem value="Europe/London">London</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Maintenance Mode</Label>
                <p className="text-sm text-gray-500">Temporarily disable platform access</p>
              </div>
              <Switch defaultChecked={platformSettings.general.maintenanceMode} />
            </div>
          </CardContent>
        </Card>

        {/* Security Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-red-600" />
              <span>Security & Authentication</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Require Two-Factor Authentication</Label>
                <p className="text-sm text-gray-500">Force 2FA for all users</p>
              </div>
              <Switch defaultChecked={platformSettings.security.requireTwoFactor} />
            </div>

            <div className="space-y-2">
              <Label htmlFor="passwordMinLength">Minimum Password Length</Label>
              <Input
                id="passwordMinLength"
                type="number"
                defaultValue={platformSettings.security.passwordMinLength}
                min="6"
                max="32"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="sessionTimeout">Session Timeout (hours)</Label>
              <Input
                id="sessionTimeout"
                type="number"
                defaultValue={platformSettings.security.sessionTimeout}
                min="1"
                max="168"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="maxLoginAttempts">Max Login Attempts</Label>
              <Input
                id="maxLoginAttempts"
                type="number"
                defaultValue={platformSettings.security.maxLoginAttempts}
                min="3"
                max="10"
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Enable Audit Logs</Label>
                <p className="text-sm text-gray-500">Track all user activities</p>
              </div>
              <Switch defaultChecked={platformSettings.security.enableAuditLogs} />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Require Email Verification</Label>
                <p className="text-sm text-gray-500">Verify email addresses on signup</p>
              </div>
              <Switch defaultChecked={platformSettings.security.requireEmailVerification} />
            </div>
          </CardContent>
        </Card>

        {/* Feature Flags */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Zap className="h-5 w-5 text-purple-600" />
              <span>Feature Flags</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Enable User Registration</Label>
                <p className="text-sm text-gray-500">Allow new user signups</p>
              </div>
              <Switch defaultChecked={platformSettings.features.enableRegistration} />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Enable Invitations</Label>
                <p className="text-sm text-gray-500">Allow users to invite team members</p>
              </div>
              <Switch defaultChecked={platformSettings.features.enableInvitations} />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Enable API Access</Label>
                <p className="text-sm text-gray-500">Allow API key generation</p>
              </div>
              <Switch defaultChecked={platformSettings.features.enableApiAccess} />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Enable Webhooks</Label>
                <p className="text-sm text-gray-500">Allow webhook configurations</p>
              </div>
              <Switch defaultChecked={platformSettings.features.enableWebhooks} />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Enable Email Campaigns</Label>
                <p className="text-sm text-gray-500">Allow email marketing features</p>
              </div>
              <Switch defaultChecked={platformSettings.features.enableEmailCampaigns} />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Enable Advanced Reporting</Label>
                <p className="text-sm text-gray-500">Advanced analytics and reports</p>
              </div>
              <Switch defaultChecked={platformSettings.features.enableAdvancedReporting} />
            </div>
          </CardContent>
        </Card>

        {/* Platform Limits */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Database className="h-5 w-5 text-green-600" />
              <span>Platform Limits</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="maxUsers">Max Users per Company</Label>
              <Input
                id="maxUsers"
                type="number"
                defaultValue={platformSettings.limits.maxUsersPerCompany}
                min="1"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="maxStorage">Max Storage per Company (GB)</Label>
              <Input
                id="maxStorage"
                type="number"
                defaultValue={platformSettings.limits.maxStoragePerCompany}
                min="1"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="maxApiCalls">Max API Calls per Hour</Label>
              <Input
                id="maxApiCalls"
                type="number"
                defaultValue={platformSettings.limits.maxApiCallsPerHour}
                min="100"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="maxEmails">Max Emails per Month</Label>
              <Input
                id="maxEmails"
                type="number"
                defaultValue={platformSettings.limits.maxEmailsPerMonth}
                min="1000"
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Integrations Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Key className="h-5 w-5 text-orange-600" />
            <span>Third-Party Integrations</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <CreditCard className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="font-medium">Stripe</p>
                  <p className="text-sm text-gray-500">Payment processing</p>
                </div>
              </div>
              <Badge variant="outline" className="text-green-600 border-green-200">
                Connected
              </Badge>
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-orange-600" />
                <div>
                  <p className="font-medium">Mailgun</p>
                  <p className="text-sm text-gray-500">Email delivery</p>
                </div>
              </div>
              <Badge variant="outline" className="text-green-600 border-green-200">
                Connected
              </Badge>
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <Bell className="h-5 w-5 text-purple-600" />
                <div>
                  <p className="font-medium">Slack</p>
                  <p className="text-sm text-gray-500">Notifications</p>
                </div>
              </div>
              <Badge variant="outline" className="text-gray-600 border-gray-200">
                Disconnected
              </Badge>
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <Zap className="h-5 w-5 text-yellow-600" />
                <div>
                  <p className="font-medium">Webhooks</p>
                  <p className="text-sm text-gray-500">Event notifications</p>
                </div>
              </div>
              <Badge variant="outline" className="text-green-600 border-green-200">
                Active
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Save Actions */}
      <div className="flex items-center justify-end space-x-4 p-6 bg-gray-50 rounded-lg">
        <p className="text-sm text-gray-600">Changes will be applied immediately to all users</p>
        <Button variant="outline">Cancel</Button>
        <Button>
          <Save className="h-4 w-4 mr-2" />
          Save All Settings
        </Button>
      </div>
    </div>
  )
}
