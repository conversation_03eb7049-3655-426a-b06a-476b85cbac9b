import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { redirect } from 'next/navigation'
import { Settings, Cog, Shield } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default async function SuperAdminSettingsPage() {
  const session = await getServerSession(authOptions)
  
  // Check if user is super admin
  if (!session?.user?.isSuperAdmin && session?.user?.role !== 'SUPER_ADMIN') {
    redirect('/dashboard')
  }

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="flex items-center space-x-4">
        <div className="p-3 bg-gray-100 rounded-lg">
          <Settings className="h-8 w-8 text-gray-600" />
        </div>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Platform Settings</h1>
          <p className="text-gray-600">Configure platform-wide settings and preferences</p>
        </div>
      </div>

      {/* Coming Soon */}
      <Card>
        <CardHeader>
          <CardTitle>Platform Configuration</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <Cog className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Platform Settings Coming Soon</h3>
            <p className="text-gray-600 mb-6">
              Comprehensive platform configuration and administrative settings.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-500">
              <div>• Global platform settings</div>
              <div>• Security and authentication</div>
              <div>• Feature flags and configurations</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
