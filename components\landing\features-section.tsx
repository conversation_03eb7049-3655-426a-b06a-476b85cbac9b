'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  FileText,
  Receipt,

  Users, 
  BarChart3, 
  Shield,
  Mail,
  Bell,
  Code,
  CreditCard,
  HelpCircle,
  Rocket,
  ArrowRight,
  Sparkles,
  Zap,
  Globe
} from 'lucide-react'
import Link from 'next/link'

export function FeaturesSection() {
  const coreFeatures = [
    {
      icon: FileText,
 Receipt,
      title: 'Smart Quotations',
      description: 'Create professional quotations with AI-powered content generation, automated calculations, and customizable templates.',
      features: ['AI content generation', 'Automated calculations', 'Custom templates', 'Digital signatures'],
      color: 'text-blue-600 bg-blue-100'
    },
    {
      icon: Receipt,
      title: 'Invoice Management',
      description: 'Generate, send, and track invoices with automated payment reminders, status updates, and payment processing.',
      features: ['Automated reminders', 'Payment tracking', 'Multi-currency support', 'Tax calculations'],
      color: 'text-green-600 bg-green-100'
    },
    {
      icon: FileText,
 Receipt,
      title: 'Contract Handling',
      description: 'Manage contracts from creation to completion with digital signatures, milestone tracking, and compliance monitoring.',
      features: ['Digital signatures', 'Milestone tracking', 'Compliance monitoring', 'Template library'],
      color: 'text-purple-600 bg-purple-100'
    },
    {
      icon: Users,
      title: 'Customer CRM',
      description: 'Centralized customer database with interaction history, communication tracking, and relationship management.',
      features: ['Contact management', 'Interaction history', 'Communication tracking', 'Segmentation'],
      color: 'text-orange-600 bg-orange-100'
    },
    {
      icon: BarChart3,
      title: 'Analytics Dashboard',
      description: 'Real-time insights into your business performance with detailed reports, metrics, and predictive analytics.',
      features: ['Real-time insights', 'Custom reports', 'Predictive analytics', 'KPI tracking'],
      color: 'text-indigo-600 bg-indigo-100'
    },
    {
      icon: Shield,
      title: 'Security & Compliance',
      description: 'Enterprise-grade security with data encryption, compliance monitoring, and audit trails.',
      features: ['Data encryption', 'Compliance monitoring', 'Audit trails', 'Access controls'],
      color: 'text-red-600 bg-red-100'
    }
  ]

  const advancedFeatures = [
    {
      icon: Mail,
      title: 'Email Integration',
      description: 'Automated email campaigns and notifications',
      badge: 'Professional'
    },
    {
      icon: Code,
      title: 'API & Webhooks',
      description: 'Powerful API for custom integrations',
      badge: 'Professional'
    },
    {
      icon: Bell,
      title: 'Smart Notifications',
      description: 'Real-time alerts and reminders',
      badge: 'All Plans'
    },
    {
      icon: CreditCard,
      title: 'Payment Processing',
      description: 'Integrated payment gateway',
      badge: 'Professional'
    },
    {
      icon: HelpCircle,
      title: 'Priority Support',
      description: '24/7 dedicated support team',
      badge: 'Enterprise'
    },
    {
      icon: Rocket,
      title: 'Custom Branding',
      description: 'White-label solutions',
      badge: 'Enterprise'
    }
  ]

  return (
    <section id="features" className="py-20 px-4 bg-white">
      <div className="container mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <Badge variant="outline" className="mb-4">
            <Sparkles className="h-4 w-4 mr-2" />
            Powerful Features
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Everything You Need to
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent block">
              Manage Your Business
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            From lead generation to contract completion, our platform handles every aspect of your business workflow 
            with intelligent automation and powerful insights.
          </p>
        </div>
        
        {/* Core Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {coreFeatures.map((feature, index) => (
            <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
              <CardHeader className="pb-4">
                <div className={`w-14 h-14 rounded-xl flex items-center justify-center mb-4 ${feature.color} group-hover:scale-110 transition-transform`}>
                  <feature.icon className="h-7 w-7" />
                </div>
                <CardTitle className="text-xl mb-2">{feature.title}</CardTitle>
                <CardDescription className="text-gray-600 leading-relaxed">
                  {feature.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {feature.features.map((item, idx) => (
                    <li key={idx} className="flex items-center text-sm text-gray-600">
                      <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-3 flex-shrink-0" />
                      {item}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Advanced Features */}
        <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-8 md:p-12">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              Advanced Features for Growing Businesses
            </h3>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Unlock powerful capabilities with our Professional and Enterprise plans
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {advancedFeatures.map((feature, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
                <div className="flex items-start justify-between mb-4">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <feature.icon className="h-5 w-5 text-blue-600" />
                  </div>
                  <Badge variant="secondary" className="text-xs">
                    {feature.badge}
                  </Badge>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">{feature.title}</h4>
                <p className="text-sm text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>

          <div className="text-center">
            <Link href="#pricing">
              <Button size="lg" className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                View All Features
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </div>
        </div>

        {/* Integration Showcase */}
        <div className="mt-20 text-center">
          <h3 className="text-3xl font-bold text-gray-900 mb-6">
            Integrates with Your Favorite Tools
          </h3>
          <p className="text-lg text-gray-600 mb-12 max-w-2xl mx-auto">
            Connect BusinessSaaS with the tools you already use to create a seamless workflow
          </p>
          
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center opacity-60">
            {/* Integration logos would go here */}
            <div className="bg-gray-100 rounded-lg p-4 h-16 flex items-center justify-center">
              <Globe className="h-8 w-8 text-gray-400" />
            </div>
            <div className="bg-gray-100 rounded-lg p-4 h-16 flex items-center justify-center">
              <Mail className="h-8 w-8 text-gray-400" />
            </div>
            <div className="bg-gray-100 rounded-lg p-4 h-16 flex items-center justify-center">
              <Code className="h-8 w-8 text-gray-400" />
            </div>
            <div className="bg-gray-100 rounded-lg p-4 h-16 flex items-center justify-center">
              <CreditCard className="h-8 w-8 text-gray-400" />
            </div>
            <div className="bg-gray-100 rounded-lg p-4 h-16 flex items-center justify-center">
              <BarChart3 className="h-8 w-8 text-gray-400" />
            </div>
            <div className="bg-gray-100 rounded-lg p-4 h-16 flex items-center justify-center">
              <Zap className="h-8 w-8 text-gray-400" />
            </div>
          </div>
          
          <div className="mt-8">
            <Link href="#integrations">
              <Button variant="outline" size="lg">
                View All Integrations
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  )
}
