import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if item exists and belongs to the company
    const originalItem = await prisma.item.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      }
    })

    if (!originalItem) {
      return NextResponse.json({ error: 'Item not found' }, { status: 404 })
    }

    // Generate new SKU if original has one
    let newSku = null
    if (originalItem.sku) {
      const lastItem = await prisma.item.findFirst({
        where: { 
          companyId: session.user.companyId,
          sku: { not: null }
        },
        orderBy: { createdAt: 'desc' },
        select: { sku: true }
      })
      newSku = generateSku(lastItem?.sku)
    }

    // Create duplicate item
    const duplicatedItem = await prisma.item.create({
      data: {
        name: `${originalItem.name} (Copy)`,
        description: originalItem.description,
        sku: newSku,
        type: originalItem.type,
        category: originalItem.category,
        price: originalItem.price,
        costPrice: originalItem.costPrice,
        unit: originalItem.unit,
        stockQuantity: originalItem.type === 'PRODUCT' ? 0 : null, // Reset stock for products
        lowStockThreshold: originalItem.lowStockThreshold,
        status: 'INACTIVE', // Start as inactive
        taxRate: originalItem.taxRate,
        notes: originalItem.notes,
        companyId: session.user.companyId,
        createdById: session.user.id,
      },
      include: {
        _count: {
          select: {
            quotationItems: true,
            invoiceItems: true,
          }
        }
      }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'ITEM_CREATED',
        title: 'Item duplicated',
        description: `Item "${duplicatedItem.name}" was created as a copy of "${originalItem.name}"`,
        itemId: duplicatedItem.id,
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    return NextResponse.json(duplicatedItem, { status: 201 })
  } catch (error) {
    console.error('Error duplicating item:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function generateSku(lastSku?: string | null): string {
  const currentYear = new Date().getFullYear().toString().slice(-2)
  const currentMonth = String(new Date().getMonth() + 1).padStart(2, '0')
  
  let nextNumber = 1
  
  if (lastSku) {
    const match = lastSku.match(/SKU-(\d{2})(\d{2})-(\d{4})/)
    if (match) {
      const [, year, month, num] = match
      if (year === currentYear && month === currentMonth) {
        nextNumber = parseInt(num) + 1
      }
    }
  }
  
  return `SKU-${currentYear}${currentMonth}-${String(nextNumber).padStart(4, '0')}`
}
