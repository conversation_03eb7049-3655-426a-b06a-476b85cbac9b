import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const leadSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  status: z.enum(['NEW', 'CONTACTED', 'QUALIFIED', 'PROPOSAL', 'NEGOTIATION', 'CLOSED_WON', 'CLOSED_LOST']),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']),
  value: z.number().min(0).optional(),
  source: z.string().optional(),
  customerId: z.string().min(1, 'Customer is required'),
})

const statusUpdateSchema = z.object({
  status: z.enum(['NEW', 'CONTACTED', 'QUALIFIED', 'PROPOSAL', 'NEGOTIATION', 'CLOSED_WON', 'CLOSED_LOST']),
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const lead = await prisma.lead.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      },
      include: {
        customer: {
          select: { id: true, name: true, email: true, company: true, phone: true }
        },
        quotations: {
          orderBy: { createdAt: 'desc' },
          take: 5,
        },
        activities: {
          include: {
            createdBy: {
              select: { name: true, firstName: true, lastName: true }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
        _count: {
          select: {
            quotations: true,
            activities: true,
          }
        }
      }
    })

    if (!lead) {
      return NextResponse.json({ error: 'Lead not found' }, { status: 404 })
    }

    return NextResponse.json(lead)
  } catch (error) {
    console.error('Error fetching lead:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    
    // Check if this is just a status update (for pipeline drag & drop)
    const isStatusUpdate = Object.keys(body).length === 1 && 'status' in body
    
    let validatedData
    if (isStatusUpdate) {
      validatedData = statusUpdateSchema.parse(body)
    } else {
      validatedData = leadSchema.parse(body)
    }

    // Check if lead exists and belongs to the company
    const existingLead = await prisma.lead.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      }
    })

    if (!existingLead) {
      return NextResponse.json({ error: 'Lead not found' }, { status: 404 })
    }

    // If updating customer, verify it belongs to the company
    if ('customerId' in validatedData) {
      const customer = await prisma.customer.findFirst({
        where: {
          id: validatedData.customerId as string,
          companyId: session.user.companyId,
        }
      })

      if (!customer) {
        return NextResponse.json(
          { error: 'Customer not found' },
          { status: 400 }
        )
      }
    }

    const lead = await prisma.lead.update({
      where: { id: params.id },
      data: validatedData,
      include: {
        customer: {
          select: { id: true, name: true, email: true, company: true }
        },
        _count: {
          select: {
            quotations: true,
            activities: true,
          }
        }
      }
    })

    // Create activity log for status changes
    if ('status' in validatedData && validatedData.status !== existingLead.status) {
      const statusMessages: Record<string, string> = {
        'NEW': 'Lead status changed to New',
        'CONTACTED': 'Lead status changed to Contacted',
        'QUALIFIED': 'Lead status changed to Qualified',
        'PROPOSAL': 'Lead status changed to Proposal',
        'NEGOTIATION': 'Lead status changed to Negotiation',
        'CLOSED_WON': 'Lead converted to customer',
        'CLOSED_LOST': 'Lead marked as lost',
      }

      await prisma.activity.create({
        data: {
          type: validatedData.status === 'CLOSED_WON' ? 'LEAD_CONVERTED' : 'NOTE',
          title: statusMessages[validatedData.status] || 'Lead updated',
          description: `Lead "${lead.title}" status changed from ${existingLead.status} to ${validatedData.status}`,
          leadId: lead.id,
          customerId: lead.customerId,
          companyId: session.user.companyId,
          createdById: session.user.id,
        }
      })
    } else if (!isStatusUpdate) {
      // Create general update activity
      await prisma.activity.create({
        data: {
          type: 'NOTE',
          title: 'Lead updated',
          description: `Lead "${lead.title}" was updated`,
          leadId: lead.id,
          customerId: lead.customerId,
          companyId: session.user.companyId,
          createdById: session.user.id,
        }
      })
    }

    return NextResponse.json(lead)
  } catch (error) {
    console.error('Error updating lead:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if lead exists and belongs to the company
    const lead = await prisma.lead.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      },
      include: {
        _count: {
          select: {
            quotations: true,
          }
        }
      }
    })

    if (!lead) {
      return NextResponse.json({ error: 'Lead not found' }, { status: 404 })
    }

    // Check if lead has related quotations
    if (lead._count.quotations > 0) {
      return NextResponse.json(
        { error: 'Cannot delete lead with existing quotations' },
        { status: 400 }
      )
    }

    await prisma.lead.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: 'Lead deleted successfully' })
  } catch (error) {
    console.error('Error deleting lead:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
