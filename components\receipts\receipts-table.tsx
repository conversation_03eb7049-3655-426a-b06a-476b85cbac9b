'use client'

import Link from 'next/link'
import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { formatCurrency, formatDate, getStatusColor } from '@/lib/utils'
import { 
  Eye, 
  Edit, 
  Trash2, 
  Download,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  Receipt,
  DollarSign,
  Calendar,
  User,
  Building,
  CreditCard,
  Banknote,
  Smartphone,
  FileText,
  CheckCircle,
  Clock,
  XCircle
} from 'lucide-react'
import toast from 'react-hot-toast'

interface ReceiptData {
  id: string
  receiptNumber: string
  amount: number
  paymentMethod: string
  status: string
  paidAt: Date
  notes: string | null
  createdAt: Date
  invoice: {
    id: string
    invoiceNumber: string
    total: number
    customer: {
      id: string
      name: string
      email: string | null
      company: string | null
    } | null
  }
  createdBy: {
    name: string | null
    firstName: string | null
    lastName: string | null
  } | null
}

interface ReceiptsTableProps {
  receipts: ReceiptData[]
  currentPage: number
  totalPages: number
  totalCount: number
}

export function ReceiptsTable({ 
  receipts, 
  currentPage, 
  totalPages, 
  totalCount 
}: ReceiptsTableProps) {
  const [deletingId, setDeletingId] = useState<string | null>(null)

  const handleDelete = async (receiptId: string) => {
    if (!confirm('Are you sure you want to delete this receipt?')) {
      return
    }

    setDeletingId(receiptId)
    try {
      const response = await fetch(`/api/receipts/${receiptId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to delete receipt')
      }

      toast.success('Receipt deleted successfully')
      window.location.reload()
    } catch (error) {
      toast.error('Failed to delete receipt')
    } finally {
      setDeletingId(null)
    }
  }

  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case 'CASH':
        return Banknote
      case 'CREDIT_CARD':
      case 'DEBIT_CARD':
        return CreditCard
      case 'UPI':
        return Smartphone
      case 'BANK_TRANSFER':
        return Building
      default:
        return DollarSign
    }
  }

  const getPaymentMethodColor = (method: string) => {
    switch (method) {
      case 'CASH':
        return 'text-green-600'
      case 'CREDIT_CARD':
      case 'DEBIT_CARD':
        return 'text-blue-600'
      case 'UPI':
        return 'text-purple-600'
      case 'BANK_TRANSFER':
        return 'text-indigo-600'
      case 'CHEQUE':
        return 'text-orange-600'
      default:
        return 'text-gray-600'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'CONFIRMED':
        return CheckCircle
      case 'PENDING':
        return Clock
      case 'CANCELLED':
        return XCircle
      default:
        return Receipt
    }
  }

  const getUserName = (user: any) => {
    if (!user) return 'System'
    if (user.name) return user.name
    if (user.firstName && user.lastName) return `${user.firstName} ${user.lastName}`
    if (user.firstName) return user.firstName
    return 'Unknown User'
  }

  if (receipts.length === 0) {
    return (
      <Card>
        <CardContent className="py-12">
          <div className="text-center">
            <Receipt className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No receipts found</h3>
            <p className="text-gray-500 mb-6">
              Get started by creating your first payment receipt.
            </p>
            <Link href="/dashboard/receipts/new">
              <Button>
                Create Receipt
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Receipts ({totalCount})</span>
            <span className="text-sm font-normal text-gray-500">
              Page {currentPage} of {totalPages}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Desktop Table */}
          <div className="hidden md:block overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Receipt</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Invoice</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Customer</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Amount</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Payment Method</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Date</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">Actions</th>
                </tr>
              </thead>
              <tbody>
                {receipts.map((receipt) => {
                  const PaymentIcon = getPaymentMethodIcon(receipt.paymentMethod)
                  const StatusIcon = getStatusIcon(receipt.status)
                  
                  return (
                    <tr key={receipt.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-4 px-4">
                        <div>
                          <div className="font-medium text-gray-900">{receipt.receiptNumber}</div>
                          <div className="text-sm text-gray-600">
                            by {getUserName(receipt.createdBy)}
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <Link 
                          href={`/dashboard/invoices/${receipt.invoice.id}`}
                          className="text-blue-600 hover:text-blue-800"
                        >
                          {receipt.invoice.invoiceNumber}
                        </Link>
                        <div className="text-sm text-gray-600">
                          {formatCurrency(receipt.invoice.total)}
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        {receipt.invoice.customer ? (
                          <div>
                            <div className="font-medium text-gray-900">
                              {receipt.invoice.customer.name}
                            </div>
                            {receipt.invoice.customer.company && (
                              <div className="text-sm text-gray-600">
                                {receipt.invoice.customer.company}
                              </div>
                            )}
                          </div>
                        ) : (
                          <span className="text-gray-400">No customer</span>
                        )}
                      </td>
                      <td className="py-4 px-4">
                        <div className="font-medium text-gray-900">
                          {formatCurrency(receipt.amount)}
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center space-x-2">
                          <PaymentIcon className={`h-4 w-4 ${getPaymentMethodColor(receipt.paymentMethod)}`} />
                          <span className="text-sm text-gray-900">
                            {receipt.paymentMethod.replace('_', ' ')}
                          </span>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center space-x-2">
                          <StatusIcon className={`h-4 w-4 ${getStatusColor(receipt.status).replace('border-', 'text-').replace('text-', 'text-')}`} />
                          <Badge className={getStatusColor(receipt.status)} variant="outline">
                            {receipt.status}
                          </Badge>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div>
                          <div className="text-sm text-gray-900">
                            {formatDate(receipt.paidAt)}
                          </div>
                          <div className="text-xs text-gray-500">
                            Created {formatDate(receipt.createdAt)}
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center justify-end space-x-2">
                          <Link href={`/dashboard/receipts/${receipt.id}`}>
                            <Button variant="ghost" >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </Link>
                          <Link href={`/dashboard/receipts/${receipt.id}/edit`}>
                            <Button variant="ghost" >
                              <Edit className="h-4 w-4" />
                            </Button>
                          </Link>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-blue-600 hover:text-blue-700"
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(receipt.id)}
                            disabled={deletingId === receipt.id}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>

          {/* Mobile Cards */}
          <div className="md:hidden space-y-4">
            {receipts.map((receipt) => {
              const PaymentIcon = getPaymentMethodIcon(receipt.paymentMethod)
              const StatusIcon = getStatusIcon(receipt.status)
              
              return (
                <Card key={receipt.id} className="p-4">
                  <div className="space-y-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">{receipt.receiptNumber}</div>
                        <div className="text-sm text-gray-600 mt-1">
                          Invoice: {receipt.invoice.invoiceNumber}
                        </div>
                      </div>
                      <Button variant="ghost" >
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <StatusIcon className={`h-4 w-4 ${getStatusColor(receipt.status).replace('border-', 'text-').replace('text-', 'text-')}`} />
                        <Badge className={getStatusColor(receipt.status)} variant="outline">
                          {receipt.status}
                        </Badge>
                      </div>
                      <div className="text-right">
                        <div className="font-medium text-gray-900">
                          {formatCurrency(receipt.amount)}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <PaymentIcon className={`h-4 w-4 ${getPaymentMethodColor(receipt.paymentMethod)}`} />
                        <span className="text-sm text-gray-600">
                          {receipt.paymentMethod.replace('_', ' ')}
                        </span>
                      </div>
                      <div className="text-sm text-gray-500">
                        {formatDate(receipt.paidAt)}
                      </div>
                    </div>

                    {receipt.invoice.customer && (
                      <div className="flex items-center space-x-2">
                        <User className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-600">
                          {receipt.invoice.customer.name}
                          {receipt.invoice.customer.company && ` (${receipt.invoice.customer.company})`}
                        </span>
                      </div>
                    )}

                    <div className="flex items-center justify-end space-x-2 pt-2 border-t">
                      <Link href={`/dashboard/receipts/${receipt.id}`}>
                        <Button variant="ghost" >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </Link>
                      <Link href={`/dashboard/receipts/${receipt.id}/edit`}>
                        <Button variant="ghost" >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </Link>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-blue-600"
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </Card>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            Showing {((currentPage - 1) * 10) + 1} to {Math.min(currentPage * 10, totalCount)} of {totalCount} receipts
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage <= 1}
              asChild
            >
              <Link href={`?page=${currentPage - 1}`}>
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Link>
            </Button>
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage >= totalPages}
              asChild
            >
              <Link href={`?page=${currentPage + 1}`}>
                Next
                <ChevronRight className="h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
