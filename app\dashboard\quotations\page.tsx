import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { QuotationsTable } from '@/components/quotations/quotations-table'
import { QuotationStats } from '@/components/quotations/quotation-stats'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Plus, Search, Filter, FileText } from 'lucide-react'
import Link from 'next/link'

interface SearchParams {
  search?: string
  status?: string
  customerId?: string
  leadId?: string
  page?: string
}

export default async function QuotationsPage({
  searchParams,
}: {
  searchParams: SearchParams
}) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  const search = searchParams.search || ''
  const status = searchParams.status || ''
  const customerId = searchParams.customerId || ''
  const leadId = searchParams.leadId || ''
  const page = parseInt(searchParams.page || '1')
  const limit = 10
  const offset = (page - 1) * limit

  // Build where clause
  const where: any = {
    companyId: session.user.companyId,
  }

  if (search) {
    where.OR = [
      { quotationNumber: { contains: search } },
      { title: { contains: search } },
      { description: { contains: search } },
      { customer: { name: { contains: search } } },
    ]
  }

  if (status) {
    where.status = status
  }

  if (customerId) {
    where.customerId = customerId
  }

  if (leadId) {
    where.leadId = leadId
  }

  // Fetch quotations and stats
  const [quotations, totalCount, stats, customers, leads] = await Promise.all([
    prisma.quotation.findMany({
      where,
      include: {
        customer: {
          select: { id: true, name: true, email: true, company: true }
        },
        lead: {
          select: { id: true, title: true }
        },
        items: {
          include: {
            item: {
              select: { name: true, category: true }
            }
          }
        },
        _count: {
          select: {
            items: true,
            activities: true,
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      skip: offset,
      take: limit,
    }),
    prisma.quotation.count({ where }),
    prisma.quotation.groupBy({
      by: ['status'],
      where: { companyId: session.user.companyId },
      _count: true,
      _sum: { total: true },
    }),
    prisma.customer.findMany({
      where: { companyId: session.user.companyId },
      select: { id: true, name: true },
      orderBy: { name: 'asc' },
    }),
    prisma.lead.findMany({
      where: { companyId: session.user.companyId },
      select: { id: true, title: true },
      orderBy: { createdAt: 'desc' },
      take: 50,
    })
  ])

  const totalPages = Math.ceil(totalCount / limit)

  // Calculate stats
  const quotationStats = {
    total: totalCount,
    draft: stats.find(s => s.status === 'DRAFT')?._count || 0,
    sent: stats.find(s => s.status === 'SENT')?._count || 0,
    viewed: stats.find(s => s.status === 'VIEWED')?._count || 0,
    accepted: stats.find(s => s.status === 'ACCEPTED')?._count || 0,
    rejected: stats.find(s => s.status === 'REJECTED')?._count || 0,
    expired: stats.find(s => s.status === 'EXPIRED')?._count || 0,
    totalValue: stats.reduce((sum, stat) => sum + (stat._sum.total || 0), 0),
    acceptedValue: stats.find(s => s.status === 'ACCEPTED')?._sum.total || 0,
    pendingValue: stats.filter(s => ['SENT', 'VIEWED'].includes(s.status))
      .reduce((sum, stat) => sum + (stat._sum.total || 0), 0),
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Quotations</h1>
          <p className="text-gray-600 mt-1">
            Create and manage professional quotations
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Link href="/dashboard/quotations/templates">
            <Button variant="outline">
              <FileText className="h-4 w-4 mr-2" />
              Templates
            </Button>
          </Link>
          <Link href="/dashboard/quotations/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Quotation
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats */}
      <QuotationStats stats={quotationStats} />

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search quotations..."
              className="pl-10"
              defaultValue={search}
              name="search"
            />
          </div>
        </div>
        <div className="flex gap-2">
          <select
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            defaultValue={status}
            name="status"
          >
            <option value="">All Status</option>
            <option value="DRAFT">Draft</option>
            <option value="SENT">Sent</option>
            <option value="VIEWED">Viewed</option>
            <option value="ACCEPTED">Accepted</option>
            <option value="REJECTED">Rejected</option>
            <option value="EXPIRED">Expired</option>
          </select>
          <select
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            defaultValue={customerId}
            name="customerId"
          >
            <option value="">All Customers</option>
            {customers.map((customer) => (
              <option key={customer.id} value={customer.id}>
                {customer.name}
              </option>
            ))}
          </select>
          {leads.length > 0 && (
            <select
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              defaultValue={leadId}
              name="leadId"
            >
              <option value="">All Leads</option>
              {leads.map((lead) => (
                <option key={lead.id} value={lead.id}>
                  {lead.title}
                </option>
              ))}
            </select>
          )}
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
        </div>
      </div>

      {/* Quotations Table */}
      <QuotationsTable 
        quotations={quotations}
        currentPage={page}
        totalPages={totalPages}
        totalCount={totalCount}
      />
    </div>
  )
}
