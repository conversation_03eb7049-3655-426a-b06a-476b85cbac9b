import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const paymentMethodsSchema = z.object({
  global: z.object({
    card: z.boolean(),
    applePay: z.boolean(),
    googlePay: z.boolean(),
  }).optional(),
  india: z.object({
    card: z.boolean(),
    upi: z.boolean(),
    netbanking: z.boolean(),
    wallet: z.boolean(),
    emi: z.boolean(),
  }).optional(),
  us: z.object({
    card: z.boolean(),
    applePay: z.boolean(),
    googlePay: z.boolean(),
    achDebit: z.boolean(),
    link: z.boolean(),
  }).optional(),
  eu: z.object({
    card: z.boolean(),
    sepaDebit: z.boolean(),
    ideal: z.boolean(),
    bancontact: z.boolean(),
    giropay: z.boolean(),
    sofort: z.boolean(),
  }).optional(),
  currencies: z.object({
    usd: z.boolean(),
    inr: z.boolean(),
    eur: z.boolean(),
    gbp: z.boolean(),
  }).optional(),
  stripeConfig: z.object({
    publishableKey: z.string().optional(),
    webhookSecret: z.string().optional(),
    testMode: z.boolean(),
  }).optional()
})

// Get payment methods configuration
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.isSuperAdmin && session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // In a real app, these would come from a payment_methods_config table
    // For now, return current configuration from environment/defaults
    const config = {
      global: {
        card: true,
        applePay: true,
        googlePay: true,
      },
      india: {
        card: true,
        upi: true,
        netbanking: true,
        wallet: true,
        emi: false,
      },
      us: {
        card: true,
        applePay: true,
        googlePay: true,
        achDebit: false,
        link: false,
      },
      eu: {
        card: true,
        sepaDebit: true,
        ideal: true,
        bancontact: true,
        giropay: false,
        sofort: false,
      },
      currencies: {
        usd: true,
        inr: true,
        eur: true,
        gbp: false,
      },
      stripeConfig: {
        publishableKey: process.env.STRIPE_PUBLISHABLE_KEY ? '***' + process.env.STRIPE_PUBLISHABLE_KEY.slice(-4) : '',
        webhookSecret: process.env.STRIPE_WEBHOOK_SECRET ? '***' + process.env.STRIPE_WEBHOOK_SECRET.slice(-4) : '',
        testMode: process.env.NODE_ENV !== 'production',
      }
    }

    return NextResponse.json(config)
  } catch (error) {
    console.error('Error fetching payment methods config:', error)
    return NextResponse.json(
      { error: 'Failed to fetch payment methods configuration' },
      { status: 500 }
    )
  }
}

// Update payment methods configuration
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.isSuperAdmin && session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = paymentMethodsSchema.parse(body)

    // In a real app, you would save these to a payment_methods_config table
    // For now, we'll just validate and return success
    
    // Log the payment methods update activity
    await prisma.activity.create({
      data: {
        type: 'SYSTEM',
        title: 'Payment methods configuration updated',
        description: 'Payment methods and gateway settings were updated by super admin',
        companyId: session.user.companyId!,
        createdById: session.user.id,
        metadata: {
          updatedSections: Object.keys(validatedData),
          timestamp: new Date().toISOString(),
          enabledMethods: {
            global: validatedData.global ? Object.entries(validatedData.global).filter(([_, enabled]) => enabled).map(([method]) => method) : [],
            india: validatedData.india ? Object.entries(validatedData.india).filter(([_, enabled]) => enabled).map(([method]) => method) : [],
            us: validatedData.us ? Object.entries(validatedData.us).filter(([_, enabled]) => enabled).map(([method]) => method) : [],
            eu: validatedData.eu ? Object.entries(validatedData.eu).filter(([_, enabled]) => enabled).map(([method]) => method) : [],
          },
          enabledCurrencies: validatedData.currencies ? Object.entries(validatedData.currencies).filter(([_, enabled]) => enabled).map(([currency]) => currency) : []
        }
      }
    })

    return NextResponse.json({ 
      success: true, 
      message: 'Payment methods configuration updated successfully',
      updatedAt: new Date().toISOString()
    })
  } catch (error) {
    console.error('Error updating payment methods config:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Failed to update payment methods configuration' },
      { status: 500 }
    )
  }
}

// Get payment methods statistics
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.isSuperAdmin && session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get payment statistics from the database
    const [
      totalPayments,
      paymentsByMethod,
      paymentsByCurrency,
      recentPayments
    ] = await Promise.all([
      // Total payments count
      prisma.payment.count(),
      
      // Payments by method (this would need to be added to the payment model)
      // For now, return mock data
      Promise.resolve([
        { method: 'card', count: 1250, amount: 125000 },
        { method: 'upi', count: 890, amount: 45000 },
        { method: 'netbanking', count: 340, amount: 67000 },
        { method: 'wallet', count: 120, amount: 12000 },
      ]),
      
      // Payments by currency
      prisma.payment.groupBy({
        by: ['currency'],
        _count: true,
        _sum: { amount: true }
      }),
      
      // Recent payments
      prisma.payment.findMany({
        take: 10,
        orderBy: { createdAt: 'desc' },
        include: {
          subscription: {
            include: {
              company: {
                select: { name: true }
              }
            }
          }
        }
      })
    ])

    const stats = {
      totalPayments,
      paymentsByMethod,
      paymentsByCurrency: paymentsByCurrency.map(p => ({
        currency: p.currency,
        count: p._count,
        amount: p._sum.amount || 0
      })),
      recentPayments: recentPayments.map(p => ({
        id: p.id,
        amount: p.amount,
        currency: p.currency,
        status: p.status,
        companyName: p.subscription?.company?.name || 'Unknown',
        createdAt: p.createdAt
      }))
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching payment methods statistics:', error)
    return NextResponse.json(
      { error: 'Failed to fetch payment methods statistics' },
      { status: 500 }
    )
  }
}
