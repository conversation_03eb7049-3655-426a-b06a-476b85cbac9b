#!/bin/bash

echo "🚀 Setting up Business Management SaaS..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check if PostgreSQL is installed
if ! command -v psql &> /dev/null; then
    echo "❌ PostgreSQL is not installed. Please install PostgreSQL first."
    exit 1
fi

echo "✅ Prerequisites check passed"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Copy environment file
if [ ! -f .env ]; then
    echo "📝 Creating environment file..."
    cp .env.example .env
    echo "⚠️  Please update the DATABASE_URL and other settings in .env file"
else
    echo "✅ Environment file already exists"
fi

# Generate Prisma client
echo "🔧 Generating Prisma client..."
npm run db:generate

# Check if database exists and is accessible
echo "🗄️  Checking database connection..."
if npm run db:push > /dev/null 2>&1; then
    echo "✅ Database connection successful"
    
    # Seed database
    echo "🌱 Seeding database with sample data..."
    npm run db:seed
    
    echo "🎉 Setup completed successfully!"
    echo ""
    echo "📧 Demo Login Credentials:"
    echo "   Email: <EMAIL>"
    echo "   Password: password123"
    echo ""
    echo "🚀 Start the development server:"
    echo "   npm run dev"
    echo ""
    echo "🌐 Open your browser to:"
    echo "   http://localhost:3000"
    
else
    echo "❌ Database connection failed"
    echo "Please check your DATABASE_URL in .env file and ensure PostgreSQL is running"
    echo ""
    echo "Example DATABASE_URL:"
    echo "DATABASE_URL=\"postgresql://username:password@localhost:5432/nextjs_saas_production\""
    exit 1
fi
