import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if quotation exists and belongs to the company
    const quotation = await prisma.quotation.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      },
      include: {
        customer: {
          select: { 
            id: true, 
            name: true, 
            email: true, 
            company: true 
          }
        },
        items: true,
      }
    })

    if (!quotation) {
      return NextResponse.json({ error: 'Quotation not found' }, { status: 404 })
    }

    // Check if quotation can be sent
    if (quotation.status !== 'DRAFT') {
      return NextResponse.json(
        { error: 'Only draft quotations can be sent' },
        { status: 400 }
      )
    }

    // Check if customer has email
    if (!quotation.customer?.email) {
      return NextResponse.json(
        { error: 'Customer email is required to send quotation' },
        { status: 400 }
      )
    }

    // Update quotation status to SENT
    const updatedQuotation = await prisma.quotation.update({
      where: { id: params.id },
      data: {
        status: 'SENT',
        sentAt: new Date(),
      },
      include: {
        customer: {
          select: { id: true, name: true, email: true, company: true }
        },
        lead: {
          select: { id: true, title: true }
        },
        items: {
          include: {
            item: {
              select: { name: true, category: true }
            }
          }
        },
        _count: {
          select: {
            items: true,
            activities: true,
          }
        }
      }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'QUOTATION_SENT',
        title: 'Quotation sent',
        description: `Quotation "${quotation.quotationNumber}" was sent to ${quotation.customer.name}`,
        quotationId: quotation.id,
        customerId: quotation.customerId,
        leadId: quotation.leadId,
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    // TODO: Send email to customer
    // This would integrate with your email service (SendGrid, AWS SES, etc.)
    // For now, we'll just simulate the email sending
    
    try {
      // Simulate email sending
      console.log(`Sending quotation ${quotation.quotationNumber} to ${quotation.customer.email}`)
      
      // In a real implementation, you would:
      // 1. Generate PDF of the quotation
      // 2. Send email with PDF attachment
      // 3. Include tracking links for viewing
      
    } catch (emailError) {
      console.error('Error sending email:', emailError)
      // Don't fail the request if email fails, just log it
    }

    return NextResponse.json({
      message: 'Quotation sent successfully',
      quotation: updatedQuotation
    })
  } catch (error) {
    console.error('Error sending quotation:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
