import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { CustomersTable } from '@/components/customers/customers-table'
import { CustomerStats } from '@/components/customers/customer-stats'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Plus, Search, Filter } from 'lucide-react'
import Link from 'next/link'

interface SearchParams {
  search?: string
  status?: string
  page?: string
}

export default async function CustomersPage({
  searchParams,
}: {
  searchParams: SearchParams
}) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  const search = searchParams.search || ''
  const status = searchParams.status || ''
  const page = parseInt(searchParams.page || '1')
  const limit = 10
  const offset = (page - 1) * limit

  // Build where clause
  const where: any = {
    companyId: session.user.companyId,
  }

  if (search) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { email: { contains: search, mode: 'insensitive' } },
      { company: { contains: search, mode: 'insensitive' } },
      { phone: { contains: search, mode: 'insensitive' } },
    ]
  }

  if (status) {
    where.status = status
  }

  // Fetch customers and stats
  const [customers, totalCount, stats] = await Promise.all([
    prisma.customer.findMany({
      where,
      include: {
        _count: {
          select: {
            leads: true,
            quotations: true,
            invoices: true,
            contracts: true,
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      skip: offset,
      take: limit,
    }),
    prisma.customer.count({ where }),
    prisma.customer.groupBy({
      by: ['status'],
      where: { companyId: session.user.companyId },
      _count: true,
    })
  ])

  const totalPages = Math.ceil(totalCount / limit)

  // Calculate stats
  const customerStats = {
    total: totalCount,
    active: stats.find(s => s.status === 'ACTIVE')?._count || 0,
    inactive: stats.find(s => s.status === 'INACTIVE')?._count || 0,
    prospects: stats.find(s => s.status === 'PROSPECT')?._count || 0,
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Customers</h1>
          <p className="text-gray-600 mt-1">
            Manage your customer relationships and contacts
          </p>
        </div>
        <Link href="/dashboard/customers/new">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Add Customer
          </Button>
        </Link>
      </div>

      {/* Stats */}
      <CustomerStats stats={customerStats} />

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search customers..."
              className="pl-10"
              defaultValue={search}
              name="search"
            />
          </div>
        </div>
        <div className="flex gap-2">
          <select
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            defaultValue={status}
            name="status"
          >
            <option value="">All Status</option>
            <option value="ACTIVE">Active</option>
            <option value="INACTIVE">Inactive</option>
            <option value="PROSPECT">Prospect</option>
          </select>
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
        </div>
      </div>

      {/* Customers Table */}
      <CustomersTable 
        customers={customers}
        currentPage={page}
        totalPages={totalPages}
        totalCount={totalCount}
      />
    </div>
  )
}
