import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { TemplateForm } from '@/components/contract-templates/template-form'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import { notFound } from 'next/navigation'

export default async function EditTemplatePage({
  params,
}: {
  params: { id: string }
}) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  // Fetch the template to edit
  const template = await prisma.contractTemplate.findFirst({
    where: {
      id: params.id,
      companyId: session.user.companyId,
    }
  })

  if (!template) {
    notFound()
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href={`/dashboard/contract-templates/${template.id}`}>
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Template
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Edit Template</h1>
          <p className="text-gray-600 mt-1">
            Update contract template "{template.name}"
          </p>
        </div>
      </div>

      {/* Template Form */}
      <TemplateForm mode="edit" template={template} />
    </div>
  )
}
