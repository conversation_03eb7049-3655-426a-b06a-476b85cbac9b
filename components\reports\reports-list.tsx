'use client'

import Link from 'next/link'
import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { formatDate } from '@/lib/utils'
import { 
  Eye, 
  Download, 
  Trash2, 
  Share2,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  FileText,
  Calendar,
  User,
  BarChart3,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import toast from 'react-hot-toast'

interface ReportData {
  id: string
  name: string
  description: string | null
  type: string
  category: string
  status: string
  format: string
  createdAt: Date
  updatedAt: Date
  createdBy: {
    name: string | null
    firstName: string | null
    lastName: string | null
  } | null
  parameters: any
  lastGenerated: Date | null
  downloadCount: number
}

interface ReportsListProps {
  reports: ReportData[]
  currentPage: number
  totalPages: number
  totalCount: number
}

export function ReportsList({ 
  reports, 
  currentPage, 
  totalPages, 
  totalCount 
}: ReportsListProps) {
  const [deletingId, setDeletingId] = useState<string | null>(null)

  const handleDelete = async (reportId: string) => {
    if (!confirm('Are you sure you want to delete this report?')) {
      return
    }

    setDeletingId(reportId)
    try {
      const response = await fetch(`/api/reports/${reportId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to delete report')
      }

      toast.success('Report deleted successfully')
      window.location.reload()
    } catch (error) {
      toast.error('Failed to delete report')
    } finally {
      setDeletingId(null)
    }
  }

  const handleDownload = async (reportId: string, reportName: string) => {
    try {
      const response = await fetch(`/api/reports/${reportId}/download`)
      
      if (!response.ok) {
        throw new Error('Failed to download report')
      }
      
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${reportName.replace(/\s+/g, '_')}.pdf`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
      
      toast.success('Report downloaded successfully')
    } catch (error) {
      toast.error('Failed to download report')
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return CheckCircle
      case 'PROCESSING':
        return Clock
      case 'FAILED':
        return AlertCircle
      default:
        return FileText
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'text-green-600 border-green-200'
      case 'PROCESSING':
        return 'text-yellow-600 border-yellow-200'
      case 'FAILED':
        return 'text-red-600 border-red-200'
      default:
        return 'text-gray-600 border-gray-200'
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'financial':
        return 'text-green-600 bg-green-100'
      case 'sales':
        return 'text-blue-600 bg-blue-100'
      case 'customer':
        return 'text-purple-600 bg-purple-100'
      case 'operational':
        return 'text-orange-600 bg-orange-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getUserName = (user: any) => {
    if (!user) return 'System'
    if (user.name) return user.name
    if (user.firstName && user.lastName) return `${user.firstName} ${user.lastName}`
    if (user.firstName) return user.firstName
    return 'Unknown User'
  }

  if (reports.length === 0) {
    return (
      <Card>
        <CardContent className="py-12">
          <div className="text-center">
            <BarChart3 className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No reports found</h3>
            <p className="text-gray-500 mb-6">
              Get started by generating your first business report.
            </p>
            <Link href="/dashboard/reports/builder">
              <Button>
                Create Report
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Saved Reports ({totalCount})</span>
            <span className="text-sm font-normal text-gray-500">
              Page {currentPage} of {totalPages}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Desktop Table */}
          <div className="hidden md:block overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Report</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Category</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Type</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Created</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Downloads</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">Actions</th>
                </tr>
              </thead>
              <tbody>
                {reports.map((report) => {
                  const StatusIcon = getStatusIcon(report.status)
                  
                  return (
                    <tr key={report.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-4 px-4">
                        <div>
                          <div className="font-medium text-gray-900">{report.name}</div>
                          {report.description && (
                            <div className="text-sm text-gray-600 mt-1 truncate max-w-xs">
                              {report.description}
                            </div>
                          )}
                          <div className="text-xs text-gray-500 mt-1">
                            by {getUserName(report.createdBy)}
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <Badge className={getCategoryColor(report.category)}>
                          {report.category}
                        </Badge>
                      </td>
                      <td className="py-4 px-4">
                        <span className="text-sm text-gray-900 capitalize">{report.type}</span>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center space-x-2">
                          <StatusIcon className={`h-4 w-4 ${getStatusColor(report.status).split(' ')[0]}`} />
                          <Badge className={getStatusColor(report.status)} variant="outline">
                            {report.status}
                          </Badge>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="text-sm text-gray-900">
                          {formatDate(report.createdAt)}
                        </div>
                        {report.lastGenerated && (
                          <div className="text-xs text-gray-500">
                            Last: {formatDate(report.lastGenerated)}
                          </div>
                        )}
                      </td>
                      <td className="py-4 px-4">
                        <div className="text-sm text-gray-900">
                          {report.downloadCount}
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center justify-end space-x-2">
                          <Link href={`/dashboard/reports/${report.id}`}>
                            <Button variant="ghost" >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </Link>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDownload(report.id, report.name)}
                            className="text-blue-600 hover:text-blue-700"
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" >
                            <Share2 className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(report.id)}
                            disabled={deletingId === report.id}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>

          {/* Mobile Cards */}
          <div className="md:hidden space-y-4">
            {reports.map((report) => {
              const StatusIcon = getStatusIcon(report.status)
              
              return (
                <Card key={report.id} className="p-4">
                  <div className="space-y-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">{report.name}</div>
                        {report.description && (
                          <div className="text-sm text-gray-600 mt-1">
                            {report.description}
                          </div>
                        )}
                      </div>
                      <Button variant="ghost" >
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <StatusIcon className={`h-4 w-4 ${getStatusColor(report.status).split(' ')[0]}`} />
                        <Badge className={getStatusColor(report.status)} variant="outline">
                          {report.status}
                        </Badge>
                      </div>
                      <Badge className={getCategoryColor(report.category)}>
                        {report.category}
                      </Badge>
                    </div>

                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <div className="flex items-center space-x-2">
                        <User className="h-4 w-4" />
                        <span>{getUserName(report.createdBy)}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4" />
                        <span>{formatDate(report.createdAt)}</span>
                      </div>
                    </div>

                    <div className="flex items-center justify-end space-x-2 pt-2 border-t">
                      <Link href={`/dashboard/reports/${report.id}`}>
                        <Button variant="ghost" >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </Link>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDownload(report.id, report.name)}
                        className="text-blue-600"
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" >
                        <Share2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </Card>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            Showing {((currentPage - 1) * 10) + 1} to {Math.min(currentPage * 10, totalCount)} of {totalCount} reports
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage <= 1}
              asChild
            >
              <Link href={`?page=${currentPage - 1}`}>
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Link>
            </Button>
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage >= totalPages}
              asChild
            >
              <Link href={`?page=${currentPage + 1}`}>
                Next
                <ChevronRight className="h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
