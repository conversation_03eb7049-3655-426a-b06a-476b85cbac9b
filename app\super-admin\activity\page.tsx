import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { redirect } from 'next/navigation'
import {
  Activity,
  Clock,
  Filter,
  Search,
  User,
  Building2,
  FileText,
  Mail,
  CreditCard,
  Shield,
  Settings,
  AlertTriangle,
  CheckCircle,
  Eye,
  Download,
  Calendar
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { formatDate } from '@/lib/utils'
import Link from 'next/link'

interface SearchParams {
  type?: string
  company?: string
  user?: string
  level?: string
  search?: string
  page?: string
}

export default async function SuperAdminActivityPage({
  searchParams,
}: {
  searchParams: SearchParams
}) {
  const session = await getServerSession(authOptions)

  // Check if user is super admin
  if (!session?.user?.isSuperAdmin && session?.user?.role !== 'SUPER_ADMIN') {
    redirect('/dashboard')
  }

  const { type, company, user, level, search, page = '1' } = searchParams
  const currentPage = parseInt(page)
  const pageSize = 50

  // Build where clause for filtering
  const where: any = {}

  if (type && type !== 'all') {
    where.type = type
  }

  if (company && company !== 'all') {
    where.companyId = company
  }

  if (user && user !== 'all') {
    where.createdById = user
  }

  if (level && level !== 'all') {
    where.level = level
  }

  if (search) {
    where.OR = [
      { title: { contains: search } },
      { description: { contains: search } }
    ]
  }

  // Calculate date ranges
  const now = new Date()
  const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const startOfWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)

  // Fetch activities and statistics
  const [
    activities,
    totalActivities,
    activityStats,
    activitiesByType,
    activitiesByLevel,
    topActiveCompanies,
    topActiveUsers
  ] = await Promise.all([
    prisma.activity.findMany({
      where,
      include: {
        company_rel: {
          select: { id: true, name: true, plan: true }
        },
        createdBy: {
          select: {
            id: true,
            name: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      skip: (currentPage - 1) * pageSize,
      take: pageSize
    }),
    prisma.activity.count({ where }),

    // Activity statistics
    Promise.all([
      prisma.activity.count({ where: { createdAt: { gte: startOfDay } } }),
      prisma.activity.count({ where: { createdAt: { gte: startOfWeek } } }),
      prisma.activity.count({ where: { createdAt: { gte: startOfMonth } } }),
      prisma.activity.count()
    ]).then(([today, week, month, total]) => ({ today, week, month, total })),

    // Activities by type
    prisma.activity.groupBy({
      by: ['type'],
      _count: true,
      orderBy: { _count: { type: 'desc' } }
    }),

    // Activities by level (mock data for now)
    Promise.resolve([
      { level: 'INFO', _count: 1247 },
      { level: 'WARN', _count: 89 },
      { level: 'ERROR', _count: 12 }
    ]),

    // Top active companies
    prisma.activity.groupBy({
      by: ['companyId'],
      _count: true,
      orderBy: { _count: { companyId: 'desc' } },
      take: 5
    }).then(async (results) => {
      const companyIds = results.map(r => r.companyId).filter(Boolean)
      const companies = await prisma.company.findMany({
        where: { id: { in: companyIds } },
        select: { id: true, name: true, plan: true }
      })
      return results.map(result => ({
        ...result,
        company: companies.find(c => c.id === result.companyId)
      }))
    }),

    // Top active users
    prisma.activity.groupBy({
      by: ['createdById'],
      _count: true,
      orderBy: { _count: { createdById: 'desc' } },
      take: 5
    }).then(async (results) => {
      const userIds = results.map(r => r.createdById).filter(Boolean)
      const users = await prisma.user.findMany({
        where: { id: { in: userIds } },
        select: {
          id: true,
          name: true,
          firstName: true,
          lastName: true,
          email: true,
          company: { select: { name: true } }
        }
      })
      return results.map(result => ({
        ...result,
        user: users.find(u => u.id === result.createdById)
      }))
    })
  ])

  const totalPages = Math.ceil(totalActivities / pageSize)

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'USER_LOGIN':
      case 'USER_LOGOUT':
        return User
      case 'COMPANY_CREATED':
      case 'COMPANY_UPDATED':
        return Building2
      case 'INVOICE_CREATED':
      case 'INVOICE_SENT':
      case 'INVOICE_PAID':
        return CreditCard
      case 'EMAIL_SENT':
        return Mail
      case 'DOCUMENT_CREATED':
      case 'DOCUMENT_UPDATED':
        return FileText
      case 'SECURITY_EVENT':
        return Shield
      case 'SYSTEM_EVENT':
        return Settings
      default:
        return Activity
    }
  }

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'USER_LOGIN':
      case 'USER_LOGOUT':
        return 'text-blue-600'
      case 'COMPANY_CREATED':
      case 'COMPANY_UPDATED':
        return 'text-green-600'
      case 'INVOICE_CREATED':
      case 'INVOICE_SENT':
      case 'INVOICE_PAID':
        return 'text-purple-600'
      case 'EMAIL_SENT':
        return 'text-orange-600'
      case 'DOCUMENT_CREATED':
      case 'DOCUMENT_UPDATED':
        return 'text-indigo-600'
      case 'SECURITY_EVENT':
        return 'text-red-600'
      case 'SYSTEM_EVENT':
        return 'text-gray-600'
      default:
        return 'text-gray-600'
    }
  }

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'INFO':
        return 'bg-blue-100 text-blue-800'
      case 'WARN':
        return 'bg-yellow-100 text-yellow-800'
      case 'ERROR':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="p-3 bg-orange-100 rounded-lg">
            <Activity className="h-8 w-8 text-orange-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Activity Logs</h1>
            <p className="text-gray-600">Platform-wide activity monitoring and audit trails</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export Logs
          </Button>
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
        </div>
      </div>

      {/* Activity Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Today</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activityStats.today.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Activities today</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">This Week</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activityStats.week.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Activities this week</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">This Month</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activityStats.month.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Activities this month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activityStats.total.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">All activities</p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Activity Feed - Takes up 3 columns */}
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <CardTitle>Activity Feed</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {activities.map((activity) => {
                  const ActivityIcon = getActivityIcon(activity.type)
                  const iconColor = getActivityColor(activity.type)

                  return (
                    <div key={activity.id} className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
                      <div className={`p-2 rounded-full bg-white ${iconColor}`}>
                        <ActivityIcon className="h-4 w-4" />
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                          <div className="flex items-center space-x-2">
                            {activity.level && (
                              <Badge className={getLevelColor(activity.level)} variant="outline">
                                {activity.level}
                              </Badge>
                            )}
                            <span className="text-xs text-gray-500">
                              {formatDate(activity.createdAt)}
                            </span>
                          </div>
                        </div>

                        {activity.description && (
                          <p className="text-sm text-gray-600 mt-1">{activity.description}</p>
                        )}

                        <div className="flex items-center space-x-4 mt-2">
                          {activity.createdBy && (
                            <div className="flex items-center space-x-2">
                              <Avatar className="h-6 w-6">
                                <AvatarImage src={`/avatars/${activity.createdBy.email?.split('@')[0]}.svg`} />
                                <AvatarFallback className="text-xs">
                                  {activity.createdBy.name?.charAt(0) || activity.createdBy.email?.charAt(0) || 'U'}
                                </AvatarFallback>
                              </Avatar>
                              <span className="text-xs text-gray-500">
                                {activity.createdBy.name ||
                                 `${activity.createdBy.firstName || ''} ${activity.createdBy.lastName || ''}`.trim() ||
                                 activity.createdBy.email}
                              </span>
                            </div>
                          )}

                          {activity.company_rel && (
                            <div className="flex items-center space-x-1">
                              <Building2 className="h-3 w-3 text-gray-400" />
                              <span className="text-xs text-gray-500">{activity.company_rel.name}</span>
                              <Badge variant="outline" className="text-xs">
                                {activity.company_rel.plan}
                              </Badge>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-6">
                  <div className="text-sm text-gray-700">
                    Showing {(currentPage - 1) * pageSize + 1} to {Math.min(currentPage * pageSize, totalActivities)} of {totalActivities} activities
                  </div>
                  <div className="flex items-center space-x-2">
                    {currentPage > 1 && (
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/super-admin/activity?page=${currentPage - 1}`}>
                          Previous
                        </Link>
                      </Button>
                    )}
                    {currentPage < totalPages && (
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/super-admin/activity?page=${currentPage + 1}`}>
                          Next
                        </Link>
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Right Sidebar - Analytics */}
        <div className="space-y-6">
          {/* Activity Types */}
          <Card>
            <CardHeader>
              <CardTitle>Activity Types</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {activitiesByType.slice(0, 8).map((type) => (
                <div key={type.type} className="flex items-center justify-between">
                  <span className="text-sm font-medium">{type.type.replace('_', ' ')}</span>
                  <Badge variant="outline">{type._count}</Badge>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Activity Levels */}
          <Card>
            <CardHeader>
              <CardTitle>Activity Levels</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {activitiesByLevel.map((level) => (
                <div key={level.level} className="flex items-center justify-between">
                  <span className="text-sm font-medium">{level.level}</span>
                  <Badge className={getLevelColor(level.level)} variant="outline">
                    {level._count}
                  </Badge>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Top Active Companies */}
          <Card>
            <CardHeader>
              <CardTitle>Most Active Companies</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {topActiveCompanies.map((item, index) => (
                <div key={item.companyId} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="text-xs text-gray-500">#{index + 1}</span>
                    <span className="text-sm font-medium">
                      {item.company?.name || 'Unknown Company'}
                    </span>
                  </div>
                  <Badge variant="outline">{item._count}</Badge>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Top Active Users */}
          <Card>
            <CardHeader>
              <CardTitle>Most Active Users</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {topActiveUsers.map((item, index) => (
                <div key={item.createdById} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="text-xs text-gray-500">#{index + 1}</span>
                    <span className="text-sm font-medium">
                      {item.user?.name ||
                       `${item.user?.firstName || ''} ${item.user?.lastName || ''}`.trim() ||
                       item.user?.email ||
                       'Unknown User'}
                    </span>
                  </div>
                  <Badge variant="outline">{item._count}</Badge>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
