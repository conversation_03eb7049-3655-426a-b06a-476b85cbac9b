import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const templateSchema = z.object({
  name: z.string().min(1, 'Template name is required'),
  description: z.string().optional().nullable(),
  category: z.string().optional().nullable(),
  status: z.enum(['ACTIVE', 'DRAFT', 'ARCHIVED']).default('DRAFT'),
  content: z.string().min(1, 'Template content is required'),
  variables: z.array(z.object({
    name: z.string().min(1, 'Variable name is required'),
    label: z.string().min(1, 'Variable label is required'),
    type: z.enum(['TEXT', 'NUMBER', 'DATE', 'BOOLEAN', 'EMAIL', 'PHONE']),
    required: z.boolean().default(false),
    defaultValue: z.string().optional().nullable(),
    description: z.string().optional().nullable(),
  })).default([]),
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const template = await prisma.contractTemplate.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      },
      include: {
        createdBy: {
          select: { name: true, firstName: true, lastName: true }
        },
        contracts: {
          include: {
            customer: {
              select: { name: true }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: 10
        },
        _count: {
          select: {
            contracts: true
          }
        }
      }
    })

    if (!template) {
      return NextResponse.json({ error: 'Template not found' }, { status: 404 })
    }

    return NextResponse.json(template)
  } catch (error) {
    console.error('Error fetching contract template:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = templateSchema.parse(body)

    // Check if template exists and belongs to the company
    const existingTemplate = await prisma.contractTemplate.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      }
    })

    if (!existingTemplate) {
      return NextResponse.json({ error: 'Template not found' }, { status: 404 })
    }

    // Check if template name conflicts with another template (if changed)
    if (validatedData.name !== existingTemplate.name) {
      const conflictingTemplate = await prisma.contractTemplate.findFirst({
        where: {
          name: validatedData.name,
          companyId: session.user.companyId,
          id: { not: params.id },
        }
      })

      if (conflictingTemplate) {
        return NextResponse.json(
          { error: 'Template name already exists' },
          { status: 400 }
        )
      }
    }

    // Validate variables have unique names
    const variableNames = validatedData.variables.map(v => v.name)
    const uniqueNames = new Set(variableNames)
    if (variableNames.length !== uniqueNames.size) {
      return NextResponse.json(
        { error: 'Variable names must be unique' },
        { status: 400 }
      )
    }

    // Validate variable names are valid identifiers
    const invalidNames = variableNames.filter(name => name && !/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(name))
    if (invalidNames.length > 0) {
      return NextResponse.json(
        { error: `Invalid variable names: ${invalidNames.join(', ')}. Use only letters, numbers, and underscores.` },
        { status: 400 }
      )
    }

    const template = await prisma.contractTemplate.update({
      where: { id: params.id },
      data: {
        name: validatedData.name,
        description: validatedData.description,
        category: validatedData.category,
        status: validatedData.status,
        content: validatedData.content,
        variables: validatedData.variables,
      },
      include: {
        createdBy: {
          select: { name: true, firstName: true, lastName: true }
        },
        _count: {
          select: {
            contracts: true
          }
        }
      }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'NOTE',
        title: 'Contract template updated',
        description: `Template "${template.name}" was updated`,
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    return NextResponse.json(template)
  } catch (error) {
    console.error('Error updating contract template:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if template exists and belongs to the company
    const template = await prisma.contractTemplate.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      },
      include: {
        _count: {
          select: {
            contracts: true
          }
        }
      }
    })

    if (!template) {
      return NextResponse.json({ error: 'Template not found' }, { status: 404 })
    }

    // Check if template is being used by contracts
    if (template._count.contracts > 0) {
      return NextResponse.json(
        { error: `Cannot delete template. It is being used by ${template._count.contracts} contract(s).` },
        { status: 400 }
      )
    }

    await prisma.contractTemplate.delete({
      where: { id: params.id }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'NOTE',
        title: 'Contract template deleted',
        description: `Template "${template.name}" was deleted`,
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    return NextResponse.json({ message: 'Template deleted successfully' })
  } catch (error) {
    console.error('Error deleting contract template:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
