'use client'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { formatCurrency } from '@/lib/utils'
import { 
  Package, 
  ShoppingCart, 
  Wrench, 
  CheckCircle, 
  XCircle,
  AlertTriangle,
  DollarSign,
  TrendingUp,
  BarChart3,
  Archive
} from 'lucide-react'

interface ItemStatsProps {
  stats: {
    total: number
    active: number
    inactive: number
    products: number
    services: number
    lowStock: number
    totalValue: number
    totalStock: number
    avgPrice: number
  }
}

export function ItemStats({ stats }: ItemStatsProps) {
  const activeRate = stats.total > 0 ? ((stats.active / stats.total) * 100).toFixed(1) : '0'
  const productRate = stats.total > 0 ? ((stats.products / stats.total) * 100).toFixed(1) : '0'
  const serviceRate = stats.total > 0 ? ((stats.services / stats.total) * 100).toFixed(1) : '0'

  const statCards = [
    {
      title: 'Total Items',
      value: stats.total.toString(),
      icon: Package,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      description: 'All catalog items'
    },
    {
      title: 'Active Items',
      value: stats.active.toString(),
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      description: 'Available for sale'
    },
    {
      title: 'Inactive Items',
      value: stats.inactive.toString(),
      icon: XCircle,
      color: 'text-red-600',
      bgColor: 'bg-red-100',
      description: 'Not available'
    },
    {
      title: 'Products',
      value: stats.products.toString(),
      icon: ShoppingCart,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      description: 'Physical products'
    },
    {
      title: 'Services',
      value: stats.services.toString(),
      icon: Wrench,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-100',
      description: 'Service offerings'
    },
    {
      title: 'Low Stock',
      value: stats.lowStock.toString(),
      icon: AlertTriangle,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      description: 'Need restocking'
    },
    {
      title: 'Total Value',
      value: formatCurrency(stats.totalValue),
      icon: DollarSign,
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-100',
      description: 'Catalog value'
    },
    {
      title: 'Total Stock',
      value: stats.totalStock.toLocaleString(),
      icon: Archive,
      color: 'text-cyan-600',
      bgColor: 'bg-cyan-100',
      description: 'Units in stock'
    },
    {
      title: 'Avg Price',
      value: formatCurrency(stats.avgPrice),
      icon: BarChart3,
      color: 'text-pink-600',
      bgColor: 'bg-pink-100',
      description: 'Average item price'
    },
    {
      title: 'Active Rate',
      value: `${activeRate}%`,
      icon: TrendingUp,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      description: 'Active items ratio'
    },
    {
      title: 'Product Mix',
      value: `${productRate}%`,
      icon: ShoppingCart,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      description: 'Products vs services'
    },
    {
      title: 'Service Mix',
      value: `${serviceRate}%`,
      icon: Wrench,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-100',
      description: 'Services vs products'
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4">
      {statCards.map((stat, index) => (
        <Card key={index} className="hover:shadow-lg transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              {stat.title}
            </CardTitle>
            <div className={`p-2 rounded-lg ${stat.bgColor}`}>
              <stat.icon className={`h-4 w-4 ${stat.color}`} />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">
              {stat.value}
            </div>
            <p className="text-xs text-gray-500 mt-1">
              {stat.description}
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
