'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { getStatusColor, formatDate, formatCurrency } from '@/lib/utils'
import { 
  Receipt, 
  DollarSign, 
  Calendar, 
  User,
  Building,
  FileText,
  CreditCard,
  Banknote,
  Smartphone,
  CheckCircle,
  Clock,
  XCircle,
  Download,
  Edit,
  Trash2,
  Copy
} from 'lucide-react'
import toast from 'react-hot-toast'

interface ReceiptDetailsProps {
  receipt: {
    id: string
    receiptNumber: string
    amount: number
    paymentMethod: string
    status: string
    paidAt: Date
    notes: string | null
    referenceNumber: string | null
    createdAt: Date
    invoice: {
      id: string
      invoiceNumber: string
      total: number
      customer: {
        id: string
        name: string
        email: string | null
        company: string | null
        phone: string | null
        address: string | null
        city: string | null
        state: string | null
        postalCode: string | null
        country: string | null
      } | null
    }
    createdBy: {
      name: string | null
      firstName: string | null
      lastName: string | null
    } | null
  }
  company: {
    name: string
    email: string | null
    phone: string | null
    address: string | null
    city: string | null
    state: string | null
    postalCode: string | null
    country: string | null
    logo: string | null
  } | null
}

export function ReceiptDetails({ receipt, company }: ReceiptDetailsProps) {
  const [isDeleting, setIsDeleting] = useState(false)

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this receipt? This action cannot be undone.')) {
      return
    }

    setIsDeleting(true)
    try {
      const response = await fetch(`/api/receipts/${receipt.id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete receipt')
      }

      toast.success('Receipt deleted successfully')
      window.location.href = '/dashboard/receipts'
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete receipt'
      toast.error(errorMessage)
    } finally {
      setIsDeleting(false)
    }
  }

  const handleDownloadPDF = async () => {
    try {
      const response = await fetch(`/api/receipts/${receipt.id}/pdf`)
      if (!response.ok) {
        throw new Error('Failed to generate PDF')
      }
      
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${receipt.receiptNumber}.pdf`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      toast.error('Failed to download PDF')
    }
  }

  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case 'CASH':
        return Banknote
      case 'CREDIT_CARD':
      case 'DEBIT_CARD':
        return CreditCard
      case 'UPI':
        return Smartphone
      case 'BANK_TRANSFER':
        return Building
      default:
        return DollarSign
    }
  }

  const getPaymentMethodColor = (method: string) => {
    switch (method) {
      case 'CASH':
        return 'text-green-600'
      case 'CREDIT_CARD':
      case 'DEBIT_CARD':
        return 'text-blue-600'
      case 'UPI':
        return 'text-purple-600'
      case 'BANK_TRANSFER':
        return 'text-indigo-600'
      case 'CHEQUE':
        return 'text-orange-600'
      default:
        return 'text-gray-600'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'CONFIRMED':
        return CheckCircle
      case 'PENDING':
        return Clock
      case 'CANCELLED':
        return XCircle
      default:
        return Receipt
    }
  }

  const getUserName = (user: any) => {
    if (!user) return 'System'
    if (user.name) return user.name
    if (user.firstName && user.lastName) return `${user.firstName} ${user.lastName}`
    if (user.firstName) return user.firstName
    return 'Unknown User'
  }

  const PaymentIcon = getPaymentMethodIcon(receipt.paymentMethod)
  const StatusIcon = getStatusIcon(receipt.status)

  return (
    <div className="space-y-6">
      {/* Receipt Header - Printable Format */}
      <Card className="print:shadow-none print:border-none">
        <CardContent className="p-8">
          {/* Company Header */}
          <div className="flex items-start justify-between mb-8">
            <div>
              {company?.logo && (
                <img src={company.logo} alt={company.name} className="h-12 mb-4" />
              )}
              <h2 className="text-2xl font-bold text-gray-900">{company?.name || 'Company Name'}</h2>
              {company?.address && (
                <div className="text-sm text-gray-600 mt-2">
                  <p>{company.address}</p>
                  <p>
                    {company.city && `${company.city}, `}
                    {company.state && `${company.state} `}
                    {company.postalCode}
                  </p>
                  {company.country && <p>{company.country}</p>}
                  {company.email && <p>Email: {company.email}</p>}
                  {company.phone && <p>Phone: {company.phone}</p>}
                </div>
              )}
            </div>
            <div className="text-right">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">RECEIPT</h1>
              <div className="text-lg font-medium text-gray-700">{receipt.receiptNumber}</div>
              <div className="text-sm text-gray-500 mt-1">
                Date: {formatDate(receipt.paidAt)}
              </div>
            </div>
          </div>

          {/* Customer Information */}
          {receipt.invoice.customer && (
            <div className="mb-8">
              <h3 className="text-lg font-medium text-gray-900 mb-3">Received From:</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="font-medium text-gray-900">{receipt.invoice.customer.name}</div>
                {receipt.invoice.customer.company && (
                  <div className="text-gray-700">{receipt.invoice.customer.company}</div>
                )}
                {receipt.invoice.customer.address && (
                  <div className="text-sm text-gray-600 mt-2">
                    <p>{receipt.invoice.customer.address}</p>
                    <p>
                      {receipt.invoice.customer.city && `${receipt.invoice.customer.city}, `}
                      {receipt.invoice.customer.state && `${receipt.invoice.customer.state} `}
                      {receipt.invoice.customer.postalCode}
                    </p>
                    {receipt.invoice.customer.country && <p>{receipt.invoice.customer.country}</p>}
                  </div>
                )}
                {receipt.invoice.customer.email && (
                  <div className="text-sm text-gray-600 mt-1">
                    Email: {receipt.invoice.customer.email}
                  </div>
                )}
                {receipt.invoice.customer.phone && (
                  <div className="text-sm text-gray-600">
                    Phone: {receipt.invoice.customer.phone}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Payment Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-3">Payment Details:</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">Invoice Number:</span>
                  <Link 
                    href={`/dashboard/invoices/${receipt.invoice.id}`}
                    className="text-blue-600 hover:text-blue-800 font-medium"
                  >
                    {receipt.invoice.invoiceNumber}
                  </Link>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Invoice Total:</span>
                  <span className="font-medium">{formatCurrency(receipt.invoice.total)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Amount Received:</span>
                  <span className="font-bold text-lg text-green-600">
                    {formatCurrency(receipt.amount)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Payment Method:</span>
                  <div className="flex items-center space-x-2">
                    <PaymentIcon className={`h-4 w-4 ${getPaymentMethodColor(receipt.paymentMethod)}`} />
                    <span className="font-medium">
                      {receipt.paymentMethod.replace('_', ' ')}
                    </span>
                  </div>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Payment Date:</span>
                  <span className="font-medium">{formatDate(receipt.paidAt)}</span>
                </div>
                {receipt.referenceNumber && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Reference:</span>
                    <span className="font-medium">{receipt.referenceNumber}</span>
                  </div>
                )}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-3">Receipt Status:</h3>
              <div className="flex items-center space-x-2 mb-4">
                <StatusIcon className={`h-5 w-5 ${getStatusColor(receipt.status).replace('border-', 'text-').replace('text-', 'text-')}`} />
                <Badge className={getStatusColor(receipt.status)} variant="outline">
                  {receipt.status}
                </Badge>
              </div>
              
              <div className="space-y-2 text-sm text-gray-600">
                <div>
                  <span className="font-medium">Created:</span> {formatDate(receipt.createdAt)}
                </div>
                <div>
                  <span className="font-medium">Created by:</span> {getUserName(receipt.createdBy)}
                </div>
              </div>
            </div>
          </div>

          {/* Notes */}
          {receipt.notes && (
            <div className="mb-8">
              <h3 className="text-lg font-medium text-gray-900 mb-3">Notes:</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-gray-700 whitespace-pre-wrap">{receipt.notes}</p>
              </div>
            </div>
          )}

          {/* Footer */}
          <div className="border-t pt-6 mt-8">
            <div className="text-center text-sm text-gray-500">
              <p>Thank you for your payment!</p>
              <p className="mt-1">This is a computer-generated receipt.</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons - Hidden in print */}
      <div className="flex items-center justify-end space-x-2 print:hidden">
        <Link href={`/dashboard/receipts/${receipt.id}/edit`}>
          <Button variant="outline">
            <Edit className="h-4 w-4 mr-2" />
            Edit Receipt
          </Button>
        </Link>
        <Button variant="outline" onClick={handleDownloadPDF}>
          <Download className="h-4 w-4 mr-2" />
          Download PDF
        </Button>
        <Button variant="outline" onClick={() => window.print()}>
          <Download className="h-4 w-4 mr-2" />
          Print
        </Button>
        <Button variant="outline">
          <Copy className="h-4 w-4 mr-2" />
          Duplicate
        </Button>
        <Button 
          variant="outline" 
          onClick={handleDelete}
          disabled={isDeleting}
          className="text-red-600 hover:text-red-700"
        >
          <Trash2 className="h-4 w-4 mr-2" />
          {isDeleting ? 'Deleting...' : 'Delete'}
        </Button>
      </div>
    </div>
  )
}
