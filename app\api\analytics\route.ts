import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '30d'
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    // Calculate date range
    const now = new Date()
    let dateStart: Date
    let dateEnd: Date = now

    if (startDate && endDate) {
      dateStart = new Date(startDate)
      dateEnd = new Date(endDate)
    } else {
      switch (period) {
        case '7d':
          dateStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case '30d':
          dateStart = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          break
        case '90d':
          dateStart = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
          break
        case '1y':
          dateStart = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
          break
        default:
          dateStart = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      }
    }

    // Fetch comprehensive analytics data
    const [
      // Overview metrics
      totalCustomers,
      totalLeads,
      totalQuotations,
      totalInvoices,
      totalContracts,
      totalItems,
      
      // Revenue data
      totalRevenue,
      paidInvoicesCount,
      
      // Status breakdowns
      leadsByStatus,
      quotationsByStatus,
      invoicesByStatus,
      contractsByStatus,
      
      // Top performers
      topCustomersByRevenue,
      topItemsByUsage,
      
      // Monthly trends
      monthlyRevenueTrend,
      monthlyQuotationTrend,
      monthlyInvoiceTrend,
      monthlyContractTrend,
      
      // Recent activity
      recentActivities
    ] = await Promise.all([
      // Overview counts
      prisma.customer.count({ where: { companyId: session.user.companyId } }),
      prisma.lead.count({ where: { companyId: session.user.companyId } }),
      prisma.quotation.count({ where: { companyId: session.user.companyId } }),
      prisma.invoice.count({ where: { companyId: session.user.companyId } }),
      prisma.contract.count({ where: { companyId: session.user.companyId } }),
      prisma.item.count({ where: { companyId: session.user.companyId } }),
      
      // Revenue aggregation
      prisma.invoice.aggregate({
        where: {
          companyId: session.user.companyId,
          status: 'PAID',
          paidAt: { gte: dateStart, lte: dateEnd }
        },
        _sum: { total: true }
      }),
      
      prisma.invoice.count({
        where: {
          companyId: session.user.companyId,
          status: 'PAID',
          paidAt: { gte: dateStart, lte: dateEnd }
        }
      }),
      
      // Status breakdowns
      prisma.lead.groupBy({
        by: ['status'],
        where: { companyId: session.user.companyId },
        _count: true
      }),
      
      prisma.quotation.groupBy({
        by: ['status'],
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: dateStart, lte: dateEnd }
        },
        _count: true,
        _sum: { total: true }
      }),
      
      prisma.invoice.groupBy({
        by: ['status'],
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: dateStart, lte: dateEnd }
        },
        _count: true,
        _sum: { total: true }
      }),
      
      prisma.contract.groupBy({
        by: ['status'],
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: dateStart, lte: dateEnd }
        },
        _count: true,
        _sum: { value: true }
      }),
      
      // Top customers by revenue
      prisma.customer.findMany({
        where: { companyId: session.user.companyId },
        include: {
          invoices: {
            where: {
              status: 'PAID',
              paidAt: { gte: dateStart, lte: dateEnd }
            },
            select: { total: true }
          },
          quotations: {
            where: {
              createdAt: { gte: dateStart, lte: dateEnd }
            },
            select: { total: true }
          },
          _count: {
            select: {
              invoices: true,
              quotations: true
            }
          }
        },
        take: 10
      }),
      
      // Top items by usage
      prisma.item.findMany({
        where: { companyId: session.user.companyId },
        include: {
          quotationItems: {
            where: {
              quotation: {
                createdAt: { gte: dateStart, lte: dateEnd }
              }
            }
          },
          invoiceItems: {
            where: {
              invoice: {
                createdAt: { gte: dateStart, lte: dateEnd }
              }
            }
          },
          _count: {
            select: {
              quotationItems: true,
              invoiceItems: true
            }
          }
        },
        take: 10
      }),
      
      // Monthly revenue trend (last 12 months)
      prisma.$queryRaw`
        SELECT 
          DATE_FORMAT(paidAt, '%Y-%m') as month,
          SUM(total) as revenue,
          COUNT(*) as count
        FROM Invoice 
        WHERE companyId = ${session.user.companyId}
          AND status = 'PAID'
          AND paidAt >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(paidAt, '%Y-%m')
        ORDER BY month
      `,
      
      // Monthly quotation trend
      prisma.$queryRaw`
        SELECT 
          DATE_FORMAT(createdAt, '%Y-%m') as month,
          COUNT(*) as count,
          SUM(total) as value
        FROM Quotation 
        WHERE companyId = ${session.user.companyId}
          AND createdAt >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(createdAt, '%Y-%m')
        ORDER BY month
      `,
      
      // Monthly invoice trend
      prisma.$queryRaw`
        SELECT 
          DATE_FORMAT(createdAt, '%Y-%m') as month,
          COUNT(*) as count,
          SUM(total) as value
        FROM Invoice 
        WHERE companyId = ${session.user.companyId}
          AND createdAt >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(createdAt, '%Y-%m')
        ORDER BY month
      `,
      
      // Monthly contract trend
      prisma.$queryRaw`
        SELECT 
          DATE_FORMAT(createdAt, '%Y-%m') as month,
          COUNT(*) as count,
          SUM(value) as value
        FROM Contract 
        WHERE companyId = ${session.user.companyId}
          AND createdAt >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(createdAt, '%Y-%m')
        ORDER BY month
      `,
      
      // Recent activities
      prisma.activity.findMany({
        where: {
          companyId: session.user.companyId,
          createdAt: { gte: dateStart, lte: dateEnd }
        },
        include: {
          createdBy: {
            select: { name: true, firstName: true, lastName: true }
          },
          customer: {
            select: { name: true }
          },
          lead: {
            select: { name: true }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 50
      })
    ])

    // Process top customers
    const processedTopCustomers = topCustomersByRevenue
      .map(customer => ({
        ...customer,
        totalRevenue: customer.invoices.reduce((sum, inv) => sum + inv.total, 0),
        quotationValue: customer.quotations.reduce((sum, quot) => sum + quot.total, 0)
      }))
      .sort((a, b) => b.totalRevenue - a.totalRevenue)
      .slice(0, 5)

    // Process top items
    const processedTopItems = topItemsByUsage
      .map(item => ({
        ...item,
        totalUsage: item.quotationItems.length + item.invoiceItems.length
      }))
      .sort((a, b) => b.totalUsage - a.totalUsage)
      .slice(0, 5)

    // Calculate conversion rates
    const quotationConversionRate = totalQuotations > 0 
      ? ((quotationsByStatus.find(q => q.status === 'ACCEPTED')?._count || 0) / totalQuotations * 100).toFixed(1)
      : '0'

    const invoicePaymentRate = totalInvoices > 0
      ? ((invoicesByStatus.find(i => i.status === 'PAID')?._count || 0) / totalInvoices * 100).toFixed(1)
      : '0'

    const contractSignatureRate = totalContracts > 0
      ? ((contractsByStatus.filter(c => ['SIGNED', 'EXECUTED'].includes(c.status))
          .reduce((sum, c) => sum + c._count, 0) / totalContracts) * 100).toFixed(1)
      : '0'

    return NextResponse.json({
      overview: {
        totalCustomers,
        totalLeads,
        totalQuotations,
        totalInvoices,
        totalContracts,
        totalItems,
        totalRevenue: totalRevenue._sum.total || 0,
        paidInvoices: paidInvoicesCount
      },
      pipeline: {
        leads: leadsByStatus,
        quotations: quotationsByStatus,
        invoices: invoicesByStatus,
        contracts: contractsByStatus
      },
      performance: {
        quotationConversionRate: parseFloat(quotationConversionRate),
        invoicePaymentRate: parseFloat(invoicePaymentRate),
        contractSignatureRate: parseFloat(contractSignatureRate)
      },
      topPerformers: {
        customers: processedTopCustomers,
        items: processedTopItems
      },
      trends: {
        revenue: monthlyRevenueTrend,
        quotations: monthlyQuotationTrend,
        invoices: monthlyInvoiceTrend,
        contracts: monthlyContractTrend
      },
      activities: recentActivities,
      period: {
        period,
        startDate: dateStart,
        endDate: dateEnd
      }
    })
  } catch (error) {
    console.error('Error fetching analytics:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
