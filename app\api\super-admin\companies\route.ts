import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const companyUpdateSchema = z.object({
  name: z.string().min(1, 'Company name is required').optional(),
  email: z.string().email('Invalid email address').optional(),
  phone: z.string().optional(),
  address: z.string().optional(),
  plan: z.enum(['BASIC', 'PROFESSIONAL', 'ENTERPRISE']).optional(),
  status: z.enum(['ACTIVE', 'SUSPENDED', 'CANCELLED']).optional(),
  maxUsers: z.number().min(1).optional(),
  features: z.array(z.string()).optional()
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const plan = searchParams.get('plan') || ''
    const status = searchParams.get('status') || ''
    const search = searchParams.get('search') || ''
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = (page - 1) * limit

    // Build where clause
    const where: any = {}

    if (plan) {
      where.plan = plan
    }

    if (status === 'active') {
      where.users = {
        some: {
          lastLoginAt: { gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
        }
      }
    } else if (status === 'inactive') {
      where.OR = [
        {
          users: {
            none: {}
          }
        },
        {
          users: {
            every: {
              OR: [
                { lastLoginAt: null },
                { lastLoginAt: { lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } }
              ]
            }
          }
        }
      ]
    }

    if (search) {
      where.OR = [
        { name: { contains: search } },
        { email: { contains: search } },
        { domain: { contains: search } },
      ]
    }

    const [companies, totalCount] = await Promise.all([
      prisma.company.findMany({
        where,
        include: {
          _count: {
            select: {
              users: true,
              customers: true,
              quotations: true,
              invoices: true,
              contracts: true
            }
          },
          users: {
            where: { role: 'OWNER' },
            select: { 
              id: true,
              name: true, 
              firstName: true, 
              lastName: true, 
              email: true, 
              lastLoginAt: true,
              createdAt: true
            },
            take: 1
          }
        },
        orderBy: { createdAt: 'desc' },
        skip: offset,
        take: limit,
      }),
      prisma.company.count({ where })
    ])

    return NextResponse.json({
      companies,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      }
    })
  } catch (error) {
    console.error('Error fetching companies:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { name, email, plan = 'BASIC', ownerEmail, ownerName } = body

    if (!name || !email || !ownerEmail || !ownerName) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Check if company email already exists
    const existingCompany = await prisma.company.findFirst({
      where: { email }
    })

    if (existingCompany) {
      return NextResponse.json(
        { error: 'Company email already exists' },
        { status: 400 }
      )
    }

    // Check if owner email already exists
    const existingUser = await prisma.user.findFirst({
      where: { email: ownerEmail }
    })

    if (existingUser) {
      return NextResponse.json(
        { error: 'Owner email already exists' },
        { status: 400 }
      )
    }

    // Create company and owner in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create company
      const company = await tx.company.create({
        data: {
          name,
          email,
          plan,
          status: 'ACTIVE',
          maxUsers: plan === 'ENTERPRISE' ? 100 : plan === 'PROFESSIONAL' ? 25 : 5,
          features: plan === 'ENTERPRISE' 
            ? ['advanced_analytics', 'custom_branding', 'api_access', 'priority_support']
            : plan === 'PROFESSIONAL'
            ? ['advanced_analytics', 'custom_branding']
            : ['basic_features']
        }
      })

      // Create owner user
      const owner = await tx.user.create({
        data: {
          email: ownerEmail,
          name: ownerName,
          role: 'OWNER',
          companyId: company.id,
          emailVerified: new Date() // Auto-verify for super admin created accounts
        }
      })

      return { company, owner }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'COMPANY',
        title: 'Company created',
        description: `Company "${name}" created by super admin`,
        companyId: result.company.id,
        createdById: session.user.id,
      }
    })

    return NextResponse.json(result.company, { status: 201 })
  } catch (error) {
    console.error('Error creating company:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { companyId, ...updateData } = body
    
    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      )
    }

    const validatedData = companyUpdateSchema.parse(updateData)

    // Check if company exists
    const existingCompany = await prisma.company.findUnique({
      where: { id: companyId }
    })

    if (!existingCompany) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    // Update company
    const updatedCompany = await prisma.company.update({
      where: { id: companyId },
      data: validatedData,
      include: {
        _count: {
          select: {
            users: true,
            customers: true,
            quotations: true,
            invoices: true,
            contracts: true
          }
        }
      }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'COMPANY',
        title: 'Company updated',
        description: `Company "${updatedCompany.name}" updated by super admin`,
        companyId: updatedCompany.id,
        createdById: session.user.id,
      }
    })

    return NextResponse.json(updatedCompany)
  } catch (error) {
    console.error('Error updating company:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const companyId = searchParams.get('companyId')
    
    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      )
    }

    // Check if company exists
    const existingCompany = await prisma.company.findUnique({
      where: { id: companyId },
      include: {
        _count: {
          select: {
            users: true,
            customers: true,
            quotations: true,
            invoices: true,
            contracts: true
          }
        }
      }
    })

    if (!existingCompany) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    // Check if company has data (prevent accidental deletion)
    const hasData = existingCompany._count.users > 0 || 
                   existingCompany._count.customers > 0 || 
                   existingCompany._count.quotations > 0 || 
                   existingCompany._count.invoices > 0 || 
                   existingCompany._count.contracts > 0

    if (hasData) {
      return NextResponse.json(
        { error: 'Cannot delete company with existing data. Please contact support for data migration.' },
        { status: 400 }
      )
    }

    // Delete company (cascade will handle related records)
    await prisma.company.delete({
      where: { id: companyId }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'COMPANY',
        title: 'Company deleted',
        description: `Company "${existingCompany.name}" deleted by super admin`,
        companyId: existingCompany.id,
        createdById: session.user.id,
      }
    })

    return NextResponse.json({ message: 'Company deleted successfully' })
  } catch (error) {
    console.error('Error deleting company:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
