'use client'

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { 
  CheckCircle, 
  Circle,
  Clock,
  Rocket,
  Building2,
  Users,
  FileText,
  Sparkles
} from 'lucide-react'

interface OnboardingProgressProps {
  steps: Array<{
    id: string
    title: string
    description: string
    completed: boolean
  }>
  currentStep: string
  progressPercentage: number
}

export function OnboardingProgress({ steps, currentStep, progressPercentage }: OnboardingProgressProps) {
  const getStepIcon = (stepId: string) => {
    switch (stepId) {
      case 'welcome':
        return Rocket
      case 'company-setup':
        return Building2
      case 'first-customer':
        return Users
      case 'first-quotation':
        return FileText
      case 'explore-features':
        return Sparkles
      default:
        return Circle
    }
  }

  const getStepStatus = (step: any) => {
    if (step.completed) return 'completed'
    if (step.id === currentStep) return 'current'
    return 'pending'
  }

  const getStepColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 border-green-200 bg-green-50'
      case 'current':
        return 'text-blue-600 border-blue-200 bg-blue-50'
      case 'pending':
        return 'text-gray-400 border-gray-200 bg-gray-50'
      default:
        return 'text-gray-400 border-gray-200 bg-gray-50'
    }
  }

  const completedStepsCount = steps.filter(step => step.completed).length
  const totalSteps = steps.length - 1 // Exclude the 'complete' step from count

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Onboarding Progress</CardTitle>
          <Badge variant="outline">
            {completedStepsCount} of {totalSteps} completed
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Progress Bar */}
          <div>
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>Progress</span>
              <span>{Math.round(progressPercentage)}%</span>
            </div>
            <Progress value={progressPercentage} className="h-2" />
          </div>

          {/* Steps List */}
          <div className="space-y-4">
            {steps.filter(step => step.id !== 'complete').map((step, index) => {
              const StepIcon = getStepIcon(step.id)
              const status = getStepStatus(step)
              const isLast = index === steps.length - 2 // Account for filtered 'complete' step
              
              return (
                <div key={step.id} className="relative">
                  <div className="flex items-start space-x-4">
                    {/* Step Icon */}
                    <div className={`flex-shrink-0 w-10 h-10 rounded-full border-2 flex items-center justify-center ${getStepColor(status)}`}>
                      {status === 'completed' ? (
                        <CheckCircle className="h-5 w-5" />
                      ) : status === 'current' ? (
                        <Clock className="h-5 w-5" />
                      ) : (
                        <StepIcon className="h-5 w-5" />
                      )}
                    </div>

                    {/* Step Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <h4 className={`font-medium ${
                          status === 'completed' ? 'text-green-900' :
                          status === 'current' ? 'text-blue-900' :
                          'text-gray-500'
                        }`}>
                          {step.title}
                        </h4>
                        {status === 'completed' && (
                          <Badge variant="secondary" className="text-xs">
                            Complete
                          </Badge>
                        )}
                        {status === 'current' && (
                          <Badge className="text-xs">
                            Current
                          </Badge>
                        )}
                      </div>
                      <p className={`text-sm mt-1 ${
                        status === 'completed' ? 'text-green-700' :
                        status === 'current' ? 'text-blue-700' :
                        'text-gray-500'
                      }`}>
                        {step.description}
                      </p>
                    </div>

                    {/* Step Number */}
                    <div className="flex-shrink-0">
                      <div className={`w-6 h-6 rounded-full text-xs font-medium flex items-center justify-center ${
                        status === 'completed' ? 'bg-green-100 text-green-800' :
                        status === 'current' ? 'bg-blue-100 text-blue-800' :
                        'bg-gray-100 text-gray-500'
                      }`}>
                        {index + 1}
                      </div>
                    </div>
                  </div>

                  {/* Connector Line */}
                  {!isLast && (
                    <div className="absolute left-5 top-10 w-0.5 h-6 bg-gray-200"></div>
                  )}
                </div>
              )
            })}
          </div>

          {/* Completion Message */}
          {progressPercentage === 100 && (
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <div>
                  <div className="font-medium text-green-900">
                    Onboarding Complete!
                  </div>
                  <div className="text-sm text-green-700">
                    You're ready to start using BusinessSaaS to its full potential.
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Next Step Hint */}
          {progressPercentage < 100 && (
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-start space-x-2">
                <Clock className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <div className="font-medium text-blue-900">
                    Next: {steps.find(step => step.id === currentStep)?.title}
                  </div>
                  <div className="text-sm text-blue-700">
                    {steps.find(step => step.id === currentStep)?.description}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Quick Stats */}
          <div className="grid grid-cols-3 gap-4 pt-4 border-t">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {completedStepsCount}
              </div>
              <div className="text-xs text-gray-600">Completed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {totalSteps - completedStepsCount}
              </div>
              <div className="text-xs text-gray-600">Remaining</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {Math.round(progressPercentage)}%
              </div>
              <div className="text-xs text-gray-600">Progress</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
