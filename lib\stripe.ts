import Stripe from 'stripe'

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY is not set in environment variables')
}

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2023-10-16',
  typescript: true,
})

// Stripe configuration
export const stripeConfig = {
  publishableKey: process.env.STRIPE_PUBLISHABLE_KEY!,
  secretKey: process.env.STRIPE_SECRET_KEY!,
  webhookSecret: process.env.STRIPE_WEBHOOK_SECRET!,
  currency: 'usd',
  
  // Plan configurations with Stripe Price IDs
  plans: {
    starter: {
      name: 'Starter',
      monthly: {
        priceId: 'price_starter_monthly', // Replace with actual Stripe Price ID
        amount: 2900, // $29.00 in cents
      },
      yearly: {
        priceId: 'price_starter_yearly', // Replace with actual Stripe Price ID
        amount: 29000, // $290.00 in cents
      }
    },
    professional: {
      name: 'Professional',
      monthly: {
        priceId: 'price_professional_monthly', // Replace with actual Stripe Price ID
        amount: 7900, // $79.00 in cents
      },
      yearly: {
        priceId: 'price_professional_yearly', // Replace with actual Stripe Price ID
        amount: 79000, // $790.00 in cents
      }
    },
    enterprise: {
      name: 'Enterprise',
      monthly: {
        priceId: 'price_enterprise_monthly', // Replace with actual Stripe Price ID
        amount: 19900, // $199.00 in cents
      },
      yearly: {
        priceId: 'price_enterprise_yearly', // Replace with actual Stripe Price ID
        amount: 199000, // $1990.00 in cents
      }
    }
  }
}

// Helper function to get price ID
export function getStripePriceId(plan: string, interval: 'monthly' | 'yearly'): string {
  const planConfig = stripeConfig.plans[plan as keyof typeof stripeConfig.plans]
  if (!planConfig) {
    throw new Error(`Invalid plan: ${plan}`)
  }
  
  return planConfig[interval].priceId
}

// Helper function to get price amount
export function getStripeAmount(plan: string, interval: 'monthly' | 'yearly'): number {
  const planConfig = stripeConfig.plans[plan as keyof typeof stripeConfig.plans]
  if (!planConfig) {
    throw new Error(`Invalid plan: ${plan}`)
  }
  
  return planConfig[interval].amount
}

// Helper function to format amount for display
export function formatStripeAmount(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: stripeConfig.currency.toUpperCase(),
  }).format(amount / 100)
}

// Create Stripe customer
export async function createStripeCustomer(data: {
  email: string
  name: string
  companyName?: string
}): Promise<Stripe.Customer> {
  return await stripe.customers.create({
    email: data.email,
    name: data.name,
    metadata: {
      companyName: data.companyName || '',
    },
  })
}

// Create Stripe checkout session
export async function createCheckoutSession(data: {
  customerId: string
  priceId: string
  successUrl: string
  cancelUrl: string
  companyId: string
  userId: string
}): Promise<Stripe.Checkout.Session> {
  return await stripe.checkout.sessions.create({
    customer: data.customerId,
    payment_method_types: ['card'],
    line_items: [
      {
        price: data.priceId,
        quantity: 1,
      },
    ],
    mode: 'subscription',
    success_url: data.successUrl,
    cancel_url: data.cancelUrl,
    metadata: {
      companyId: data.companyId,
      userId: data.userId,
    },
    subscription_data: {
      metadata: {
        companyId: data.companyId,
        userId: data.userId,
      },
    },
  })
}

// Create Stripe portal session
export async function createPortalSession(data: {
  customerId: string
  returnUrl: string
}): Promise<Stripe.BillingPortal.Session> {
  return await stripe.billingPortal.sessions.create({
    customer: data.customerId,
    return_url: data.returnUrl,
  })
}

// Retrieve Stripe subscription
export async function getStripeSubscription(subscriptionId: string): Promise<Stripe.Subscription> {
  return await stripe.subscriptions.retrieve(subscriptionId)
}

// Cancel Stripe subscription
export async function cancelStripeSubscription(subscriptionId: string): Promise<Stripe.Subscription> {
  return await stripe.subscriptions.cancel(subscriptionId)
}

// Update Stripe subscription
export async function updateStripeSubscription(
  subscriptionId: string,
  data: {
    priceId: string
  }
): Promise<Stripe.Subscription> {
  const subscription = await stripe.subscriptions.retrieve(subscriptionId)
  
  return await stripe.subscriptions.update(subscriptionId, {
    items: [
      {
        id: subscription.items.data[0].id,
        price: data.priceId,
      },
    ],
    proration_behavior: 'create_prorations',
  })
}

// Verify webhook signature
export function verifyWebhookSignature(
  payload: string | Buffer,
  signature: string,
  secret: string
): Stripe.Event {
  return stripe.webhooks.constructEvent(payload, signature, secret)
}

// Get upcoming invoice
export async function getUpcomingInvoice(customerId: string): Promise<Stripe.Invoice> {
  return await stripe.invoices.retrieveUpcoming({
    customer: customerId,
  })
}

// List payment methods
export async function listPaymentMethods(customerId: string): Promise<Stripe.PaymentMethod[]> {
  const paymentMethods = await stripe.paymentMethods.list({
    customer: customerId,
    type: 'card',
  })
  
  return paymentMethods.data
}

// Detach payment method
export async function detachPaymentMethod(paymentMethodId: string): Promise<Stripe.PaymentMethod> {
  return await stripe.paymentMethods.detach(paymentMethodId)
}

export default stripe
