import Stripe from 'stripe'

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY is not set in environment variables')
}

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2023-10-16',
  typescript: true,
})

// Stripe configuration
export const stripeConfig = {
  publishableKey: process.env.STRIPE_PUBLISHABLE_KEY || '',
  secretKey: process.env.STRIPE_SECRET_KEY || '',
  webhookSecret: process.env.STRIPE_WEBHOOK_SECRET || '',
  currency: 'usd',
  
  // Plan configurations with Stripe Price IDs
  plans: {
    starter: {
      name: 'Starter',
      monthly: {
        priceId: 'price_starter_monthly', // Replace with actual Stripe Price ID
        amount: 2900, // $29.00 in cents
        amountINR: 240000, // ₹2,400 in paise (approx $29)
      },
      yearly: {
        priceId: 'price_starter_yearly', // Replace with actual Stripe Price ID
        amount: 29000, // $290.00 in cents
        amountINR: 2400000, // ₹24,000 in paise (approx $290)
      }
    },
    professional: {
      name: 'Professional',
      monthly: {
        priceId: 'price_professional_monthly', // Replace with actual Stripe Price ID
        amount: 7900, // $79.00 in cents
        amountINR: 650000, // ₹6,500 in paise (approx $79)
      },
      yearly: {
        priceId: 'price_professional_yearly', // Replace with actual Stripe Price ID
        amount: 79000, // $790.00 in cents
        amountINR: 6500000, // ₹65,000 in paise (approx $790)
      }
    },
    enterprise: {
      name: 'Enterprise',
      monthly: {
        priceId: 'price_enterprise_monthly', // Replace with actual Stripe Price ID
        amount: 19900, // $199.00 in cents
        amountINR: 1650000, // ₹16,500 in paise (approx $199)
      },
      yearly: {
        priceId: 'price_enterprise_yearly', // Replace with actual Stripe Price ID
        amount: 199000, // $1990.00 in cents
        amountINR: ********, // ₹1,65,000 in paise (approx $1990)
      }
    }
  },

  // Payment method configurations
  paymentMethods: {
    global: ['card'],
    india: ['card'], // Note: UPI, netbanking, wallet support depends on Stripe configuration
    us: ['card'],
    eu: ['card'],
  },

  // Supported currencies
  currencies: {
    usd: 'USD',
    inr: 'INR',
    eur: 'EUR',
    gbp: 'GBP',
  }
}

// Helper function to get price ID
export function getStripePriceId(plan: string, interval: 'monthly' | 'yearly'): string {
  const planConfig = stripeConfig.plans[plan as keyof typeof stripeConfig.plans]
  if (!planConfig) {
    throw new Error(`Invalid plan: ${plan}`)
  }
  
  return planConfig[interval].priceId
}

// Helper function to get price amount
export function getStripeAmount(plan: string, interval: 'monthly' | 'yearly', currency: 'USD' | 'INR' = 'USD'): number {
  const planConfig = stripeConfig.plans[plan as keyof typeof stripeConfig.plans]
  if (!planConfig) {
    throw new Error(`Invalid plan: ${plan}`)
  }

  if (currency === 'INR') {
    return planConfig[interval].amountINR
  }

  return planConfig[interval].amount
}

// Helper function to get payment methods by country
export function getPaymentMethodsByCountry(country: string): string[] {
  switch (country.toLowerCase()) {
    case 'in':
    case 'india':
      return stripeConfig.paymentMethods.india
    case 'us':
    case 'usa':
    case 'united states':
      return stripeConfig.paymentMethods.us
    case 'eu':
    case 'europe':
      return stripeConfig.paymentMethods.eu
    default:
      return stripeConfig.paymentMethods.global
  }
}

// Helper function to get currency by country
export function getCurrencyByCountry(country: string): string {
  switch (country.toLowerCase()) {
    case 'in':
    case 'india':
      return 'inr'
    case 'us':
    case 'usa':
    case 'united states':
      return 'usd'
    case 'eu':
    case 'europe':
      return 'eur'
    case 'gb':
    case 'uk':
    case 'united kingdom':
      return 'gbp'
    default:
      return 'usd'
  }
}

// Helper function to format amount for display
export function formatStripeAmount(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: stripeConfig.currency.toUpperCase(),
  }).format(amount / 100)
}

// Create Stripe customer
export async function createStripeCustomer(data: {
  email: string
  name: string
  companyName?: string
}): Promise<Stripe.Customer> {
  return await stripe.customers.create({
    email: data.email,
    name: data.name,
    metadata: {
      companyName: data.companyName || '',
    },
  })
}

// Create Stripe checkout session
export async function createCheckoutSession(data: {
  customerId: string
  priceId: string
  successUrl: string
  cancelUrl: string
  companyId: string
  userId: string
  paymentMethods?: string[]
  currency?: string
  country?: string
}): Promise<Stripe.Checkout.Session> {
  // Determine payment methods and currency based on country
  const country = data.country || 'us'
  const paymentMethods = data.paymentMethods || getPaymentMethodsByCountry(country)
  const currency = data.currency || getCurrencyByCountry(country)

  const sessionConfig: Stripe.Checkout.SessionCreateParams = {
    customer: data.customerId,
    payment_method_types: paymentMethods,
    line_items: [
      {
        price: data.priceId,
        quantity: 1,
      },
    ],
    mode: 'subscription',
    success_url: data.successUrl,
    cancel_url: data.cancelUrl,
    metadata: {
      companyId: data.companyId,
      userId: data.userId,
      country: country,
    },
    subscription_data: {
      metadata: {
        companyId: data.companyId,
        userId: data.userId,
        country: country,
      },
    },
  }

  // Add currency if not USD (Stripe default)
  if (currency !== 'usd') {
    sessionConfig.currency = currency
  }

  // Enable automatic tax calculation for supported countries
  if (['in', 'us', 'eu'].includes(country.toLowerCase())) {
    sessionConfig.automatic_tax = {
      enabled: true,
    }
  }

  // Add Indian-specific configurations
  if (country.toLowerCase() === 'in' || country.toLowerCase() === 'india') {
    // Note: These options may not be available in all Stripe versions
    // sessionConfig.payment_method_options = {
    //   upi: {
    //     preferred_language: 'en',
    //   },
    //   netbanking: {
    //     preferred_language: 'en',
    //   },
    // }
  }

  return await stripe.checkout.sessions.create(sessionConfig)
}

// Create Stripe portal session
export async function createPortalSession(data: {
  customerId: string
  returnUrl: string
}): Promise<Stripe.BillingPortal.Session> {
  return await stripe.billingPortal.sessions.create({
    customer: data.customerId,
    return_url: data.returnUrl,
  })
}

// Retrieve Stripe subscription
export async function getStripeSubscription(subscriptionId: string): Promise<Stripe.Subscription> {
  return await stripe.subscriptions.retrieve(subscriptionId)
}

// Cancel Stripe subscription
export async function cancelStripeSubscription(subscriptionId: string): Promise<Stripe.Subscription> {
  return await stripe.subscriptions.cancel(subscriptionId)
}

// Update Stripe subscription
export async function updateStripeSubscription(
  subscriptionId: string,
  data: {
    priceId: string
  }
): Promise<Stripe.Subscription> {
  const subscription = await stripe.subscriptions.retrieve(subscriptionId)
  
  return await stripe.subscriptions.update(subscriptionId, {
    items: [
      {
        id: subscription.items.data[0].id,
        price: data.priceId,
      },
    ],
    proration_behavior: 'create_prorations',
  })
}

// Verify webhook signature
export function verifyWebhookSignature(
  payload: string | Buffer,
  signature: string,
  secret: string
): Stripe.Event {
  return stripe.webhooks.constructEvent(payload, signature, secret)
}

// Get upcoming invoice
// Note: This function is commented out due to TypeScript compatibility issues
// export async function getUpcomingInvoice(customerId: string) {
//   try {
//     return await stripe.invoices.upcoming({
//       customer: customerId,
//     })
//   } catch (error) {
//     console.error('Error retrieving upcoming invoice:', error)
//     return null
//   }
// }

// List payment methods
export async function listPaymentMethods(customerId: string): Promise<Stripe.PaymentMethod[]> {
  const paymentMethods = await stripe.paymentMethods.list({
    customer: customerId,
    type: 'card',
  })
  
  return paymentMethods.data
}

// Detach payment method
export async function detachPaymentMethod(paymentMethodId: string): Promise<Stripe.PaymentMethod> {
  return await stripe.paymentMethods.detach(paymentMethodId)
}

export default stripe
