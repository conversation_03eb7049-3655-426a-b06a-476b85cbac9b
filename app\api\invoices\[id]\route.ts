import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const invoiceItemSchema = z.object({
  itemId: z.string().optional(),
  name: z.string().min(1, 'Item name is required'),
  description: z.string().optional(),
  quantity: z.number().min(0.01, 'Quantity must be greater than 0'),
  unitPrice: z.number().min(0, 'Unit price must be non-negative'),
  discount: z.number().min(0).max(100).default(0),
  taxRate: z.number().min(0).max(100).default(18),
})

const invoiceSchema = z.object({
  invoiceNumber: z.string().min(1, 'Invoice number is required'),
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  customerId: z.string().min(1, 'Customer is required'),
  quotationId: z.string().optional(),
  dueDate: z.date().optional(),
  terms: z.string().optional(),
  paymentTerms: z.string().optional(),
  taxRate: z.number().min(0).max(100).default(18),
  discountType: z.enum(['PERCENTAGE', 'FIXED']).default('PERCENTAGE'),
  discountValue: z.number().min(0).default(0),
  subtotal: z.number().min(0),
  total: z.number().min(0),
  items: z.array(invoiceItemSchema).min(1, 'At least one item is required'),
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const invoice = await prisma.invoice.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      },
      include: {
        customer: {
          select: { 
            id: true, 
            name: true, 
            email: true, 
            company: true, 
            phone: true,
            address: true,
            city: true,
            state: true,
            country: true,
            postalCode: true
          }
        },
        quotation: {
          select: { id: true, quotationNumber: true, title: true }
        },
        items: {
          include: {
            item: {
              select: { name: true, category: true, unit: true }
            }
          }
        },
        activities: {
          include: {
            createdBy: {
              select: { name: true, firstName: true, lastName: true }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: 15,
        },
        _count: {
          select: {
            items: true,
            activities: true,
          }
        }
      }
    })

    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 })
    }

    return NextResponse.json(invoice)
  } catch (error) {
    console.error('Error fetching invoice:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = invoiceSchema.parse(body)

    // Check if invoice exists and belongs to the company
    const existingInvoice = await prisma.invoice.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      }
    })

    if (!existingInvoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 })
    }

    // Check if invoice can be edited (only DRAFT invoices can be fully edited)
    if (existingInvoice.status !== 'DRAFT') {
      return NextResponse.json(
        { error: 'Only draft invoices can be edited' },
        { status: 400 }
      )
    }

    // Verify customer belongs to the company
    const customer = await prisma.customer.findFirst({
      where: {
        id: validatedData.customerId,
        companyId: session.user.companyId,
      }
    })

    if (!customer) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 400 }
      )
    }

    // Verify quotation belongs to the company (if provided)
    if (validatedData.quotationId) {
      const quotation = await prisma.quotation.findFirst({
        where: {
          id: validatedData.quotationId,
          companyId: session.user.companyId,
        }
      })

      if (!quotation) {
        return NextResponse.json(
          { error: 'Quotation not found' },
          { status: 400 }
        )
      }
    }

    // Check if invoice number conflicts with another invoice
    if (validatedData.invoiceNumber !== existingInvoice.invoiceNumber) {
      const conflictingInvoice = await prisma.invoice.findFirst({
        where: {
          invoiceNumber: validatedData.invoiceNumber,
          companyId: session.user.companyId,
          id: { not: params.id },
        }
      })

      if (conflictingInvoice) {
        return NextResponse.json(
          { error: 'Invoice number already exists' },
          { status: 400 }
        )
      }
    }

    const invoice = await prisma.invoice.update({
      where: { id: params.id },
      data: {
        invoiceNumber: validatedData.invoiceNumber,
        title: validatedData.title,
        description: validatedData.description,
        dueDate: validatedData.dueDate,
        terms: validatedData.terms,
        paymentTerms: validatedData.paymentTerms,
        taxRate: validatedData.taxRate,
        discountType: validatedData.discountType,
        discountValue: validatedData.discountValue,
        subtotal: validatedData.subtotal,
        total: validatedData.total,
        customerId: validatedData.customerId,
        quotationId: validatedData.quotationId,
        items: {
          deleteMany: {},
          create: validatedData.items.map(item => ({
            itemId: item.itemId,
            name: item.name,
            description: item.description,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            discount: item.discount,
            taxRate: item.taxRate,
          }))
        }
      },
      include: {
        customer: {
          select: { id: true, name: true, email: true, company: true }
        },
        quotation: {
          select: { id: true, quotationNumber: true, title: true }
        },
        items: {
          include: {
            item: {
              select: { name: true, category: true }
            }
          }
        },
        _count: {
          select: {
            items: true,
            activities: true,
          }
        }
      }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'NOTE',
        title: 'Invoice updated',
        description: `Invoice "${invoice.invoiceNumber}" was updated`,
        invoiceId: invoice.id,
        customerId: invoice.customerId,
        quotationId: invoice.quotationId,
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    return NextResponse.json(invoice)
  } catch (error) {
    console.error('Error updating invoice:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if invoice exists and belongs to the company
    const invoice = await prisma.invoice.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      }
    })

    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 })
    }

    // Check if invoice can be deleted (only DRAFT invoices can be deleted)
    if (invoice.status !== 'DRAFT') {
      return NextResponse.json(
        { error: 'Only draft invoices can be deleted' },
        { status: 400 }
      )
    }

    await prisma.invoice.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: 'Invoice deleted successfully' })
  } catch (error) {
    console.error('Error deleting invoice:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
