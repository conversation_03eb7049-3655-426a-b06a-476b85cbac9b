'use client'

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card'
import { 
  FileText, 
  CheckCircle, 
  Edit, 
  Archive,
  TrendingUp,
  Users,
  Calendar,
  BarChart3
} from 'lucide-react'

interface TemplateStatsProps {
  stats: {
    totalTemplates: number
    templatesByStatus: Array<{
      status: string
      _count: number
    }>
    templatesByCategory: Array<{
      category: string | null
      _count: number
    }>
    templateUsage: Array<{
      templateId: string
      _count: number
    }>
    recentTemplatesCount: number
    activeTemplates: number
    draftTemplates: number
  }
}

export function TemplateStats({ stats }: TemplateStatsProps) {
  const archivedTemplates = stats.templatesByStatus.find(s => s.status === 'ARCHIVED')?._count || 0
  const totalUsage = stats.templateUsage.reduce((sum, usage) => sum + usage._count, 0)
  const averageUsage = stats.totalTemplates > 0 ? (totalUsage / stats.totalTemplates).toFixed(1) : '0'

  const overviewCards = [
    {
      title: 'Total Templates',
      value: stats.totalTemplates.toString(),
      icon: FileText,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      description: 'All contract templates'
    },
    {
      title: 'Active Templates',
      value: stats.activeTemplates.toString(),
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      description: 'Ready for use'
    },
    {
      title: 'Draft Templates',
      value: stats.draftTemplates.toString(),
      icon: Edit,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
      description: 'In development'
    },
    {
      title: 'Total Usage',
      value: totalUsage.toString(),
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      description: 'Contracts generated'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {overviewCards.map((card, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {card.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${card.bgColor}`}>
                <card.icon className={`h-4 w-4 ${card.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">
                {card.value}
              </div>
              <p className="text-xs text-gray-500 mt-1">
                {card.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Detailed Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Status Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle>Template Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <div>
                    <p className="font-medium text-green-800">Active</p>
                    <p className="text-sm text-green-600">
                      Ready for contract generation
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-bold text-green-800">{stats.activeTemplates}</p>
                  <p className="text-xs text-green-600">
                    {stats.totalTemplates > 0 ? ((stats.activeTemplates / stats.totalTemplates) * 100).toFixed(1) : '0'}%
                  </p>
                </div>
              </div>

              <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <Edit className="h-5 w-5 text-yellow-600" />
                  <div>
                    <p className="font-medium text-yellow-800">Draft</p>
                    <p className="text-sm text-yellow-600">
                      Under development
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-bold text-yellow-800">{stats.draftTemplates}</p>
                  <p className="text-xs text-yellow-600">
                    {stats.totalTemplates > 0 ? ((stats.draftTemplates / stats.totalTemplates) * 100).toFixed(1) : '0'}%
                  </p>
                </div>
              </div>

              {archivedTemplates > 0 && (
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Archive className="h-5 w-5 text-gray-600" />
                    <div>
                      <p className="font-medium text-gray-800">Archived</p>
                      <p className="text-sm text-gray-600">
                        No longer in use
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-gray-800">{archivedTemplates}</p>
                    <p className="text-xs text-gray-600">
                      {stats.totalTemplates > 0 ? ((archivedTemplates / stats.totalTemplates) * 100).toFixed(1) : '0'}%
                    </p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Categories */}
        <Card>
          <CardHeader>
            <CardTitle>Template Categories</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats.templatesByCategory.length > 0 ? (
                stats.templatesByCategory.map((category, index) => {
                  const percentage = stats.totalTemplates > 0 
                    ? ((category._count / stats.totalTemplates) * 100).toFixed(1)
                    : '0'

                  return (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 rounded-lg bg-blue-100">
                          <FileText className="h-4 w-4 text-blue-600" />
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">
                            {category.category || 'Uncategorized'}
                          </p>
                          <p className="text-sm text-gray-600">
                            {category._count} templates
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-bold text-gray-900">{percentage}%</p>
                      </div>
                    </div>
                  )
                })
              ) : (
                <div className="text-center py-6">
                  <FileText className="h-8 w-8 text-gray-300 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">No categories found</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Usage Insights */}
      <Card>
        <CardHeader>
          <CardTitle>Usage Insights</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-800">
                {totalUsage}
              </div>
              <div className="text-sm text-blue-600">Total Contracts</div>
              <div className="text-xs text-blue-500 mt-1">
                Generated from templates
              </div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-800">
                {averageUsage}
              </div>
              <div className="text-sm text-green-600">Avg. Usage</div>
              <div className="text-xs text-green-500 mt-1">
                Per template
              </div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-800">
                {stats.recentTemplatesCount}
              </div>
              <div className="text-sm text-purple-600">Recent Templates</div>
              <div className="text-xs text-purple-500 mt-1">
                Last 30 days
              </div>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-800">
                {stats.templateUsage.length}
              </div>
              <div className="text-sm text-orange-600">Used Templates</div>
              <div className="text-xs text-orange-500 mt-1">
                Have generated contracts
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
