# Business Management SaaS

A complete business management solution built with Next.js, featuring quotations, invoices, contracts, customer management, and more.

## 🚀 Features

- **Customer Management** - Complete CRM with customer profiles and interaction history
- **Lead Management** - Track and convert leads with pipeline management
- **Quotation System** - Create professional quotations with AI-powered content generation
- **Invoice Management** - Generate, send, and track invoices with payment status
- **Contract Handling** - Manage contracts from creation to completion
- **Item Catalog** - Centralized product/service management
- **Activity Tracking** - Complete audit trail of all business activities
- **Dashboard Analytics** - Real-time insights and business metrics
- **Multi-tenant** - Support for multiple companies with role-based access
- **Responsive Design** - Works perfectly on desktop, tablet, and mobile

## 🛠️ Tech Stack

- **Framework**: Next.js 14 (App Router)
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth.js
- **UI Components**: Radix UI + Tailwind CSS
- **Forms**: React Hook Form + Zod validation
- **Styling**: Tailwind CSS
- **Icons**: Lucide React
- **Notifications**: React Hot Toast
- **PDF Generation**: Puppeteer
- **TypeScript**: Full type safety

## 📋 Prerequisites

- Node.js 18+ 
- PostgreSQL database
- npm or yarn package manager

## 🚀 Quick Start

### 1. Clone and Install

```bash
git clone <repository-url>
cd nextjs-saas-production
npm install
```

### 2. Database Setup

```bash
# Create PostgreSQL database
createdb nextjs_saas_production

# Update DATABASE_URL in .env file
DATABASE_URL="postgresql://username:password@localhost:5432/nextjs_saas_production"

# Generate Prisma client and push schema
npm run db:generate
npm run db:push

# Seed database with sample data
npm run db:seed
```

### 3. Environment Configuration

Copy `.env.example` to `.env` and update the values:

```bash
cp .env.example .env
```

Required environment variables:
- `DATABASE_URL` - PostgreSQL connection string
- `NEXTAUTH_URL` - Your app URL (http://localhost:3000 for development)
- `NEXTAUTH_SECRET` - Random secret for JWT signing

### 4. Start Development Server

```bash
npm run dev
```

Visit [http://localhost:3000](http://localhost:3000) to see the application.

## 🔐 Demo Credentials

After running the seed script, you can login with:

- **Email**: <EMAIL>
- **Password**: password123

## 📁 Project Structure

```
nextjs-saas-production/
├── app/                    # Next.js app directory
│   ├── api/               # API routes
│   ├── auth/              # Authentication pages
│   ├── dashboard/         # Dashboard pages
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Landing page
├── components/            # React components
│   ├── dashboard/         # Dashboard components
│   ├── landing/           # Landing page components
│   └── ui/                # Reusable UI components
├── lib/                   # Utility libraries
│   ├── auth.ts            # NextAuth configuration
│   ├── prisma.ts          # Prisma client
│   └── utils.ts           # Utility functions
├── prisma/                # Database schema and migrations
│   ├── schema.prisma      # Database schema
│   └── seed.ts            # Database seeding
└── public/                # Static assets
```

## 🗄️ Database Schema

The application uses a comprehensive database schema with the following main entities:

- **Users** - System users with role-based access
- **Companies** - Multi-tenant company management
- **Customers** - Customer profiles and contact information
- **Leads** - Sales pipeline and lead tracking
- **Items** - Product/service catalog
- **Quotations** - Professional quotation management
- **Invoices** - Invoice generation and tracking
- **Contracts** - Contract lifecycle management
- **Activities** - Audit trail and activity logging

## 🔧 Available Scripts

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint

# Database
npm run db:generate  # Generate Prisma client
npm run db:push      # Push schema to database
npm run db:migrate   # Run database migrations
npm run db:studio    # Open Prisma Studio
npm run db:seed      # Seed database with sample data
```

## 🚀 Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically

### Docker

```bash
# Build Docker image
docker build -t nextjs-saas .

# Run container
docker run -p 3000:3000 nextjs-saas
```

### Manual Deployment

```bash
# Build the application
npm run build

# Start production server
npm run start
```

## 🔒 Security Features

- **Authentication** - Secure JWT-based authentication
- **Authorization** - Role-based access control
- **Data Validation** - Comprehensive input validation with Zod
- **SQL Injection Protection** - Prisma ORM prevents SQL injection
- **CSRF Protection** - Built-in CSRF protection
- **Environment Variables** - Secure configuration management

## 📊 Business Features

### Customer Management
- Complete customer profiles
- Contact information and history
- Customer status tracking
- Activity timeline

### Lead Management
- Lead pipeline with stages
- Priority and value tracking
- Lead source attribution
- Conversion tracking

### Quotation System
- Professional quotation templates
- Item-based pricing
- Tax and discount calculations
- PDF generation
- Status tracking (Draft, Sent, Viewed, Accepted, Rejected)

### Invoice Management
- Invoice generation from quotations
- Payment tracking
- Due date management
- Automated calculations

### Contract Management
- Contract templates
- Digital signatures
- Milestone tracking
- Renewal management

## 🎨 Customization

### Branding
- Update logo in `components/landing/landing-page.tsx`
- Modify colors in `tailwind.config.ts`
- Update company information in environment variables

### Features
- Add new modules in `app/dashboard/`
- Extend database schema in `prisma/schema.prisma`
- Create new API routes in `app/api/`

## 🐛 Troubleshooting

### Database Connection Issues
```bash
# Check PostgreSQL is running
pg_isready

# Verify database exists
psql -l | grep nextjs_saas_production

# Reset database
npm run db:push --force-reset
npm run db:seed
```

### Authentication Issues
```bash
# Clear Next.js cache
rm -rf .next

# Verify NEXTAUTH_SECRET is set
echo $NEXTAUTH_SECRET
```

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For support and questions:
- Email: <EMAIL>
- Documentation: [Link to docs]
- Issues: [GitHub Issues]

## 🔄 Updates

This is a production-ready application with:
- ✅ Complete authentication system
- ✅ Multi-tenant architecture
- ✅ Comprehensive business modules
- ✅ Professional UI/UX
- ✅ Database seeding
- ✅ Type safety
- ✅ Error handling
- ✅ Responsive design
- ✅ Production optimizations

Ready for immediate deployment and customization!
