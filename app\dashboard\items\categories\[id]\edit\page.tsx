import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { notFound } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Tag } from 'lucide-react'
import { CategoryForm } from '@/components/items/category-form'

interface PageProps {
  params: {
    id: string
  }
}

export default async function EditCategoryPage({ params }: PageProps) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  // Fetch the category to edit
  const category = await prisma.itemCategory.findFirst({
    where: {
      id: params.id,
      companyId: session.user.companyId
    },
    include: {
      parent: {
        select: { id: true, name: true }
      }
    }
  })

  if (!category) {
    notFound()
  }

  // Fetch all categories for parent selection (excluding current category and its children)
  const categories = await prisma.itemCategory.findMany({
    where: {
      companyId: session.user.companyId,
      isActive: true,
      id: { not: params.id }, // Exclude current category
      parentId: { not: params.id } // Exclude children of current category
    },
    select: {
      id: true,
      name: true,
      parentId: true
    },
    orderBy: { name: 'asc' }
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href={`/dashboard/items/categories/${category.id}`}>
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Category
          </Button>
        </Link>
        <div className="p-2 bg-blue-100 rounded-lg">
          <Tag className="h-6 w-6 text-blue-600" />
        </div>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Edit Category</h1>
          <p className="text-gray-600 mt-1">
            Modify "{category.name}" category
          </p>
        </div>
      </div>

      {/* Category Form */}
      <CategoryForm 
        mode="edit" 
        category={category}
        categories={categories}
      />
    </div>
  )
}
