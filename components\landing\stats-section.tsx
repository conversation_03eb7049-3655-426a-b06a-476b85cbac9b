'use client'

import { 
  Users,
  TrendingUp,
  Globe,
  Award,
  Clock,
  Shield
} from 'lucide-react'

export function StatsSection() {
  const stats = [
    {
      icon: Users,
      value: '10,000+',
      label: 'Active Users',
      description: 'Businesses worldwide',
      color: 'text-blue-600'
    },
    {
      icon: TrendingUp,
      value: '$2M+',
      label: 'Revenue Processed',
      description: 'Through our platform',
      color: 'text-green-600'
    },
    {
      icon: Globe,
      value: '50+',
      label: 'Countries',
      description: 'Global presence',
      color: 'text-purple-600'
    },
    {
      icon: Award,
      value: '99.9%',
      label: 'Uptime SLA',
      description: 'Reliable service',
      color: 'text-orange-600'
    },
    {
      icon: Clock,
      value: '24/7',
      label: 'Support',
      description: 'Always available',
      color: 'text-red-600'
    },
    {
      icon: Shield,
      value: '100%',
      label: 'Secure',
      description: 'Enterprise-grade',
      color: 'text-indigo-600'
    }
  ]

  return (
    <section className="py-16 px-4 bg-white border-b">
      <div className="container mx-auto">
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8">
          {stats.map((stat, index) => (
            <div key={index} className="text-center group">
              <div className="mb-4">
                <div className="w-12 h-12 mx-auto bg-gray-50 rounded-xl flex items-center justify-center group-hover:bg-gray-100 transition-colors">
                  <stat.icon className={`h-6 w-6 ${stat.color}`} />
                </div>
              </div>
              <div className="text-2xl md:text-3xl font-bold text-gray-900 mb-1">
                {stat.value}
              </div>
              <div className="font-semibold text-gray-900 mb-1">
                {stat.label}
              </div>
              <div className="text-sm text-gray-600">
                {stat.description}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
