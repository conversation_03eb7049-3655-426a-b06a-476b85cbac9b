import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { UserSettings } from '@/components/settings/user-settings'
import { Button } from '@/components/ui/button'
import { Settings, User, Shield, Bell, CreditCard } from 'lucide-react'
import Link from 'next/link'

export default async function UserSettingsPage() {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.id) {
    return <div>Error: No user found</div>
  }

  // Fetch user data and preferences
  const [user, company, notificationPreferences] = await Promise.all([
    prisma.user.findUnique({
      where: { id: session.user.id },
      include: {
        company: {
          include: {
            subscriptions: true
          }
        }
      }
    }),

    prisma.company.findUnique({
      where: { id: session.user.companyId! },
      include: {
        subscriptions: true,
        users: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      }
    }),

    prisma.notificationPreference.findFirst({
      where: { userId: session.user.id }
    })
  ])

  if (!user || !company) {
    return <div>Error: User or company not found</div>
  }

  const userSettings = {
    profile: {
      name: user.name || '',
      firstName: user.firstName || '',
      lastName: user.lastName || '',
      email: user.email || '',
      role: user.role,
      avatar: user.image || ''
    },
    company: {
      name: company.name,
      plan: company.plan,
      status: company.status,
      userCount: company.users.length,
      subscription: company.subscription
    },
    notifications: notificationPreferences || {
      email: true,
      push: true,
      sms: false,
      marketing: false,
      security: true,
      updates: true
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="p-3 bg-blue-100 rounded-lg">
            <Settings className="h-8 w-8 text-blue-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
            <p className="text-gray-600">Manage your account and preferences</p>
          </div>
        </div>
      </div>

      {/* Quick Navigation */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Link href="#profile">
          <div className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer">
            <div className="flex items-center space-x-3">
              <User className="h-5 w-5 text-blue-600" />
              <div>
                <h3 className="font-medium">Profile</h3>
                <p className="text-sm text-gray-500">Personal information</p>
              </div>
            </div>
          </div>
        </Link>

        <Link href="#security">
          <div className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer">
            <div className="flex items-center space-x-3">
              <Shield className="h-5 w-5 text-green-600" />
              <div>
                <h3 className="font-medium">Security</h3>
                <p className="text-sm text-gray-500">Password & 2FA</p>
              </div>
            </div>
          </div>
        </Link>

        <Link href="/dashboard/notifications/settings">
          <div className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer">
            <div className="flex items-center space-x-3">
              <Bell className="h-5 w-5 text-purple-600" />
              <div>
                <h3 className="font-medium">Notifications</h3>
                <p className="text-sm text-gray-500">Email & push settings</p>
              </div>
            </div>
          </div>
        </Link>

        <Link href="/dashboard/billing">
          <div className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer">
            <div className="flex items-center space-x-3">
              <CreditCard className="h-5 w-5 text-orange-600" />
              <div>
                <h3 className="font-medium">Billing</h3>
                <p className="text-sm text-gray-500">Subscription & payments</p>
              </div>
            </div>
          </div>
        </Link>
      </div>

      {/* Settings Form */}
      <UserSettings 
        userId={session.user.id}
        initialSettings={userSettings}
      />
    </div>
  )
}
