'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Loader2, 
  Save, 
  X, 
  FileText,
  Code,
  Eye,
  Plus,
  Trash2,
  Info
} from 'lucide-react'
import toast from 'react-hot-toast'

const templateSchema = z.object({
  name: z.string().min(1, 'Template name is required'),
  description: z.string().optional(),
  category: z.string().optional(),
  status: z.enum(['ACTIVE', 'DRAFT', 'ARCHIVED']).default('DRAFT'),
  content: z.string().min(1, 'Template content is required'),
  variables: z.array(z.object({
    name: z.string().min(1, 'Variable name is required'),
    label: z.string().min(1, 'Variable label is required'),
    type: z.enum(['TEXT', 'NUMBER', 'DATE', 'BOOLEAN', 'EMAIL', 'PHONE']),
    required: z.boolean().default(false),
    defaultValue: z.string().optional(),
    description: z.string().optional(),
  })).default([]),
})

type TemplateFormData = z.infer<typeof templateSchema>

interface TemplateFormProps {
  mode: 'create' | 'edit'
  template?: any
}

export function TemplateForm({ mode, template }: TemplateFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [showPreview, setShowPreview] = useState(false)
  const [variables, setVariables] = useState<any[]>(template?.variables || [])
  const router = useRouter()

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
  } = useForm<TemplateFormData>({
    resolver: zodResolver(templateSchema),
    defaultValues: template ? {
      name: template.name || '',
      description: template.description || '',
      category: template.category || '',
      status: template.status || 'DRAFT',
      content: template.content || '',
      variables: template.variables || [],
    } : {
      name: '',
      description: '',
      category: '',
      status: 'DRAFT',
      content: '',
      variables: [],
    }
  })

  const watchedContent = watch('content')

  // Update variables in form when local state changes
  useEffect(() => {
    setValue('variables', variables)
  }, [variables, setValue])

  const addVariable = () => {
    const newVariable = {
      name: '',
      label: '',
      type: 'TEXT' as const,
      required: false,
      defaultValue: '',
      description: '',
    }
    setVariables([...variables, newVariable])
  }

  const updateVariable = (index: number, field: string, value: any) => {
    const updatedVariables = [...variables]
    updatedVariables[index] = { ...updatedVariables[index], [field]: value }
    setVariables(updatedVariables)
  }

  const removeVariable = (index: number) => {
    setVariables(variables.filter((_, i) => i !== index))
  }

  const insertVariable = (variableName: string) => {
    const textarea = document.getElementById('content') as HTMLTextAreaElement
    if (textarea) {
      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      const currentContent = watchedContent || ''
      const newContent = 
        currentContent.substring(0, start) + 
        `{{${variableName}}}` + 
        currentContent.substring(end)
      
      setValue('content', newContent)
      
      // Set cursor position after the inserted variable
      setTimeout(() => {
        textarea.focus()
        textarea.setSelectionRange(start + variableName.length + 4, start + variableName.length + 4)
      }, 0)
    }
  }

  const onSubmit = async (data: TemplateFormData) => {
    setIsLoading(true)
    setError('')

    try {
      const url = mode === 'create' ? '/api/contract-templates' : `/api/contract-templates/${template.id}`
      const method = mode === 'create' ? 'POST' : 'PUT'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to save template')
      }

      const result = await response.json()
      
      toast.success(mode === 'create' ? 'Template created successfully!' : 'Template updated successfully!')
      
      if (mode === 'create') {
        router.push(`/dashboard/contract-templates/${result.id}`)
      } else {
        router.push('/dashboard/contract-templates')
      }
    } catch (error) {
      console.error('Error saving template:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to save template'
      setError(errorMessage)
      toast.error(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    if (mode === 'create') {
      reset()
    } else {
      router.push('/dashboard/contract-templates')
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Template Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>Template Information</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Template Name *</Label>
              <Input
                id="name"
                {...register('name')}
                placeholder="Service Agreement Template"
                disabled={isLoading}
              />
              {errors.name && (
                <p className="text-sm text-red-600 mt-1">{errors.name.message}</p>
              )}
            </div>
            <div>
              <Label htmlFor="category">Category</Label>
              <Input
                id="category"
                {...register('category')}
                placeholder="Service Agreements"
                disabled={isLoading}
              />
            </div>
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <textarea
              id="description"
              {...register('description')}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Describe what this template is used for..."
              disabled={isLoading}
            />
          </div>

          <div>
            <Label htmlFor="status">Status</Label>
            <select
              id="status"
              {...register('status')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={isLoading}
            >
              <option value="DRAFT">Draft</option>
              <option value="ACTIVE">Active</option>
              <option value="ARCHIVED">Archived</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Variables */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <Code className="h-5 w-5" />
              <span>Template Variables</span>
            </CardTitle>
            <Button type="button" onClick={addVariable} >
              <Plus className="h-4 w-4 mr-2" />
              Add Variable
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-3 bg-blue-50 rounded-lg">
              <div className="flex items-start space-x-2">
                <Info className="h-4 w-4 text-blue-600 mt-0.5" />
                <div className="text-sm text-blue-800">
                  <p className="font-medium">How to use variables:</p>
                  <p>Use variables in your template content like this: <code className="bg-blue-100 px-1 rounded">{'{{variable_name}}'}</code></p>
                  <p>Variables will be replaced with actual values when generating contracts.</p>
                </div>
              </div>
            </div>

            {variables.length > 0 ? (
              <div className="space-y-4">
                {variables.map((variable, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div>
                        <Label>Variable Name *</Label>
                        <Input
                          value={variable.name}
                          onChange={(e) => updateVariable(index, 'name', e.target.value)}
                          placeholder="customer_name"
                          disabled={isLoading}
                        />
                      </div>
                      <div>
                        <Label>Display Label *</Label>
                        <Input
                          value={variable.label}
                          onChange={(e) => updateVariable(index, 'label', e.target.value)}
                          placeholder="Customer Name"
                          disabled={isLoading}
                        />
                      </div>
                      <div>
                        <Label>Type</Label>
                        <select
                          value={variable.type}
                          onChange={(e) => updateVariable(index, 'type', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          disabled={isLoading}
                        >
                          <option value="TEXT">Text</option>
                          <option value="NUMBER">Number</option>
                          <option value="DATE">Date</option>
                          <option value="BOOLEAN">Boolean</option>
                          <option value="EMAIL">Email</option>
                          <option value="PHONE">Phone</option>
                        </select>
                      </div>
                      <div className="flex items-end space-x-2">
                        <div className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={variable.required}
                            onChange={(e) => updateVariable(index, 'required', e.target.checked)}
                            disabled={isLoading}
                          />
                          <Label className="text-sm">Required</Label>
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => insertVariable(variable.name)}
                          className="text-blue-600"
                          disabled={!variable.name}
                        >
                          Insert
                        </Button>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeVariable(index)}
                          className="text-red-600"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label>Default Value</Label>
                        <Input
                          value={variable.defaultValue || ''}
                          onChange={(e) => updateVariable(index, 'defaultValue', e.target.value)}
                          placeholder="Optional default value"
                          disabled={isLoading}
                        />
                      </div>
                      <div>
                        <Label>Description</Label>
                        <Input
                          value={variable.description || ''}
                          onChange={(e) => updateVariable(index, 'description', e.target.value)}
                          placeholder="Help text for this variable"
                          disabled={isLoading}
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-6 border-2 border-dashed border-gray-300 rounded-lg">
                <Code className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500">No variables defined yet</p>
                <p className="text-sm text-gray-400">Add variables to make your template dynamic</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Template Content */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Template Content</CardTitle>
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowPreview(!showPreview)}
            >
              <Eye className="h-4 w-4 mr-2" />
              {showPreview ? 'Hide Preview' : 'Show Preview'}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4">
            <div>
              <Label htmlFor="content">Contract Content *</Label>
              <textarea
                id="content"
                {...register('content')}
                rows={20}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                placeholder="Enter your contract template content here. Use {{variable_name}} for dynamic content..."
                disabled={isLoading}
              />
              {errors.content && (
                <p className="text-sm text-red-600 mt-1">{errors.content.message}</p>
              )}
            </div>

            {showPreview && (
              <div>
                <Label>Preview</Label>
                <div className="border rounded-md p-4 bg-gray-50 min-h-[200px] whitespace-pre-wrap font-serif">
                  {watchedContent || 'Template content will appear here...'}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex items-center justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={handleCancel}
          disabled={isLoading}
        >
          <X className="h-4 w-4 mr-2" />
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              {mode === 'create' ? 'Creating...' : 'Updating...'}
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              {mode === 'create' ? 'Create Template' : 'Update Template'}
            </>
          )}
        </Button>
      </div>
    </form>
  )
}
