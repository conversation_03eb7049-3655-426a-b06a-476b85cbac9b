'use client'

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { 
  <PERSON><PERSON><PERSON>cle, 
  Clock,
  CheckCircle,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Star,
  MessageSquare
} from 'lucide-react'

interface SupportStatsProps {
  stats: {
    openTickets: number
    inProgressTickets: number
    resolvedTickets: number
    recentTickets: number
    avgResponseTime: string
    satisfaction: number
    totalTickets: number
  }
}

export function SupportStats({ stats }: SupportStatsProps) {
  const getTicketTrend = () => {
    // Mock trend calculation - in real app, compare with previous period
    const trend = Math.random() > 0.5 ? 'up' : 'down'
    const percentage = Math.floor(Math.random() * 20) + 1
    return { trend, percentage }
  }

  const ticketTrend = getTicketTrend()

  const statCards = [
    {
      title: 'Open Tickets',
      value: stats.openTickets,
      icon: AlertTriangle,
      color: stats.openTickets > 0 ? 'text-yellow-600' : 'text-gray-600',
      bgColor: stats.openTickets > 0 ? 'bg-yellow-100' : 'bg-gray-100',
      change: stats.openTickets > 0 ? 'Needs attention' : 'All caught up!',
      changeColor: stats.openTickets > 0 ? 'text-yellow-600' : 'text-green-600'
    },
    {
      title: 'In Progress',
      value: stats.inProgressTickets,
      icon: Clock,
      color: stats.inProgressTickets > 0 ? 'text-blue-600' : 'text-gray-600',
      bgColor: stats.inProgressTickets > 0 ? 'bg-blue-100' : 'bg-gray-100',
      change: stats.inProgressTickets > 0 ? 'Being resolved' : 'No active work',
      changeColor: stats.inProgressTickets > 0 ? 'text-blue-600' : 'text-gray-600'
    },
    {
      title: 'Resolved This Month',
      value: stats.resolvedTickets,
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      change: `${stats.resolvedTickets} completed`,
      changeColor: 'text-green-600'
    },
    {
      title: 'Avg Response Time',
      value: stats.avgResponseTime,
      icon: MessageSquare,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      change: 'Within SLA',
      changeColor: 'text-purple-600'
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statCards.map((card, index) => (
        <Card key={index} className="hover:shadow-lg transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              {card.title}
            </CardTitle>
            <div className={`p-2 rounded-lg ${card.bgColor}`}>
              <card.icon className={`h-4 w-4 ${card.color}`} />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900 mb-1">
              {typeof card.value === 'string' ? card.value : card.value.toString()}
            </div>
            <div className={`text-xs ${card.changeColor}`}>
              {card.change}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
