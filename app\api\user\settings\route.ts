import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import bcrypt from 'bcryptjs'

const userSettingsSchema = z.object({
  profile: z.object({
    name: z.string().min(1, 'Name is required'),
    email: z.string().email('Invalid email address'),
    timezone: z.string(),
    language: z.string(),
  }).optional(),
  security: z.object({
    currentPassword: z.string().optional(),
    newPassword: z.string().optional(),
    confirmPassword: z.string().optional(),
    twoFactorEnabled: z.boolean(),
  }).optional(),
  preferences: z.object({
    emailNotifications: z.boolean(),
    pushNotifications: z.boolean(),
    marketingEmails: z.boolean(),
    securityAlerts: z.boolean(),
  }).optional()
})

// Get user settings
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Fetch user data and preferences
    const [user, notificationPreferences] = await Promise.all([
      prisma.user.findUnique({
        where: { id: session.user.id },
        select: {
          id: true,
          name: true,
          firstName: true,
          lastName: true,
          email: true,
          role: true,
          image: true,
          company: {
            select: {
              name: true,
              plan: true,
              status: true
            }
          }
        }
      }),
      
      prisma.notificationPreference.findFirst({
        where: { userId: session.user.id }
      })
    ])

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const settings = {
      profile: {
        name: user.name || '',
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        email: user.email || '',
        role: user.role,
        avatar: user.image || ''
      },
      security: {
        twoFactorEnabled: false
      },
      preferences: {
        emailNotifications: notificationPreferences?.email ?? true,
        pushNotifications: notificationPreferences?.push ?? true,
        marketingEmails: notificationPreferences?.marketing ?? false,
        securityAlerts: notificationPreferences?.security ?? true
      },
      company: user.company
    }

    return NextResponse.json(settings)
  } catch (error) {
    console.error('Error fetching user settings:', error)
    return NextResponse.json(
      { error: 'Failed to fetch user settings' },
      { status: 500 }
    )
  }
}

// Update user settings
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = userSettingsSchema.parse(body)

    // Update profile information
    if (validatedData.profile) {
      await prisma.user.update({
        where: { id: session.user.id },
        data: {
          name: validatedData.profile.name,
          email: validatedData.profile.email,
          timezone: validatedData.profile.timezone,
          language: validatedData.profile.language
        }
      })
    }

    // Update security settings
    if (validatedData.security) {
      const updateData: any = {
        twoFactorEnabled: validatedData.security.twoFactorEnabled
      }

      // Handle password change
      if (validatedData.security.newPassword && validatedData.security.currentPassword) {
        // Verify current password
        const user = await prisma.user.findUnique({
          where: { id: session.user.id },
          select: { password: true }
        })

        if (!user?.password) {
          return NextResponse.json(
            { error: 'Current password not found' },
            { status: 400 }
          )
        }

        const isCurrentPasswordValid = await bcrypt.compare(
          validatedData.security.currentPassword,
          user.password
        )

        if (!isCurrentPasswordValid) {
          return NextResponse.json(
            { error: 'Current password is incorrect' },
            { status: 400 }
          )
        }

        // Hash new password
        const hashedPassword = await bcrypt.hash(validatedData.security.newPassword, 12)
        updateData.password = hashedPassword
      }

      await prisma.user.update({
        where: { id: session.user.id },
        data: updateData
      })
    }

    // Update notification preferences
    if (validatedData.preferences) {
      await prisma.notificationPreference.upsert({
        where: { userId: session.user.id },
        create: {
          userId: session.user.id,
          email: validatedData.preferences.emailNotifications,
          push: validatedData.preferences.pushNotifications,
          marketing: validatedData.preferences.marketingEmails,
          security: validatedData.preferences.securityAlerts
        },
        update: {
          email: validatedData.preferences.emailNotifications,
          push: validatedData.preferences.pushNotifications,
          marketing: validatedData.preferences.marketingEmails,
          security: validatedData.preferences.securityAlerts
        }
      })
    }

    // Log the settings update activity
    await prisma.activity.create({
      data: {
        type: 'USER',
        title: 'User settings updated',
        description: 'User settings were updated',
        companyId: session.user.companyId!,
        createdById: session.user.id,
        metadata: {
          updatedSections: Object.keys(validatedData),
          timestamp: new Date().toISOString()
        }
      }
    })

    return NextResponse.json({ 
      success: true, 
      message: 'Settings updated successfully',
      updatedAt: new Date().toISOString()
    })
  } catch (error) {
    console.error('Error updating user settings:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Failed to update user settings' },
      { status: 500 }
    )
  }
}

// Delete user account
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is company owner
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: {
        company: {
          include: {
            users: true
          }
        }
      }
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    if (user.role === 'ADMIN' && user.company?.users.length > 1) {
      return NextResponse.json(
        { error: 'Cannot delete account. Transfer ownership first.' },
        { status: 400 }
      )
    }

    // Soft delete user (mark as inactive)
    await prisma.user.update({
      where: { id: session.user.id },
      data: {
        email: `deleted_${Date.now()}_${user.email}`,
        name: 'Deleted User',
        deletedAt: new Date()
      }
    })

    // Log the account deletion
    await prisma.activity.create({
      data: {
        type: 'USER',
        title: 'Account deleted',
        description: 'User account was deleted',
        companyId: session.user.companyId!,
        createdById: session.user.id,
        metadata: {
          deletedAt: new Date().toISOString()
        }
      }
    })

    return NextResponse.json({ 
      success: true, 
      message: 'Account deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting user account:', error)
    return NextResponse.json(
      { error: 'Failed to delete account' },
      { status: 500 }
    )
  }
}
