import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ReceiptStats } from '@/components/receipts/receipt-stats'
import { ReceiptsTable } from '@/components/receipts/receipts-table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Plus, Search, Filter, Download, Receipt } from 'lucide-react'
import Link from 'next/link'

interface SearchParams {
  search?: string
  status?: string
  paymentMethod?: string
  page?: string
  startDate?: string
  endDate?: string
}

export default async function ReceiptsPage({
  searchParams,
}: {
  searchParams: SearchParams
}) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  const search = searchParams.search || ''
  const status = searchParams.status || ''
  const paymentMethod = searchParams.paymentMethod || ''
  const page = parseInt(searchParams.page || '1')
  const limit = 10
  const offset = (page - 1) * limit

  // Build where clause for receipts
  const where: any = {
    companyId: session.user.companyId,
  }

  if (search) {
    where.OR = [
      { receiptNumber: { contains: search } },
      { invoice: { invoiceNumber: { contains: search } } },
      { invoice: { customer: { name: { contains: search } } } },
      { notes: { contains: search } },
    ]
  }

  if (status) {
    where.status = status
  }

  if (paymentMethod) {
    where.paymentMethod = paymentMethod
  }

  if (searchParams.startDate && searchParams.endDate) {
    where.paidAt = {
      gte: new Date(searchParams.startDate),
      lte: new Date(searchParams.endDate),
    }
  }

  // Fetch receipts and stats
  const [receipts, totalCount, receiptStats] = await Promise.all([
    prisma.receipt.findMany({
      where,
      include: {
        invoice: {
          include: {
            customer: {
              select: { id: true, name: true, email: true, company: true }
            }
          }
        },
        createdBy: {
          select: { name: true, firstName: true, lastName: true }
        }
      },
      orderBy: { createdAt: 'desc' },
      skip: offset,
      take: limit,
    }),
    prisma.receipt.count({ where }),
    
    // Receipt statistics
    Promise.all([
      // Total receipts
      prisma.receipt.count({
        where: { companyId: session.user.companyId }
      }),
      
      // Total amount received
      prisma.receipt.aggregate({
        where: { 
          companyId: session.user.companyId,
          status: 'CONFIRMED'
        },
        _sum: { amount: true }
      }),
      
      // Receipts by status
      prisma.receipt.groupBy({
        by: ['status'],
        where: { companyId: session.user.companyId },
        _count: true,
        _sum: { amount: true }
      }),
      
      // Receipts by payment method
      prisma.receipt.groupBy({
        by: ['paymentMethod'],
        where: { companyId: session.user.companyId },
        _count: true,
        _sum: { amount: true }
      }),
      
      // Recent receipts count (last 30 days)
      prisma.receipt.count({
        where: {
          companyId: session.user.companyId,
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
          }
        }
      }),
      
      // Pending receipts
      prisma.receipt.count({
        where: {
          companyId: session.user.companyId,
          status: 'PENDING'
        }
      }),
    ])
  ])

  const [
    totalReceipts,
    totalAmountData,
    receiptsByStatus,
    receiptsByPaymentMethod,
    recentReceiptsCount,
    pendingReceiptsCount
  ] = receiptStats

  const stats = {
    totalReceipts,
    totalAmount: totalAmountData._sum.amount || 0,
    receiptsByStatus,
    receiptsByPaymentMethod,
    recentReceiptsCount,
    pendingReceiptsCount,
    averageAmount: totalReceipts > 0 ? (totalAmountData._sum.amount || 0) / totalReceipts : 0
  }

  const totalPages = Math.ceil(totalCount / limit)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Payment Receipts</h1>
          <p className="text-gray-600 mt-1">
            Manage payment receipts and track transactions
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Link href="/dashboard/receipts/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Receipt
            </Button>
          </Link>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Download
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <ReceiptStats stats={{
        ...stats,
        receiptsByPaymentMethod: stats.receiptsByPaymentMethod.map(item => ({
          paymentMethod: item.paymentMethod || 'Unknown',
          _count: item._count,
          _sum: item._sum
        }))
      }} />

      {/* Filters */}
      <div className="flex items-center space-x-4 p-4 bg-white rounded-lg border">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search receipts, invoices, or customers..."
              className="pl-10"
              defaultValue={search}
              name="search"
            />
          </div>
        </div>
        <select
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          defaultValue={status}
          name="status"
        >
          <option value="">All Status</option>
          <option value="PENDING">Pending</option>
          <option value="CONFIRMED">Confirmed</option>
          <option value="CANCELLED">Cancelled</option>
        </select>
        <select
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          defaultValue={paymentMethod}
          name="paymentMethod"
        >
          <option value="">All Methods</option>
          <option value="CASH">Cash</option>
          <option value="BANK_TRANSFER">Bank Transfer</option>
          <option value="CREDIT_CARD">Credit Card</option>
          <option value="DEBIT_CARD">Debit Card</option>
          <option value="UPI">UPI</option>
          <option value="CHEQUE">Cheque</option>
          <option value="OTHER">Other</option>
        </select>
        <Button variant="outline">
          <Filter className="h-4 w-4 mr-2" />
          More Filters
        </Button>
      </div>

      {/* Receipts Table */}
      <ReceiptsTable 
        receipts={receipts.map(receipt => ({
          ...receipt,
          paymentMethod: receipt.paymentMethod || 'CASH',
          paidAt: receipt.paidAt || receipt.createdAt,
          invoice: receipt.invoice || {
            id: 'unknown',
            invoiceNumber: 'Unknown',
            total: 0,
            customer: null
          }
        }))}
        currentPage={page}
        totalPages={totalPages}
        totalCount={totalCount}
      />
    </div>
  )
}
