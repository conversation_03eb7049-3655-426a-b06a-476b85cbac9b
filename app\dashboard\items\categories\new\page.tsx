import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Tag } from 'lucide-react'
import { CategoryForm } from '@/components/items/category-form'

export default async function NewCategoryPage() {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  // Fetch existing categories for parent selection
  const categories = await prisma.itemCategory.findMany({
    where: {
      companyId: session.user.companyId,
      isActive: true
    },
    select: {
      id: true,
      name: true,
      parentId: true
    },
    orderBy: { name: 'asc' }
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href="/dashboard/items/categories">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Categories
          </Button>
        </Link>
        <div className="p-2 bg-blue-100 rounded-lg">
          <Tag className="h-6 w-6 text-blue-600" />
        </div>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Create Item Category</h1>
          <p className="text-gray-600 mt-1">
            Add a new category to organize your items
          </p>
        </div>
      </div>

      {/* Category Form */}
      <CategoryForm mode="create" categories={categories} />
    </div>
  )
}
