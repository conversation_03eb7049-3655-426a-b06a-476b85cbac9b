'use client'

import Link from 'next/link'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { formatCurrency, formatDate, getStatusColor } from '@/lib/utils'
import { 
  Clock,
  FileText,
  Receipt,
  ExternalLink
} from 'lucide-react'

interface UpcomingTasksProps {
  quotations: Array<{
    id: string
    quotationNumber: string
    title: string
    status: string
    total: number
    validUntil: Date | null
    customer: {
      name: string
    }
  }>
  invoices: Array<{
    id: string
    invoiceNumber: string
    title: string
    status: string
    total: number
    dueDate: Date | null
    customer: {
      name: string
    }
  }>
}

export function UpcomingTasks({ quotations, invoices }: UpcomingTasksProps) {
  // Filter for pending/urgent items
  const pendingQuotations = quotations.filter(q => 
    ['SENT', 'VIEWED'].includes(q.status) && 
    q.validUntil && 
    new Date(q.validUntil) > new Date()
  )

  const overdueInvoices = invoices.filter(i => 
    i.status !== 'PAID' && 
    i.dueDate && 
    new Date(i.dueDate) < new Date()
  )

  const upcomingDueInvoices = invoices.filter(i => 
    i.status !== 'PAID' && 
    i.dueDate && 
    new Date(i.dueDate) >= new Date() &&
    new Date(i.dueDate) <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // Next 7 days
  )

  const allTasks = [
    ...pendingQuotations.map(q => ({
      id: q.id,
      type: 'quotation' as const,
      title: q.title,
      subtitle: `${q.quotationNumber} • ${q.customer.name}`,
      amount: q.total,
      date: q.validUntil!,
      status: q.status,
      href: `/dashboard/quotations/${q.id}`,
      urgency: 'medium' as const,
      label: 'Expires'
    })),
    ...overdueInvoices.map(i => ({
      id: i.id,
      type: 'invoice' as const,
      title: i.title,
      subtitle: `${i.invoiceNumber} • ${i.customer.name}`,
      amount: i.total,
      date: i.dueDate!,
      status: i.status,
      href: `/dashboard/invoices/${i.id}`,
      urgency: 'high' as const,
      label: 'Overdue'
    })),
    ...upcomingDueInvoices.map(i => ({
      id: i.id,
      type: 'invoice' as const,
      title: i.title,
      subtitle: `${i.invoiceNumber} • ${i.customer.name}`,
      amount: i.total,
      date: i.dueDate!,
      status: i.status,
      href: `/dashboard/invoices/${i.id}`,
      urgency: 'medium' as const,
      label: 'Due'
    }))
  ].sort((a, b) => {
    // Sort by urgency first, then by date
    if (a.urgency === 'high' && b.urgency !== 'high') return -1
    if (b.urgency === 'high' && a.urgency !== 'high') return 1
    return new Date(a.date).getTime() - new Date(b.date).getTime()
  }).slice(0, 8) // Show max 8 items

  if (allTasks.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="h-5 w-5 mr-2" />
            Upcoming Tasks
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Clock className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500">No upcoming tasks</p>
            <p className="text-sm text-gray-400 mt-1">
              You're all caught up!
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Clock className="h-5 w-5 mr-2" />
          Upcoming Tasks
          {allTasks.length > 0 && (
            <Badge variant="secondary" className="ml-2">
              {allTasks.length}
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {allTasks.map((task) => (
            <div key={`${task.type}-${task.id}`} className="flex items-start space-x-3">
              <div className={`p-2 rounded-full ${
                task.type === 'quotation' ? 'text-purple-600 bg-purple-100' : 'text-orange-600 bg-orange-100'
              }`}>
                {task.type === 'quotation' ? (
                  <FileText className="h-4 w-4" />
                ) : (
                  <Receipt className="h-4 w-4" />
                )}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <Link 
                    href={task.href}
                    className="text-sm font-medium text-gray-900 hover:text-blue-600 flex items-center"
                  >
                    {task.title}
                    <ExternalLink className="h-3 w-3 ml-1" />
                  </Link>
                  <Badge 
                    variant={task.urgency === 'high' ? 'destructive' : 'secondary'}
                    className="text-xs"
                  >
                    {task.label}
                  </Badge>
                </div>
                
                <p className="text-xs text-gray-600 mt-1">
                  {task.subtitle}
                </p>
                
                <div className="flex items-center justify-between mt-2">
                  <span className="text-sm font-medium text-gray-900">
                    {formatCurrency(task.amount)}
                  </span>
                  <span className={`text-xs ${
                    task.urgency === 'high' ? 'text-red-600' : 'text-gray-500'
                  }`}>
                    {formatDate(task.date)}
                  </span>
                </div>
                
                <div className="mt-2">
                  <Badge 
                    className={`text-xs ${getStatusColor(task.status)}`}
                    variant="outline"
                  >
                    {task.status.replace('_', ' ')}
                  </Badge>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {(pendingQuotations.length > 4 || overdueInvoices.length > 4 || upcomingDueInvoices.length > 4) && (
          <div className="mt-6 text-center">
            <Link 
              href="/dashboard/tasks"
              className="text-sm text-blue-600 hover:text-blue-800 font-medium"
            >
              View all tasks
            </Link>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
