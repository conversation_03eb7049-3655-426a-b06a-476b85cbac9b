import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const reportSchema = z.object({
  name: z.string().min(1, 'Report name is required'),
  description: z.string().optional(),
  category: z.enum(['financial', 'sales', 'customer', 'operational', 'analytics']),
  type: z.enum(['summary', 'detailed', 'trend', 'comparison']),
  dataSource: z.enum(['customers', 'leads', 'quotations', 'invoices', 'contracts', 'receipts', 'items', 'activities']),
  dateRange: z.string(),
  customStartDate: z.string().optional(),
  customEndDate: z.string().optional(),
  filters: z.array(z.object({
    field: z.string(),
    operator: z.string(),
    value: z.string()
  })).default([]),
  groupBy: z.string().optional(),
  metrics: z.array(z.string()).default(['count']),
  format: z.enum(['pdf', 'excel', 'csv']).default('pdf')
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search') || ''
    const category = searchParams.get('category') || ''
    const type = searchParams.get('type') || ''
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = (page - 1) * limit

    // Build where clause
    const where: any = {
      companyId: session.user.companyId,
    }

    if (search) {
      where.OR = [
        { name: { contains: search } },
        { description: { contains: search } },
      ]
    }

    if (category) {
      where.category = category
    }

    if (type) {
      where.type = type
    }

    const [reports, totalCount] = await Promise.all([
      prisma.report.findMany({
        where,
        include: {
          createdBy: {
            select: { name: true, firstName: true, lastName: true }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip: offset,
        take: limit,
      }),
      prisma.report.count({ where })
    ])

    return NextResponse.json({
      reports,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      }
    })
  } catch (error) {
    console.error('Error fetching reports:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = reportSchema.parse(body)

    // Check if report name already exists
    const existingReport = await prisma.report.findFirst({
      where: {
        name: validatedData.name,
        companyId: session.user.companyId,
      }
    })

    if (existingReport) {
      return NextResponse.json(
        { error: 'Report name already exists' },
        { status: 400 }
      )
    }

    // Calculate date range
    const now = new Date()
    let startDate: Date
    let endDate: Date = now

    if (validatedData.dateRange === 'custom' && validatedData.customStartDate && validatedData.customEndDate) {
      startDate = new Date(validatedData.customStartDate)
      endDate = new Date(validatedData.customEndDate)
    } else {
      switch (validatedData.dateRange) {
        case '7d':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case '30d':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          break
        case '90d':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
          break
        case '1y':
          startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
          break
        default:
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      }
    }

    // Generate report data based on configuration
    const reportData = await generateReportData(
      session.user.companyId,
      validatedData.dataSource,
      startDate,
      endDate,
      validatedData.filters,
      validatedData.metrics
    )

    const report = await prisma.report.create({
      data: {
        name: validatedData.name,
        description: validatedData.description,
        category: validatedData.category,
        type: validatedData.type,
        dataSource: validatedData.dataSource,
        parameters: {
          dateRange: validatedData.dateRange,
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          filters: validatedData.filters,
          groupBy: validatedData.groupBy,
          metrics: validatedData.metrics,
          format: validatedData.format
        },
        data: reportData,
        status: 'COMPLETED',
        format: validatedData.format,
        companyId: session.user.companyId,
        createdById: session.user.id,
        lastGenerated: now,
        downloadCount: 0
      },
      include: {
        createdBy: {
          select: { name: true, firstName: true, lastName: true }
        }
      }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'NOTE',
        title: 'Report created',
        description: `Report "${report.name}" was created`,
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    return NextResponse.json(report, { status: 201 })
  } catch (error) {
    console.error('Error creating report:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function generateReportData(
  companyId: string,
  dataSource: string,
  startDate: Date,
  endDate: Date,
  filters: any[],
  metrics: string[]
) {
  // Build base where clause
  const baseWhere: any = {
    companyId,
    createdAt: { gte: startDate, lte: endDate }
  }

  // Apply filters
  filters.forEach(filter => {
    if (filter.field && filter.operator && filter.value) {
      switch (filter.operator) {
        case 'equals':
          baseWhere[filter.field] = filter.value
          break
        case 'not_equals':
          baseWhere[filter.field] = { not: filter.value }
          break
        case 'greater_than':
          baseWhere[filter.field] = { gt: parseFloat(filter.value) || filter.value }
          break
        case 'less_than':
          baseWhere[filter.field] = { lt: parseFloat(filter.value) || filter.value }
          break
        case 'contains':
          baseWhere[filter.field] = { contains: filter.value }
          break
      }
    }
  })

  let data: any = {}

  try {
    switch (dataSource) {
      case 'customers':
        data = await generateCustomerReport(baseWhere, metrics)
        break
      case 'leads':
        data = await generateLeadReport(baseWhere, metrics)
        break
      case 'quotations':
        data = await generateQuotationReport(baseWhere, metrics)
        break
      case 'invoices':
        data = await generateInvoiceReport(baseWhere, metrics)
        break
      case 'contracts':
        data = await generateContractReport(baseWhere, metrics)
        break
      case 'receipts':
        data = await generateReceiptReport(baseWhere, metrics)
        break
      case 'items':
        data = await generateItemReport(baseWhere, metrics)
        break
      case 'activities':
        data = await generateActivityReport(baseWhere, metrics)
        break
      default:
        data = { error: 'Unknown data source' }
    }
  } catch (error) {
    console.error('Error generating report data:', error)
    data = { error: 'Failed to generate report data' }
  }

  return data
}

async function generateCustomerReport(where: any, metrics: string[]) {
  const [count, statusBreakdown] = await Promise.all([
    prisma.customer.count({ where }),
    prisma.customer.groupBy({
      by: ['status'],
      where,
      _count: true
    })
  ])

  return {
    summary: { totalCustomers: count },
    breakdown: { byStatus: statusBreakdown },
    metrics: metrics.includes('count') ? { count } : {}
  }
}

async function generateLeadReport(where: any, metrics: string[]) {
  const [count, statusBreakdown, sourceBreakdown] = await Promise.all([
    prisma.lead.count({ where }),
    prisma.lead.groupBy({
      by: ['status'],
      where,
      _count: true
    }),
    prisma.lead.groupBy({
      by: ['source'],
      where: { ...where, source: { not: null } },
      _count: true
    })
  ])

  return {
    summary: { totalLeads: count },
    breakdown: { byStatus: statusBreakdown, bySource: sourceBreakdown },
    metrics: metrics.includes('count') ? { count } : {}
  }
}

async function generateQuotationReport(where: any, metrics: string[]) {
  const [count, aggregate, statusBreakdown] = await Promise.all([
    prisma.quotation.count({ where }),
    prisma.quotation.aggregate({
      where,
      _sum: { total: true },
      _avg: { total: true }
    }),
    prisma.quotation.groupBy({
      by: ['status'],
      where,
      _count: true,
      _sum: { total: true }
    })
  ])

  return {
    summary: { 
      totalQuotations: count,
      totalValue: aggregate._sum.total || 0,
      averageValue: aggregate._avg.total || 0
    },
    breakdown: { byStatus: statusBreakdown },
    metrics: {
      count: metrics.includes('count') ? count : undefined,
      sum: metrics.includes('sum') ? aggregate._sum.total : undefined,
      average: metrics.includes('average') ? aggregate._avg.total : undefined
    }
  }
}

async function generateInvoiceReport(where: any, metrics: string[]) {
  const [count, aggregate, statusBreakdown] = await Promise.all([
    prisma.invoice.count({ where }),
    prisma.invoice.aggregate({
      where,
      _sum: { total: true },
      _avg: { total: true }
    }),
    prisma.invoice.groupBy({
      by: ['status'],
      where,
      _count: true,
      _sum: { total: true }
    })
  ])

  return {
    summary: { 
      totalInvoices: count,
      totalValue: aggregate._sum.total || 0,
      averageValue: aggregate._avg.total || 0
    },
    breakdown: { byStatus: statusBreakdown },
    metrics: {
      count: metrics.includes('count') ? count : undefined,
      sum: metrics.includes('sum') ? aggregate._sum.total : undefined,
      average: metrics.includes('average') ? aggregate._avg.total : undefined
    }
  }
}

async function generateContractReport(where: any, metrics: string[]) {
  const [count, aggregate, statusBreakdown] = await Promise.all([
    prisma.contract.count({ where }),
    prisma.contract.aggregate({
      where,
      _sum: { value: true },
      _avg: { value: true }
    }),
    prisma.contract.groupBy({
      by: ['status'],
      where,
      _count: true,
      _sum: { value: true }
    })
  ])

  return {
    summary: { 
      totalContracts: count,
      totalValue: aggregate._sum.value || 0,
      averageValue: aggregate._avg.value || 0
    },
    breakdown: { byStatus: statusBreakdown },
    metrics: {
      count: metrics.includes('count') ? count : undefined,
      sum: metrics.includes('sum') ? aggregate._sum.value : undefined,
      average: metrics.includes('average') ? aggregate._avg.value : undefined
    }
  }
}

async function generateReceiptReport(where: any, metrics: string[]) {
  const [count, aggregate, statusBreakdown, methodBreakdown] = await Promise.all([
    prisma.receipt.count({ where }),
    prisma.receipt.aggregate({
      where,
      _sum: { amount: true },
      _avg: { amount: true }
    }),
    prisma.receipt.groupBy({
      by: ['status'],
      where,
      _count: true,
      _sum: { amount: true }
    }),
    prisma.receipt.groupBy({
      by: ['paymentMethod'],
      where,
      _count: true,
      _sum: { amount: true }
    })
  ])

  return {
    summary: { 
      totalReceipts: count,
      totalAmount: aggregate._sum.amount || 0,
      averageAmount: aggregate._avg.amount || 0
    },
    breakdown: { 
      byStatus: statusBreakdown,
      byPaymentMethod: methodBreakdown
    },
    metrics: {
      count: metrics.includes('count') ? count : undefined,
      sum: metrics.includes('sum') ? aggregate._sum.amount : undefined,
      average: metrics.includes('average') ? aggregate._avg.amount : undefined
    }
  }
}

async function generateItemReport(where: any, metrics: string[]) {
  const [count, typeBreakdown, categoryBreakdown] = await Promise.all([
    prisma.item.count({ where }),
    prisma.item.groupBy({
      by: ['type'],
      where,
      _count: true
    }),
    prisma.item.groupBy({
      by: ['category'],
      where: { ...where, category: { not: null } },
      _count: true
    })
  ])

  return {
    summary: { totalItems: count },
    breakdown: { 
      byType: typeBreakdown,
      byCategory: categoryBreakdown
    },
    metrics: metrics.includes('count') ? { count } : {}
  }
}

async function generateActivityReport(where: any, metrics: string[]) {
  const [count, typeBreakdown] = await Promise.all([
    prisma.activity.count({ where }),
    prisma.activity.groupBy({
      by: ['type'],
      where,
      _count: true
    })
  ])

  return {
    summary: { totalActivities: count },
    breakdown: { byType: typeBreakdown },
    metrics: metrics.includes('count') ? { count } : {}
  }
}
