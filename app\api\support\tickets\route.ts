import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const ticketSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title too long'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  category: z.enum(['TECHNICAL', 'BILLING', 'FEATURE_REQUEST', 'BUG_REPORT', 'ACCOUNT', 'INTEGRATION', 'TRAINING', 'OTHER']),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).default('MEDIUM')
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const priority = searchParams.get('priority')
    const category = searchParams.get('category')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = (page - 1) * limit

    // Build where clause
    const where: any = {
      companyId: session.user.companyId
    }

    if (status) {
      where.status = status
    }

    if (priority) {
      where.priority = priority
    }

    if (category) {
      where.category = category
    }

    const [tickets, totalCount] = await Promise.all([
      prisma.supportTicket.findMany({
        where,
        include: {
          createdBy: {
            select: { name: true, firstName: true, lastName: true, email: true }
          },
          assignedTo: {
            select: { name: true, firstName: true, lastName: true, email: true }
          },
          _count: {
            select: { messages: true }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip: offset,
        take: limit,
      }),
      prisma.supportTicket.count({ where })
    ])

    return NextResponse.json({
      tickets,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      }
    })
  } catch (error) {
    console.error('Error fetching support tickets:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = ticketSchema.parse(body)

    // Generate ticket number
    const ticketCount = await prisma.supportTicket.count({
      where: { companyId: session.user.companyId }
    })
    const ticketNumber = `TICKET-${Date.now()}-${(ticketCount + 1).toString().padStart(4, '0')}`

    // Create support ticket
    const ticket = await prisma.supportTicket.create({
      data: {
        ticketNumber,
        title: validatedData.title,
        description: validatedData.description,
        category: validatedData.category,
        priority: validatedData.priority,
        status: 'OPEN',
        companyId: session.user.companyId,
        createdById: session.user.id
      },
      include: {
        createdBy: {
          select: { name: true, firstName: true, lastName: true, email: true }
        }
      }
    })

    // Create initial message
    await prisma.supportMessage.create({
      data: {
        ticketId: ticket.id,
        content: validatedData.description,
        isFromCustomer: true,
        createdById: session.user.id
      }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'SUPPORT',
        title: 'Support ticket created',
        description: `Support ticket "${validatedData.title}" created`,
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    // Send notification email to support team (in real implementation)
    // await sendSupportNotification(ticket)

    return NextResponse.json(ticket, { status: 201 })
  } catch (error) {
    console.error('Error creating support ticket:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Utility function to send support notifications
async function sendSupportNotification(ticket: any) {
  try {
    // In a real implementation, this would send an email to the support team
    // using your email service (SendGrid, AWS SES, etc.)
    
    const supportEmail = process.env.SUPPORT_EMAIL || '<EMAIL>'
    
    // Example email content
    const emailContent = {
      to: supportEmail,
      subject: `New Support Ticket: ${ticket.title}`,
      html: `
        <h2>New Support Ticket Created</h2>
        <p><strong>Ticket Number:</strong> ${ticket.ticketNumber}</p>
        <p><strong>Title:</strong> ${ticket.title}</p>
        <p><strong>Category:</strong> ${ticket.category}</p>
        <p><strong>Priority:</strong> ${ticket.priority}</p>
        <p><strong>Created By:</strong> ${ticket.createdBy?.name || ticket.createdBy?.email}</p>
        <p><strong>Description:</strong></p>
        <p>${ticket.description}</p>
        <p><a href="${process.env.NEXTAUTH_URL}/admin/support/tickets/${ticket.id}">View Ticket</a></p>
      `
    }

    // Send email using your preferred service
    // await emailService.send(emailContent)
    
    console.log('Support notification sent for ticket:', ticket.ticketNumber)
  } catch (error) {
    console.error('Error sending support notification:', error)
  }
}

// Utility function to auto-assign tickets based on category
async function autoAssignTicket(ticketId: string, category: string) {
  try {
    // In a real implementation, you would have logic to assign tickets
    // to appropriate support agents based on category, workload, etc.
    
    const assignmentRules = {
      TECHNICAL: 'tech-support-team',
      BILLING: 'billing-team',
      API: 'developer-support',
      BUG_REPORT: 'qa-team',
      // ... other categories
    }

    // For now, we'll just log the assignment
    console.log(`Ticket ${ticketId} should be assigned to: ${assignmentRules[category as keyof typeof assignmentRules] || 'general-support'}`)
    
    // Update ticket with assignment
    // await prisma.supportTicket.update({
    //   where: { id: ticketId },
    //   data: { assignedToId: assignedUserId }
    // })
  } catch (error) {
    console.error('Error auto-assigning ticket:', error)
  }
}

// Utility function to calculate SLA deadlines
function calculateSLADeadline(priority: string, plan: string): Date {
  const now = new Date()
  
  // SLA response times in hours
  const slaHours = {
    ENTERPRISE: {
      URGENT: 1,
      HIGH: 2,
      MEDIUM: 4,
      LOW: 8
    },
    PROFESSIONAL: {
      URGENT: 2,
      HIGH: 4,
      MEDIUM: 8,
      LOW: 24
    },
    BASIC: {
      URGENT: 4,
      HIGH: 8,
      MEDIUM: 24,
      LOW: 48
    }
  }

  const planSLA = slaHours[plan as keyof typeof slaHours] || slaHours.BASIC
  const hours = planSLA[priority as keyof typeof planSLA] || 24

  return new Date(now.getTime() + hours * 60 * 60 * 1000)
}

// Utility function to check SLA compliance
async function checkSLACompliance() {
  try {
    const now = new Date()
    
    // Find tickets that are approaching or past SLA deadline
    const tickets = await prisma.supportTicket.findMany({
      where: {
        status: { in: ['OPEN', 'IN_PROGRESS'] },
        slaDeadline: { lte: now }
      },
      include: {
        company: { select: { name: true, plan: true } },
        createdBy: { select: { name: true, email: true } }
      }
    })

    // Send SLA breach notifications
    for (const ticket of tickets) {
      console.log(`SLA breach for ticket ${ticket.ticketNumber}`)
      // Send notification to support managers
      // await sendSLABreachNotification(ticket)
    }

    return tickets
  } catch (error) {
    console.error('Error checking SLA compliance:', error)
    return []
  }
}
