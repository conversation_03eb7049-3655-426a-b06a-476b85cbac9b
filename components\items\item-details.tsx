'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { getStatusColor, formatDate, formatCurrency } from '@/lib/utils'
import { 
  Package, 
  ShoppingCart, 
  Wrench, 
  Tag, 
  DollarSign,
  Archive,
  AlertTriangle,
  Calendar,
  Edit,
  Copy,
  Trash2,
  BarChart3,
  TrendingUp,
  Percent
} from 'lucide-react'
import toast from 'react-hot-toast'

interface ItemDetailsProps {
  item: {
    id: string
    name: string
    description: string | null
    sku: string | null
    type: string
    category: string | null
    price: number
    costPrice: number | null
    unit: string | null
    stockQuantity: number | null
    lowStockThreshold: number | null
    status: string
    taxRate: number | null
    notes: string | null
    createdAt: Date
    _count: {
      quotationItems: number
      invoiceItems: number
    }
  }
}

export function ItemDetails({ item }: ItemDetailsProps) {
  const [isDuplicating, setIsDuplicating] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)

  const handleDuplicate = async () => {
    setIsDuplicating(true)
    try {
      const response = await fetch(`/api/items/${item.id}/duplicate`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to duplicate item')
      }

      const result = await response.json()
      toast.success('Item duplicated successfully')
      window.location.href = `/dashboard/items/${result.id}/edit`
    } catch (error) {
      toast.error('Failed to duplicate item')
    } finally {
      setIsDuplicating(false)
    }
  }

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
      return
    }

    setIsDeleting(true)
    try {
      const response = await fetch(`/api/items/${item.id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete item')
      }

      toast.success('Item deleted successfully')
      window.location.href = '/dashboard/items'
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete item'
      toast.error(errorMessage)
    } finally {
      setIsDeleting(false)
    }
  }

  const isLowStock = item.type === 'PRODUCT' && 
                   item.stockQuantity !== null && 
                   item.lowStockThreshold !== null &&
                   item.stockQuantity <= item.lowStockThreshold

  const profitMargin = item.price && item.costPrice 
    ? (((item.price - item.costPrice) / item.price) * 100).toFixed(1)
    : null

  const getTypeIcon = () => {
    return item.type === 'PRODUCT' ? ShoppingCart : Wrench
  }

  const getTypeColor = () => {
    return item.type === 'PRODUCT' ? 'text-purple-600' : 'text-indigo-600'
  }

  const TypeIcon = getTypeIcon()

  return (
    <div className="space-y-6">
      {/* Item Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Item Information</CardTitle>
            <div className="flex items-center space-x-2">
              <Link href={`/dashboard/items/${item.id}/edit`}>
                <Button variant="outline" >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>
              </Link>
              <Button
                variant="outline"
                size="sm"
                onClick={handleDuplicate}
                disabled={isDuplicating}
                className="text-green-600 hover:text-green-700"
              >
                <Copy className="h-4 w-4 mr-2" />
                {isDuplicating ? 'Duplicating...' : 'Duplicate'}
              </Button>
              {item._count.quotationItems === 0 && item._count.invoiceItems === 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDelete}
                  disabled={isDeleting}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  {isDeleting ? 'Deleting...' : 'Delete'}
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="font-medium text-gray-900">Basic Details</h3>
              
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-gray-600">Name</p>
                  <p className="font-medium text-gray-900">{item.name}</p>
                </div>

                {item.description && (
                  <div>
                    <p className="text-sm text-gray-600">Description</p>
                    <p className="text-gray-900 whitespace-pre-wrap">{item.description}</p>
                  </div>
                )}

                {item.sku && (
                  <div>
                    <p className="text-sm text-gray-600">SKU</p>
                    <p className="font-medium text-gray-900">{item.sku}</p>
                  </div>
                )}

                <div className="flex items-center space-x-4">
                  <div>
                    <p className="text-sm text-gray-600">Type</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <TypeIcon className={`h-4 w-4 ${getTypeColor()}`} />
                      <span className="font-medium text-gray-900">{item.type}</span>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Status</p>
                    <Badge className={getStatusColor(item.status)} variant="outline">
                      {item.status}
                    </Badge>
                  </div>
                </div>

                {item.category && (
                  <div className="flex items-center space-x-3">
                    <Tag className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">Category</p>
                      <p className="font-medium text-gray-900">{item.category}</p>
                    </div>
                  </div>
                )}

                <div className="flex items-center space-x-3">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-600">Created</p>
                    <p className="font-medium text-gray-900">{formatDate(item.createdAt)}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Pricing Information */}
            <div className="space-y-4">
              <h3 className="font-medium text-gray-900">Pricing & Financial</h3>
              
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <DollarSign className="h-4 w-4 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-600">Selling Price</p>
                    <p className="font-medium text-gray-900 text-lg">
                      {formatCurrency(item.price)}
                      {item.unit && <span className="text-gray-500 text-sm">/{item.unit}</span>}
                    </p>
                  </div>
                </div>

                {item.costPrice && (
                  <div className="flex items-center space-x-3">
                    <BarChart3 className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">Cost Price</p>
                      <p className="font-medium text-gray-900">{formatCurrency(item.costPrice)}</p>
                    </div>
                  </div>
                )}

                {profitMargin && (
                  <div className="flex items-center space-x-3">
                    <TrendingUp className="h-4 w-4 text-green-600" />
                    <div>
                      <p className="text-sm text-gray-600">Profit Margin</p>
                      <p className="font-medium text-green-600">{profitMargin}%</p>
                    </div>
                  </div>
                )}

                {item.taxRate && (
                  <div className="flex items-center space-x-3">
                    <Percent className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">Tax Rate</p>
                      <p className="font-medium text-gray-900">{item.taxRate}%</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Inventory Information (Products only) */}
      {item.type === 'PRODUCT' && (
        <Card>
          <CardHeader>
            <CardTitle>Inventory Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="flex items-center space-x-3">
                <Archive className="h-4 w-4 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-600">Current Stock</p>
                  <p className="font-medium text-gray-900 text-lg">
                    {item.stockQuantity || 0} {item.unit && `${item.unit}s`}
                  </p>
                </div>
              </div>

              {item.lowStockThreshold && (
                <div className="flex items-center space-x-3">
                  <AlertTriangle className={`h-4 w-4 ${isLowStock ? 'text-red-600' : 'text-gray-400'}`} />
                  <div>
                    <p className="text-sm text-gray-600">Low Stock Threshold</p>
                    <p className={`font-medium ${isLowStock ? 'text-red-600' : 'text-gray-900'}`}>
                      {item.lowStockThreshold} {item.unit && `${item.unit}s`}
                    </p>
                    {isLowStock && (
                      <p className="text-xs text-red-600 mt-1">Stock is below threshold!</p>
                    )}
                  </div>
                </div>
              )}
            </div>

            {isLowStock && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                  <p className="text-sm font-medium text-red-800">Low Stock Alert</p>
                </div>
                <p className="text-sm text-red-700 mt-1">
                  This item is running low on stock. Consider restocking soon.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Additional Information */}
      {item.notes && (
        <Card>
          <CardHeader>
            <CardTitle>Additional Notes</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-900 whitespace-pre-wrap">{item.notes}</p>
          </CardContent>
        </Card>
      )}

      {/* Usage Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Usage Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 mb-2">
                <Package className="h-4 w-4 text-blue-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900">{item._count.quotationItems}</div>
              <div className="text-sm text-gray-600">Quotations</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-green-100 mb-2">
                <DollarSign className="h-4 w-4 text-green-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900">{item._count.invoiceItems}</div>
              <div className="text-sm text-gray-600">Invoices</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-purple-100 mb-2">
                <Calendar className="h-4 w-4 text-purple-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900">
                {Math.ceil((new Date().getTime() - new Date(item.createdAt).getTime()) / (1000 * 60 * 60 * 24))}
              </div>
              <div className="text-sm text-gray-600">Days Old</div>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-orange-100 mb-2">
                <BarChart3 className="h-4 w-4 text-orange-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900">
                {item._count.quotationItems + item._count.invoiceItems}
              </div>
              <div className="text-sm text-gray-600">Total Usage</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
