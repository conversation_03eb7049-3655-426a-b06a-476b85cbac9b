'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { formatCurrency } from '@/lib/utils'
import { 
  Building2, 
  Users, 
  TrendingUp, 
  TrendingDown,
  DollarSign, 
  FileText,
  Receipt,

  Activity,
  Globe,
  Server,
  Zap
} from 'lucide-react'

interface PlatformStatsProps {
  data: {
    companies: {
      total: number
      active: number
      newThisMonth: number
      growthRate: number
    }
    users: {
      total: number
      active: number
      newThisMonth: number
    }
    business: {
      quotations: number
      invoices: number
      contracts: number
      revenue: number
    }
    systemHealth: {
      uptime: string
      responseTime: string
      errorRate: string
      activeConnections: number
    }
  }
}

export function PlatformStats({ data }: PlatformStatsProps) {
  const overviewCards = [
    {
      title: 'Total Companies',
      value: data.companies.total.toString(),
      icon: Building2,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      change: `+${data.companies.newThisMonth} this month`,
      changeColor: 'text-green-600',
      changeIcon: TrendingUp
    },
    {
      title: 'Active Companies',
      value: data.companies.active.toString(),
      icon: Activity,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      change: `${((data.companies.active / data.companies.total) * 100).toFixed(1)}% active`,
      changeColor: 'text-green-600',
      changeIcon: TrendingUp
    },
    {
      title: 'Total Users',
      value: data.users.total.toString(),
      icon: Users,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      change: `+${data.users.newThisMonth} this month`,
      changeColor: 'text-green-600',
      changeIcon: TrendingUp
    },
    {
      title: 'Platform Revenue',
      value: formatCurrency(data.business.revenue),
      icon: DollarSign,
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-100',
      change: 'Total processed',
      changeColor: 'text-emerald-600',
      changeIcon: DollarSign
    }
  ]

  const businessMetrics = [
    {
      title: 'Quotations',
      value: data.business.quotations,
      icon: FileText,
 Receipt,
      color: 'text-blue-600'
    },
    {
      title: 'Invoices',
      value: data.business.invoices,
      icon: Receipt,
      color: 'text-green-600'
    },
    {
      title: 'Contracts',
      value: data.business.contracts,
      icon: FileText,
 Receipt,
      color: 'text-purple-600'
    },
    {
      title: 'Active Users',
      value: data.users.active,
      icon: Users,
      color: 'text-orange-600'
    }
  ]

  const systemMetrics = [
    {
      title: 'System Uptime',
      value: data.systemHealth.uptime,
      icon: Server,
      color: 'text-green-600'
    },
    {
      title: 'Response Time',
      value: data.systemHealth.responseTime,
      icon: Zap,
      color: 'text-yellow-600'
    },
    {
      title: 'Error Rate',
      value: data.systemHealth.errorRate,
      icon: Activity,
      color: 'text-red-600'
    },
    {
      title: 'Connections',
      value: data.systemHealth.activeConnections.toString(),
      icon: Globe,
      color: 'text-indigo-600'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Main Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {overviewCards.map((card, index) => {
          const ChangeIcon = card.changeIcon
          
          return (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {card.title}
                </CardTitle>
                <div className={`p-2 rounded-lg ${card.bgColor}`}>
                  <card.icon className={`h-5 w-5 ${card.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-gray-900">
                  {card.value}
                </div>
                <div className={`flex items-center text-sm mt-2 ${card.changeColor}`}>
                  <ChangeIcon className="h-4 w-4 mr-1" />
                  <span>{card.change}</span>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Business Metrics */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Business Metrics</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {businessMetrics.map((metric, index) => (
            <Card key={index} className="text-center hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 mb-4`}>
                  <metric.icon className={`h-6 w-6 ${metric.color}`} />
                </div>
                <div className="text-2xl font-bold text-gray-900">
                  {metric.value.toLocaleString()}
                </div>
                <div className="text-sm text-gray-600">
                  {metric.title}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* System Health */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">System Health</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {systemMetrics.map((metric, index) => (
            <Card key={index} className="text-center hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 mb-4`}>
                  <metric.icon className={`h-6 w-6 ${metric.color}`} />
                </div>
                <div className="text-2xl font-bold text-gray-900">
                  {metric.value}
                </div>
                <div className="text-sm text-gray-600">
                  {metric.title}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Growth Indicators */}
      <Card>
        <CardHeader>
          <CardTitle>Growth Indicators</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-3xl font-bold text-blue-800">
                {data.companies.growthRate >= 0 ? '+' : ''}{data.companies.growthRate}%
              </div>
              <div className="text-sm text-blue-600">Company Growth Rate</div>
              <div className="text-xs text-blue-500 mt-1">Month over month</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-3xl font-bold text-green-800">
                {((data.users.active / data.users.total) * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-green-600">User Engagement</div>
              <div className="text-xs text-green-500 mt-1">Active in last 30 days</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-3xl font-bold text-purple-800">
                {data.companies.total > 0 ? (data.users.total / data.companies.total).toFixed(1) : '0'}
              </div>
              <div className="text-sm text-purple-600">Avg. Users per Company</div>
              <div className="text-xs text-purple-500 mt-1">Platform average</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
