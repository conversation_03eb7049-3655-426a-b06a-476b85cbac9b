import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const upgradeSchema = z.object({
  plan: z.enum(['BASIC', 'PROFESSIONAL', 'ENTERPRISE']),
  interval: z.enum(['MONTHLY', 'YEARLY']).optional()
})

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = upgradeSchema.parse(body)

    // Get company and current subscription
    const [company, currentSubscription] = await Promise.all([
      prisma.company.findUnique({
        where: { id: session.user.companyId }
      }),
      prisma.subscription.findFirst({
        where: { companyId: session.user.companyId }
      })
    ])

    if (!company) {
      return NextResponse.json({ error: 'Company not found' }, { status: 404 })
    }

    // Check if already on the requested plan
    if (company.plan === validatedData.plan) {
      return NextResponse.json(
        { error: 'Already on the requested plan' },
        { status: 400 }
      )
    }

    // Get plan pricing
    const planPricing = {
      BASIC: { monthly: 29, yearly: 290 },
      PROFESSIONAL: { monthly: 79, yearly: 790 },
      ENTERPRISE: { monthly: 199, yearly: 1990 }
    }

    const interval = validatedData.interval || currentSubscription?.interval || 'MONTHLY'
    const newAmount = interval === 'YEARLY' 
      ? planPricing[validatedData.plan].yearly
      : planPricing[validatedData.plan].monthly

    // Determine if this is an upgrade or downgrade
    const planOrder = ['BASIC', 'PROFESSIONAL', 'ENTERPRISE']
    const currentIndex = planOrder.indexOf(company.plan)
    const newIndex = planOrder.indexOf(validatedData.plan)
    const isUpgrade = newIndex > currentIndex

    if (currentSubscription) {
      // Update existing subscription
      const updatedSubscription = await prisma.subscription.update({
        where: { id: currentSubscription.id },
        data: {
          plan: validatedData.plan,
          amount: newAmount,
          interval: interval as any,
          // For upgrades, charge immediately. For downgrades, apply at next billing cycle
          ...(isUpgrade && {
            currentPeriodStart: new Date(),
            currentPeriodEnd: new Date(
              Date.now() + (interval === 'YEARLY' ? 365 : 30) * 24 * 60 * 60 * 1000
            ),
            nextBillingDate: new Date(
              Date.now() + (interval === 'YEARLY' ? 365 : 30) * 24 * 60 * 60 * 1000
            )
          })
        }
      })

      // Create payment record for immediate charges (upgrades)
      if (isUpgrade) {
        // Calculate prorated amount
        const currentAmount = currentSubscription.amount
        const daysRemaining = Math.ceil(
          (currentSubscription.currentPeriodEnd.getTime() - Date.now()) / (1000 * 60 * 60 * 24)
        )
        const totalDays = interval === 'YEARLY' ? 365 : 30
        const proratedAmount = Math.max(0, newAmount - (currentAmount * daysRemaining / totalDays))

        if (proratedAmount > 0) {
          await prisma.payment.create({
            data: {
              subscriptionId: updatedSubscription.id,
              amount: proratedAmount,
              currency: 'USD',
              status: 'SUCCEEDED',
              description: `Plan upgrade to ${validatedData.plan} (prorated)`,
              stripePaymentIntentId: `pi_${Math.random().toString(36).substr(2, 9)}`,
              paidAt: new Date()
            }
          })
        }
      }

      // Update company plan
      await prisma.company.update({
        where: { id: session.user.companyId },
        data: { plan: validatedData.plan }
      })

      // Create activity log
      await prisma.activity.create({
        data: {
          type: 'SUBSCRIPTION',
          title: `Plan ${isUpgrade ? 'upgraded' : 'downgraded'}`,
          description: `Plan ${isUpgrade ? 'upgraded' : 'downgraded'} to ${validatedData.plan}`,
          companyId: session.user.companyId,
          createdById: session.user.id,
        }
      })

      // TODO: In a real implementation, integrate with Stripe
      // const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY)
      // const stripeSubscription = await stripe.subscriptions.update(
      //   currentSubscription.stripeSubscriptionId,
      //   {
      //     items: [{
      //       id: currentSubscription.stripeItemId,
      //       price: getPriceId(validatedData.plan, interval)
      //     }],
      //     proration_behavior: isUpgrade ? 'create_prorations' : 'none'
      //   }
      // )

      return NextResponse.json({
        subscription: updatedSubscription,
        message: isUpgrade 
          ? 'Plan upgraded successfully' 
          : 'Plan will be downgraded at the next billing cycle',
        url: '/dashboard/billing' // Redirect URL
      })
    } else {
      // Create new subscription
      const newSubscription = await prisma.subscription.create({
        data: {
          companyId: session.user.companyId,
          plan: validatedData.plan,
          status: 'ACTIVE',
          amount: newAmount,
          currency: 'USD',
          interval: interval as any,
          currentPeriodStart: new Date(),
          currentPeriodEnd: new Date(
            Date.now() + (interval === 'YEARLY' ? 365 : 30) * 24 * 60 * 60 * 1000
          ),
          nextBillingDate: new Date(
            Date.now() + (interval === 'YEARLY' ? 365 : 30) * 24 * 60 * 60 * 1000
          ),
          stripeSubscriptionId: `sub_${Math.random().toString(36).substr(2, 9)}`,
          stripeCustomerId: `cus_${Math.random().toString(36).substr(2, 9)}`
        }
      })

      // Create initial payment
      await prisma.payment.create({
        data: {
          subscriptionId: newSubscription.id,
          amount: newAmount,
          currency: 'USD',
          status: 'SUCCEEDED',
          description: `${validatedData.plan} plan subscription`,
          stripePaymentIntentId: `pi_${Math.random().toString(36).substr(2, 9)}`,
          paidAt: new Date()
        }
      })

      // Update company plan
      await prisma.company.update({
        where: { id: session.user.companyId },
        data: { plan: validatedData.plan }
      })

      // Create activity log
      await prisma.activity.create({
        data: {
          type: 'SUBSCRIPTION',
          title: 'Subscription created',
          description: `${validatedData.plan} subscription created`,
          companyId: session.user.companyId,
          createdById: session.user.id,
        }
      })

      // TODO: In a real implementation, create Stripe checkout session
      // const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY)
      // const session = await stripe.checkout.sessions.create({
      //   customer: company.stripeCustomerId,
      //   payment_method_types: ['card'],
      //   line_items: [{
      //     price: getPriceId(validatedData.plan, interval),
      //     quantity: 1,
      //   }],
      //   mode: 'subscription',
      //   success_url: `${process.env.NEXTAUTH_URL}/dashboard/billing?success=true`,
      //   cancel_url: `${process.env.NEXTAUTH_URL}/dashboard/billing?cancelled=true`,
      // })

      return NextResponse.json({
        subscription: newSubscription,
        message: 'Subscription created successfully',
        url: '/dashboard/billing' // In real implementation, this would be session.url
      })
    }
  } catch (error) {
    console.error('Error upgrading subscription:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Helper function to get Stripe price IDs (would be used in real implementation)
function getPriceId(plan: string, interval: string): string {
  const priceIds = {
    BASIC: {
      MONTHLY: 'price_basic_monthly',
      YEARLY: 'price_basic_yearly'
    },
    PROFESSIONAL: {
      MONTHLY: 'price_pro_monthly',
      YEARLY: 'price_pro_yearly'
    },
    ENTERPRISE: {
      MONTHLY: 'price_enterprise_monthly',
      YEARLY: 'price_enterprise_yearly'
    }
  }

  return priceIds[plan as keyof typeof priceIds][interval as keyof typeof priceIds.BASIC]
}

// Helper function to calculate prorated amount
function calculateProratedAmount(
  currentAmount: number,
  newAmount: number,
  daysRemaining: number,
  totalDays: number
): number {
  const unusedAmount = currentAmount * (daysRemaining / totalDays)
  const newPeriodAmount = newAmount
  return Math.max(0, newPeriodAmount - unusedAmount)
}
