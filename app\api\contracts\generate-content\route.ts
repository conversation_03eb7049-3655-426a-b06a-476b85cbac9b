import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { customer, quotation, title, value, startDate, endDate } = body

    // For now, we'll generate content based on templates
    // In a real implementation, you would integrate with OpenAI or another AI service
    
    const generatedContent = generateContractContent(customer, quotation, title, value, startDate, endDate)

    return NextResponse.json(generatedContent)
  } catch (error) {
    console.error('Error generating AI content:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function generateContractContent(customer: any, quotation: any, title: string, value: number, startDate: string, endDate: string) {
  const customerName = customer?.name || '[CUSTOMER_NAME]'
  const companyName = customer?.company || customerName
  const contractTitle = title || 'Service Agreement'
  const contractValue = value ? `₹${value.toLocaleString('en-IN')}` : '[CONTRACT_VALUE]'
  const start = startDate ? new Date(startDate).toLocaleDateString('en-IN') : '[START_DATE]'
  const end = endDate ? new Date(endDate).toLocaleDateString('en-IN') : '[END_DATE]'
  
  // Generate contract content
  const content = `SERVICE AGREEMENT

This Service Agreement ("Agreement") is entered into on [DATE] between:

COMPANY: [COMPANY_NAME]
Address: [COMPANY_ADDRESS]
Email: [COMPANY_EMAIL]
Phone: [COMPANY_PHONE]

CLIENT: ${customerName}${companyName !== customerName ? `\nCompany: ${companyName}` : ''}
Address: ${customer?.address ? `${customer.address}, ${customer.city}, ${customer.state}, ${customer.country}` : '[CLIENT_ADDRESS]'}
Email: ${customer?.email || '[CLIENT_EMAIL]'}

1. SCOPE OF SERVICES

${quotation ? `This agreement covers the services outlined in Quotation ${quotation.quotationNumber}: "${quotation.title}".` : 'The Company agrees to provide the following services to the Client:'}

[DETAILED_SCOPE_OF_SERVICES]

2. TERM

This Agreement shall commence on ${start} and shall continue until ${end}, unless terminated earlier in accordance with the provisions herein.

3. COMPENSATION

The total compensation for the services shall be ${contractValue}. Payment terms are as follows:

- 50% advance payment upon signing this agreement
- 50% balance payment upon completion of services
- All payments are due within 30 days of invoice date
- Late payments may incur a 2% monthly service charge

4. DELIVERABLES

The Company shall deliver the following:

[LIST_OF_DELIVERABLES]

All deliverables shall be provided in accordance with the specifications and timeline agreed upon.

5. INTELLECTUAL PROPERTY

All work products, including but not limited to designs, code, documentation, and other materials created under this Agreement, shall become the exclusive property of the Client upon full payment.

6. CONFIDENTIALITY

Both parties agree to maintain the confidentiality of all proprietary information shared during the course of this Agreement.

7. WARRANTIES AND REPRESENTATIONS

The Company warrants that:
- Services will be performed in a professional and workmanlike manner
- All work will be original and will not infringe upon any third-party rights
- Company has the necessary skills and resources to perform the services

8. LIMITATION OF LIABILITY

The Company's liability under this Agreement shall not exceed the total amount paid by the Client under this Agreement.

9. TERMINATION

Either party may terminate this Agreement with 30 days written notice. Upon termination:
- Client shall pay for all services performed up to the termination date
- Company shall deliver all completed work products
- Both parties shall return any confidential information

10. FORCE MAJEURE

Neither party shall be liable for any delay or failure to perform due to circumstances beyond their reasonable control.

11. GOVERNING LAW

This Agreement shall be governed by the laws of India. Any disputes shall be resolved through binding arbitration in [JURISDICTION].

12. ENTIRE AGREEMENT

This Agreement constitutes the entire agreement between the parties and supersedes all prior negotiations, representations, or agreements.

13. AMENDMENTS

This Agreement may only be amended in writing, signed by both parties.

14. SIGNATURES

By signing below, both parties agree to be bound by the terms and conditions of this Agreement.

COMPANY:                           CLIENT:

_____________________             _____________________
[COMPANY_REPRESENTATIVE]          ${customerName}
Date: _______________             Date: _______________

Digital Signature Required: Yes
Auto-Execute After Signing: ${quotation ? 'Yes' : 'No'}`

  // Generate terms
  const terms = `ADDITIONAL TERMS AND CONDITIONS:

1. CHANGE REQUESTS: Any changes to the scope of work must be agreed upon in writing and may result in additional charges.

2. COMMUNICATION: All official communications shall be conducted via email or written correspondence.

3. COMPLIANCE: Both parties shall comply with all applicable laws and regulations.

4. INSURANCE: Company maintains appropriate professional liability insurance.

5. SUBCONTRACTORS: Company may engage qualified subcontractors with Client's prior written consent.

6. DATA PROTECTION: Both parties shall comply with applicable data protection and privacy laws.

7. DISPUTE RESOLUTION: Any disputes shall first be addressed through good faith negotiations before proceeding to arbitration.

8. SEVERABILITY: If any provision of this Agreement is deemed invalid, the remainder shall remain in full force and effect.

9. ASSIGNMENT: Neither party may assign this Agreement without the other party's written consent.

10. NOTICES: All notices shall be sent to the addresses specified in this Agreement.`

  return {
    content,
    terms
  }
}

// Alternative implementation with OpenAI (commented out)
/*
async function generateAIContent(customer: any, quotation: any, title: string, value: number, startDate: string, endDate: string) {
  const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  })

  const prompt = `Generate a professional service contract for:
Customer: ${customer?.name} (${customer?.company})
Service: ${title}
Value: ${value ? `₹${value}` : 'TBD'}
Duration: ${startDate} to ${endDate}
${quotation ? `Based on quotation: ${quotation.title}` : ''}

Please generate:
1. A complete contract with all standard clauses
2. Professional terms and conditions

Make it legally sound and business-appropriate for Indian jurisdiction.`

  try {
    const completion = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: "You are a legal contract specialist. Generate professional, legally sound contracts with proper structure and clauses."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      max_tokens: 2000,
      temperature: 0.3,
    })

    const content = completion.choices[0]?.message?.content || ''
    
    return {
      content: content,
      terms: "Generated terms and conditions"
    }
  } catch (error) {
    console.error('OpenAI API error:', error)
    throw error
  }
}
*/
