import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ContractForm } from '@/components/contracts/contract-form'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'

interface SearchParams {
  customerId?: string
  quotationId?: string
  templateId?: string
}

export default async function NewContractPage({
  searchParams,
}: {
  searchParams: SearchParams
}) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  // Fetch required data
  const [customers, quotations, templates, company] = await Promise.all([
    prisma.customer.findMany({
      where: { 
        companyId: session.user.companyId,
        status: { in: ['ACTIVE', 'PROSPECT'] }
      },
      select: { 
        id: true, 
        name: true, 
        email: true, 
        company: true,
        address: true,
        city: true,
        state: true,
        country: true,
        postalCode: true
      },
      orderBy: { name: 'asc' },
    }),
    prisma.quotation.findMany({
      where: { 
        companyId: session.user.companyId,
        status: 'ACCEPTED'
      },
      include: {
        customer: {
          select: { id: true, name: true, company: true }
        }
      },
      orderBy: { createdAt: 'desc' },
    }),
    prisma.contractTemplate.findMany({
      where: { companyId: session.user.companyId },
      select: { 
        id: true, 
        name: true, 
        description: true,
        content: true,
        category: true
      },
      orderBy: { name: 'asc' },
    }),
    prisma.company.findUnique({
      where: { id: session.user.companyId },
      select: {
        name: true,
        email: true,
        phone: true,
        address: true,
        city: true,
        state: true,
        country: true,
        postalCode: true,
        website: true,
        taxId: true
      }
    })
  ])

  const preselectedCustomerId = searchParams.customerId
  const preselectedQuotationId = searchParams.quotationId
  const preselectedTemplateId = searchParams.templateId

  // Generate next contract number
  const lastContract = await prisma.contract.findFirst({
    where: { companyId: session.user.companyId },
    orderBy: { createdAt: 'desc' },
    select: { contractNumber: true }
  })

  const nextContractNumber = generateContractNumber(lastContract?.contractNumber)

  // If quotation is preselected, get its details
  let preselectedQuotation = null
  if (preselectedQuotationId) {
    preselectedQuotation = quotations.find(q => q.id === preselectedQuotationId)
  }

  // If template is preselected, get its details
  let preselectedTemplate = null
  if (preselectedTemplateId) {
    preselectedTemplate = templates.find(t => t.id === preselectedTemplateId)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href="/dashboard/contracts">
          <Button variant="ghost" >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Contracts
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Create New Contract</h1>
          <p className="text-gray-600 mt-1">
            Create a professional contract with digital signature support
          </p>
        </div>
      </div>

      {/* Contract Form */}
      <ContractForm 
        mode="create" 
        customers={customers}
        quotations={quotations}
        templates={templates}
        company={company}
        preselectedCustomerId={preselectedCustomerId}
        preselectedQuotationId={preselectedQuotationId}
        preselectedQuotation={preselectedQuotation}
        preselectedTemplate={preselectedTemplate}
        contractNumber={nextContractNumber}
      />
    </div>
  )
}

function generateContractNumber(lastNumber?: string): string {
  const currentYear = new Date().getFullYear()
  const currentMonth = String(new Date().getMonth() + 1).padStart(2, '0')
  
  let nextNumber = 1
  
  if (lastNumber) {
    const match = lastNumber.match(/CON-(\d{4})(\d{2})-(\d{4})/)
    if (match) {
      const [, year, month, num] = match
      if (year === currentYear.toString() && month === currentMonth) {
        nextNumber = parseInt(num) + 1
      }
    }
  }
  
  return `CON-${currentYear}${currentMonth}-${String(nextNumber).padStart(4, '0')}`
}
