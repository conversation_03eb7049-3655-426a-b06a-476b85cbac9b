import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const notificationSchema = z.object({
  type: z.enum([
    'INVOICE_OVERDUE',
    'PAYMENT_RECEIVED',
    'QUOTATION_ACCEPTED',
    'QUOTATION_EXPIRED',
    'CONTRACT_SIGNED',
    'CUSTOMER_CREATED',
    'LEAD_CONVERTED',
    'REMINDER',
    'DEADLINE',
    'EMAIL',
    'SYSTEM'
  ]),
  title: z.string().min(1, 'Title is required'),
  message: z.string().min(1, 'Message is required'),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH']).default('MEDIUM'),
  userId: z.string().optional(),
  companyId: z.string().optional(),
  actionUrl: z.string().optional(),
  metadata: z.any().optional()
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') || ''
    const status = searchParams.get('status') || ''
    const priority = searchParams.get('priority') || ''
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = (page - 1) * limit

    // Build where clause
    const where: any = {
      OR: [
        { userId: session.user.id },
        { companyId: session.user.companyId }
      ]
    }

    if (type) {
      where.type = type
    }

    if (status) {
      where.status = status
    }

    if (priority) {
      where.priority = priority
    }

    const [notifications, totalCount] = await Promise.all([
      prisma.notification.findMany({
        where,
        include: {
          user: {
            select: { name: true, firstName: true, lastName: true, email: true }
          }
        },
        orderBy: [
          { priority: 'desc' },
          { createdAt: 'desc' }
        ],
        skip: offset,
        take: limit,
      }),
      prisma.notification.count({ where })
    ])

    return NextResponse.json({
      notifications,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      }
    })
  } catch (error) {
    console.error('Error fetching notifications:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = notificationSchema.parse(body)

    // Determine target user and company
    const targetUserId = validatedData.userId || session.user.id
    const targetCompanyId = validatedData.companyId || session.user.companyId

    const notification = await prisma.notification.create({
      data: {
        type: validatedData.type,
        title: validatedData.title,
        message: validatedData.message,
        priority: validatedData.priority,
        status: 'UNREAD',
        actionUrl: validatedData.actionUrl,
        metadata: validatedData.metadata || {},
        userId: targetUserId,
        companyId: targetCompanyId,
        createdById: session.user.id,
      },
      include: {
        user: {
          select: { name: true, firstName: true, lastName: true, email: true }
        }
      }
    })

    // TODO: Send real-time notification via WebSocket/SSE
    // TODO: Send email notification if user preferences allow
    // TODO: Send push notification if user preferences allow

    return NextResponse.json(notification, { status: 201 })
  } catch (error) {
    console.error('Error creating notification:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Utility function to create notifications (can be called from other parts of the app)
async function createNotification(data: {
  type: string
  title: string
  message: string
  priority?: string
  userId?: string
  companyId?: string
  actionUrl?: string
  metadata?: any
  createdById: string
}) {
  try {
    const notification = await prisma.notification.create({
      data: {
        type: data.type as any,
        title: data.title,
        message: data.message,
        priority: (data.priority as any) || 'MEDIUM',
        status: 'UNREAD',
        actionUrl: data.actionUrl,
        metadata: data.metadata || {},
        userId: data.userId || data.createdById,
        companyId: data.companyId,
        createdById: data.createdById,
      }
    })

    // TODO: Implement real-time notification delivery
    // TODO: Check user preferences and send email/push notifications

    return notification
  } catch (error) {
    console.error('Error creating notification:', error)
    throw error
  }
}

// Utility function to create system-wide notifications
async function createSystemNotification(
  companyId: string,
  type: string,
  title: string,
  message: string,
  priority: string = 'MEDIUM',
  actionUrl?: string,
  metadata?: any
) {
  try {
    // Get all users in the company
    const users = await prisma.user.findMany({
      where: { companyId },
      select: { id: true }
    })

    // Create notifications for all users
    const notifications = await Promise.all(
      users.map(user =>
        prisma.notification.create({
          data: {
            type: type as any,
            title,
            message,
            priority: priority as any,
            status: 'UNREAD',
            actionUrl,
            metadata: metadata || {},
            userId: user.id,
            companyId,
            createdById: 'system', // System-generated
          }
        })
      )
    )

    return notifications
  } catch (error) {
    console.error('Error creating system notifications:', error)
    throw error
  }
}

// Utility function to create business event notifications
async function createBusinessNotification(
  companyId: string,
  type: string,
  title: string,
  message: string,
  entityId?: string,
  entityType?: string,
  priority: string = 'MEDIUM',
  createdById?: string
) {
  try {
    const actionUrl = entityId && entityType 
      ? `/dashboard/${entityType}s/${entityId}`
      : undefined

    const metadata = {
      entityId,
      entityType,
      businessEvent: true
    }

    // Get company admin users for business notifications
    const adminUsers = await prisma.user.findMany({
      where: { 
        companyId,
        role: { in: ['ADMIN', 'OWNER'] }
      },
      select: { id: true }
    })

    // Create notifications for admin users
    const notifications = await Promise.all(
      adminUsers.map(user =>
        prisma.notification.create({
          data: {
            type: type as any,
            title,
            message,
            priority: priority as any,
            status: 'UNREAD',
            actionUrl,
            metadata,
            userId: user.id,
            companyId,
            createdById: createdById || 'system',
          }
        })
      )
    )

    return notifications
  } catch (error) {
    console.error('Error creating business notifications:', error)
    throw error
  }
}
