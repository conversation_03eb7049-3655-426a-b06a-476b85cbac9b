'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useRouter } from 'next/navigation'
import { toast } from 'react-hot-toast'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Save, Loader2, Eye, Code } from 'lucide-react'

const templateSchema = z.object({
  name: z.string().min(1, 'Template name is required'),
  subject: z.string().min(1, 'Subject is required'),
  body: z.string().min(1, 'Body content is required'),
  type: z.enum([
    'QUOTATION',
    'INVOICE',
    'CONTRACT',
    'RECEIPT',
    'REMINDER',
    'NOTIFICATION',
    'MARKETING',
    'CUSTOM'
  ]),
  status: z.enum(['ACTIVE', 'INACTIVE', 'DRAFT']),
  variables: z.array(z.string()).optional()
})

type TemplateFormData = z.infer<typeof templateSchema>

interface EmailTemplateFormProps {
  mode: 'create' | 'edit'
  template?: any
}

export function EmailTemplateForm({ mode, template }: EmailTemplateFormProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [previewMode, setPreviewMode] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue
  } = useForm<TemplateFormData>({
    resolver: zodResolver(templateSchema),
    defaultValues: template || {
      name: '',
      subject: '',
      body: '',
      type: 'CUSTOM',
      status: 'DRAFT',
      variables: []
    }
  })

  const onSubmit = async (data: TemplateFormData) => {
    setIsLoading(true)

    try {
      const url = mode === 'create' ? '/api/email-templates' : `/api/email-templates/${template.id}`
      const method = mode === 'create' ? 'POST' : 'PUT'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to save template')
      }

      const result = await response.json()
      
      toast.success(mode === 'create' ? 'Template created successfully!' : 'Template updated successfully!')
      
      if (mode === 'create') {
        router.push(`/dashboard/emails/templates/${result.id}`)
      } else {
        router.push('/dashboard/emails/templates')
      }
    } catch (error) {
      console.error('Error saving template:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to save template'
      toast.error(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const templateTypes = [
    { value: 'QUOTATION', label: 'Quotation' },
    { value: 'INVOICE', label: 'Invoice' },
    { value: 'CONTRACT', label: 'Contract' },
    { value: 'RECEIPT', label: 'Receipt' },
    { value: 'REMINDER', label: 'Reminder' },
    { value: 'NOTIFICATION', label: 'Notification' },
    { value: 'MARKETING', label: 'Marketing' },
    { value: 'CUSTOM', label: 'Custom' }
  ]

  const availableVariables = [
    '{{company.name}}',
    '{{company.email}}',
    '{{customer.name}}',
    '{{customer.email}}',
    '{{user.name}}',
    '{{date}}',
    '{{amount}}',
    '{{invoice.number}}',
    '{{quotation.number}}',
    '{{contract.number}}'
  ]

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Form */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Template Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Template Name</Label>
                  <Input
                    id="name"
                    {...register('name')}
                    placeholder="Enter template name"
                  />
                  {errors.name && (
                    <p className="text-sm text-red-600">{errors.name.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="type">Template Type</Label>
                  <select
                    id="type"
                    {...register('type')}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {templateTypes.map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                  {errors.type && (
                    <p className="text-sm text-red-600">{errors.type.message}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="subject">Email Subject</Label>
                <Input
                  id="subject"
                  {...register('subject')}
                  placeholder="Enter email subject"
                />
                {errors.subject && (
                  <p className="text-sm text-red-600">{errors.subject.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="body">Email Body</Label>
                  <div className="flex items-center space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setPreviewMode(!previewMode)}
                    >
                      {previewMode ? <Code className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      {previewMode ? 'Edit' : 'Preview'}
                    </Button>
                  </div>
                </div>
                
                {previewMode ? (
                  <div 
                    className="min-h-[300px] p-4 border border-gray-300 rounded-md bg-white"
                    dangerouslySetInnerHTML={{ __html: watch('body') }}
                  />
                ) : (
                  <Textarea
                    id="body"
                    {...register('body')}
                    placeholder="Enter email body content (HTML supported)"
                    rows={12}
                    className="font-mono"
                  />
                )}
                {errors.body && (
                  <p className="text-sm text-red-600">{errors.body.message}</p>
                )}
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Template Status</Label>
                  <p className="text-sm text-gray-500">Set template as active or draft</p>
                </div>
                <select
                  {...register('status')}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="DRAFT">Draft</option>
                  <option value="ACTIVE">Active</option>
                  <option value="INACTIVE">Inactive</option>
                </select>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Available Variables</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p className="text-sm text-gray-600 mb-4">
                  Click to insert variables into your template:
                </p>
                <div className="space-y-2">
                  {availableVariables.map((variable) => (
                    <Button
                      key={variable}
                      type="button"
                      variant="outline"
                      size="sm"
                      className="w-full justify-start text-xs"
                      onClick={() => {
                        const currentBody = watch('body')
                        setValue('body', currentBody + variable)
                      }}
                    >
                      {variable}
                    </Button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Template Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label className="text-xs">Subject:</Label>
                  <p className="text-sm font-medium">{watch('subject') || 'No subject'}</p>
                </div>
                <div>
                  <Label className="text-xs">Type:</Label>
                  <Badge variant="outline">{watch('type')}</Badge>
                </div>
                <div>
                  <Label className="text-xs">Status:</Label>
                  <Badge variant={watch('status') === 'ACTIVE' ? 'default' : 'secondary'}>
                    {watch('status')}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center justify-end space-x-4 p-6 bg-gray-50 rounded-lg">
        <Button
          type="button"
          variant="outline"
          onClick={() => router.back()}
        >
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <Save className="h-4 w-4 mr-2" />
          )}
          {mode === 'create' ? 'Create Template' : 'Update Template'}
        </Button>
      </div>
    </form>
  )
}
