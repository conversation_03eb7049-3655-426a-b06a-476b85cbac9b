'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  BookOpen, 
  ExternalLink,
  TrendingUp,
  ThumbsUp,
  ThumbsDown,
  Eye,
  Search,
  ArrowRight
} from 'lucide-react'
import Link from 'next/link'

interface KnowledgeBaseWidgetProps {
  articles: Array<{
    id: string
    title: string
    slug: string
    category: string
    views: number
    helpful: number
    notHelpful: number
  }>
}

export function KnowledgeBaseWidget({ articles }: KnowledgeBaseWidgetProps) {
  const getCategoryColor = (category: string) => {
    switch (category.toLowerCase()) {
      case 'getting started':
        return 'text-green-600 border-green-200'
      case 'billing':
        return 'text-blue-600 border-blue-200'
      case 'api':
        return 'text-purple-600 border-purple-200'
      case 'troubleshooting':
        return 'text-red-600 border-red-200'
      case 'features':
        return 'text-orange-600 border-orange-200'
      default:
        return 'text-gray-600 border-gray-200'
    }
  }

  const getHelpfulnessRatio = (helpful: number, notHelpful: number) => {
    const total = helpful + notHelpful
    if (total === 0) return 0
    return (helpful / total) * 100
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <BookOpen className="h-5 w-5" />
            <span>Knowledge Base</span>
          </CardTitle>
          <Link href="/dashboard/support/knowledge-base">
            <Button variant="ghost" >
              <ExternalLink className="h-4 w-4" />
            </Button>
          </Link>
        </div>
      </CardHeader>
      <CardContent>
        {articles.length > 0 ? (
          <div className="space-y-4">
            {/* Popular Articles */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center space-x-2">
                <TrendingUp className="h-4 w-4" />
                <span>Popular Articles</span>
              </h4>
              <div className="space-y-3">
                {articles.slice(0, 5).map((article) => {
                  const helpfulnessRatio = getHelpfulnessRatio(article.helpful, article.notHelpful)
                  
                  return (
                    <div key={article.id} className="group">
                      <Link href={`/dashboard/support/knowledge-base/${article.slug}`}>
                        <div className="p-3 border rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                          <div className="flex items-start justify-between mb-2">
                            <h5 className="font-medium text-gray-900 text-sm group-hover:text-blue-600 transition-colors line-clamp-2">
                              {article.title}
                            </h5>
                            <ArrowRight className="h-4 w-4 text-gray-400 group-hover:text-blue-600 transition-colors flex-shrink-0 ml-2" />
                          </div>
                          
                          <div className="flex items-center justify-between">
                            <Badge className={getCategoryColor(article.category)} variant="outline" >
                              {article.category}
                            </Badge>
                            
                            <div className="flex items-center space-x-3 text-xs text-gray-500">
                              <div className="flex items-center space-x-1">
                                <Eye className="h-3 w-3" />
                                <span>{article.views}</span>
                              </div>
                              {(article.helpful > 0 || article.notHelpful > 0) && (
                                <div className="flex items-center space-x-1">
                                  <ThumbsUp className="h-3 w-3" />
                                  <span>{helpfulnessRatio.toFixed(0)}%</span>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </Link>
                    </div>
                  )
                })}
              </div>
            </div>

            {/* Quick Actions */}
            <div className="pt-4 border-t">
              <div className="space-y-2">
                <Link href="/dashboard/support/knowledge-base">
                  <Button variant="ghost" className="w-full justify-start" >
                    <Search className="h-4 w-4 mr-2" />
                    Search All Articles
                  </Button>
                </Link>
                <Link href="/dashboard/support/knowledge-base/categories">
                  <Button variant="ghost" className="w-full justify-start" >
                    <BookOpen className="h-4 w-4 mr-2" />
                    Browse Categories
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-6">
            <BookOpen className="h-8 w-8 text-gray-300 mx-auto mb-3" />
            <h4 className="font-medium text-gray-900 mb-2">No Articles Available</h4>
            <p className="text-sm text-gray-500 mb-4">
              Knowledge base articles will appear here once they're published.
            </p>
            <Link href="/dashboard/support/knowledge-base">
              <Button variant="outline" >
                <BookOpen className="h-4 w-4 mr-2" />
                Browse Knowledge Base
              </Button>
            </Link>
          </div>
        )}

        {/* Knowledge Base Stats */}
        {articles.length > 0 && (
          <div className="mt-6 pt-4 border-t">
            <div className="grid grid-cols-2 gap-4 text-center">
              <div className="p-3 bg-blue-50 rounded-lg">
                <div className="text-lg font-bold text-blue-800">
                  {articles.length}
                </div>
                <div className="text-xs text-blue-600">Articles</div>
              </div>
              <div className="p-3 bg-green-50 rounded-lg">
                <div className="text-lg font-bold text-green-800">
                  {articles.reduce((sum, article) => sum + article.views, 0)}
                </div>
                <div className="text-xs text-green-600">Total Views</div>
              </div>
            </div>
          </div>
        )}

        {/* Help Text */}
        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
          <div className="text-sm text-gray-700 mb-2">
            <strong>Can't find what you're looking for?</strong>
          </div>
          <div className="text-xs text-gray-600 mb-3">
            Try searching our knowledge base or create a support ticket for personalized help.
          </div>
          <div className="flex space-x-2">
            <Link href="/dashboard/support/knowledge-base">
              <Button variant="outline" size="sm" className="flex-1">
                Search KB
              </Button>
            </Link>
            <Button size="sm" className="flex-1">
              Create Ticket
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
