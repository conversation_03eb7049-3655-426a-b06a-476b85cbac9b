'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { formatCurrency, formatDate, getStatusColor } from '@/lib/utils'
import { 
  Eye, 
  Edit, 
  MoreHorizontal,
  DollarSign,
  Calendar,
  User,
  Building
} from 'lucide-react'
import toast from 'react-hot-toast'

interface Lead {
  id: string
  title: string
  description: string | null
  status: string
  priority: string
  value: number | null
  source: string | null
  createdAt: Date
  customer: {
    id: string
    name: string
    email: string | null
    company: string | null
  } | null
  _count: {
    quotations: number
    activities: number
  }
}

interface LeadsPipelineProps {
  leads: Lead[]
}

const PIPELINE_STAGES = [
  { key: 'NEW', label: 'New', color: 'bg-blue-50 border-blue-200' },
  { key: 'CONTACTED', label: 'Contacted', color: 'bg-yellow-50 border-yellow-200' },
  { key: 'QUALIFIED', label: 'Qualified', color: 'bg-purple-50 border-purple-200' },
  { key: 'PROPOSAL', label: 'Proposal', color: 'bg-orange-50 border-orange-200' },
  { key: 'NEGOTIATION', label: 'Negotiation', color: 'bg-indigo-50 border-indigo-200' },
  { key: 'CLOSED_WON', label: 'Closed Won', color: 'bg-green-50 border-green-200' },
  { key: 'CLOSED_LOST', label: 'Closed Lost', color: 'bg-red-50 border-red-200' },
]

export function LeadsPipeline({ leads }: LeadsPipelineProps) {
  const [draggedLead, setDraggedLead] = useState<string | null>(null)

  // Group leads by status
  const groupedLeads = PIPELINE_STAGES.reduce((acc, stage) => {
    acc[stage.key] = leads.filter(lead => lead.status === stage.key)
    return acc
  }, {} as Record<string, Lead[]>)

  const handleDragStart = (leadId: string) => {
    setDraggedLead(leadId)
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
  }

  const handleDrop = async (e: React.DragEvent, newStatus: string) => {
    e.preventDefault()
    
    if (!draggedLead) return

    try {
      const response = await fetch(`/api/leads/${draggedLead}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      })

      if (!response.ok) {
        throw new Error('Failed to update lead status')
      }

      toast.success('Lead status updated successfully')
      window.location.reload()
    } catch (error) {
      toast.error('Failed to update lead status')
    } finally {
      setDraggedLead(null)
    }
  }

  const calculateStageValue = (stageLeads: Lead[]) => {
    return stageLeads.reduce((sum, lead) => sum + (lead.value || 0), 0)
  }

  return (
    <div className="overflow-x-auto">
      <div className="flex space-x-4 min-w-max pb-4">
        {PIPELINE_STAGES.map((stage) => {
          const stageLeads = groupedLeads[stage.key] || []
          const stageValue = calculateStageValue(stageLeads)

          return (
            <div
              key={stage.key}
              className={`w-80 ${stage.color} rounded-lg border-2 border-dashed`}
              onDragOver={handleDragOver}
              onDrop={(e) => handleDrop(e, stage.key)}
            >
              {/* Stage Header */}
              <div className="p-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-gray-900">{stage.label}</h3>
                  <Badge variant="secondary">{stageLeads.length}</Badge>
                </div>
                {stageValue > 0 && (
                  <p className="text-sm text-gray-600 mt-1">
                    {formatCurrency(stageValue)}
                  </p>
                )}
              </div>

              {/* Stage Content */}
              <div className="p-4 space-y-3 max-h-96 overflow-y-auto">
                {stageLeads.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-gray-500 text-sm">No leads in this stage</p>
                  </div>
                ) : (
                  stageLeads.map((lead) => (
                    <Card
                      key={lead.id}
                      className="cursor-move hover:shadow-md transition-shadow bg-white"
                      draggable
                      onDragStart={() => handleDragStart(lead.id)}
                    >
                      <CardContent className="p-4">
                        <div className="space-y-3">
                          {/* Lead Title and Priority */}
                          <div className="flex items-start justify-between">
                            <h4 className="font-medium text-gray-900 text-sm leading-tight">
                              {lead.title}
                            </h4>
                            <Badge 
                              className={getStatusColor(lead.priority)} 
                              variant="outline"
                              >
                              {lead.priority}
                            </Badge>
                          </div>

                          {/* Lead Description */}
                          {lead.description && (
                            <p className="text-xs text-gray-600 line-clamp-2">
                              {lead.description}
                            </p>
                          )}

                          {/* Customer Info */}
                          {lead.customer && (
                            <div className="flex items-center space-x-2">
                              <User className="h-3 w-3 text-gray-400" />
                              <span className="text-xs text-gray-600">
                                {lead.customer.name}
                              </span>
                              {lead.customer.company && (
                                <>
                                  <Building className="h-3 w-3 text-gray-400" />
                                  <span className="text-xs text-gray-600">
                                    {lead.customer.company}
                                  </span>
                                </>
                              )}
                            </div>
                          )}

                          {/* Lead Value */}
                          {lead.value && (
                            <div className="flex items-center space-x-2">
                              <DollarSign className="h-3 w-3 text-gray-400" />
                              <span className="text-sm font-medium text-gray-900">
                                {formatCurrency(lead.value)}
                              </span>
                            </div>
                          )}

                          {/* Lead Date */}
                          <div className="flex items-center space-x-2">
                            <Calendar className="h-3 w-3 text-gray-400" />
                            <span className="text-xs text-gray-500">
                              {formatDate(lead.createdAt)}
                            </span>
                          </div>

                          {/* Lead Source */}
                          {lead.source && (
                            <div className="text-xs text-gray-500">
                              Source: {lead.source}
                            </div>
                          )}

                          {/* Activity Count */}
                          <div className="flex items-center justify-between text-xs text-gray-500">
                            <span>{lead._count.quotations} quotations</span>
                            <span>{lead._count.activities} activities</span>
                          </div>

                          {/* Actions */}
                          <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                            <div className="flex items-center space-x-1">
                              <Link href={`/dashboard/leads/${lead.id}`}>
                                <Button variant="ghost" >
                                  <Eye className="h-3 w-3" />
                                </Button>
                              </Link>
                              <Link href={`/dashboard/leads/${lead.id}/edit`}>
                                <Button variant="ghost" >
                                  <Edit className="h-3 w-3" />
                                </Button>
                              </Link>
                            </div>
                            <Button variant="ghost" >
                              <MoreHorizontal className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}
