'use client'

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { formatDate, formatTimeAgo, getStatusColor } from '@/lib/utils'
import { 
  Activity, 
  User, 
  Calendar, 
  FileText,
  Send,
  Eye,
  CheckCircle,
  XCircle,
  Edit,
  MessageSquare
} from 'lucide-react'

interface QuotationActivityProps {
  activities: Array<{
    id: string
    type: string
    title: string
    description: string | null
    createdAt: Date
    createdBy: {
      name: string | null
      firstName: string | null
      lastName: string | null
    }
  }>
}

export function QuotationActivity({ activities }: QuotationActivityProps) {
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'QUOTATION_CREATED':
        return FileText
      case 'QUOTATION_SENT':
        return Send
      case 'QUOTATION_VIEWED':
        return Eye
      case 'QUOTATION_ACCEPTED':
        return CheckCircle
      case 'QUOTATION_REJECTED':
        return XCircle
      case 'QUOTATION_UPDATED':
        return Edit
      case 'NOTE':
        return MessageSquare
      default:
        return Activity
    }
  }

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'QUOTATION_CREATED':
        return 'text-blue-600'
      case 'QUOTATION_SENT':
        return 'text-purple-600'
      case 'QUOTATION_VIEWED':
        return 'text-yellow-600'
      case 'QUOTATION_ACCEPTED':
        return 'text-green-600'
      case 'QUOTATION_REJECTED':
        return 'text-red-600'
      case 'QUOTATION_UPDATED':
        return 'text-orange-600'
      case 'NOTE':
        return 'text-gray-600'
      default:
        return 'text-gray-600'
    }
  }

  const getUserName = (user: any) => {
    if (user.name) return user.name
    if (user.firstName || user.lastName) {
      return `${user.firstName || ''} ${user.lastName || ''}`.trim()
    }
    return 'Unknown User'
  }

  if (activities.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>Activity Timeline</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Activity className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No activity yet</h3>
            <p className="text-gray-600">
              Quotation activity will appear here as actions are taken.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Activity className="h-5 w-5" />
          <span>Activity Timeline</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity, index) => {
            const ActivityIcon = getActivityIcon(activity.type)
            const iconColor = getActivityColor(activity.type)
            
            return (
              <div key={activity.id} className="flex items-start space-x-3">
                {/* Timeline line */}
                <div className="flex flex-col items-center">
                  <div className={`p-2 rounded-full bg-gray-100 ${iconColor}`}>
                    <ActivityIcon className="h-4 w-4" />
                  </div>
                  {index < activities.length - 1 && (
                    <div className="w-px h-8 bg-gray-200 mt-2" />
                  )}
                </div>
                
                {/* Activity content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-medium text-gray-900">
                      {activity.title}
                    </h4>
                    <span className="text-xs text-gray-500">
                      {formatTimeAgo(activity.createdAt)}
                    </span>
                  </div>
                  
                  {activity.description && (
                    <p className="text-sm text-gray-600 mt-1">
                      {activity.description}
                    </p>
                  )}
                  
                  <div className="flex items-center space-x-2 mt-2">
                    <User className="h-3 w-3 text-gray-400" />
                    <span className="text-xs text-gray-500">
                      {getUserName(activity.createdBy)}
                    </span>
                    <Calendar className="h-3 w-3 text-gray-400 ml-2" />
                    <span className="text-xs text-gray-500">
                      {formatDate(activity.createdAt)}
                    </span>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
