import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const contractSchema = z.object({
  contractNumber: z.string().min(1, 'Contract number is required'),
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  customerId: z.string().min(1, 'Customer is required'),
  quotationId: z.string().optional(),
  content: z.string().min(1, 'Contract content is required'),
  value: z.number().min(0).optional().nullable(),
  startDate: z.date().optional().nullable(),
  endDate: z.date().optional().nullable(),
  terms: z.string().optional(),
  signatureRequired: z.boolean().default(true),
  autoExecute: z.boolean().default(false),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || ''
    const customerId = searchParams.get('customerId') || ''
    const quotationId = searchParams.get('quotationId') || ''
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = (page - 1) * limit

    // Build where clause
    const where: any = {
      companyId: session.user.companyId,
    }

    if (search) {
      where.OR = [
        { contractNumber: { contains: search } },
        { title: { contains: search } },
        { description: { contains: search } },
        { customer: { name: { contains: search } } },
      ]
    }

    if (status) {
      where.status = status
    }

    if (customerId) {
      where.customerId = customerId
    }

    if (quotationId) {
      where.quotationId = quotationId
    }

    const [contracts, totalCount] = await Promise.all([
      prisma.contract.findMany({
        where,
        include: {
          customer: {
            select: { id: true, name: true, email: true, company: true }
          },
          quotation: {
            select: { id: true, quotationNumber: true, title: true }
          },
          signatures: {
            include: {
              signedBy: {
                select: { name: true, firstName: true, lastName: true }
              }
            }
          },
          _count: {
            select: {
              signatures: true,
              activities: true,
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip: offset,
        take: limit,
      }),
      prisma.contract.count({ where })
    ])

    return NextResponse.json({
      contracts,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      }
    })
  } catch (error) {
    console.error('Error fetching contracts:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = contractSchema.parse(body)

    // Verify customer belongs to the company
    const customer = await prisma.customer.findFirst({
      where: {
        id: validatedData.customerId,
        companyId: session.user.companyId,
      }
    })

    if (!customer) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 400 }
      )
    }

    // Verify quotation belongs to the company (if provided)
    if (validatedData.quotationId) {
      const quotation = await prisma.quotation.findFirst({
        where: {
          id: validatedData.quotationId,
          companyId: session.user.companyId,
        }
      })

      if (!quotation) {
        return NextResponse.json(
          { error: 'Quotation not found' },
          { status: 400 }
        )
      }
    }

    // Check if contract number already exists
    const existingContract = await prisma.contract.findFirst({
      where: {
        contractNumber: validatedData.contractNumber,
        companyId: session.user.companyId,
      }
    })

    if (existingContract) {
      return NextResponse.json(
        { error: 'Contract number already exists' },
        { status: 400 }
      )
    }

    const contract = await prisma.contract.create({
      data: {
        contractNumber: validatedData.contractNumber,
        title: validatedData.title,
        description: validatedData.description,
        content: validatedData.content,
        value: validatedData.value,
        startDate: validatedData.startDate,
        endDate: validatedData.endDate,
        terms: validatedData.terms,
        signatureRequired: validatedData.signatureRequired,
        autoExecute: validatedData.autoExecute,
        status: 'DRAFT',
        customerId: validatedData.customerId,
        quotationId: validatedData.quotationId,
        companyId: session.user.companyId,
        createdById: session.user.id,
      },
      include: {
        customer: {
          select: { id: true, name: true, email: true, company: true }
        },
        quotation: {
          select: { id: true, quotationNumber: true, title: true }
        },
        signatures: {
          include: {
            signedBy: {
              select: { name: true, firstName: true, lastName: true }
            }
          }
        },
        _count: {
          select: {
            signatures: true,
            activities: true,
          }
        }
      }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'CONTRACT_CREATED',
        title: 'Contract created',
        description: `Contract "${contract.contractNumber}" was created`,
        contractId: contract.id,
        customerId: contract.customerId,
        quotationId: contract.quotationId,
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    return NextResponse.json(contract, { status: 201 })
  } catch (error) {
    console.error('Error creating contract:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
