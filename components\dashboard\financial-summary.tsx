'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { formatCurrency } from '@/lib/utils'
import { 
  DollarSign, 
  TrendingUp, 
  TrendingDown,
  FileText,
  Receipt,

  Clock,
  CheckCircle,
  AlertTriangle
} from 'lucide-react'

interface FinancialSummaryProps {
  data: {
    quotationStats: Array<{
      status: string
      _count: number
      _sum: { total: number | null }
    }>
    invoiceStats: Array<{
      status: string
      _count: number
      _sum: { total: number | null }
    }>
    contractStats: Array<{
      status: string
      _count: number
      _sum: { value: number | null }
    }>
    period: string
    startDate: Date
    endDate: Date
  }
}

export function FinancialSummary({ data }: FinancialSummaryProps) {
  // Calculate quotation metrics
  const quotationMetrics = {
    total: data.quotationStats.reduce((sum, stat) => sum + (stat._sum.total || 0), 0),
    count: data.quotationStats.reduce((sum, stat) => sum + stat._count, 0),
    sent: data.quotationStats.find(s => s.status === 'SENT')?._sum.total || 0,
    accepted: data.quotationStats.find(s => s.status === 'ACCEPTED')?._sum.total || 0,
    rejected: data.quotationStats.find(s => s.status === 'REJECTED')?._sum.total || 0,
  }

  // Calculate invoice metrics
  const invoiceMetrics = {
    total: data.invoiceStats.reduce((sum, stat) => sum + (stat._sum.total || 0), 0),
    count: data.invoiceStats.reduce((sum, stat) => sum + stat._count, 0),
    paid: data.invoiceStats.find(s => s.status === 'PAID')?._sum.total || 0,
    pending: data.invoiceStats.filter(s => ['SENT', 'VIEWED'].includes(s.status))
      .reduce((sum, stat) => sum + (stat._sum.total || 0), 0),
    overdue: data.invoiceStats.find(s => s.status === 'OVERDUE')?._sum.total || 0,
  }

  // Calculate contract metrics
  const contractMetrics = {
    total: data.contractStats.reduce((sum, stat) => sum + (stat._sum.value || 0), 0),
    count: data.contractStats.reduce((sum, stat) => sum + stat._count, 0),
    signed: data.contractStats.find(s => s.status === 'SIGNED')?._sum.value || 0,
    executed: data.contractStats.find(s => s.status === 'EXECUTED')?._sum.value || 0,
    pending: data.contractStats.filter(s => ['SENT', 'VIEWED'].includes(s.status))
      .reduce((sum, stat) => sum + (stat._sum.value || 0), 0),
  }

  // Calculate conversion rates
  const quotationToInvoiceRate = quotationMetrics.count > 0 
    ? ((invoiceMetrics.count / quotationMetrics.count) * 100).toFixed(1)
    : '0'

  const invoicePaymentRate = invoiceMetrics.count > 0
    ? ((data.invoiceStats.find(s => s.status === 'PAID')?._count || 0) / invoiceMetrics.count * 100).toFixed(1)
    : '0'

  const contractSignatureRate = contractMetrics.count > 0
    ? ((data.contractStats.filter(s => ['SIGNED', 'EXECUTED'].includes(s.status))
        .reduce((sum, stat) => sum + stat._count, 0) / contractMetrics.count) * 100).toFixed(1)
    : '0'

  const financialCards = [
    {
      title: 'Quotation Value',
      value: formatCurrency(quotationMetrics.total),
      icon: FileText,
 Receipt,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      description: `${quotationMetrics.count} quotations`,
      subMetrics: [
        { label: 'Accepted', value: formatCurrency(quotationMetrics.accepted), color: 'text-green-600' },
        { label: 'Pending', value: formatCurrency(quotationMetrics.sent), color: 'text-yellow-600' },
        { label: 'Rejected', value: formatCurrency(quotationMetrics.rejected), color: 'text-red-600' },
      ]
    },
    {
      title: 'Invoice Value',
      value: formatCurrency(invoiceMetrics.total),
      icon: Receipt,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      description: `${invoiceMetrics.count} invoices`,
      subMetrics: [
        { label: 'Paid', value: formatCurrency(invoiceMetrics.paid), color: 'text-green-600' },
        { label: 'Pending', value: formatCurrency(invoiceMetrics.pending), color: 'text-yellow-600' },
        { label: 'Overdue', value: formatCurrency(invoiceMetrics.overdue), color: 'text-red-600' },
      ]
    },
    {
      title: 'Contract Value',
      value: formatCurrency(contractMetrics.total),
      icon: FileText,
 Receipt,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      description: `${contractMetrics.count} contracts`,
      subMetrics: [
        { label: 'Executed', value: formatCurrency(contractMetrics.executed), color: 'text-green-600' },
        { label: 'Signed', value: formatCurrency(contractMetrics.signed), color: 'text-blue-600' },
        { label: 'Pending', value: formatCurrency(contractMetrics.pending), color: 'text-yellow-600' },
      ]
    },
    {
      title: 'Conversion Rates',
      value: `${quotationToInvoiceRate}%`,
      icon: TrendingUp,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-100',
      description: 'Quote to invoice',
      subMetrics: [
        { label: 'Payment Rate', value: `${invoicePaymentRate}%`, color: 'text-green-600' },
        { label: 'Signature Rate', value: `${contractSignatureRate}%`, color: 'text-blue-600' },
        { label: 'Overall Health', value: 'Good', color: 'text-green-600' },
      ]
    }
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Financial Summary</span>
          <span className="text-sm font-normal text-gray-500">
            {data.period === '7d' ? 'Last 7 days' :
             data.period === '30d' ? 'Last 30 days' :
             data.period === '90d' ? 'Last 90 days' :
             data.period === '1y' ? 'Last year' : 'Custom period'}
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {financialCards.map((card, index) => (
            <div key={index} className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className={`p-2 rounded-lg ${card.bgColor}`}>
                  <card.icon className={`h-5 w-5 ${card.color}`} />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">{card.title}</p>
                  <p className="text-xl font-bold text-gray-900">{card.value}</p>
                  <p className="text-xs text-gray-500">{card.description}</p>
                </div>
              </div>
              
              <div className="space-y-1">
                {card.subMetrics.map((metric, metricIndex) => (
                  <div key={metricIndex} className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">{metric.label}</span>
                    <span className={`text-xs font-medium ${metric.color}`}>
                      {metric.value}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Key Performance Indicators */}
        <div className="mt-6 pt-6 border-t">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Key Performance Indicators</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-sm font-medium text-gray-900">Revenue Growth</p>
                <p className="text-xs text-gray-500">
                  {invoiceMetrics.paid > 0 ? 'Positive' : 'No revenue yet'}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-yellow-600" />
              <div>
                <p className="text-sm font-medium text-gray-900">Pending Value</p>
                <p className="text-xs text-gray-500">
                  {formatCurrency(invoiceMetrics.pending + contractMetrics.pending)}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <AlertTriangle className={`h-4 w-4 ${invoiceMetrics.overdue > 0 ? 'text-red-600' : 'text-gray-400'}`} />
              <div>
                <p className="text-sm font-medium text-gray-900">Overdue Amount</p>
                <p className="text-xs text-gray-500">
                  {formatCurrency(invoiceMetrics.overdue)}
                </p>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
