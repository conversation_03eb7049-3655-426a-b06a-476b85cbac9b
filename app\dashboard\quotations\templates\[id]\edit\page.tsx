import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { notFound } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { ArrowLeft, FileText } from 'lucide-react'
import { QuotationTemplateForm } from '@/components/quotations/quotation-template-form'

interface PageProps {
  params: {
    id: string
  }
}

export default async function EditQuotationTemplatePage({ params }: PageProps) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  const template = await prisma.quotationTemplate.findFirst({
    where: {
      id: params.id,
      companyId: session.user.companyId
    }
  })

  if (!template) {
    notFound()
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href={`/dashboard/quotations/templates/${template.id}`}>
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Template
          </Button>
        </Link>
        <div className="p-2 bg-blue-100 rounded-lg">
          <FileText className="h-6 w-6 text-blue-600" />
        </div>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Edit Quotation Template</h1>
          <p className="text-gray-600 mt-1">
            Modify "{template.name}" template
          </p>
        </div>
      </div>

      {/* Template Form */}
      <QuotationTemplateForm mode="edit" template={template} />
    </div>
  )
}
