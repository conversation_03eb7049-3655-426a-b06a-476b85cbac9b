'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { 
  Activity, 
  TrendingUp,
  TrendingDown,
  Clock,
  Zap,
  CheckCircle,
  XCircle,
  AlertTriangle
} from 'lucide-react'

interface ApiUsageStatsProps {
  usage: {
    monthlyRequests: number
    dailyRequests: number
    totalKeys: number
    activeKeys: number
    totalWebhooks: number
    activeWebhooks: number
    topEndpoints: Array<{
      endpoint: string
      _count: number
    }>
    statusCodes: Array<{
      statusCode: number
      _count: number
    }>
  }
  limits: {
    requestsPerMinute: number
    requestsPerDay: number
    requestsPerMonth: number
  }
}

export function ApiUsageStats({ usage, limits }: ApiUsageStatsProps) {
  const getUsagePercentage = (used: number, limit: number) => {
    return Math.min((used / limit) * 100, 100)
  }

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-600'
    if (percentage >= 75) return 'text-yellow-600'
    return 'text-green-600'
  }

  const dailyUsagePercentage = getUsagePercentage(usage.dailyRequests, limits.requestsPerDay)
  const monthlyUsagePercentage = getUsagePercentage(usage.monthlyRequests, limits.requestsPerMonth)

  // Calculate success rate
  const totalRequests = usage.statusCodes.reduce((sum, status) => sum + status._count, 0)
  const successfulRequests = usage.statusCodes
    .filter(status => status.statusCode >= 200 && status.statusCode < 300)
    .reduce((sum, status) => sum + status._count, 0)
  const successRate = totalRequests > 0 ? (successfulRequests / totalRequests * 100).toFixed(1) : '0'

  const errorRequests = usage.statusCodes
    .filter(status => status.statusCode >= 400)
    .reduce((sum, status) => sum + status._count, 0)
  const errorRate = totalRequests > 0 ? (errorRequests / totalRequests * 100).toFixed(1) : '0'

  return (
    <div className="space-y-6">
      {/* Usage Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>Usage Statistics</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Daily Usage */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-700">Daily Requests</span>
                <span className={`text-sm font-medium ${getUsageColor(dailyUsagePercentage)}`}>
                  {usage.dailyRequests.toLocaleString()} / {limits.requestsPerDay.toLocaleString()}
                </span>
              </div>
              <Progress value={dailyUsagePercentage} className="h-2" />
              <div className="text-xs text-gray-500 mt-1">
                {dailyUsagePercentage.toFixed(1)}% of daily limit
              </div>
            </div>

            {/* Monthly Usage */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-700">Monthly Requests</span>
                <span className={`text-sm font-medium ${getUsageColor(monthlyUsagePercentage)}`}>
                  {usage.monthlyRequests.toLocaleString()} / {limits.requestsPerMonth.toLocaleString()}
                </span>
              </div>
              <Progress value={monthlyUsagePercentage} className="h-2" />
              <div className="text-xs text-gray-500 mt-1">
                {monthlyUsagePercentage.toFixed(1)}% of monthly limit
              </div>
            </div>

            {/* Rate Limit Info */}
            <div className="p-3 bg-blue-50 rounded-lg">
              <div className="flex items-center space-x-2 mb-1">
                <Zap className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium text-blue-900">Rate Limit</span>
              </div>
              <div className="text-lg font-bold text-blue-900">
                {limits.requestsPerMinute.toLocaleString()} req/min
              </div>
              <div className="text-xs text-blue-700">
                Maximum requests per minute
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* API Health */}
      <Card>
        <CardHeader>
          <CardTitle>API Health</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Success Rate */}
            <div className="text-center p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg">
              <div className="text-3xl font-bold text-gray-900 mb-1">
                {successRate}%
              </div>
              <div className="text-sm text-gray-600 mb-2">Success Rate</div>
              <div className="flex items-center justify-center space-x-1">
                {parseFloat(successRate) >= 95 ? (
                  <>
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="text-xs text-green-600">Excellent</span>
                  </>
                ) : parseFloat(successRate) >= 90 ? (
                  <>
                    <AlertTriangle className="h-4 w-4 text-yellow-600" />
                    <span className="text-xs text-yellow-600">Good</span>
                  </>
                ) : (
                  <>
                    <XCircle className="h-4 w-4 text-red-600" />
                    <span className="text-xs text-red-600">Needs Attention</span>
                  </>
                )}
              </div>
            </div>

            {/* Error Rate */}
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-4 w-4 text-red-600" />
                <span className="text-sm font-medium text-gray-700">Error Rate</span>
              </div>
              <span className="text-lg font-bold text-gray-900">{errorRate}%</span>
            </div>

            {/* Total Requests */}
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-2">
                <Activity className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium text-gray-700">Total Requests</span>
              </div>
              <span className="text-lg font-bold text-gray-900">{totalRequests.toLocaleString()}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Status Code Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Response Status</CardTitle>
        </CardHeader>
        <CardContent>
          {usage.statusCodes.length > 0 ? (
            <div className="space-y-3">
              {usage.statusCodes.map((status, index) => {
                const percentage = totalRequests > 0 ? (status._count / totalRequests * 100).toFixed(1) : '0'
                const isSuccess = status.statusCode >= 200 && status.statusCode < 300
                const isClientError = status.statusCode >= 400 && status.statusCode < 500
                const isServerError = status.statusCode >= 500
                
                return (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${
                        isSuccess ? 'bg-green-500' :
                        isClientError ? 'bg-yellow-500' :
                        isServerError ? 'bg-red-500' : 'bg-gray-500'
                      }`} />
                      <div>
                        <div className="font-medium text-gray-900">
                          HTTP {status.statusCode}
                        </div>
                        <div className="text-sm text-gray-600">
                          {isSuccess ? 'Success' :
                           isClientError ? 'Client Error' :
                           isServerError ? 'Server Error' : 'Other'}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-gray-900">
                        {status._count.toLocaleString()}
                      </div>
                      <div className="text-sm text-gray-600">
                        {percentage}%
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          ) : (
            <div className="text-center py-6">
              <Activity className="h-8 w-8 text-gray-300 mx-auto mb-2" />
              <p className="text-gray-500 text-sm">No API requests yet</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* API Resources */}
      <Card>
        <CardHeader>
          <CardTitle>API Resources</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-800">
                {usage.activeKeys}
              </div>
              <div className="text-sm text-blue-600">Active API Keys</div>
              <div className="text-xs text-blue-500 mt-1">
                {usage.totalKeys} total
              </div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-800">
                {usage.activeWebhooks}
              </div>
              <div className="text-sm text-purple-600">Active Webhooks</div>
              <div className="text-xs text-purple-500 mt-1">
                {usage.totalWebhooks} total
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Stats</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Avg. Daily Requests</span>
              <span className="font-medium text-gray-900">
                {Math.round(usage.monthlyRequests / 30).toLocaleString()}
              </span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Peak Usage</span>
              <span className="font-medium text-gray-900">
                {Math.max(dailyUsagePercentage, monthlyUsagePercentage).toFixed(1)}%
              </span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">API Efficiency</span>
              <span className="font-medium text-gray-900">
                {parseFloat(successRate) >= 95 ? 'Excellent' : 
                 parseFloat(successRate) >= 90 ? 'Good' : 'Needs Improvement'}
              </span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Resource Utilization</span>
              <span className="font-medium text-gray-900">
                {usage.activeKeys > 0 || usage.activeWebhooks > 0 ? 'Active' : 'Inactive'}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
