import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const itemSchema = z.object({
  name: z.string().min(1, 'Item name is required'),
  description: z.string().optional().nullable(),
  sku: z.string().optional().nullable(),
  type: z.enum(['PRODUCT', 'SERVICE']),
  category: z.string().optional().nullable(),
  price: z.number().min(0, 'Price must be non-negative'),
  costPrice: z.number().min(0).optional().nullable(),
  unit: z.string().optional().nullable(),
  stockQuantity: z.number().min(0).optional().nullable(),
  lowStockThreshold: z.number().min(0).optional().nullable(),
  status: z.enum(['ACTIVE', 'INACTIVE']).default('ACTIVE'),
  taxRate: z.number().min(0).max(100).default(18),
  notes: z.string().optional().nullable(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const item = await prisma.item.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      },
      include: {
        quotationItems: {
          include: {
            quotation: {
              select: { id: true, quotationNumber: true, title: true, status: true, createdAt: true }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
        invoiceItems: {
          include: {
            invoice: {
              select: { id: true, invoiceNumber: true, title: true, status: true, createdAt: true }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
        _count: {
          select: {
            quotationItems: true,
            invoiceItems: true,
          }
        }
      }
    })

    if (!item) {
      return NextResponse.json({ error: 'Item not found' }, { status: 404 })
    }

    return NextResponse.json(item)
  } catch (error) {
    console.error('Error fetching item:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = itemSchema.parse(body)

    // Check if item exists and belongs to the company
    const existingItem = await prisma.item.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      }
    })

    if (!existingItem) {
      return NextResponse.json({ error: 'Item not found' }, { status: 404 })
    }

    // Check if SKU conflicts with another item (if provided and changed)
    if (validatedData.sku && validatedData.sku !== existingItem.sku) {
      const conflictingItem = await prisma.item.findFirst({
        where: {
          sku: validatedData.sku,
          companyId: session.user.companyId,
          id: { not: params.id },
        }
      })

      if (conflictingItem) {
        return NextResponse.json(
          { error: 'SKU already exists' },
          { status: 400 }
        )
      }
    }

    const item = await prisma.item.update({
      where: { id: params.id },
      data: {
        name: validatedData.name,
        description: validatedData.description,
        sku: validatedData.sku,
        type: validatedData.type,
        category: validatedData.category,
        price: validatedData.price,
        costPrice: validatedData.costPrice,
        unit: validatedData.unit,
        stockQuantity: validatedData.stockQuantity,
        lowStockThreshold: validatedData.lowStockThreshold,
        status: validatedData.status,
        taxRate: validatedData.taxRate,
        notes: validatedData.notes,
      },
      include: {
        _count: {
          select: {
            quotationItems: true,
            invoiceItems: true,
          }
        }
      }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'NOTE',
        title: 'Item updated',
        description: `Item "${item.name}" was updated`,
        itemId: item.id,
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    return NextResponse.json(item)
  } catch (error) {
    console.error('Error updating item:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if item exists and belongs to the company
    const item = await prisma.item.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      },
      include: {
        _count: {
          select: {
            quotationItems: true,
            invoiceItems: true,
          }
        }
      }
    })

    if (!item) {
      return NextResponse.json({ error: 'Item not found' }, { status: 404 })
    }

    // Check if item is being used in quotations or invoices
    if (item._count.quotationItems > 0 || item._count.invoiceItems > 0) {
      return NextResponse.json(
        { error: 'Cannot delete item that is used in quotations or invoices. Set status to inactive instead.' },
        { status: 400 }
      )
    }

    await prisma.item.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: 'Item deleted successfully' })
  } catch (error) {
    console.error('Error deleting item:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
