import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { redirect } from 'next/navigation'
import { 
  CreditCard, 
  Plus, 
  Edit, 
  Trash2, 
  Star,
  Zap,
  Crown,
  DollarSign,
  Users,
  CheckCircle,
  XCircle,
  Settings
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { formatCurrency } from '@/lib/utils'

export default async function SuperAdminPlansPage() {
  const session = await getServerSession(authOptions)
  
  // Check if user is super admin
  if (!session?.user?.isSuperAdmin && session?.user?.role !== 'SUPER_ADMIN') {
    redirect('/dashboard')
  }

  // Get plan statistics
  const [
    planStats,
    recentSubscriptions,
    planRevenue
  ] = await Promise.all([
    // Plan distribution
    prisma.company.groupBy({
      by: ['plan'],
      _count: true,
      orderBy: { _count: { plan: 'desc' } }
    }),
    
    // Recent subscriptions
    prisma.subscription.findMany({
      take: 10,
      orderBy: { createdAt: 'desc' },
      include: {
        company: {
          select: { name: true, plan: true }
        }
      }
    }),
    
    // Revenue by plan
    prisma.subscription.groupBy({
      by: ['plan'],
      _sum: { amount: true },
      _count: true
    })
  ])

  // Define current plans (in a real app, these would come from database)
  const plans = [
    {
      id: 'starter',
      name: 'Starter',
      description: 'Perfect for small businesses getting started',
      monthlyPrice: 29,
      yearlyPrice: 290,
      icon: Star,
      color: 'blue',
      features: [
        'Up to 100 customers',
        '50 quotations per month',
        '25 invoices per month',
        'Basic email support',
        'Standard templates',
        '1 user account',
        'Basic reporting',
        'Mobile app access'
      ],
      limits: {
        users: 1,
        customers: 100,
        quotations: 50,
        invoices: 25,
        storage: '1GB'
      },
      isActive: true,
      isPopular: false
    },
    {
      id: 'professional',
      name: 'Professional',
      description: 'Ideal for growing businesses with advanced needs',
      monthlyPrice: 79,
      yearlyPrice: 790,
      icon: Zap,
      color: 'purple',
      features: [
        'Up to 1,000 customers',
        'Unlimited quotations',
        'Unlimited invoices',
        'Priority email & chat support',
        'Custom templates',
        'Up to 5 user accounts',
        'Advanced reporting & analytics',
        'API access',
        'Custom branding',
        'Email campaigns'
      ],
      limits: {
        users: 5,
        customers: 1000,
        quotations: -1, // Unlimited
        invoices: -1,
        storage: '10GB'
      },
      isActive: true,
      isPopular: true
    },
    {
      id: 'enterprise',
      name: 'Enterprise',
      description: 'For large organizations requiring maximum flexibility',
      monthlyPrice: 199,
      yearlyPrice: 1990,
      icon: Crown,
      color: 'gold',
      features: [
        'Unlimited customers',
        'Unlimited quotations',
        'Unlimited invoices',
        '24/7 phone & email support',
        'Fully custom templates',
        'Unlimited user accounts',
        'Enterprise reporting & analytics',
        'Full API access',
        'White-label solution',
        'Advanced email campaigns',
        'Dedicated account manager',
        'SLA guarantee'
      ],
      limits: {
        users: -1, // Unlimited
        customers: -1,
        quotations: -1,
        invoices: -1,
        storage: '100GB'
      },
      isActive: true,
      isPopular: false
    }
  ]

  const getPlanIcon = (planName: string) => {
    switch (planName.toLowerCase()) {
      case 'starter':
        return Star
      case 'professional':
        return Zap
      case 'enterprise':
        return Crown
      default:
        return CreditCard
    }
  }

  const getPlanColor = (planName: string) => {
    switch (planName.toLowerCase()) {
      case 'starter':
        return 'text-blue-600'
      case 'professional':
        return 'text-purple-600'
      case 'enterprise':
        return 'text-yellow-600'
      default:
        return 'text-gray-600'
    }
  }

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="p-3 bg-green-100 rounded-lg">
            <CreditCard className="h-8 w-8 text-green-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Plan Management</h1>
            <p className="text-gray-600">Manage subscription plans, pricing, and features</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline">
            <Settings className="h-4 w-4 mr-2" />
            Plan Settings
          </Button>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Create Plan
          </Button>
        </div>
      </div>

      {/* Plan Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Plans</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{plans.length}</div>
            <p className="text-xs text-muted-foreground">Active subscription plans</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Subscribers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {planStats.reduce((sum, stat) => sum + stat._count, 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">Across all plans</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(planRevenue.reduce((sum, plan) => sum + (plan._sum.amount || 0), 0))}
            </div>
            <p className="text-xs text-muted-foreground">From subscriptions</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Most Popular</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {planStats.length > 0 ? planStats[0].plan : 'N/A'}
            </div>
            <p className="text-xs text-muted-foreground">
              {planStats.length > 0 ? `${planStats[0]._count} subscribers` : 'No data'}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Plans Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {plans.map((plan) => {
          const Icon = plan.icon
          const planStat = planStats.find(s => s.plan.toLowerCase() === plan.name.toLowerCase())
          const subscriberCount = planStat?._count || 0
          
          return (
            <Card key={plan.id} className={`relative ${plan.isPopular ? 'ring-2 ring-purple-500' : ''}`}>
              {plan.isPopular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-purple-500 text-white">Most Popular</Badge>
                </div>
              )}
              
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg bg-${plan.color}-100`}>
                      <Icon className={`h-6 w-6 text-${plan.color}-600`} />
                    </div>
                    <div>
                      <CardTitle className="text-xl">{plan.name}</CardTitle>
                      <p className="text-sm text-gray-500">{plan.description}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {plan.isActive ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-500" />
                    )}
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-6">
                {/* Pricing */}
                <div className="text-center">
                  <div className="text-3xl font-bold">
                    {formatCurrency(plan.monthlyPrice)}
                    <span className="text-lg font-normal text-gray-500">/month</span>
                  </div>
                  <div className="text-sm text-gray-500">
                    or {formatCurrency(plan.yearlyPrice)}/year (save {Math.round((1 - plan.yearlyPrice / (plan.monthlyPrice * 12)) * 100)}%)
                  </div>
                </div>

                {/* Subscriber Count */}
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-gray-900">{subscriberCount}</div>
                  <div className="text-sm text-gray-500">Active Subscribers</div>
                </div>

                {/* Key Limits */}
                <div className="space-y-2">
                  <h4 className="font-medium text-gray-900">Plan Limits</h4>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>Users: {plan.limits.users === -1 ? 'Unlimited' : plan.limits.users}</div>
                    <div>Storage: {plan.limits.storage}</div>
                    <div>Customers: {plan.limits.customers === -1 ? 'Unlimited' : plan.limits.customers.toLocaleString()}</div>
                    <div>Invoices: {plan.limits.invoices === -1 ? 'Unlimited' : plan.limits.invoices}</div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm" className="flex-1">
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Plan
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1">
                    <Settings className="h-4 w-4 mr-2" />
                    Configure
                  </Button>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Recent Subscriptions */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Subscriptions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentSubscriptions.map((subscription) => {
              const PlanIcon = getPlanIcon(subscription.plan)
              const planColor = getPlanColor(subscription.plan)
              
              return (
                <div key={subscription.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-4">
                    <PlanIcon className={`h-5 w-5 ${planColor}`} />
                    <div>
                      <p className="font-medium">{subscription.company?.name}</p>
                      <p className="text-sm text-gray-500">
                        {subscription.plan} Plan • {formatCurrency(subscription.amount)}/{subscription.interval.toLowerCase()}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge variant="outline" className="text-green-600 border-green-200">
                      {subscription.status}
                    </Badge>
                    <p className="text-sm text-gray-500 mt-1">
                      {new Date(subscription.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
