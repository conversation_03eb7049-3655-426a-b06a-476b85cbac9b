'use client'

import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { formatDate, formatCurrency, getStatusColor } from '@/lib/utils'
import { 
  Eye, 
  Download, 
  Send, 
  Edit,
  FileText,
  Calendar,
  User,
  Building,
  DollarSign,
  Printer,
  Share2
} from 'lucide-react'
import toast from 'react-hot-toast'

interface QuotationPreviewProps {
  quotation: {
    id: string
    quotationNumber: string
    title: string
    description: string | null
    status: string
    issueDate: Date
    expiryDate: Date | null
    total: number
    subtotal: number
    taxAmount: number | null
    discountAmount: number | null
    notes: string | null
    customer: {
      id: string
      name: string
      email: string | null
      company: string | null
      address: string | null
      city: string | null
      state: string | null
      country: string | null
      postalCode: string | null
    }
    company: {
      name: string
      email: string | null
      phone: string | null
      address: string | null
      city: string | null
      state: string | null
      country: string | null
      postalCode: string | null
    }
    items: Array<{
      id: string
      description: string
      quantity: number
      unitPrice: number
      total: number
    }>
    createdAt: Date
    updatedAt: Date
  }
}

export function QuotationPreview({ quotation }: QuotationPreviewProps) {
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false)
  const [isSending, setIsSending] = useState(false)

  const handleGeneratePDF = async () => {
    setIsGeneratingPDF(true)
    try {
      const response = await fetch(`/api/quotations/${quotation.id}/pdf`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to generate PDF')
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.style.display = 'none'
      a.href = url
      a.download = `${quotation.quotationNumber}.pdf`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)

      toast.success('PDF generated successfully')
    } catch (error) {
      console.error('Error generating PDF:', error)
      toast.error('Failed to generate PDF')
    } finally {
      setIsGeneratingPDF(false)
    }
  }

  const handleSendQuotation = async () => {
    setIsSending(true)
    try {
      const response = await fetch(`/api/quotations/${quotation.id}/send`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to send quotation')
      }

      toast.success('Quotation sent successfully')
      window.location.reload()
    } catch (error) {
      console.error('Error sending quotation:', error)
      toast.error('Failed to send quotation')
    } finally {
      setIsSending(false)
    }
  }

  const handlePrint = () => {
    window.print()
  }

  return (
    <div className="space-y-6">
      {/* Preview Actions */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <Eye className="h-5 w-5" />
              <span>Quotation Preview</span>
            </CardTitle>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handlePrint}
              >
                <Printer className="h-4 w-4 mr-2" />
                Print
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleGeneratePDF}
                disabled={isGeneratingPDF}
              >
                <Download className="h-4 w-4 mr-2" />
                {isGeneratingPDF ? 'Generating...' : 'Download PDF'}
              </Button>
              {quotation.status === 'DRAFT' && (
                <Button
                  size="sm"
                  onClick={handleSendQuotation}
                  disabled={isSending}
                >
                  <Send className="h-4 w-4 mr-2" />
                  {isSending ? 'Sending...' : 'Send Quotation'}
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Quotation Document */}
      <Card className="print:shadow-none print:border-none">
        <CardContent className="p-8 print:p-0">
          {/* Header */}
          <div className="text-center mb-8 print:mb-6">
            <h1 className="text-3xl font-bold text-gray-900 mb-2 print:text-2xl">
              QUOTATION
            </h1>
            <p className="text-lg text-gray-600 print:text-base">
              #{quotation.quotationNumber}
            </p>
            <Badge className={`${getStatusColor(quotation.status)} mt-2 print:hidden`} variant="outline">
              {quotation.status}
            </Badge>
          </div>

          {/* Quotation Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8 print:mb-6">
            {/* Company Information */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4 print:text-base">
                From
              </h3>
              <div className="space-y-2 text-sm print:text-xs">
                <p className="font-medium">{quotation.company.name}</p>
                {quotation.company.email && <p>{quotation.company.email}</p>}
                {quotation.company.phone && <p>{quotation.company.phone}</p>}
                {quotation.company.address && (
                  <div>
                    <p>{quotation.company.address}</p>
                    <p>
                      {[quotation.company.city, quotation.company.state, quotation.company.postalCode]
                        .filter(Boolean)
                        .join(', ')}
                    </p>
                    {quotation.company.country && <p>{quotation.company.country}</p>}
                  </div>
                )}
              </div>
            </div>

            {/* Customer Information */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4 print:text-base">
                Quote For
              </h3>
              <div className="space-y-2 text-sm print:text-xs">
                <p className="font-medium">{quotation.customer.name}</p>
                {quotation.customer.company && <p>{quotation.customer.company}</p>}
                {quotation.customer.email && <p>{quotation.customer.email}</p>}
                {quotation.customer.address && (
                  <div>
                    <p>{quotation.customer.address}</p>
                    <p>
                      {[quotation.customer.city, quotation.customer.state, quotation.customer.postalCode]
                        .filter(Boolean)
                        .join(', ')}
                    </p>
                    {quotation.customer.country && <p>{quotation.customer.country}</p>}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Quotation Information */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 print:mb-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-1">Issue Date</h4>
              <p className="text-sm text-gray-600">{formatDate(quotation.issueDate)}</p>
            </div>
            {quotation.expiryDate && (
              <div>
                <h4 className="font-medium text-gray-900 mb-1">Valid Until</h4>
                <p className="text-sm text-gray-600">{formatDate(quotation.expiryDate)}</p>
              </div>
            )}
            <div>
              <h4 className="font-medium text-gray-900 mb-1">Total Amount</h4>
              <p className="text-sm text-gray-600">{formatCurrency(quotation.total)}</p>
            </div>
          </div>

          {/* Items Table */}
          <div className="mb-8 print:mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 print:text-base">
              Items
            </h3>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse border border-gray-300">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="border border-gray-300 px-4 py-2 text-left text-sm font-medium text-gray-900">
                      Description
                    </th>
                    <th className="border border-gray-300 px-4 py-2 text-right text-sm font-medium text-gray-900">
                      Qty
                    </th>
                    <th className="border border-gray-300 px-4 py-2 text-right text-sm font-medium text-gray-900">
                      Unit Price
                    </th>
                    <th className="border border-gray-300 px-4 py-2 text-right text-sm font-medium text-gray-900">
                      Total
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {quotation.items.map((item) => (
                    <tr key={item.id}>
                      <td className="border border-gray-300 px-4 py-2 text-sm text-gray-900">
                        {item.description}
                      </td>
                      <td className="border border-gray-300 px-4 py-2 text-sm text-gray-900 text-right">
                        {item.quantity}
                      </td>
                      <td className="border border-gray-300 px-4 py-2 text-sm text-gray-900 text-right">
                        {formatCurrency(item.unitPrice)}
                      </td>
                      <td className="border border-gray-300 px-4 py-2 text-sm text-gray-900 text-right">
                        {formatCurrency(item.total)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Totals */}
          <div className="flex justify-end mb-8 print:mb-6">
            <div className="w-full max-w-sm space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Subtotal:</span>
                <span className="text-gray-900">{formatCurrency(quotation.subtotal)}</span>
              </div>
              {quotation.discountAmount && quotation.discountAmount > 0 && (
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Discount:</span>
                  <span className="text-gray-900">-{formatCurrency(quotation.discountAmount)}</span>
                </div>
              )}
              {quotation.taxAmount && quotation.taxAmount > 0 && (
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Tax:</span>
                  <span className="text-gray-900">{formatCurrency(quotation.taxAmount)}</span>
                </div>
              )}
              <div className="flex justify-between text-lg font-semibold border-t pt-2">
                <span className="text-gray-900">Total:</span>
                <span className="text-gray-900">{formatCurrency(quotation.total)}</span>
              </div>
            </div>
          </div>

          {/* Notes */}
          {quotation.notes && (
            <div className="mb-8 print:mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 print:text-base">
                Notes
              </h3>
              <div className="text-sm text-gray-700 print:text-xs">
                <div dangerouslySetInnerHTML={{ __html: quotation.notes.replace(/\n/g, '<br>') }} />
              </div>
            </div>
          )}

          {/* Footer */}
          <div className="mt-8 pt-4 border-t border-gray-200 text-center print:mt-6">
            <p className="text-xs text-gray-500">
              Generated on {formatDate(new Date())} | Quotation #{quotation.quotationNumber}
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
