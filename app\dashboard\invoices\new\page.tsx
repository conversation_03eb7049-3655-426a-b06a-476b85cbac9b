import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { InvoiceForm } from '@/components/invoices/invoice-form'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'

interface SearchParams {
  customerId?: string
  quotationId?: string
}

export default async function NewInvoicePage({
  searchParams,
}: {
  searchParams: SearchParams
}) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  // Fetch required data
  const [customers, quotations, items, company] = await Promise.all([
    prisma.customer.findMany({
      where: { 
        companyId: session.user.companyId,
        status: { in: ['ACTIVE', 'PROSPECT'] }
      },
      select: { 
        id: true, 
        name: true, 
        email: true, 
        company: true,
        address: true,
        city: true,
        state: true,
        country: true,
        postalCode: true
      },
      orderBy: { name: 'asc' },
    }),
    prisma.quotation.findMany({
      where: { 
        companyId: session.user.companyId,
        status: 'ACCEPTED'
      },
      include: {
        customer: {
          select: { id: true, name: true, company: true }
        },
        items: true
      },
      orderBy: { createdAt: 'desc' },
    }),
    prisma.item.findMany({
      where: { companyId: session.user.companyId },
      select: { 
        id: true, 
        name: true, 
        description: true,
        price: true,
        category: true,
        unit: true,
        taxRate: true
      },
      orderBy: { name: 'asc' },
    }),
    prisma.company.findUnique({
      where: { id: session.user.companyId },
      select: {
        name: true,
        email: true,
        phone: true,
        address: true,
        city: true,
        state: true,
        country: true,
        postalCode: true,
        website: true,
        taxId: true
      }
    })
  ])

  const preselectedCustomerId = searchParams.customerId
  const preselectedQuotationId = searchParams.quotationId

  // Generate next invoice number
  const lastInvoice = await prisma.invoice.findFirst({
    where: { companyId: session.user.companyId },
    orderBy: { createdAt: 'desc' },
    select: { invoiceNumber: true }
  })

  const nextInvoiceNumber = generateInvoiceNumber(lastInvoice?.invoiceNumber)

  // If quotation is preselected, get its details
  let preselectedQuotation = null
  if (preselectedQuotationId) {
    preselectedQuotation = quotations.find(q => q.id === preselectedQuotationId)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href="/dashboard/invoices">
          <Button variant="ghost" >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Invoices
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Create New Invoice</h1>
          <p className="text-gray-600 mt-1">
            Create a professional invoice for your customer
          </p>
        </div>
      </div>

      {/* Invoice Form */}
      <InvoiceForm 
        mode="create" 
        customers={customers}
        quotations={quotations}
        items={items}
        company={company}
        preselectedCustomerId={preselectedCustomerId}
        preselectedQuotationId={preselectedQuotationId}
        preselectedQuotation={preselectedQuotation}
        invoiceNumber={nextInvoiceNumber}
      />
    </div>
  )
}

function generateInvoiceNumber(lastNumber?: string): string {
  const currentYear = new Date().getFullYear()
  const currentMonth = String(new Date().getMonth() + 1).padStart(2, '0')
  
  let nextNumber = 1
  
  if (lastNumber) {
    const match = lastNumber.match(/INV-(\d{4})(\d{2})-(\d{4})/)
    if (match) {
      const [, year, month, num] = match
      if (year === currentYear.toString() && month === currentMonth) {
        nextNumber = parseInt(num) + 1
      }
    }
  }
  
  return `INV-${currentYear}${currentMonth}-${String(nextNumber).padStart(4, '0')}`
}
