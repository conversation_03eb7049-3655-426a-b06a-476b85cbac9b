'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  HelpCircle, 
  Clock,
  CheckCircle,
  AlertTriangle,
  MessageSquare,
  Phone,
  Mail,
  Star,
  TrendingUp,
  Users,
  Crown,
  Award
} from 'lucide-react'
import Link from 'next/link'

interface SupportOverviewProps {
  company: any
  stats: {
    openTickets: number
    inProgressTickets: number
    resolvedTickets: number
    recentTickets: number
    avgResponseTime: string
    satisfaction: number
    totalTickets: number
  }
  plan: string
}

export function SupportOverview({ company, stats, plan }: SupportOverviewProps) {
  const getPlanIcon = (plan: string) => {
    switch (plan) {
      case 'ENTERPRISE':
        return Crown
      case 'PROFESSIONAL':
        return Star
      case 'BASIC':
        return Award
      default:
        return HelpCircle
    }
  }

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'ENTERPRISE':
        return 'text-purple-600 bg-purple-100 border-purple-200'
      case 'PROFESSIONAL':
        return 'text-blue-600 bg-blue-100 border-blue-200'
      case 'BASIC':
        return 'text-green-600 bg-green-100 border-green-200'
      default:
        return 'text-gray-600 bg-gray-100 border-gray-200'
    }
  }

  const getSupportFeatures = (plan: string) => {
    switch (plan) {
      case 'ENTERPRISE':
        return {
          responseTime: '1 hour',
          channels: ['Email', 'Phone', 'Live Chat', 'Dedicated Manager'],
          availability: '24/7',
          priority: 'Highest',
          features: ['Custom integrations', 'On-site training', 'Priority escalation']
        }
      case 'PROFESSIONAL':
        return {
          responseTime: '4 hours',
          channels: ['Email', 'Phone', 'Live Chat'],
          availability: 'Business Hours',
          priority: 'High',
          features: ['Advanced troubleshooting', 'Video calls', 'Priority queue']
        }
      case 'BASIC':
        return {
          responseTime: '24 hours',
          channels: ['Email', 'Knowledge Base'],
          availability: 'Business Hours',
          priority: 'Standard',
          features: ['Self-service portal', 'Community forum', 'Documentation']
        }
      default:
        return {
          responseTime: '24 hours',
          channels: ['Email'],
          availability: 'Business Hours',
          priority: 'Standard',
          features: ['Basic support']
        }
    }
  }

  const PlanIcon = getPlanIcon(plan)
  const supportFeatures = getSupportFeatures(plan)

  const overviewCards = [
    {
      title: 'Open Tickets',
      value: stats.openTickets.toString(),
      icon: AlertTriangle,
      color: stats.openTickets > 0 ? 'text-yellow-600' : 'text-gray-600',
      bgColor: stats.openTickets > 0 ? 'bg-yellow-100' : 'bg-gray-100',
      description: 'Awaiting response'
    },
    {
      title: 'In Progress',
      value: stats.inProgressTickets.toString(),
      icon: Clock,
      color: stats.inProgressTickets > 0 ? 'text-blue-600' : 'text-gray-600',
      bgColor: stats.inProgressTickets > 0 ? 'bg-blue-100' : 'bg-gray-100',
      description: 'Being worked on'
    },
    {
      title: 'Resolved',
      value: stats.resolvedTickets.toString(),
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      description: 'Successfully resolved'
    },
    {
      title: 'Satisfaction',
      value: `${stats.satisfaction}/5`,
      icon: Star,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      description: 'Average rating'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {overviewCards.map((card, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {card.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${card.bgColor}`}>
                <card.icon className={`h-4 w-4 ${card.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">
                {card.value}
              </div>
              <p className="text-xs text-gray-500 mt-1">
                {card.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Support Plan Details */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Current Support Plan */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <PlanIcon className="h-5 w-5" />
              <span>Your Support Plan</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Plan Type</span>
                <Badge className={getPlanColor(plan)} variant="outline">
                  {plan}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Response Time</span>
                <span className="text-sm text-gray-900">{supportFeatures.responseTime}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Availability</span>
                <span className="text-sm text-gray-900">{supportFeatures.availability}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Priority Level</span>
                <span className="text-sm text-gray-900">{supportFeatures.priority}</span>
              </div>

              <div>
                <span className="text-sm font-medium text-gray-700 block mb-2">Support Channels</span>
                <div className="flex flex-wrap gap-2">
                  {supportFeatures.channels.map((channel, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {channel}
                    </Badge>
                  ))}
                </div>
              </div>

              <div>
                <span className="text-sm font-medium text-gray-700 block mb-2">Included Features</span>
                <ul className="text-sm text-gray-600 space-y-1">
                  {supportFeatures.features.map((feature, index) => (
                    <li key={index} className="flex items-center space-x-2">
                      <CheckCircle className="h-3 w-3 text-green-600" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {plan !== 'ENTERPRISE' && (
                <div className="pt-4 border-t">
                  <Link href="/dashboard/billing">
                    <Button className="w-full">
                      <TrendingUp className="h-4 w-4 mr-2" />
                      Upgrade Support Plan
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Support Metrics */}
        <Card>
          <CardHeader>
            <CardTitle>Support Metrics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Response Time */}
              <div className="text-center p-4 bg-gradient-to-r from-blue-50 to-green-50 rounded-lg">
                <div className="text-2xl font-bold text-gray-900 mb-1">
                  {stats.avgResponseTime}
                </div>
                <div className="text-sm text-gray-600 mb-2">Average Response Time</div>
                <div className="flex items-center justify-center space-x-1">
                  <Clock className="h-4 w-4 text-blue-600" />
                  <span className="text-xs text-blue-600">
                    {plan === 'ENTERPRISE' ? 'Excellent' : plan === 'PROFESSIONAL' ? 'Good' : 'Standard'}
                  </span>
                </div>
              </div>

              {/* Ticket Resolution */}
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <div className="text-lg font-bold text-green-800">
                    {stats.resolvedTickets}
                  </div>
                  <div className="text-xs text-green-600">Resolved</div>
                </div>
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="text-lg font-bold text-blue-800">
                    {stats.recentTickets}
                  </div>
                  <div className="text-xs text-blue-600">This Month</div>
                </div>
              </div>

              {/* Satisfaction Score */}
              <div className="p-4 bg-purple-50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-purple-900">Satisfaction Score</span>
                  <div className="flex items-center space-x-1">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star
                        key={star}
                        className={`h-4 w-4 ${
                          star <= Math.floor(stats.satisfaction)
                            ? 'text-yellow-400 fill-current'
                            : 'text-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                </div>
                <div className="text-2xl font-bold text-purple-900">
                  {stats.satisfaction}/5.0
                </div>
                <div className="text-xs text-purple-700">
                  Based on recent ticket feedback
                </div>
              </div>

              {/* Quick Actions */}
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-gray-900">Quick Actions</h4>
                <div className="grid grid-cols-2 gap-2">
                  <Link href="/dashboard/support/chat">
                    <Button variant="outline" size="sm" className="w-full">
                      <MessageSquare className="h-3 w-3 mr-1" />
                      Chat
                    </Button>
                  </Link>
                  <Link href="/dashboard/support/knowledge-base">
                    <Button variant="outline" size="sm" className="w-full">
                      <HelpCircle className="h-3 w-3 mr-1" />
                      Help
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Support Channels */}
      <Card>
        <CardHeader>
          <CardTitle>Available Support Channels</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Email Support */}
            <div className="text-center p-4 border rounded-lg hover:bg-gray-50 transition-colors">
              <Mail className="h-8 w-8 text-blue-600 mx-auto mb-3" />
              <h3 className="font-medium text-gray-900 mb-2">Email Support</h3>
              <p className="text-sm text-gray-600 mb-3">
                Get help via email with detailed responses
              </p>
              <div className="text-xs text-gray-500">
                Response: {supportFeatures.responseTime}
              </div>
            </div>

            {/* Live Chat */}
            {(plan === 'PROFESSIONAL' || plan === 'ENTERPRISE') && (
              <div className="text-center p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                <MessageSquare className="h-8 w-8 text-green-600 mx-auto mb-3" />
                <h3 className="font-medium text-gray-900 mb-2">Live Chat</h3>
                <p className="text-sm text-gray-600 mb-3">
                  Instant help through live chat support
                </p>
                <div className="text-xs text-gray-500">
                  Available: {supportFeatures.availability}
                </div>
              </div>
            )}

            {/* Phone Support */}
            {(plan === 'PROFESSIONAL' || plan === 'ENTERPRISE') && (
              <div className="text-center p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                <Phone className="h-8 w-8 text-purple-600 mx-auto mb-3" />
                <h3 className="font-medium text-gray-900 mb-2">Phone Support</h3>
                <p className="text-sm text-gray-600 mb-3">
                  Direct phone support for urgent issues
                </p>
                <div className="text-xs text-gray-500">
                  {plan === 'ENTERPRISE' ? '24/7 Available' : 'Business Hours'}
                </div>
              </div>
            )}

            {/* Dedicated Manager (Enterprise only) */}
            {plan === 'ENTERPRISE' && (
              <div className="text-center p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                <Users className="h-8 w-8 text-indigo-600 mx-auto mb-3" />
                <h3 className="font-medium text-gray-900 mb-2">Account Manager</h3>
                <p className="text-sm text-gray-600 mb-3">
                  Dedicated account manager for personalized support
                </p>
                <div className="text-xs text-gray-500">
                  Direct contact available
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
