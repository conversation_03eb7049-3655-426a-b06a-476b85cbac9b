import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ItemDetails } from '@/components/items/item-details'
import { ItemUsage } from '@/components/items/item-usage'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Edit, Trash2, Copy, Package } from 'lucide-react'
import Link from 'next/link'
import { notFound } from 'next/navigation'

export default async function ItemDetailPage({
  params,
}: {
  params: { id: string }
}) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  const item = await prisma.item.findFirst({
    where: {
      id: params.id,
      companyId: session.user.companyId,
    },
    include: {
      quotationItems: {
        include: {
          quotation: {
            select: { 
              id: true, 
              quotationNumber: true, 
              title: true, 
              status: true, 
              createdAt: true,
              customer: {
                select: { name: true, company: true }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 10,
      },
      invoiceItems: {
        include: {
          invoice: {
            select: { 
              id: true, 
              invoiceNumber: true, 
              title: true, 
              status: true, 
              createdAt: true,
              customer: {
                select: { name: true, company: true }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 10,
      },
      _count: {
        select: {
          quotationItems: true,
          invoiceItems: true,
        }
      }
    }
  })

  if (!item) {
    notFound()
  }

  const isLowStock = item.type === 'PRODUCT' && 
                   item.stockQuantity !== null && 
                   item.lowStockThreshold !== null &&
                   item.stockQuantity <= item.lowStockThreshold

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/items">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Items
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{item.name}</h1>
            <p className="text-gray-600 mt-1">
              {item.type} • {item.category || 'No category'}
              {isLowStock && (
                <span className="ml-2 text-red-600 font-medium">• Low Stock</span>
              )}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Link href={`/dashboard/items/${item.id}/edit`}>
            <Button variant="outline">
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
          </Link>
          <Button variant="outline">
            <Copy className="h-4 w-4 mr-2" />
            Duplicate
          </Button>
          {item._count.quotationItems === 0 && item._count.invoiceItems === 0 && (
            <Button variant="outline" className="text-red-600 hover:text-red-700">
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Item Details */}
        <div className="lg:col-span-2 space-y-6">
          <ItemDetails item={item} />
        </div>

        {/* Right Column - Usage */}
        <div>
          <ItemUsage 
            quotationItems={item.quotationItems} 
            invoiceItems={item.invoiceItems}
            totalQuotations={item._count.quotationItems}
            totalInvoices={item._count.invoiceItems}
          />
        </div>
      </div>
    </div>
  )
}
