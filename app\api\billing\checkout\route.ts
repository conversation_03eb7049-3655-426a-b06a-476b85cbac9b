import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { stripe, createCheckoutSession, createStripeCustomer, getStripePriceId } from '@/lib/stripe'
import { z } from 'zod'

const checkoutSchema = z.object({
  plan: z.enum(['starter', 'professional', 'enterprise']),
  interval: z.enum(['monthly', 'yearly']),
  companyId: z.string(),
  successUrl: z.string().url(),
  cancelUrl: z.string().url()
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = checkoutSchema.parse(body)

    // Get company details
    const company = await prisma.company.findUnique({
      where: { id: validatedData.companyId },
      include: {
        owner: true
      }
    })

    if (!company) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    // Create or get Stripe customer
    let stripeCustomerId = company.stripeCustomerId

    if (!stripeCustomerId) {
      const stripeCustomer = await createStripeCustomer({
        email: company.owner.email,
        name: company.owner.name || `${company.owner.firstName} ${company.owner.lastName}`,
        companyName: company.name
      })

      stripeCustomerId = stripeCustomer.id

      // Update company with Stripe customer ID
      await prisma.company.update({
        where: { id: company.id },
        data: { stripeCustomerId }
      })
    }

    // Get Stripe price ID
    const priceId = getStripePriceId(validatedData.plan, validatedData.interval)

    // Create checkout session
    const session = await createCheckoutSession({
      customerId: stripeCustomerId,
      priceId,
      successUrl: validatedData.successUrl,
      cancelUrl: validatedData.cancelUrl,
      companyId: company.id,
      userId: company.ownerId
    })

    // Log activity
    await prisma.activity.create({
      data: {
        type: 'BILLING',
        title: 'Checkout session created',
        description: `Checkout session created for ${validatedData.plan} plan`,
        companyId: company.id,
        createdById: company.ownerId,
        metadata: {
          plan: validatedData.plan,
          interval: validatedData.interval,
          sessionId: session.id
        }
      }
    })

    return NextResponse.json({
      sessionId: session.id,
      url: session.url
    })

  } catch (error) {
    console.error('Checkout error:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Handle successful payment webhook
export async function handleSuccessfulPayment(sessionId: string) {
  try {
    // Retrieve the checkout session
    const session = await stripe.checkout.sessions.retrieve(sessionId, {
      expand: ['subscription', 'customer']
    })

    if (!session.metadata?.companyId) {
      throw new Error('No company ID in session metadata')
    }

    const companyId = session.metadata.companyId
    const subscription = session.subscription as any

    // Update company with subscription details
    await prisma.company.update({
      where: { id: companyId },
      data: {
        status: 'ACTIVE',
        stripeCustomerId: session.customer as string,
        plan: subscription.items.data[0].price.lookup_key || 'PROFESSIONAL'
      }
    })

    // Create subscription record
    const subscriptionRecord = await prisma.subscription.create({
      data: {
        companyId,
        stripeSubscriptionId: subscription.id,
        status: subscription.status.toUpperCase(),
        plan: subscription.items.data[0].price.lookup_key || 'PROFESSIONAL',
        amount: subscription.items.data[0].price.unit_amount / 100,
        currency: subscription.currency.toUpperCase(),
        interval: subscription.items.data[0].price.recurring.interval.toUpperCase(),
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
        cancelAtPeriodEnd: subscription.cancel_at_period_end
      }
    })

    // Create initial payment record
    await prisma.payment.create({
      data: {
        subscriptionId: subscriptionRecord.id,
        companyId,
        amount: subscription.items.data[0].price.unit_amount / 100,
        currency: subscription.currency.toUpperCase(),
        status: 'SUCCEEDED',
        description: `${subscription.items.data[0].price.lookup_key} subscription`,
        stripePaymentIntentId: session.payment_intent as string,
        paidAt: new Date()
      }
    })

    // Log activity
    await prisma.activity.create({
      data: {
        type: 'SUBSCRIPTION',
        title: 'Subscription activated',
        description: `Subscription successfully activated`,
        companyId,
        createdById: session.metadata.userId!,
        metadata: {
          subscriptionId: subscription.id,
          plan: subscription.items.data[0].price.lookup_key
        }
      }
    })

    console.log(`Subscription activated for company ${companyId}`)

  } catch (error) {
    console.error('Error handling successful payment:', error)
    throw error
  }
}
