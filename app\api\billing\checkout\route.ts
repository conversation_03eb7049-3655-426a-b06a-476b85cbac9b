import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { stripe, createCheckoutSession, createStripeCustomer, getStripePriceId, getPaymentMethodsByCountry, getCurrencyByCountry } from '@/lib/stripe'
import { z } from 'zod'

const checkoutSchema = z.object({
  plan: z.enum(['starter', 'professional', 'enterprise']),
  interval: z.enum(['monthly', 'yearly']),
  companyId: z.string(),
  successUrl: z.string().url(),
  cancelUrl: z.string().url(),
  country: z.string().optional(),
  currency: z.string().optional()
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = checkoutSchema.parse(body)

    // Get company details
    const company = await prisma.company.findUnique({
      where: { id: validatedData.companyId },
      include: {
        owner: true
      }
    })

    if (!company) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    // Create or get Stripe customer
    let stripeCustomerId = company.subscriptionId

    if (!stripeCustomerId) {
      const stripeCustomer = await createStripeCustomer({
        email: company.owner.email,
        name: company.owner.name || `${company.owner.firstName} ${company.owner.lastName}`,
        companyName: company.name
      })

      stripeCustomerId = stripeCustomer.id

      // Update company with Stripe customer ID
      await prisma.company.update({
        where: { id: company.id },
        data: { subscriptionId: stripeCustomerId }
      })
    }

    // Detect country from request headers or use provided country
    const country = validatedData.country ||
                   request.headers.get('cf-ipcountry') || // Cloudflare country header
                   request.headers.get('x-country') ||    // Custom country header
                   'us' // Default to US

    // Get appropriate payment methods and currency for the country
    const paymentMethods = getPaymentMethodsByCountry(country)
    const currency = validatedData.currency || getCurrencyByCountry(country)

    // Get Stripe price ID
    const priceId = getStripePriceId(validatedData.plan, validatedData.interval)

    // Create checkout session with country-specific settings
    const session = await createCheckoutSession({
      customerId: stripeCustomerId,
      priceId,
      successUrl: validatedData.successUrl,
      cancelUrl: validatedData.cancelUrl,
      companyId: company.id,
      userId: company.ownerId,
      paymentMethods,
      currency,
      country
    })

    // Log activity
    await prisma.activity.create({
      data: {
        type: 'BILLING',
        title: 'Checkout session created',
        description: `Checkout session created for ${validatedData.plan} plan`,
        companyId: company.id,
        createdById: company.ownerId,
        metadata: {
          plan: validatedData.plan,
          interval: validatedData.interval,
          sessionId: session.id
        }
      }
    })

    return NextResponse.json({
      sessionId: session.id,
      url: session.url
    })

  } catch (error) {
    console.error('Checkout error:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}


