import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

// Get integration status
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.isSuperAdmin && session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check integration status
    const integrations = {
      stripe: {
        name: 'Stripe',
        description: 'Payment processing',
        status: process.env.STRIPE_SECRET_KEY ? 'connected' : 'disconnected',
        configured: !!(process.env.STRIPE_SECRET_KEY && process.env.STRIPE_PUBLISHABLE_KEY),
        lastChecked: new Date().toISOString()
      },
      mailgun: {
        name: 'Mailgun',
        description: 'Email delivery service',
        status: process.env.MAILGUN_API_KEY ? 'connected' : 'disconnected',
        configured: !!(process.env.MAILGUN_API_KEY && process.env.MAILGUN_DOMAIN),
        lastChecked: new Date().toISOString()
      },
      smtp: {
        name: 'SMTP Email',
        description: 'Direct SMTP email sending',
        status: process.env.EMAIL_SERVER_HOST ? 'connected' : 'disconnected',
        configured: !!(process.env.EMAIL_SERVER_HOST && process.env.EMAIL_SERVER_USER),
        lastChecked: new Date().toISOString()
      },
      openai: {
        name: 'OpenAI',
        description: 'AI content generation',
        status: process.env.OPENAI_API_KEY ? 'connected' : 'disconnected',
        configured: !!process.env.OPENAI_API_KEY,
        lastChecked: new Date().toISOString()
      },
      grok: {
        name: 'Grok AI',
        description: 'X.AI Grok language model',
        status: process.env.GROK_API_KEY ? 'connected' : 'disconnected',
        configured: !!process.env.GROK_API_KEY,
        lastChecked: new Date().toISOString()
      },
      slack: {
        name: 'Slack',
        description: 'Team notifications',
        status: process.env.SLACK_WEBHOOK_URL ? 'connected' : 'disconnected',
        configured: !!process.env.SLACK_WEBHOOK_URL,
        lastChecked: new Date().toISOString()
      },
      webhooks: {
        name: 'Webhooks',
        description: 'Event notifications',
        status: 'active', // Always active if webhooks feature is enabled
        configured: true,
        lastChecked: new Date().toISOString()
      }
    }

    return NextResponse.json(integrations)
  } catch (error) {
    console.error('Error fetching integration status:', error)
    return NextResponse.json(
      { error: 'Failed to fetch integration status' },
      { status: 500 }
    )
  }
}

// Test integration connection
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.isSuperAdmin && session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { integration } = await request.json()

    let testResult = { success: false, message: 'Unknown integration' }

    switch (integration) {
      case 'stripe':
        // Test Stripe connection
        try {
          if (process.env.STRIPE_SECRET_KEY) {
            // In a real app, you would test the Stripe connection here
            testResult = { success: true, message: 'Stripe connection successful' }
          } else {
            testResult = { success: false, message: 'Stripe API key not configured' }
          }
        } catch (error) {
          testResult = { success: false, message: 'Stripe connection failed' }
        }
        break

      case 'mailgun':
        // Test Mailgun connection
        try {
          if (process.env.MAILGUN_API_KEY) {
            // In a real app, you would test the Mailgun connection here
            testResult = { success: true, message: 'Mailgun connection successful' }
          } else {
            testResult = { success: false, message: 'Mailgun API key not configured' }
          }
        } catch (error) {
          testResult = { success: false, message: 'Mailgun connection failed' }
        }
        break

      case 'smtp':
        // Test SMTP connection
        try {
          if (process.env.EMAIL_SERVER_HOST && process.env.EMAIL_SERVER_USER) {
            // In a real app, you would test the SMTP connection here
            testResult = { success: true, message: 'SMTP connection successful' }
          } else {
            testResult = { success: false, message: 'SMTP credentials not configured' }
          }
        } catch (error) {
          testResult = { success: false, message: 'SMTP connection failed' }
        }
        break

      case 'openai':
        // Test OpenAI connection
        try {
          if (process.env.OPENAI_API_KEY) {
            // In a real app, you would test the OpenAI API here
            testResult = { success: true, message: 'OpenAI connection successful' }
          } else {
            testResult = { success: false, message: 'OpenAI API key not configured' }
          }
        } catch (error) {
          testResult = { success: false, message: 'OpenAI connection failed' }
        }
        break

      case 'grok':
        // Test Grok AI connection
        try {
          if (process.env.GROK_API_KEY) {
            // In a real app, you would test the Grok API here
            testResult = { success: true, message: 'Grok AI connection successful' }
          } else {
            testResult = { success: false, message: 'Grok API key not configured' }
          }
        } catch (error) {
          testResult = { success: false, message: 'Grok AI connection failed' }
        }
        break

      case 'slack':
        // Test Slack connection
        try {
          if (process.env.SLACK_WEBHOOK_URL) {
            // In a real app, you would test the Slack webhook here
            testResult = { success: true, message: 'Slack connection successful' }
          } else {
            testResult = { success: false, message: 'Slack webhook URL not configured' }
          }
        } catch (error) {
          testResult = { success: false, message: 'Slack connection failed' }
        }
        break

      case 'webhooks':
        testResult = { success: true, message: 'Webhooks are active' }
        break
    }

    return NextResponse.json(testResult)
  } catch (error) {
    console.error('Error testing integration:', error)
    return NextResponse.json(
      { error: 'Failed to test integration' },
      { status: 500 }
    )
  }
}
