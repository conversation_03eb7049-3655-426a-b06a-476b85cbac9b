import { stripe } from '@/lib/stripe'
import { prisma } from '@/lib/prisma'
import <PERSON><PERSON> from 'stripe'

// Helper function to get subscription plan from Stripe subscription
function getSubscriptionPlan(subscription: Stripe.Subscription): string {
  const priceId = subscription.items.data[0].price.id

  // Map Stripe price IDs to plan names
  // In a real implementation, you'd store this mapping in your database
  // or use <PERSON><PERSON>'s lookup_key feature

  if (priceId.includes('starter')) return 'STARTER'
  if (priceId.includes('professional')) return 'PROFESSIONAL'
  if (priceId.includes('enterprise')) return 'ENTERPRISE'

  return 'PROFESSIONAL' // Default fallback
}

// Handle successful payment webhook
export async function handleSuccessfulPayment(sessionId: string) {
  try {
    // Retrieve the checkout session
    const session = await stripe.checkout.sessions.retrieve(sessionId, {
      expand: ['subscription', 'customer']
    })

    if (!session.metadata?.companyId) {
      throw new Error('No company ID in session metadata')
    }

    const companyId = session.metadata.companyId
    const subscription = session.subscription as any

    // Update company with subscription details
    await prisma.company.update({
      where: { id: companyId },
      data: {
        status: 'ACTIVE',
        subscriptionId: subscription.id,
        plan: getSubscriptionPlan(subscription)
      }
    })

    // Create subscription record
    const subscriptionRecord = await prisma.subscription.create({
      data: {
        companyId,
        stripeSubscriptionId: subscription.id,
        status: subscription.status.toUpperCase(),
        plan: getSubscriptionPlan(subscription),
        amount: (subscription.items.data[0].price.unit_amount || 0) / 100,
        currency: subscription.currency.toUpperCase(),
        interval: (subscription.items.data[0].price.recurring?.interval || 'month').toUpperCase(),
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
        cancelAtPeriodEnd: subscription.cancel_at_period_end
      }
    })

    // Create initial payment record
    await prisma.payment.create({
      data: {
        subscriptionId: subscriptionRecord.id,
        companyId,
        amount: (subscription.items.data[0].price.unit_amount || 0) / 100,
        currency: subscription.currency.toUpperCase(),
        status: 'SUCCEEDED',
        description: `${getSubscriptionPlan(subscription)} subscription`,
        stripePaymentIntentId: session.payment_intent as string,
        paidAt: new Date()
      }
    })

    // Log activity
    await prisma.activity.create({
      data: {
        type: 'SUBSCRIPTION',
        title: 'Subscription activated',
        description: `Subscription successfully activated`,
        companyId,
        createdById: session.metadata.userId!,
        metadata: {
          subscriptionId: subscription.id,
          plan: getSubscriptionPlan(subscription)
        }
      }
    })

    console.log(`Subscription activated for company ${companyId}`)

  } catch (error) {
    console.error('Error handling successful payment:', error)
    throw error
  }
}

// Handle failed payment
export async function handleFailedPayment(sessionId: string) {
  try {
    const session = await stripe.checkout.sessions.retrieve(sessionId)

    if (!session.metadata?.companyId) {
      throw new Error('No company ID in session metadata')
    }

    const companyId = session.metadata.companyId

    // Log failed payment activity
    await prisma.activity.create({
      data: {
        type: 'NOTE',
        title: 'Payment failed',
        description: 'Payment attempt failed during checkout',
        companyId,
        createdById: session.metadata.userId!,
        metadata: {
          sessionId: session.id,
          reason: 'checkout_failed'
        }
      }
    })

    console.log(`Payment failed for company ${companyId}`)

  } catch (error) {
    console.error('Error handling failed payment:', error)
    throw error
  }
}

// Process subscription update
export async function processSubscriptionUpdate(subscription: Stripe.Subscription) {
  try {
    const subscriptionRecord = await prisma.subscription.findFirst({
      where: { stripeSubscriptionId: subscription.id }
    })

    if (!subscriptionRecord) {
      console.error('Subscription not found:', subscription.id)
      return
    }

    // Update subscription record
    await prisma.subscription.update({
      where: { id: subscriptionRecord.id },
      data: {
        status: subscription.status.toUpperCase(),
        plan: getSubscriptionPlan(subscription),
        amount: subscription.items.data[0].price.unit_amount! / 100,
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
        cancelAtPeriodEnd: subscription.cancel_at_period_end
      }
    })

    // Update company plan if needed
    const newPlan = getSubscriptionPlan(subscription)
    await prisma.company.update({
      where: { id: subscriptionRecord.companyId },
      data: {
        plan: newPlan,
        status: subscription.status === 'active' ? 'ACTIVE' : 'INACTIVE'
      }
    })

    // Log activity
    await prisma.activity.create({
      data: {
        type: 'SUBSCRIPTION',
        title: 'Subscription updated',
        description: `Subscription plan changed to ${newPlan}`,
        companyId: subscriptionRecord.companyId,
        createdById: subscriptionRecord.companyId, // Use company owner
        metadata: {
          subscriptionId: subscription.id,
          newPlan,
          status: subscription.status
        }
      }
    })

    console.log(`Subscription updated for company ${subscriptionRecord.companyId}`)

  } catch (error) {
    console.error('Error processing subscription update:', error)
    throw error
  }
}

// Process subscription cancellation
export async function processSubscriptionCancellation(subscription: Stripe.Subscription) {
  try {
    const subscriptionRecord = await prisma.subscription.findFirst({
      where: { stripeSubscriptionId: subscription.id }
    })

    if (!subscriptionRecord) {
      console.error('Subscription not found:', subscription.id)
      return
    }

    // Update subscription status
    await prisma.subscription.update({
      where: { id: subscriptionRecord.id },
      data: {
        status: 'CANCELLED',
        cancelledAt: new Date()
      }
    })

    // Update company status
    await prisma.company.update({
      where: { id: subscriptionRecord.companyId },
      data: { status: 'CANCELLED' }
    })

    // Log activity
    await prisma.activity.create({
      data: {
        type: 'SUBSCRIPTION',
        title: 'Subscription cancelled',
        description: 'Subscription was cancelled',
        companyId: subscriptionRecord.companyId,
        createdById: subscriptionRecord.companyId,
        metadata: {
          subscriptionId: subscription.id,
          cancelledAt: new Date().toISOString()
        }
      }
    })

    console.log(`Subscription cancelled for company ${subscriptionRecord.companyId}`)

  } catch (error) {
    console.error('Error processing subscription cancellation:', error)
    throw error
  }
}



// Create payment record from invoice
export async function createPaymentFromInvoice(invoice: Stripe.Invoice, status: 'SUCCEEDED' | 'FAILED') {
  try {
    if (!invoice.subscription) return

    const subscription = await prisma.subscription.findFirst({
      where: { stripeSubscriptionId: invoice.subscription as string }
    })

    if (!subscription) {
      console.error('Subscription not found for invoice:', invoice.id)
      return
    }

    // Create payment record
    const payment = await prisma.payment.create({
      data: {
        subscriptionId: subscription.id,
        companyId: subscription.companyId,
        amount: status === 'SUCCEEDED' ? invoice.amount_paid / 100 : invoice.amount_due / 100,
        currency: invoice.currency.toUpperCase(),
        status,
        description: invoice.description || `Subscription payment ${status.toLowerCase()}`,
        stripePaymentIntentId: invoice.payment_intent as string,
        paidAt: status === 'SUCCEEDED' && invoice.status_transitions.paid_at 
          ? new Date(invoice.status_transitions.paid_at * 1000) 
          : undefined
      }
    })

    // Log activity
    await prisma.activity.create({
      data: {
        type: 'NOTE',
        title: status === 'SUCCEEDED' ? 'Payment received' : 'Payment failed',
        description: `Payment of $${payment.amount} ${status.toLowerCase()}`,
        companyId: subscription.companyId,
        createdById: subscription.companyId,
        metadata: {
          paymentId: payment.id,
          invoiceId: invoice.id,
          amount: payment.amount
        }
      }
    })

    return payment

  } catch (error) {
    console.error('Error creating payment from invoice:', error)
    throw error
  }
}
