import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if contract exists and belongs to the company
    const contract = await prisma.contract.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      },
      include: {
        customer: {
          select: { 
            id: true, 
            name: true, 
            email: true, 
            company: true 
          }
        },
        signatures: true
      }
    })

    if (!contract) {
      return NextResponse.json({ error: 'Contract not found' }, { status: 404 })
    }

    // Check if contract can be executed
    if (contract.status !== 'SIGNED') {
      return NextResponse.json(
        { error: 'Only signed contracts can be executed' },
        { status: 400 }
      )
    }

    // Check if signature is required and present
    if (contract.signatureRequired && contract.signatures.length === 0) {
      return NextResponse.json(
        { error: 'Contract requires signature before execution' },
        { status: 400 }
      )
    }

    // Update contract status to EXECUTED
    const updatedContract = await prisma.contract.update({
      where: { id: params.id },
      data: {
        status: 'EXECUTED',
        executedAt: new Date(),
      },
      include: {
        customer: {
          select: { id: true, name: true, email: true, company: true }
        },
        quotation: {
          select: { id: true, quotationNumber: true, title: true }
        },
        signatures: {
          include: {
            signedBy: {
              select: { name: true, firstName: true, lastName: true }
            }
          }
        },
        _count: {
          select: {
            signatures: true,
            activities: true,
          }
        }
      }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'CONTRACT_EXECUTED',
        title: 'Contract executed',
        description: `Contract "${contract.contractNumber}" was executed and is now active`,
        contractId: contract.id,
        customerId: contract.customerId,
        quotationId: contract.quotationId,
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    // Update customer status to ACTIVE if they were a PROSPECT
    if (contract.customer) {
      await prisma.customer.updateMany({
        where: {
          id: contract.customerId,
          status: 'PROSPECT',
        },
        data: {
          status: 'ACTIVE',
        }
      })
    }

    return NextResponse.json({
      message: 'Contract executed successfully',
      contract: updatedContract
    })
  } catch (error) {
    console.error('Error executing contract:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
