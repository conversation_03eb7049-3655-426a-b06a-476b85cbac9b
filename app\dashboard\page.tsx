import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { DashboardStats } from '@/components/dashboard/dashboard-stats'
import { DashboardOverview } from '@/components/dashboard/dashboard-overview'
import { RevenueChart } from '@/components/dashboard/revenue-chart'
import { SalesMetrics } from '@/components/dashboard/sales-metrics'
import { RecentActivity } from '@/components/dashboard/recent-activity'
import { TopCustomers } from '@/components/dashboard/top-customers'
import { TopItems } from '@/components/dashboard/top-items'
import { PipelineOverview } from '@/components/dashboard/pipeline-overview'
import { FinancialSummary } from '@/components/dashboard/financial-summary'
import { QuickActions } from '@/components/dashboard/quick-actions'
import { UpcomingTasks } from '@/components/dashboard/upcoming-tasks'
import { Button } from '@/components/ui/button'
import { Calendar, Download, Filter, RefreshCw } from 'lucide-react'

interface SearchParams {
  period?: string
  startDate?: string
  endDate?: string
}

export default async function DashboardPage({
  searchParams,
}: {
  searchParams: SearchParams
}) {
  const session = await getServerSession(authOptions)

  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  // Date range calculation
  const period = searchParams.period || '30d'
  const now = new Date()
  let startDate: Date
  let endDate: Date = now

  switch (period) {
    case '7d':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      break
    case '30d':
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      break
    case '90d':
      startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
      break
    case '1y':
      startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
      break
    case 'custom':
      startDate = searchParams.startDate ? new Date(searchParams.startDate) : new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      endDate = searchParams.endDate ? new Date(searchParams.endDate) : now
      break
    default:
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
  }

  // Fetch comprehensive dashboard data
  const [
    // Overview counts
    customerCount,
    leadCount,
    quotationCount,
    invoiceCount,
    contractCount,
    itemCount,

    // Financial data
    revenueData,
    quotationStats,
    invoiceStats,
    contractStats,

    // Recent activity
    recentActivities,

    // Top performers
    topCustomers,
    topItems,

    // Pipeline data
    leadsByStatus,
    quotationsByStatus,
    invoicesByStatus,
    contractsByStatus,

    // Monthly trends
    monthlyRevenue,
    monthlyQuotations,
    monthlyInvoices,
    monthlyContracts,

    // Legacy data for existing components
    recentQuotations,
    recentInvoices
  ] = await Promise.all([
    // Overview counts
    prisma.customer.count({ where: { companyId: session.user.companyId } }),
    prisma.lead.count({ where: { companyId: session.user.companyId } }),
    prisma.quotation.count({ where: { companyId: session.user.companyId } }),
    prisma.invoice.count({ where: { companyId: session.user.companyId } }),
    prisma.contract.count({ where: { companyId: session.user.companyId } }),
    prisma.item.count({ where: { companyId: session.user.companyId } }),

    // Financial aggregations
    prisma.invoice.aggregate({
      where: {
        companyId: session.user.companyId,
        status: 'PAID',
        paidAt: { gte: startDate, lte: endDate }
      },
      _sum: { total: true },
      _count: true
    }),

    // Quotation stats
    prisma.quotation.groupBy({
      by: ['status'],
      where: {
        companyId: session.user.companyId,
        createdAt: { gte: startDate, lte: endDate }
      },
      _count: true,
      _sum: { total: true }
    }),

    // Invoice stats
    prisma.invoice.groupBy({
      by: ['status'],
      where: {
        companyId: session.user.companyId,
        createdAt: { gte: startDate, lte: endDate }
      },
      _count: true,
      _sum: { total: true }
    }),

    // Contract stats
    prisma.contract.groupBy({
      by: ['status'],
      where: {
        companyId: session.user.companyId,
        createdAt: { gte: startDate, lte: endDate }
      },
      _count: true,
      _sum: { value: true }
    }),

    // Recent activities
    prisma.activity.findMany({
      where: {
        companyId: session.user.companyId,
        createdAt: { gte: startDate, lte: endDate }
      },
      include: {
        createdBy: {
          select: { name: true, firstName: true, lastName: true }
        },
        customer: {
          select: { name: true }
        },
        lead: {
          select: { name: true }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 20
    }),

    // Top customers by revenue
    prisma.customer.findMany({
      where: { companyId: session.user.companyId },
      include: {
        invoices: {
          where: {
            status: 'PAID',
            paidAt: { gte: startDate, lte: endDate }
          },
          select: { total: true }
        },
        quotations: {
          where: {
            createdAt: { gte: startDate, lte: endDate }
          },
          select: { total: true }
        }
      },
      take: 10
    }),

    // Top items by usage
    prisma.item.findMany({
      where: { companyId: session.user.companyId },
      include: {
        quotationItems: {
          where: {
            quotation: {
              createdAt: { gte: startDate, lte: endDate }
            }
          }
        },
        invoiceItems: {
          where: {
            invoice: {
              createdAt: { gte: startDate, lte: endDate }
            }
          }
        }
      },
      take: 10
    }),

    // Pipeline data
    prisma.lead.groupBy({
      by: ['status'],
      where: { companyId: session.user.companyId },
      _count: true
    }),

    prisma.quotation.groupBy({
      by: ['status'],
      where: { companyId: session.user.companyId },
      _count: true
    }),

    prisma.invoice.groupBy({
      by: ['status'],
      where: { companyId: session.user.companyId },
      _count: true
    }),

    prisma.contract.groupBy({
      by: ['status'],
      where: { companyId: session.user.companyId },
      _count: true
    }),

    // Monthly trends (simplified for now)
    [],
    [],
    [],
    [],

    // Legacy data for existing components
    prisma.quotation.findMany({
      where: { companyId: session.user.companyId },
      include: {
        customer: { select: { name: true } }
      },
      orderBy: { createdAt: 'desc' },
      take: 5
    }),
    prisma.invoice.findMany({
      where: { companyId: session.user.companyId },
      include: {
        customer: { select: { name: true } }
      },
      orderBy: { createdAt: 'desc' },
      take: 5
    })
  ])

  // Process data for components
  const overviewData = {
    totalCustomers: customerCount,
    totalLeads: leadCount,
    totalQuotations: quotationCount,
    totalInvoices: invoiceCount,
    totalContracts: contractCount,
    totalItems: itemCount,
    totalRevenue: revenueData._sum.total || 0,
    paidInvoices: revenueData._count
  }

  const salesData = {
    quotationStats,
    invoiceStats,
    contractStats,
    period,
    startDate,
    endDate
  }

  const pipelineData = {
    leads: leadsByStatus,
    quotations: quotationsByStatus,
    invoices: invoicesByStatus,
    contracts: contractsByStatus
  }

  const trendsData = {
    revenue: monthlyRevenue,
    quotations: monthlyQuotations,
    invoices: monthlyInvoices,
    contracts: monthlyContracts
  }

  // Process top customers
  const processedTopCustomers = topCustomers
    .map(customer => ({
      ...customer,
      totalRevenue: customer.invoices.reduce((sum, inv) => sum + inv.total, 0),
      totalQuotations: customer.quotations.length,
      quotationValue: customer.quotations.reduce((sum, quot) => sum + quot.total, 0)
    }))
    .sort((a, b) => b.totalRevenue - a.totalRevenue)
    .slice(0, 5)

  // Process top items
  const processedTopItems = topItems
    .map(item => ({
      ...item,
      totalUsage: item.quotationItems.length + item.invoiceItems.length,
      quotationUsage: item.quotationItems.length,
      invoiceUsage: item.invoiceItems.length
    }))
    .sort((a, b) => b.totalUsage - a.totalUsage)
    .slice(0, 5)

  // Legacy stats for existing components
  const stats = {
    customers: customerCount,
    leads: leadCount,
    quotations: quotationCount,
    invoices: invoiceCount,
    contracts: contractCount,
    revenue: revenueData._sum.total || 0,
    pendingRevenue: quotationStats
      .filter(s => ['SENT', 'VIEWED'].includes(s.status))
      .reduce((sum, s) => sum + (s._sum.total || 0), 0)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-1">
            Business overview and analytics
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <select
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            defaultValue={period}
            name="period"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
            <option value="custom">Custom range</option>
          </select>
          <Button variant="outline">
            <Calendar className="h-4 w-4 mr-2" />
            Date Range
          </Button>
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Download
          </Button>
          <Button variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <DashboardOverview data={overviewData} />

      {/* Financial Summary */}
      <FinancialSummary data={salesData} />

      {/* Charts and Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <RevenueChart data={trendsData} />
        <SalesMetrics data={salesData} />
      </div>

      {/* Pipeline and Performance */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <PipelineOverview data={pipelineData} />
        <div className="space-y-6">
          <TopCustomers customers={processedTopCustomers} />
          <TopItems items={processedTopItems} />
        </div>
      </div>

      {/* Legacy Components */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - 2/3 width */}
        <div className="lg:col-span-2 space-y-6">
          <RecentActivity activities={recentActivities} />
        </div>

        {/* Right Column - 1/3 width */}
        <div className="space-y-6">
          <QuickActions />
          <UpcomingTasks
            quotations={recentQuotations}
            invoices={recentInvoices}
          />
        </div>
      </div>
    </div>
  )
}
