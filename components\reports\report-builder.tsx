'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  BarChart3, 
  Settings, 
  Filter,
  Calendar,
  Download,
  Eye,
  Save,
  Play,
  Plus,
  Trash2,
  Info
} from 'lucide-react'
import toast from 'react-hot-toast'

interface ReportFilter {
  id: number
  field: string
  operator: string
  value: string
}

interface ReportConfig {
  name: string
  description: string
  category: string
  type: string
  dataSource: string
  dateRange: string
  customStartDate: string
  customEndDate: string
  filters: ReportFilter[]
  groupBy: string
  metrics: string[]
  format: string
}

export function ReportBuilder() {
  const [reportConfig, setReportConfig] = useState<ReportConfig>({
    name: '',
    description: '',
    category: 'financial',
    type: 'summary',
    dataSource: 'invoices',
    dateRange: '30d',
    customStartDate: '',
    customEndDate: '',
    filters: [],
    groupBy: '',
    metrics: ['count', 'sum'],
    format: 'pdf'
  })
  const [isGenerating, setIsGenerating] = useState(false)
  const [previewData, setPreviewData] = useState(null)
  const router = useRouter()

  const dataSourceOptions = [
    { value: 'customers', label: 'Customers', description: 'Customer data and demographics' },
    { value: 'leads', label: 'Leads', description: 'Lead generation and conversion' },
    { value: 'quotations', label: 'Quotations', description: 'Quote performance and conversion' },
    { value: 'invoices', label: 'Invoices', description: 'Invoice and payment data' },
    { value: 'contracts', label: 'Contracts', description: 'Contract lifecycle and status' },
    { value: 'receipts', label: 'Receipts', description: 'Payment receipts and transactions' },
    { value: 'items', label: 'Items/Services', description: 'Product and service performance' },
    { value: 'activities', label: 'Activities', description: 'Business activity tracking' }
  ]

  const categoryOptions = [
    { value: 'financial', label: 'Financial' },
    { value: 'sales', label: 'Sales' },
    { value: 'customer', label: 'Customer' },
    { value: 'operational', label: 'Operational' },
    { value: 'analytics', label: 'Analytics' }
  ]

  const typeOptions = [
    { value: 'summary', label: 'Summary Report' },
    { value: 'detailed', label: 'Detailed Report' },
    { value: 'trend', label: 'Trend Analysis' },
    { value: 'comparison', label: 'Comparison Report' }
  ]

  const dateRangeOptions = [
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 90 days' },
    { value: '1y', label: 'Last year' },
    { value: 'custom', label: 'Custom range' }
  ]

  const metricOptions = [
    { value: 'count', label: 'Count' },
    { value: 'sum', label: 'Sum' },
    { value: 'average', label: 'Average' },
    { value: 'min', label: 'Minimum' },
    { value: 'max', label: 'Maximum' },
    { value: 'growth', label: 'Growth Rate' }
  ]

  const handleConfigChange = (field: string, value: any) => {
    setReportConfig(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const addFilter = () => {
    const newFilter = {
      id: Date.now(),
      field: '',
      operator: 'equals',
      value: ''
    }
    setReportConfig(prev => ({
      ...prev,
      filters: [...prev.filters, newFilter]
    }))
  }

  const updateFilter = (filterId: number, field: string, value: any) => {
    setReportConfig(prev => ({
      ...prev,
      filters: prev.filters.map(filter => 
        filter.id === filterId ? { ...filter, [field]: value } : filter
      )
    }))
  }

  const removeFilter = (filterId: number) => {
    setReportConfig(prev => ({
      ...prev,
      filters: prev.filters.filter(filter => filter.id !== filterId)
    }))
  }

  const handlePreview = async () => {
    setIsGenerating(true)
    try {
      const response = await fetch('/api/reports/preview', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(reportConfig),
      })

      if (!response.ok) {
        throw new Error('Failed to generate preview')
      }

      const data = await response.json()
      setPreviewData(data)
      toast.success('Preview generated successfully')
    } catch (error) {
      toast.error('Failed to generate preview')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleSaveAndGenerate = async () => {
    if (!reportConfig.name) {
      toast.error('Please enter a report name')
      return
    }

    setIsGenerating(true)
    try {
      const response = await fetch('/api/reports', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(reportConfig),
      })

      if (!response.ok) {
        throw new Error('Failed to create report')
      }

      const result = await response.json()
      toast.success('Report created successfully')
      router.push(`/dashboard/reports/${result.id}`)
    } catch (error) {
      toast.error('Failed to create report')
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Configuration Panel */}
      <div className="lg:col-span-2 space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5" />
              <span>Report Configuration</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Report Name *</Label>
                <Input
                  id="name"
                  value={reportConfig.name}
                  onChange={(e) => handleConfigChange('name', e.target.value)}
                  placeholder="Monthly Sales Report"
                />
              </div>
              <div>
                <Label htmlFor="category">Category</Label>
                <select
                  id="category"
                  value={reportConfig.category}
                  onChange={(e) => handleConfigChange('category', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {categoryOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <textarea
                id="description"
                value={reportConfig.description}
                onChange={(e) => handleConfigChange('description', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Describe what this report analyzes..."
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="type">Report Type</Label>
                <select
                  id="type"
                  value={reportConfig.type}
                  onChange={(e) => handleConfigChange('type', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {typeOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <Label htmlFor="format">Output Format</Label>
                <select
                  id="format"
                  value={reportConfig.format}
                  onChange={(e) => handleConfigChange('format', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="pdf">PDF</option>
                  <option value="excel">Excel</option>
                  <option value="csv">CSV</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Data Source */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5" />
              <span>Data Source</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {dataSourceOptions.map(option => (
                <div
                  key={option.value}
                  className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                    reportConfig.dataSource === option.value
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => handleConfigChange('dataSource', option.value)}
                >
                  <div className="font-medium text-gray-900">{option.label}</div>
                  <div className="text-sm text-gray-600 mt-1">{option.description}</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Date Range */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Calendar className="h-5 w-5" />
              <span>Date Range</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label>Time Period</Label>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-2 mt-2">
                {dateRangeOptions.map(option => (
                  <button
                    key={option.value}
                    type="button"
                    className={`px-3 py-2 text-sm border rounded-md transition-colors ${
                      reportConfig.dateRange === option.value
                        ? 'border-blue-500 bg-blue-50 text-blue-700'
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                    onClick={() => handleConfigChange('dateRange', option.value)}
                  >
                    {option.label}
                  </button>
                ))}
              </div>
            </div>

            {reportConfig.dateRange === 'custom' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="startDate">Start Date</Label>
                  <Input
                    id="startDate"
                    type="date"
                    value={reportConfig.customStartDate}
                    onChange={(e) => handleConfigChange('customStartDate', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="endDate">End Date</Label>
                  <Input
                    id="endDate"
                    type="date"
                    value={reportConfig.customEndDate}
                    onChange={(e) => handleConfigChange('customEndDate', e.target.value)}
                  />
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Filters */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center space-x-2">
                <Filter className="h-5 w-5" />
                <span>Filters</span>
              </CardTitle>
              <Button type="button" onClick={addFilter} >
                <Plus className="h-4 w-4 mr-2" />
                Add Filter
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {reportConfig.filters.length > 0 ? (
              <div className="space-y-3">
                {reportConfig.filters.map((filter, index) => (
                  <div key={filter.id} className="flex items-center space-x-3 p-3 border rounded-lg">
                    <select
                      value={filter.field}
                      onChange={(e) => updateFilter(filter.id, 'field', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">Select field</option>
                      <option value="status">Status</option>
                      <option value="amount">Amount</option>
                      <option value="customer">Customer</option>
                      <option value="date">Date</option>
                    </select>
                    <select
                      value={filter.operator}
                      onChange={(e) => updateFilter(filter.id, 'operator', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="equals">Equals</option>
                      <option value="not_equals">Not equals</option>
                      <option value="greater_than">Greater than</option>
                      <option value="less_than">Less than</option>
                      <option value="contains">Contains</option>
                    </select>
                    <Input
                      value={filter.value}
                      onChange={(e) => updateFilter(filter.id, 'value', e.target.value)}
                      placeholder="Value"
                      className="flex-1"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFilter(filter.id)}
                      className="text-red-600"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-6 border-2 border-dashed border-gray-300 rounded-lg">
                <Filter className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500">No filters added</p>
                <p className="text-sm text-gray-400">Add filters to refine your report data</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Preview Panel */}
      <div className="space-y-6">
        {/* Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button
              onClick={handlePreview}
              disabled={isGenerating}
              className="w-full"
              variant="outline"
            >
              <Eye className="h-4 w-4 mr-2" />
              Preview Report
            </Button>
            <Button
              onClick={handleSaveAndGenerate}
              disabled={isGenerating || !reportConfig.name}
              className="w-full"
            >
              {isGenerating ? (
                <>
                  <Play className="h-4 w-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save & Generate
                </>
              )}
            </Button>
          </CardContent>
        </Card>

        {/* Configuration Summary */}
        <Card>
          <CardHeader>
            <CardTitle>Configuration Summary</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Data Source:</span>
                <span className="font-medium capitalize">{reportConfig.dataSource}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Date Range:</span>
                <span className="font-medium">{dateRangeOptions.find(o => o.value === reportConfig.dateRange)?.label}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Filters:</span>
                <span className="font-medium">{reportConfig.filters.length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Format:</span>
                <span className="font-medium uppercase">{reportConfig.format}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Preview Data */}
        {previewData && (
          <Card>
            <CardHeader>
              <CardTitle>Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-sm">
                <pre className="bg-gray-50 p-3 rounded-md overflow-auto">
                  {JSON.stringify(previewData, null, 2)}
                </pre>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Help */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Info className="h-5 w-5" />
              <span>Help</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-sm text-gray-600 space-y-2">
              <p>• Select a data source to analyze</p>
              <p>• Choose date range for the analysis</p>
              <p>• Add filters to refine the data</p>
              <p>• Preview before generating the final report</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
