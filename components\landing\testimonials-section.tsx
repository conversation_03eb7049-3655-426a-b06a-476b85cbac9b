'use client'

import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  Star,
  Quote,
  TrendingUp,
  Users,
  Award,
  ArrowRight
} from 'lucide-react'

export function TestimonialsSection() {
  const testimonials = [
    {
      name: '<PERSON>',
      role: 'CEO',
      company: 'TechStart Solutions',
      avatar: '/avatars/sarah.jpg',
      rating: 5,
      quote: 'BusinessSaaS transformed how we handle client relationships. The automated quotation system alone saved us 15 hours per week. The ROI was immediate.',
      metrics: {
        improvement: '300% faster quotations',
        savings: '15 hours/week saved'
      },
      industry: 'Technology'
    },
    {
      name: '<PERSON>',
      role: 'Operations Director',
      company: 'Global Consulting Group',
      avatar: '/avatars/michael.jpg',
      rating: 5,
      quote: 'The analytics dashboard gives us insights we never had before. We can now predict cash flow and identify growth opportunities with confidence.',
      metrics: {
        improvement: '40% better cash flow',
        savings: '$50K annual savings'
      },
      industry: 'Consulting'
    },
    {
      name: '<PERSON>',
      role: 'Founder',
      company: 'Creative Design Studio',
      avatar: '/avatars/emily.jpg',
      rating: 5,
      quote: 'As a creative agency, we needed something that looked professional but was easy to use. BusinessSaaS delivered on both fronts beautifully.',
      metrics: {
        improvement: '200% client satisfaction',
        savings: '25% faster invoicing'
      },
      industry: 'Design'
    },
    {
      name: 'David Thompson',
      role: 'CFO',
      company: 'Manufacturing Plus',
      avatar: '/avatars/david.jpg',
      rating: 5,
      quote: 'The contract management features are outstanding. We\'ve reduced contract processing time by 60% and eliminated manual errors completely.',
      metrics: {
        improvement: '60% faster contracts',
        savings: 'Zero manual errors'
      },
      industry: 'Manufacturing'
    },
    {
      name: 'Lisa Park',
      role: 'Business Owner',
      company: 'Park & Associates',
      avatar: '/avatars/lisa.jpg',
      rating: 5,
      quote: 'The customer support is exceptional. They helped us migrate from our old system seamlessly and provided training for our entire team.',
      metrics: {
        improvement: '100% team adoption',
        savings: '2-day migration'
      },
      industry: 'Legal Services'
    },
    {
      name: 'James Wilson',
      role: 'VP Sales',
      company: 'Enterprise Solutions Inc',
      avatar: '/avatars/james.jpg',
      rating: 5,
      quote: 'The API integration capabilities allowed us to connect with our existing CRM perfectly. It\'s like having a custom solution at a fraction of the cost.',
      metrics: {
        improvement: 'Seamless integration',
        savings: '80% cost reduction'
      },
      industry: 'Enterprise'
    }
  ]

  const stats = [
    {
      icon: Users,
      value: '10,000+',
      label: 'Happy Customers',
      description: 'Businesses trust BusinessSaaS'
    },
    {
      icon: Star,
      value: '4.9/5',
      label: 'Customer Rating',
      description: 'Based on 2,500+ reviews'
    },
    {
      icon: TrendingUp,
      value: '300%',
      label: 'Average ROI',
      description: 'Within first 6 months'
    },
    {
      icon: Award,
      value: '99.9%',
      label: 'Uptime SLA',
      description: 'Reliable service guarantee'
    }
  ]

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ))
  }

  return (
    <section id="testimonials" className="py-20 px-4 bg-white">
      <div className="container mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <Badge variant="outline" className="mb-4">
            <Star className="h-4 w-4 mr-2 text-yellow-500" />
            Customer Success
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Loved by Businesses
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent block">
              Around the World
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Join thousands of businesses that have transformed their operations with BusinessSaaS. 
            See what our customers have to say about their experience.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <stat.icon className="h-8 w-8 text-blue-600" />
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-2">{stat.value}</div>
              <div className="font-semibold text-gray-900 mb-1">{stat.label}</div>
              <div className="text-sm text-gray-600">{stat.description}</div>
            </div>
          ))}
        </div>

        {/* Testimonials Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
              <CardContent className="p-6">
                {/* Quote Icon */}
                <div className="mb-4">
                  <Quote className="h-8 w-8 text-blue-600 opacity-60" />
                </div>

                {/* Rating */}
                <div className="flex items-center mb-4">
                  {renderStars(testimonial.rating)}
                </div>

                {/* Quote */}
                <blockquote className="text-gray-700 mb-6 leading-relaxed">
                  "{testimonial.quote}"
                </blockquote>

                {/* Metrics */}
                <div className="bg-gray-50 rounded-lg p-4 mb-6">
                  <div className="grid grid-cols-1 gap-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Improvement:</span>
                      <span className="font-semibold text-green-600">{testimonial.metrics.improvement}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Savings:</span>
                      <span className="font-semibold text-blue-600">{testimonial.metrics.savings}</span>
                    </div>
                  </div>
                </div>

                {/* Author */}
                <div className="flex items-center">
                  <Avatar className="h-12 w-12 mr-4">
                    <AvatarImage src={testimonial.avatar} alt={testimonial.name} />
                    <AvatarFallback className="bg-blue-100 text-blue-600">
                      {testimonial.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-semibold text-gray-900">{testimonial.name}</div>
                    <div className="text-sm text-gray-600">{testimonial.role}</div>
                    <div className="text-sm text-gray-600">{testimonial.company}</div>
                  </div>
                </div>

                {/* Industry Badge */}
                <div className="mt-4">
                  <Badge variant="secondary" className="text-xs">
                    {testimonial.industry}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Case Study CTA */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8 md:p-12 text-center">
          <h3 className="text-3xl font-bold text-gray-900 mb-4">
            See Detailed Case Studies
          </h3>
          <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
            Dive deeper into how businesses like yours have achieved success with BusinessSaaS. 
            Read our comprehensive case studies and implementation guides.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="inline-flex items-center px-6 py-3 bg-white border border-gray-300 rounded-lg font-medium text-gray-700 hover:bg-gray-50 transition-colors">
              Download Case Studies
              <ArrowRight className="ml-2 h-5 w-5" />
            </button>
            <button className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors">
              Schedule Demo
              <ArrowRight className="ml-2 h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Trust Indicators */}
        <div className="mt-16 text-center">
          <p className="text-gray-600 mb-8">Trusted by companies of all sizes</p>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-8 items-center opacity-60">
            {/* Company logos would go here */}
            {Array.from({ length: 5 }, (_, i) => (
              <div key={i} className="bg-gray-100 rounded-lg p-4 h-16 flex items-center justify-center">
                <div className="w-20 h-8 bg-gray-300 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
