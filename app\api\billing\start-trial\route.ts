import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const trialSchema = z.object({
  companyId: z.string(),
  plan: z.enum(['starter', 'professional', 'enterprise'])
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = trialSchema.parse(body)

    // Get company details
    const company = await prisma.company.findUnique({
      where: { id: validatedData.companyId },
      include: {
        owner: true
      }
    })

    if (!company) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    // Check if company is already on a trial or active subscription
    if (company.status === 'ACTIVE' || company.status === 'TRIAL') {
      return NextResponse.json(
        { error: 'Company already has an active subscription or trial' },
        { status: 400 }
      )
    }

    // Calculate trial end date (14 days from now)
    const trialEndDate = new Date()
    trialEndDate.setDate(trialEndDate.getDate() + 14)

    // Update company to trial status
    const updatedCompany = await prisma.company.update({
      where: { id: company.id },
      data: {
        status: 'TRIAL',
        plan: validatedData.plan.toUpperCase(),
        trialEndsAt: trialEndDate,
        // Set plan limits based on selected plan
        maxUsers: validatedData.plan === 'enterprise' ? 100 : 
                  validatedData.plan === 'professional' ? 25 : 5,
        maxCustomers: validatedData.plan === 'enterprise' ? -1 : 
                     validatedData.plan === 'professional' ? 1000 : 100,
        maxQuotations: validatedData.plan === 'starter' ? 50 : -1,
        maxInvoices: validatedData.plan === 'starter' ? 25 : -1,
        maxContracts: validatedData.plan === 'enterprise' ? -1 : 
                     validatedData.plan === 'professional' ? 100 : 10,
        maxStorage: validatedData.plan === 'enterprise' ? 100000 : // 100GB
                   validatedData.plan === 'professional' ? 10000 : // 10GB
                   1000, // 1GB
        features: validatedData.plan === 'enterprise'
          ? ['advanced_analytics', 'custom_branding', 'api_access', 'priority_support', 'white_label', 'dedicated_manager']
          : validatedData.plan === 'professional'
          ? ['advanced_analytics', 'custom_branding', 'api_access', 'priority_support']
          : ['basic_features']
      }
    })

    // Create trial subscription record
    await prisma.subscription.create({
      data: {
        companyId: company.id,
        status: 'TRIALING',
        plan: validatedData.plan.toUpperCase(),
        amount: 0, // Trial is free
        currency: 'USD',
        interval: 'MONTHLY',
        currentPeriodStart: new Date(),
        currentPeriodEnd: trialEndDate,
        cancelAtPeriodEnd: false,
        trialEnd: trialEndDate
      }
    })

    // Log activity
    await prisma.activity.create({
      data: {
        type: 'TRIAL',
        title: 'Free trial started',
        description: `14-day free trial started for ${validatedData.plan} plan`,
        companyId: company.id,
        createdById: company.ownerId,
        metadata: {
          plan: validatedData.plan,
          trialEndDate: trialEndDate.toISOString()
        }
      }
    })

    // Send welcome email (optional - implement if email service is configured)
    // await sendWelcomeEmail({
    //   email: company.owner.email,
    //   name: company.owner.name,
    //   companyName: company.name,
    //   plan: validatedData.plan,
    //   trialEndDate
    // })

    return NextResponse.json({
      message: 'Trial started successfully',
      company: {
        id: updatedCompany.id,
        name: updatedCompany.name,
        plan: updatedCompany.plan,
        status: updatedCompany.status,
        trialEndsAt: updatedCompany.trialEndsAt
      }
    })

  } catch (error) {
    console.error('Start trial error:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Helper function to send welcome email (implement when email service is ready)
async function sendWelcomeEmail(data: {
  email: string
  name: string
  companyName: string
  plan: string
  trialEndDate: Date
}) {
  // TODO: Implement email sending
  // This could use services like:
  // - SendGrid
  // - Mailgun
  // - AWS SES
  // - Resend
  
  console.log('Welcome email would be sent to:', data.email)
  console.log('Trial details:', {
    plan: data.plan,
    trialEndDate: data.trialEndDate
  })
}

// Helper function to check trial status
export async function checkTrialStatus(companyId: string) {
  const company = await prisma.company.findUnique({
    where: { id: companyId },
    select: {
      status: true,
      trialEndsAt: true,
      plan: true
    }
  })

  if (!company) {
    return { status: 'not_found' }
  }

  if (company.status !== 'TRIAL') {
    return { status: company.status.toLowerCase() }
  }

  const now = new Date()
  const trialEnd = company.trialEndsAt

  if (!trialEnd) {
    return { status: 'invalid_trial' }
  }

  if (now > trialEnd) {
    // Trial has expired
    await prisma.company.update({
      where: { id: companyId },
      data: { status: 'TRIAL_EXPIRED' }
    })
    return { status: 'trial_expired', trialEndDate: trialEnd }
  }

  // Trial is still active
  const daysLeft = Math.ceil((trialEnd.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
  return { 
    status: 'trial_active', 
    trialEndDate: trialEnd,
    daysLeft
  }
}
