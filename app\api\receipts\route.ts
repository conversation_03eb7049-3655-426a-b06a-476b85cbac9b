import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const receiptSchema = z.object({
  receiptNumber: z.string().min(1, 'Receipt number is required'),
  invoiceId: z.string().min(1, 'Invoice is required'),
  amount: z.number().min(0.01, 'Amount must be greater than 0'),
  paymentMethod: z.enum(['CASH', 'BANK_TRANSFER', 'CREDIT_CARD', 'DEBIT_CARD', 'UPI', 'CHEQUE', 'OTHER']),
  paidAt: z.string().min(1, 'Payment date is required'),
  status: z.enum(['PENDING', 'CONFIRMED', 'CANCELLED']).default('CONFIRMED'),
  notes: z.string().optional().nullable(),
  referenceNumber: z.string().optional().nullable(),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || ''
    const paymentMethod = searchParams.get('paymentMethod') || ''
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = (page - 1) * limit

    // Build where clause
    const where: any = {
      companyId: session.user.companyId,
    }

    if (search) {
      where.OR = [
        { receiptNumber: { contains: search } },
        { invoice: { invoiceNumber: { contains: search } } },
        { invoice: { customer: { name: { contains: search } } } },
        { notes: { contains: search } },
      ]
    }

    if (status) {
      where.status = status
    }

    if (paymentMethod) {
      where.paymentMethod = paymentMethod
    }

    const [receipts, totalCount] = await Promise.all([
      prisma.receipt.findMany({
        where,
        include: {
          invoice: {
            include: {
              customer: {
                select: { id: true, name: true, email: true, company: true }
              }
            }
          },
          createdBy: {
            select: { name: true, firstName: true, lastName: true }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip: offset,
        take: limit,
      }),
      prisma.receipt.count({ where })
    ])

    return NextResponse.json({
      receipts,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      }
    })
  } catch (error) {
    console.error('Error fetching receipts:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = receiptSchema.parse(body)

    // Check if receipt number already exists
    const existingReceipt = await prisma.receipt.findFirst({
      where: {
        receiptNumber: validatedData.receiptNumber,
        companyId: session.user.companyId,
      }
    })

    if (existingReceipt) {
      return NextResponse.json(
        { error: 'Receipt number already exists' },
        { status: 400 }
      )
    }

    // Verify invoice exists and belongs to company
    const invoice = await prisma.invoice.findFirst({
      where: {
        id: validatedData.invoiceId,
        companyId: session.user.companyId,
      }
    })

    if (!invoice) {
      return NextResponse.json(
        { error: 'Invoice not found' },
        { status: 404 }
      )
    }

    // Create receipt in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create the receipt
      const receipt = await tx.receipt.create({
        data: {
          receiptNumber: validatedData.receiptNumber,
          title: `Receipt for ${validatedData.receiptNumber}`,
          invoiceId: validatedData.invoiceId,
          amount: validatedData.amount,
          paymentMethod: validatedData.paymentMethod,
          paidAt: new Date(validatedData.paidAt),
          status: validatedData.status,
          notes: validatedData.notes,
          referenceNumber: validatedData.referenceNumber,
          companyId: session.user.companyId,
          createdById: session.user.id,
        },
        include: {
          invoice: {
            include: {
              customer: {
                select: { id: true, name: true, email: true, company: true }
              }
            }
          },
          createdBy: {
            select: { name: true, firstName: true, lastName: true }
          }
        }
      })

      // If receipt is confirmed and amount matches invoice total, mark invoice as paid
      if (validatedData.status === 'CONFIRMED' && validatedData.amount >= invoice.total) {
        await tx.invoice.update({
          where: { id: validatedData.invoiceId },
          data: { 
            status: 'PAID',
            paidAt: new Date(validatedData.paidAt)
          }
        })
      }

      return receipt
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'NOTE',
        title: 'Payment receipt created',
        description: `Receipt ${result.receiptNumber} created for ${result.invoice?.invoiceNumber || 'invoice'}`,
        invoiceId: result.invoiceId,
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    return NextResponse.json(result, { status: 201 })
  } catch (error) {
    console.error('Error creating receipt:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
