'use client'

import { useState, useEffect } from 'react'
import { toast } from 'react-hot-toast'
import {
  CreditCard,
  Mail,
  Bell,
  Zap,
  Key,
  RefreshCw,
  CheckCircle,
  XCircle,
  Loader2
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

interface Integration {
  name: string
  description: string
  status: 'connected' | 'disconnected' | 'active'
  configured: boolean
  lastChecked: string
}

interface IntegrationsData {
  stripe: Integration
  mailgun: Integration
  slack: Integration
  webhooks: Integration
}

export function IntegrationStatus() {
  const [integrations, setIntegrations] = useState<IntegrationsData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [testingIntegration, setTestingIntegration] = useState<string | null>(null)

  const fetchIntegrations = async () => {
    try {
      const response = await fetch('/api/super-admin/integrations')
      if (response.ok) {
        const data = await response.json()
        setIntegrations(data)
      } else {
        throw new Error('Failed to fetch integrations')
      }
    } catch (error) {
      console.error('Error fetching integrations:', error)
      toast.error('Failed to load integration status')
    } finally {
      setIsLoading(false)
    }
  }

  const testIntegration = async (integrationKey: string) => {
    setTestingIntegration(integrationKey)
    try {
      const response = await fetch('/api/super-admin/integrations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ integration: integrationKey }),
      })

      const result = await response.json()
      
      if (result.success) {
        toast.success(result.message)
      } else {
        toast.error(result.message)
      }
    } catch (error) {
      console.error('Error testing integration:', error)
      toast.error('Failed to test integration')
    } finally {
      setTestingIntegration(null)
    }
  }

  useEffect(() => {
    fetchIntegrations()
  }, [])

  const getIntegrationIcon = (key: string) => {
    switch (key) {
      case 'stripe':
        return CreditCard
      case 'mailgun':
        return Mail
      case 'slack':
        return Bell
      case 'webhooks':
        return Zap
      default:
        return Key
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected':
      case 'active':
        return 'text-green-600 border-green-200'
      case 'disconnected':
        return 'text-gray-600 border-gray-200'
      default:
        return 'text-yellow-600 border-yellow-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
      case 'active':
        return CheckCircle
      case 'disconnected':
        return XCircle
      default:
        return RefreshCw
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Key className="h-5 w-5 text-orange-600" />
            <span>Third-Party Integrations</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2">Loading integrations...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!integrations) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Key className="h-5 w-5 text-orange-600" />
            <span>Third-Party Integrations</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-gray-500">Failed to load integration status</p>
            <Button onClick={fetchIntegrations} className="mt-4">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Key className="h-5 w-5 text-orange-600" />
            <span>Third-Party Integrations</span>
          </div>
          <Button variant="outline" size="sm" onClick={fetchIntegrations}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {Object.entries(integrations).map(([key, integration]) => {
            const Icon = getIntegrationIcon(key)
            const StatusIcon = getStatusIcon(integration.status)
            const isTestingThis = testingIntegration === key

            return (
              <div key={key} className="p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <Icon className="h-5 w-5 text-blue-600" />
                    <div>
                      <p className="font-medium">{integration.name}</p>
                      <p className="text-sm text-gray-500">{integration.description}</p>
                    </div>
                  </div>
                  <StatusIcon className={`h-4 w-4 ${getStatusColor(integration.status).split(' ')[0]}`} />
                </div>
                
                <div className="flex items-center justify-between">
                  <Badge variant="outline" className={getStatusColor(integration.status)}>
                    {integration.status === 'connected' ? 'Connected' : 
                     integration.status === 'active' ? 'Active' : 'Disconnected'}
                  </Badge>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => testIntegration(key)}
                    disabled={isTestingThis}
                  >
                    {isTestingThis ? (
                      <Loader2 className="h-3 w-3 animate-spin" />
                    ) : (
                      'Test'
                    )}
                  </Button>
                </div>
                
                <p className="text-xs text-gray-400 mt-2">
                  Last checked: {new Date(integration.lastChecked).toLocaleTimeString()}
                </p>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
