'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Bell, 
  Mail, 
  Smartphone,
  MessageSquare,
  Clock,
  Shield,
  Volume2,
  VolumeX,
  Calendar,
  Globe,
  Save,
  CheckCircle
} from 'lucide-react'
import toast from 'react-hot-toast'

interface NotificationSettingsProps {
  userId: string
  initialPreferences: any
}

export function NotificationSettings({ userId, initialPreferences }: NotificationSettingsProps) {
  const [preferences, setPreferences] = useState(initialPreferences)
  const [isSaving, setIsSaving] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)

  const updatePreference = (key: string, value: any) => {
    setPreferences((prev: any) => ({
      ...prev,
      [key]: value
    }))
    setHasChanges(true)
  }

  const handleSave = async () => {
    setIsSaving(true)
    try {
      const response = await fetch('/api/notifications/preferences', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          preferences
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to save preferences')
      }

      toast.success('Notification preferences saved successfully')
      setHasChanges(false)
    } catch (error) {
      toast.error('Failed to save preferences')
    } finally {
      setIsSaving(false)
    }
  }

  const notificationChannels = [
    {
      key: 'emailNotifications',
      title: 'Email Notifications',
      description: 'Receive notifications via email',
      icon: Mail,
      color: 'text-blue-600'
    },
    {
      key: 'pushNotifications',
      title: 'Push Notifications',
      description: 'Receive browser push notifications',
      icon: Bell,
      color: 'text-green-600'
    },
    {
      key: 'smsNotifications',
      title: 'SMS Notifications',
      description: 'Receive notifications via SMS (premium feature)',
      icon: MessageSquare,
      color: 'text-purple-600'
    }
  ]

  const businessNotifications = [
    {
      key: 'invoiceOverdue',
      title: 'Invoice Overdue',
      description: 'When invoices become overdue'
    },
    {
      key: 'paymentReceived',
      title: 'Payment Received',
      description: 'When payments are received'
    },
    {
      key: 'quotationAccepted',
      title: 'Quotation Accepted',
      description: 'When quotations are accepted by customers'
    },
    {
      key: 'quotationExpired',
      title: 'Quotation Expired',
      description: 'When quotations expire'
    },
    {
      key: 'contractSigned',
      title: 'Contract Signed',
      description: 'When contracts are signed'
    },
    {
      key: 'customerCreated',
      title: 'New Customer',
      description: 'When new customers are created'
    },
    {
      key: 'leadConverted',
      title: 'Lead Converted',
      description: 'When leads are converted to customers'
    }
  ]

  const systemNotifications = [
    {
      key: 'reminders',
      title: 'Reminders',
      description: 'Task and deadline reminders'
    },
    {
      key: 'deadlines',
      title: 'Deadlines',
      description: 'Important deadline notifications'
    },
    {
      key: 'systemUpdates',
      title: 'System Updates',
      description: 'System maintenance and updates'
    },
    {
      key: 'marketingEmails',
      title: 'Marketing Emails',
      description: 'Product updates and marketing content'
    }
  ]

  const digestOptions = [
    {
      key: 'weeklyDigest',
      title: 'Weekly Digest',
      description: 'Weekly summary of activities'
    },
    {
      key: 'monthlyReport',
      title: 'Monthly Report',
      description: 'Monthly business performance report'
    },
    {
      key: 'instantAlerts',
      title: 'Instant Alerts',
      description: 'Immediate notifications for urgent items'
    },
    {
      key: 'dailySummary',
      title: 'Daily Summary',
      description: 'Daily summary of activities'
    }
  ]

  const timezones = [
    'UTC',
    'America/New_York',
    'America/Chicago',
    'America/Denver',
    'America/Los_Angeles',
    'Europe/London',
    'Europe/Paris',
    'Europe/Berlin',
    'Asia/Tokyo',
    'Asia/Shanghai',
    'Australia/Sydney'
  ]

  return (
    <div className="space-y-6">
      {hasChanges && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            You have unsaved changes. Don't forget to save your preferences.
          </AlertDescription>
        </Alert>
      )}

      {/* Notification Channels */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Bell className="h-5 w-5" />
            <span>Notification Channels</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {notificationChannels.map((channel) => {
            const ChannelIcon = channel.icon
            
            return (
              <div key={channel.key} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-3">
                  <ChannelIcon className={`h-5 w-5 ${channel.color}`} />
                  <div>
                    <div className="font-medium text-gray-900">{channel.title}</div>
                    <div className="text-sm text-gray-600">{channel.description}</div>
                  </div>
                </div>
                <Switch
                  checked={preferences[channel.key]}
                  onCheckedChange={(checked) => updatePreference(channel.key, checked)}
                />
              </div>
            )
          })}
        </CardContent>
      </Card>

      {/* Business Notifications */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>Business Notifications</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {businessNotifications.map((notification) => (
              <div key={notification.key} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <div className="font-medium text-gray-900">{notification.title}</div>
                  <div className="text-sm text-gray-600">{notification.description}</div>
                </div>
                <Switch
                  checked={preferences[notification.key]}
                  onCheckedChange={(checked) => updatePreference(notification.key, checked)}
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* System Notifications */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Volume2 className="h-5 w-5" />
            <span>System Notifications</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {systemNotifications.map((notification) => (
              <div key={notification.key} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <div className="font-medium text-gray-900">{notification.title}</div>
                  <div className="text-sm text-gray-600">{notification.description}</div>
                </div>
                <Switch
                  checked={preferences[notification.key]}
                  onCheckedChange={(checked) => updatePreference(notification.key, checked)}
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Digest & Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Digest & Summary</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {digestOptions.map((option) => (
              <div key={option.key} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <div className="font-medium text-gray-900">{option.title}</div>
                  <div className="text-sm text-gray-600">{option.description}</div>
                </div>
                <Switch
                  checked={preferences[option.key]}
                  onCheckedChange={(checked) => updatePreference(option.key, checked)}
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quiet Hours */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <VolumeX className="h-5 w-5" />
            <span>Quiet Hours</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div>
              <div className="font-medium text-gray-900">Enable Quiet Hours</div>
              <div className="text-sm text-gray-600">Disable notifications during specified hours</div>
            </div>
            <Switch
              checked={preferences.quietHoursEnabled}
              onCheckedChange={(checked) => updatePreference('quietHoursEnabled', checked)}
            />
          </div>

          {preferences.quietHoursEnabled && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="quietStart">Start Time</Label>
                <Input
                  id="quietStart"
                  type="time"
                  value={preferences.quietHoursStart}
                  onChange={(e) => updatePreference('quietHoursStart', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="quietEnd">End Time</Label>
                <Input
                  id="quietEnd"
                  type="time"
                  value={preferences.quietHoursEnd}
                  onChange={(e) => updatePreference('quietHoursEnd', e.target.value)}
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Timezone */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Globe className="h-5 w-5" />
            <span>Timezone</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div>
            <Label htmlFor="timezone">Timezone</Label>
            <select
              id="timezone"
              value={preferences.timezone}
              onChange={(e) => updatePreference('timezone', e.target.value)}
              className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {timezones.map((tz) => (
                <option key={tz} value={tz}>
                  {tz}
                </option>
              ))}
            </select>
            <p className="text-sm text-gray-600 mt-1">
              Used for scheduling notifications and quiet hours
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Save Button */}
      <div className="flex items-center justify-end space-x-4">
        <Button
          onClick={handleSave}
          disabled={isSaving || !hasChanges}
          className="min-w-[120px]"
        >
          {isSaving ? (
            <>
              <Clock className="h-4 w-4 mr-2 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </>
          )}
        </Button>
      </div>
    </div>
  )
}
