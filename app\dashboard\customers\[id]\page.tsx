import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { CustomerDetails } from '@/components/customers/customer-details'
import { CustomerActivity } from '@/components/customers/customer-activity'
import { CustomerRelatedRecords } from '@/components/customers/customer-related-records'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Edit, Trash2 } from 'lucide-react'
import Link from 'next/link'
import { notFound } from 'next/navigation'

export default async function CustomerDetailPage({
  params,
}: {
  params: { id: string }
}) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  const customer = await prisma.customer.findFirst({
    where: {
      id: params.id,
      companyId: session.user.companyId,
    },
    include: {
      leads: {
        orderBy: { createdAt: 'desc' },
        take: 5,
      },
      quotations: {
        orderBy: { createdAt: 'desc' },
        take: 5,
      },
      invoices: {
        orderBy: { createdAt: 'desc' },
        take: 5,
      },
      contracts: {
        orderBy: { createdAt: 'desc' },
        take: 5,
      },
      activities: {
        include: {
          createdBy: {
            select: { name: true, firstName: true, lastName: true }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 10,
      },
      _count: {
        select: {
          leads: true,
          quotations: true,
          invoices: true,
          contracts: true,
        }
      }
    }
  })

  if (!customer) {
    notFound()
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/customers">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Customers
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{customer.name}</h1>
            <p className="text-gray-600 mt-1">
              Customer details and activity history
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Link href={`/dashboard/customers/${customer.id}/edit`}>
            <Button variant="outline">
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
          </Link>
          <Button variant="outline" className="text-red-600 hover:text-red-700">
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Customer Details */}
        <div className="lg:col-span-2 space-y-6">
          <CustomerDetails customer={customer} />
          <CustomerRelatedRecords customer={customer} />
        </div>

        {/* Right Column - Activity */}
        <div>
          <CustomerActivity activities={customer.activities} />
        </div>
      </div>
    </div>
  )
}
