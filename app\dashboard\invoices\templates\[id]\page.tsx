import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { notFound } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Receipt,
  ArrowLeft,
  Edit,
  Copy,
  Trash2,
  Eye,
  Calendar,
  User,
  Tag,
  Download
} from 'lucide-react'

interface PageProps {
  params: {
    id: string
  }
}

export default async function InvoiceTemplateViewPage({ params }: PageProps) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  const template = await prisma.invoiceTemplate.findFirst({
    where: {
      id: params.id,
      companyId: session.user.companyId
    },
    include: {
      createdBy: {
        select: { name: true, firstName: true, lastName: true }
      },
      _count: {
        select: {
          invoices: true
        }
      }
    }
  })

  if (!template) {
    notFound()
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/invoices/templates">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Templates
            </Button>
          </Link>
          <div className="p-2 bg-green-100 rounded-lg">
            <Receipt className="h-6 w-6 text-green-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{template.name}</h1>
            <p className="text-gray-600 mt-1">{template.description}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Link href={`/dashboard/invoices/templates/${template.id}/edit`}>
            <Button>
              <Edit className="h-4 w-4 mr-2" />
              Edit Template
            </Button>
          </Link>
          <Button variant="outline">
            <Copy className="h-4 w-4 mr-2" />
            Duplicate
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export PDF
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Template Preview */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Template Preview</CardTitle>
                <Button variant="outline" size="sm">
                  <Eye className="h-4 w-4 mr-2" />
                  Full Screen Preview
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="border border-gray-200 rounded-lg p-4 bg-white max-h-96 overflow-y-auto">
                <div dangerouslySetInnerHTML={{ __html: template.content }} />
              </div>
            </CardContent>
          </Card>

          {/* Template Source */}
          <Card>
            <CardHeader>
              <CardTitle>Template Source Code</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-50 p-4 rounded-lg text-sm overflow-x-auto max-h-64 overflow-y-auto">
                <code>{template.content}</code>
              </pre>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Template Details */}
          <Card>
            <CardHeader>
              <CardTitle>Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-3">
                <Tag className="h-4 w-4 text-gray-400" />
                <div>
                  <p className="text-sm font-medium">Status</p>
                  <Badge variant={template.status === 'ACTIVE' ? 'default' : 'secondary'}>
                    {template.status}
                  </Badge>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Calendar className="h-4 w-4 text-gray-400" />
                <div>
                  <p className="text-sm font-medium">Created</p>
                  <p className="text-sm text-gray-600">
                    {new Date(template.createdAt).toLocaleDateString()}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <User className="h-4 w-4 text-gray-400" />
                <div>
                  <p className="text-sm font-medium">Created By</p>
                  <p className="text-sm text-gray-600">
                    {template.createdBy?.name || 'Unknown'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Usage Stats */}
          <Card>
            <CardHeader>
              <CardTitle>Usage Statistics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <p className="text-sm font-medium">Times Used</p>
                  <p className="text-2xl font-bold text-green-600">{template._count.invoices}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Link href={`/dashboard/invoices/templates/${template.id}/edit`}>
                <Button className="w-full" variant="outline">
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Template
                </Button>
              </Link>
              <Button className="w-full" variant="outline">
                <Copy className="h-4 w-4 mr-2" />
                Duplicate Template
              </Button>
              <Button className="w-full" variant="outline">
                <Receipt className="h-4 w-4 mr-2" />
                Create Invoice
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
