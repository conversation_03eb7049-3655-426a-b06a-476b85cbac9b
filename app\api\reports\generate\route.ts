import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { type, format = 'pdf' } = await request.json()

    // Generate report based on type
    const reportData = await generateQuickReport(session.user.companyId, type)
    
    if (format === 'pdf') {
      // Generate PDF report
      const pdfBuffer = await generatePDFReport(reportData, type)
      
      return new NextResponse(pdfBuffer, {
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': `attachment; filename="${type}_report.pdf"`,
        },
      })
    } else if (format === 'excel') {
      // Generate Excel report
      const excelBuffer = await generateExcelReport(reportData, type)
      
      return new NextResponse(excelBuffer, {
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'Content-Disposition': `attachment; filename="${type}_report.xlsx"`,
        },
      })
    }

    return NextResponse.json(reportData)
  } catch (error) {
    console.error('Error generating report:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function generateQuickReport(companyId: string, reportType: string) {
  const now = new Date()
  const last30Days = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
  const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
  const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0)

  switch (reportType) {
    case 'sales-summary':
      return await generateSalesSummary(companyId, last30Days, now)
    
    case 'customer-analysis':
      return await generateCustomerAnalysis(companyId, last30Days, now)
    
    case 'financial-overview':
      return await generateFinancialOverview(companyId, last30Days, now)
    
    case 'quotation-performance':
      return await generateQuotationPerformance(companyId, last30Days, now)
    
    case 'invoice-aging':
      return await generateInvoiceAging(companyId)
    
    case 'contract-status':
      return await generateContractStatus(companyId, last30Days, now)
    
    case 'monthly-trends':
      return await generateMonthlyTrends(companyId)
    
    case 'item-performance':
      return await generateItemPerformance(companyId, last30Days, now)
    
    case 'overdue-items':
      return await generateOverdueItems(companyId)
    
    default:
      throw new Error('Unknown report type')
  }
}

async function generateSalesSummary(companyId: string, startDate: Date, endDate: Date) {
  const [
    totalQuotations,
    totalInvoices,
    totalContracts,
    totalRevenue,
    quotationsByStatus,
    invoicesByStatus,
    contractsByStatus
  ] = await Promise.all([
    prisma.quotation.count({
      where: { companyId, createdAt: { gte: startDate, lte: endDate } }
    }),
    prisma.invoice.count({
      where: { companyId, createdAt: { gte: startDate, lte: endDate } }
    }),
    prisma.contract.count({
      where: { companyId, createdAt: { gte: startDate, lte: endDate } }
    }),
    prisma.invoice.aggregate({
      where: { 
        companyId, 
        status: 'PAID',
        paidAt: { gte: startDate, lte: endDate }
      },
      _sum: { total: true }
    }),
    prisma.quotation.groupBy({
      by: ['status'],
      where: { companyId, createdAt: { gte: startDate, lte: endDate } },
      _count: true,
      _sum: { total: true }
    }),
    prisma.invoice.groupBy({
      by: ['status'],
      where: { companyId, createdAt: { gte: startDate, lte: endDate } },
      _count: true,
      _sum: { total: true }
    }),
    prisma.contract.groupBy({
      by: ['status'],
      where: { companyId, createdAt: { gte: startDate, lte: endDate } },
      _count: true,
      _sum: { value: true }
    })
  ])

  return {
    title: 'Sales Summary Report',
    period: `${startDate.toDateString()} - ${endDate.toDateString()}`,
    summary: {
      totalQuotations,
      totalInvoices,
      totalContracts,
      totalRevenue: totalRevenue._sum.total || 0
    },
    breakdown: {
      quotations: quotationsByStatus,
      invoices: invoicesByStatus,
      contracts: contractsByStatus
    }
  }
}

async function generateCustomerAnalysis(companyId: string, startDate: Date, endDate: Date) {
  const [
    totalCustomers,
    newCustomers,
    customersByStatus,
    topCustomers
  ] = await Promise.all([
    prisma.customer.count({ where: { companyId } }),
    prisma.customer.count({
      where: { companyId, createdAt: { gte: startDate, lte: endDate } }
    }),
    prisma.customer.groupBy({
      by: ['status'],
      where: { companyId },
      _count: true
    }),
    prisma.customer.findMany({
      where: { companyId },
      include: {
        invoices: {
          where: { status: 'PAID' },
          select: { total: true }
        }
      },
      take: 10
    })
  ])

  const processedTopCustomers = topCustomers
    .map(customer => ({
      ...customer,
      totalRevenue: customer.invoices.reduce((sum, inv) => sum + inv.total, 0)
    }))
    .sort((a, b) => b.totalRevenue - a.totalRevenue)
    .slice(0, 5)

  return {
    title: 'Customer Analysis Report',
    period: `${startDate.toDateString()} - ${endDate.toDateString()}`,
    summary: {
      totalCustomers,
      newCustomers,
      growthRate: totalCustomers > 0 ? ((newCustomers / totalCustomers) * 100).toFixed(1) : '0'
    },
    breakdown: {
      byStatus: customersByStatus
    },
    topCustomers: processedTopCustomers
  }
}

async function generateFinancialOverview(companyId: string, startDate: Date, endDate: Date) {
  const [
    totalRevenue,
    totalOutstanding,
    paidInvoices,
    pendingInvoices
  ] = await Promise.all([
    prisma.invoice.aggregate({
      where: { 
        companyId, 
        status: 'PAID',
        paidAt: { gte: startDate, lte: endDate }
      },
      _sum: { total: true }
    }),
    prisma.invoice.aggregate({
      where: { 
        companyId, 
        status: { in: ['SENT', 'VIEWED', 'OVERDUE'] }
      },
      _sum: { total: true },
      _count: true
    }),
    prisma.invoice.count({
      where: { 
        companyId, 
        status: 'PAID',
        paidAt: { gte: startDate, lte: endDate }
      }
    }),
    prisma.invoice.count({
      where: { 
        companyId, 
        status: { in: ['SENT', 'VIEWED', 'OVERDUE'] }
      }
    })
  ])

  return {
    title: 'Financial Overview Report',
    period: `${startDate.toDateString()} - ${endDate.toDateString()}`,
    summary: {
      totalRevenue: totalRevenue._sum.total || 0,
      totalOutstanding: totalOutstanding._sum.total || 0,
      paidInvoices,
      pendingInvoices,
      collectionRate: (paidInvoices + pendingInvoices) > 0 
        ? ((paidInvoices / (paidInvoices + pendingInvoices)) * 100).toFixed(1)
        : '0'
    }
  }
}

async function generateQuotationPerformance(companyId: string, startDate: Date, endDate: Date) {
  const [
    quotationStats,
    conversionData
  ] = await Promise.all([
    prisma.quotation.groupBy({
      by: ['status'],
      where: { companyId, createdAt: { gte: startDate, lte: endDate } },
      _count: true,
      _sum: { total: true }
    }),
    prisma.quotation.findMany({
      where: { 
        companyId, 
        createdAt: { gte: startDate, lte: endDate },
        status: 'ACCEPTED'
      },
      include: {
        contracts: true
      }
    })
  ])

  const totalQuotations = quotationStats.reduce((sum, q) => sum + q._count, 0)
  const acceptedQuotations = quotationStats.find(q => q.status === 'ACCEPTED')?._count || 0
  const conversionRate = totalQuotations > 0 ? ((acceptedQuotations / totalQuotations) * 100).toFixed(1) : '0'

  return {
    title: 'Quotation Performance Report',
    period: `${startDate.toDateString()} - ${endDate.toDateString()}`,
    summary: {
      totalQuotations,
      acceptedQuotations,
      conversionRate: parseFloat(conversionRate),
      contractsGenerated: conversionData.reduce((sum, q) => sum + q.contracts.length, 0)
    },
    breakdown: quotationStats
  }
}

async function generateInvoiceAging(companyId: string) {
  const now = new Date()
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
  const sixtyDaysAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000)
  const ninetyDaysAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)

  const [
    current,
    thirtyDays,
    sixtyDays,
    ninetyDays,
    overNinety
  ] = await Promise.all([
    prisma.invoice.aggregate({
      where: { 
        companyId, 
        status: { in: ['SENT', 'VIEWED', 'OVERDUE'] },
        createdAt: { gte: thirtyDaysAgo }
      },
      _sum: { total: true },
      _count: true
    }),
    prisma.invoice.aggregate({
      where: { 
        companyId, 
        status: { in: ['SENT', 'VIEWED', 'OVERDUE'] },
        createdAt: { gte: sixtyDaysAgo, lt: thirtyDaysAgo }
      },
      _sum: { total: true },
      _count: true
    }),
    prisma.invoice.aggregate({
      where: { 
        companyId, 
        status: { in: ['SENT', 'VIEWED', 'OVERDUE'] },
        createdAt: { gte: ninetyDaysAgo, lt: sixtyDaysAgo }
      },
      _sum: { total: true },
      _count: true
    }),
    prisma.invoice.aggregate({
      where: { 
        companyId, 
        status: { in: ['SENT', 'VIEWED', 'OVERDUE'] },
        createdAt: { gte: new Date(now.getTime() - 120 * 24 * 60 * 60 * 1000), lt: ninetyDaysAgo }
      },
      _sum: { total: true },
      _count: true
    }),
    prisma.invoice.aggregate({
      where: { 
        companyId, 
        status: { in: ['SENT', 'VIEWED', 'OVERDUE'] },
        createdAt: { lt: new Date(now.getTime() - 120 * 24 * 60 * 60 * 1000) }
      },
      _sum: { total: true },
      _count: true
    })
  ])

  return {
    title: 'Invoice Aging Report',
    generatedAt: now.toDateString(),
    aging: {
      current: { amount: current._sum.total || 0, count: current._count },
      thirtyDays: { amount: thirtyDays._sum.total || 0, count: thirtyDays._count },
      sixtyDays: { amount: sixtyDays._sum.total || 0, count: sixtyDays._count },
      ninetyDays: { amount: ninetyDays._sum.total || 0, count: ninetyDays._count },
      overNinety: { amount: overNinety._sum.total || 0, count: overNinety._count }
    }
  }
}

async function generateContractStatus(companyId: string, startDate: Date, endDate: Date) {
  const contractStats = await prisma.contract.groupBy({
    by: ['status'],
    where: { companyId, createdAt: { gte: startDate, lte: endDate } },
    _count: true,
    _sum: { value: true }
  })

  const totalContracts = contractStats.reduce((sum, c) => sum + c._count, 0)
  const totalValue = contractStats.reduce((sum, c) => sum + (c._sum.value || 0), 0)

  return {
    title: 'Contract Status Report',
    period: `${startDate.toDateString()} - ${endDate.toDateString()}`,
    summary: {
      totalContracts,
      totalValue
    },
    breakdown: contractStats
  }
}

async function generateMonthlyTrends(companyId: string) {
  // This would typically involve more complex queries for monthly trends
  // For now, return a simplified version
  return {
    title: 'Monthly Trends Report',
    message: 'Monthly trends analysis - detailed implementation would require more complex date aggregations'
  }
}

async function generateItemPerformance(companyId: string, startDate: Date, endDate: Date) {
  const items = await prisma.item.findMany({
    where: { companyId },
    include: {
      quotationItems: {
        where: {
          quotation: {
            createdAt: { gte: startDate, lte: endDate }
          }
        }
      },
      invoiceItems: {
        where: {
          invoice: {
            createdAt: { gte: startDate, lte: endDate }
          }
        }
      }
    }
  })

  const processedItems = items
    .map(item => ({
      ...item,
      totalUsage: item.quotationItems.length + item.invoiceItems.length,
      quotationUsage: item.quotationItems.length,
      invoiceUsage: item.invoiceItems.length
    }))
    .sort((a, b) => b.totalUsage - a.totalUsage)
    .slice(0, 10)

  return {
    title: 'Item Performance Report',
    period: `${startDate.toDateString()} - ${endDate.toDateString()}`,
    topItems: processedItems
  }
}

async function generateOverdueItems(companyId: string) {
  const now = new Date()
  const overdueInvoices = await prisma.invoice.findMany({
    where: {
      companyId,
      status: 'OVERDUE',
      dueDate: { lt: now }
    },
    include: {
      customer: {
        select: { name: true, email: true }
      }
    },
    orderBy: { dueDate: 'asc' }
  })

  return {
    title: 'Overdue Items Report',
    generatedAt: now.toDateString(),
    overdueInvoices,
    summary: {
      totalOverdue: overdueInvoices.length,
      totalAmount: overdueInvoices.reduce((sum, inv) => sum + inv.total, 0)
    }
  }
}

async function generatePDFReport(data: any, reportType: string): Promise<Buffer> {
  // This would integrate with a PDF generation library like puppeteer or jsPDF
  // For now, return a simple text-based PDF placeholder
  const content = `
    Report: ${data.title || reportType}
    Generated: ${new Date().toDateString()}
    
    ${JSON.stringify(data, null, 2)}
  `
  
  return Buffer.from(content, 'utf-8')
}

async function generateExcelReport(data: any, reportType: string): Promise<Buffer> {
  // This would integrate with an Excel generation library like exceljs
  // For now, return a simple CSV-like content
  const content = `Report,${data.title || reportType}\nGenerated,${new Date().toDateString()}\n\n${JSON.stringify(data, null, 2)}`
  
  return Buffer.from(content, 'utf-8')
}
