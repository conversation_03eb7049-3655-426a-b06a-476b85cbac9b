import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ApiOverview } from '@/components/api/api-overview'
import { ApiKeyManagement } from '@/components/api/api-key-management'
import { ApiUsageStats } from '@/components/api/api-usage-stats'
import { WebhookManagement } from '@/components/api/webhook-management'
import { Button } from '@/components/ui/button'
import { Code, Key, Settings, BookOpen } from 'lucide-react'
import Link from 'next/link'

export default async function ApiManagementPage() {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  // Check if company has API access
  const company = await prisma.company.findUnique({
    where: { id: session.user.companyId }
  })

  if (!company) {
    return <div>Error: Company not found</div>
  }

  // Check if API access is available for this plan
  const hasApiAccess = ['PROFESSIONAL', 'ENTERPRISE'].includes(company.plan)

  if (!hasApiAccess) {
    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Code className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">API Management</h1>
            <p className="text-gray-600 mt-1">
              Manage your API keys, webhooks, and integrations
            </p>
          </div>
        </div>

        {/* Upgrade Notice */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-8 text-center">
          <Code className="h-16 w-16 text-blue-600 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">API Access Required</h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            API access is available with Professional and Enterprise plans. Upgrade your plan to access our powerful API, 
            manage webhooks, and integrate with your existing systems.
          </p>
          <div className="flex items-center justify-center space-x-4">
            <Link href="/dashboard/billing">
              <Button size="lg">
                Upgrade Plan
              </Button>
            </Link>
            <Link href="/dashboard/api/docs">
              <Button variant="outline" size="lg">
                <BookOpen className="h-4 w-4 mr-2" />
                View Documentation
              </Button>
            </Link>
          </div>
        </div>
      </div>
    )
  }

  // Fetch API keys and usage data
  const [apiKeys, webhooks, usageStats] = await Promise.all([
    prisma.apiKey.findMany({
      where: { companyId: session.user.companyId },
      select: {
        id: true,
        name: true,
        description: true,
        keyPrefix: true,
        status: true,
        permissions: true,
        lastUsedAt: true,
        expiresAt: true,
        createdAt: true
      },
      orderBy: { createdAt: 'desc' }
    }),
    prisma.webhook.findMany({
      where: { companyId: session.user.companyId },
      select: {
        id: true,
        name: true,
        url: true,
        events: true,
        status: true,
        secret: true,
        lastTriggeredAt: true,
        createdAt: true
      },
      orderBy: { createdAt: 'desc' }
    }),
    // Get API usage statistics
    Promise.all([
      prisma.apiRequest.count({
        where: { 
          companyId: session.user.companyId,
          createdAt: { gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
        }
      }),
      prisma.apiRequest.count({
        where: { 
          companyId: session.user.companyId,
          createdAt: { gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
        }
      }),
      prisma.apiRequest.groupBy({
        by: ['endpoint'],
        where: { 
          companyId: session.user.companyId,
          createdAt: { gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
        },
        _count: true,
        orderBy: { _count: { endpoint: 'desc' } },
        take: 10
      }),
      prisma.apiRequest.groupBy({
        by: ['statusCode'],
        where: { 
          companyId: session.user.companyId,
          createdAt: { gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
        },
        _count: true
      })
    ])
  ])

  const [monthlyRequests, dailyRequests, topEndpoints, statusCodes] = usageStats

  const apiUsageData = {
    monthlyRequests,
    dailyRequests,
    topEndpoints,
    statusCodes,
    totalKeys: apiKeys.length,
    activeKeys: apiKeys.filter(key => key.status === 'ACTIVE').length,
    totalWebhooks: webhooks.length,
    activeWebhooks: webhooks.filter(webhook => webhook.status === 'ACTIVE').length
  }

  // Get rate limits based on plan
  const rateLimits = {
    PROFESSIONAL: {
      requestsPerMinute: 100,
      requestsPerDay: 10000,
      requestsPerMonth: 100000
    },
    ENTERPRISE: {
      requestsPerMinute: 1000,
      requestsPerDay: 100000,
      requestsPerMonth: 1000000
    }
  }

  const currentLimits = rateLimits[company.plan as keyof typeof rateLimits] || rateLimits.PROFESSIONAL

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Code className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">API Management</h1>
            <p className="text-gray-600 mt-1">
              Manage your API keys, webhooks, and integrations
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Link href="/dashboard/api/docs">
            <Button variant="outline">
              <BookOpen className="h-4 w-4 mr-2" />
              Documentation
            </Button>
          </Link>
          <Link href="/dashboard/api/settings">
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
          </Link>
        </div>
      </div>

      {/* API Overview */}
      <ApiOverview 
        company={company}
        usage={apiUsageData}
        limits={currentLimits}
      />

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* API Keys and Webhooks */}
        <div className="lg:col-span-2 space-y-6">
          <ApiKeyManagement
            apiKeys={apiKeys.map(key => ({
              ...key,
              keyPrefix: key.keyPrefix || '',
              permissions: key.permissions as string[]
            }))}
            companyId={session.user.companyId}
            plan={company.plan}
          />

          <WebhookManagement
            webhooks={webhooks.map(webhook => ({
              ...webhook,
              events: webhook.events as string[]
            }))}
            companyId={session.user.companyId}
          />
        </div>

        {/* Usage Statistics */}
        <div>
          <ApiUsageStats 
            usage={apiUsageData}
            limits={currentLimits}
          />
        </div>
      </div>
    </div>
  )
}
