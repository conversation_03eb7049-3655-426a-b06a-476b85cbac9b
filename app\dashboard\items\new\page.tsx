import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ItemForm } from '@/components/items/item-form'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'

export default async function NewItemPage() {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  // Fetch existing categories for dropdown
  const categories = await prisma.item.findMany({
    where: { companyId: session.user.companyId },
    select: { category: true },
    distinct: ['category'],
    orderBy: { category: 'asc' },
  }).then(items => items.map(item => item.category).filter(Boolean))

  // Generate next SKU
  const lastItem = await prisma.item.findFirst({
    where: { 
      companyId: session.user.companyId,
      sku: { not: null }
    },
    orderBy: { createdAt: 'desc' },
    select: { sku: true }
  })

  const nextSku = generateSku(lastItem?.sku)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href="/dashboard/items">
          <Button variant="ghost" >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Items
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Add New Item</h1>
          <p className="text-gray-600 mt-1">
            Add a new product or service to your catalog
          </p>
        </div>
      </div>

      {/* Item Form */}
      <ItemForm 
        mode="create" 
        categories={categories}
        suggestedSku={nextSku}
      />
    </div>
  )
}

function generateSku(lastSku?: string | null): string {
  const currentYear = new Date().getFullYear().toString().slice(-2)
  const currentMonth = String(new Date().getMonth() + 1).padStart(2, '0')
  
  let nextNumber = 1
  
  if (lastSku) {
    const match = lastSku.match(/SKU-(\d{2})(\d{2})-(\d{4})/)
    if (match) {
      const [, year, month, num] = match
      if (year === currentYear && month === currentMonth) {
        nextNumber = parseInt(num) + 1
      }
    }
  }
  
  return `SKU-${currentYear}${currentMonth}-${String(nextNumber).padStart(4, '0')}`
}
