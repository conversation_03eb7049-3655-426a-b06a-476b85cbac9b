'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  CheckCircle, 
  ArrowRight,
  ArrowLeft,
  Rocket,
  Building2,
  Users,
  FileText,
  Sparkles,
  Info
} from 'lucide-react'
import toast from 'react-hot-toast'

interface OnboardingFlowProps {
  user: any
  company: any
  currentStep: string
  completedSteps: string[]
  onboardingProgress: any
}

export function OnboardingFlow({ 
  user, 
  company, 
  currentStep, 
  completedSteps, 
  onboardingProgress 
}: OnboardingFlowProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [activeStep, setActiveStep] = useState(currentStep)
  const [formData, setFormData] = useState({
    // Company setup
    companyName: company.name || '',
    companyEmail: company.email || '',
    companyPhone: company.phone || '',
    companyAddress: company.address || '',
    industry: company.industry || '',
    
    // User profile
    firstName: user.firstName || '',
    lastName: user.lastName || '',
    jobTitle: user.jobTitle || '',
    
    // Customer data
    customerName: '',
    customerEmail: '',
    customerCompany: '',
    
    // Quotation data
    quotationTitle: '',
    quotationDescription: '',
    quotationAmount: ''
  })

  const steps = [
    {
      id: 'welcome',
      title: 'Welcome to BusinessSaaS',
      icon: Rocket,
      component: 'WelcomeStep'
    },
    {
      id: 'company-setup',
      title: 'Company Setup',
      icon: Building2,
      component: 'CompanySetupStep'
    },
    {
      id: 'first-customer',
      title: 'Add Your First Customer',
      icon: Users,
      component: 'FirstCustomerStep'
    },
    {
      id: 'first-quotation',
      title: 'Create a Quotation',
      icon: FileText,
      component: 'FirstQuotationStep'
    },
    {
      id: 'explore-features',
      title: 'Explore Features',
      icon: Sparkles,
      component: 'ExploreFeaturesStep'
    }
  ]

  const currentStepIndex = steps.findIndex(step => step.id === activeStep)
  const currentStepData = steps[currentStepIndex]

  const handleNext = async () => {
    setIsLoading(true)
    try {
      // Save current step progress
      await saveStepProgress(activeStep)
      
      // Move to next step
      if (currentStepIndex < steps.length - 1) {
        const nextStep = steps[currentStepIndex + 1].id
        setActiveStep(nextStep)
        await updateCurrentStep(nextStep)
      } else {
        // Complete onboarding
        await completeOnboarding()
      }
    } catch (error) {
      toast.error('Failed to save progress')
    } finally {
      setIsLoading(false)
    }
  }

  const handlePrevious = () => {
    if (currentStepIndex > 0) {
      const prevStep = steps[currentStepIndex - 1].id
      setActiveStep(prevStep)
      updateCurrentStep(prevStep)
    }
  }

  const saveStepProgress = async (stepId: string) => {
    const response = await fetch('/api/onboarding/progress', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        stepId,
        stepData: getStepData(stepId)
      })
    })

    if (!response.ok) {
      throw new Error('Failed to save progress')
    }
  }

  const updateCurrentStep = async (stepId: string) => {
    await fetch('/api/onboarding/current-step', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ currentStep: stepId })
    })
  }

  const completeOnboarding = async () => {
    const response = await fetch('/api/onboarding/complete', {
      method: 'POST'
    })

    if (response.ok) {
      toast.success('Onboarding completed! Welcome to BusinessSaaS!')
      window.location.href = '/dashboard'
    }
  }

  const getStepData = (stepId: string) => {
    switch (stepId) {
      case 'company-setup':
        return {
          companyName: formData.companyName,
          companyEmail: formData.companyEmail,
          companyPhone: formData.companyPhone,
          companyAddress: formData.companyAddress,
          industry: formData.industry,
          firstName: formData.firstName,
          lastName: formData.lastName,
          jobTitle: formData.jobTitle
        }
      case 'first-customer':
        return {
          customerName: formData.customerName,
          customerEmail: formData.customerEmail,
          customerCompany: formData.customerCompany
        }
      case 'first-quotation':
        return {
          quotationTitle: formData.quotationTitle,
          quotationDescription: formData.quotationDescription,
          quotationAmount: formData.quotationAmount
        }
      default:
        return {}
    }
  }

  const renderStepContent = () => {
    switch (activeStep) {
      case 'welcome':
        return <WelcomeStep />
      case 'company-setup':
        return <CompanySetupStep formData={formData} setFormData={setFormData} />
      case 'first-customer':
        return <FirstCustomerStep formData={formData} setFormData={setFormData} />
      case 'first-quotation':
        return <FirstQuotationStep formData={formData} setFormData={setFormData} />
      case 'explore-features':
        return <ExploreFeaturesStep />
      default:
        return <WelcomeStep />
    }
  }

  const canProceed = () => {
    switch (activeStep) {
      case 'welcome':
        return true
      case 'company-setup':
        return formData.companyName && formData.firstName && formData.lastName
      case 'first-customer':
        return formData.customerName && formData.customerEmail
      case 'first-quotation':
        return formData.quotationTitle && formData.quotationAmount
      case 'explore-features':
        return true
      default:
        return true
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <currentStepData.icon className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <CardTitle>{currentStepData.title}</CardTitle>
            <div className="text-sm text-gray-600">
              Step {currentStepIndex + 1} of {steps.length}
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Step Content */}
          {renderStepContent()}

          {/* Navigation */}
          <div className="flex items-center justify-between pt-6 border-t">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStepIndex === 0}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>

            <div className="text-sm text-gray-600">
              {currentStepIndex + 1} of {steps.length}
            </div>

            <Button
              onClick={handleNext}
              disabled={!canProceed() || isLoading}
            >
              {currentStepIndex === steps.length - 1 ? 'Complete' : 'Next'}
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Step Components
function WelcomeStep() {
  return (
    <div className="text-center py-8">
      <div className="mb-6">
        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Rocket className="h-8 w-8 text-blue-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Welcome to BusinessSaaS!
        </h2>
        <p className="text-gray-600 max-w-md mx-auto">
          We're excited to help you streamline your business operations. 
          Let's get you set up in just a few simple steps.
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
        <div className="p-4 bg-gray-50 rounded-lg">
          <Building2 className="h-6 w-6 text-blue-600 mx-auto mb-2" />
          <div className="text-sm font-medium text-gray-900">Setup Company</div>
          <div className="text-xs text-gray-600">Configure your business profile</div>
        </div>
        <div className="p-4 bg-gray-50 rounded-lg">
          <Users className="h-6 w-6 text-green-600 mx-auto mb-2" />
          <div className="text-sm font-medium text-gray-900">Add Customers</div>
          <div className="text-xs text-gray-600">Start managing relationships</div>
        </div>
        <div className="p-4 bg-gray-50 rounded-lg">
          <FileText className="h-6 w-6 text-purple-600 mx-auto mb-2" />
          <div className="text-sm font-medium text-gray-900">Create Documents</div>
          <div className="text-xs text-gray-600">Generate quotations & invoices</div>
        </div>
      </div>
    </div>
  )
}

function CompanySetupStep({ formData, setFormData }: any) {
  return (
    <div className="space-y-6">
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          Complete your company and personal profile to get the most out of BusinessSaaS.
        </AlertDescription>
      </Alert>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Company Information */}
        <div className="space-y-4">
          <h3 className="font-medium text-gray-900">Company Information</h3>
          
          <div>
            <Label htmlFor="companyName">Company Name *</Label>
            <Input
              id="companyName"
              value={formData.companyName}
              onChange={(e) => setFormData({ ...formData, companyName: e.target.value })}
              placeholder="Your Company Name"
            />
          </div>

          <div>
            <Label htmlFor="companyEmail">Company Email</Label>
            <Input
              id="companyEmail"
              type="email"
              value={formData.companyEmail}
              onChange={(e) => setFormData({ ...formData, companyEmail: e.target.value })}
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <Label htmlFor="companyPhone">Phone Number</Label>
            <Input
              id="companyPhone"
              value={formData.companyPhone}
              onChange={(e) => setFormData({ ...formData, companyPhone: e.target.value })}
              placeholder="+****************"
            />
          </div>

          <div>
            <Label htmlFor="industry">Industry</Label>
            <Select value={formData.industry} onValueChange={(value) => setFormData({ ...formData, industry: value })}>
              <SelectTrigger>
                <SelectValue placeholder="Select your industry" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="technology">Technology</SelectItem>
                <SelectItem value="consulting">Consulting</SelectItem>
                <SelectItem value="manufacturing">Manufacturing</SelectItem>
                <SelectItem value="retail">Retail</SelectItem>
                <SelectItem value="healthcare">Healthcare</SelectItem>
                <SelectItem value="finance">Finance</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Personal Information */}
        <div className="space-y-4">
          <h3 className="font-medium text-gray-900">Your Information</h3>
          
          <div>
            <Label htmlFor="firstName">First Name *</Label>
            <Input
              id="firstName"
              value={formData.firstName}
              onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}
              placeholder="John"
            />
          </div>

          <div>
            <Label htmlFor="lastName">Last Name *</Label>
            <Input
              id="lastName"
              value={formData.lastName}
              onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}
              placeholder="Doe"
            />
          </div>

          <div>
            <Label htmlFor="jobTitle">Job Title</Label>
            <Input
              id="jobTitle"
              value={formData.jobTitle}
              onChange={(e) => setFormData({ ...formData, jobTitle: e.target.value })}
              placeholder="CEO, Manager, etc."
            />
          </div>

          <div>
            <Label htmlFor="companyAddress">Company Address</Label>
            <Textarea
              id="companyAddress"
              value={formData.companyAddress}
              onChange={(e) => setFormData({ ...formData, companyAddress: e.target.value })}
              placeholder="123 Business St, City, State 12345"
              rows={3}
            />
          </div>
        </div>
      </div>
    </div>
  )
}

function FirstCustomerStep({ formData, setFormData }: any) {
  return (
    <div className="space-y-6">
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          Add your first customer to start managing business relationships and creating documents.
        </AlertDescription>
      </Alert>

      <div className="max-w-md mx-auto space-y-4">
        <div>
          <Label htmlFor="customerName">Customer Name *</Label>
          <Input
            id="customerName"
            value={formData.customerName}
            onChange={(e) => setFormData({ ...formData, customerName: e.target.value })}
            placeholder="Jane Smith"
          />
        </div>

        <div>
          <Label htmlFor="customerEmail">Customer Email *</Label>
          <Input
            id="customerEmail"
            type="email"
            value={formData.customerEmail}
            onChange={(e) => setFormData({ ...formData, customerEmail: e.target.value })}
            placeholder="<EMAIL>"
          />
        </div>

        <div>
          <Label htmlFor="customerCompany">Customer Company</Label>
          <Input
            id="customerCompany"
            value={formData.customerCompany}
            onChange={(e) => setFormData({ ...formData, customerCompany: e.target.value })}
            placeholder="Customer Company Inc."
          />
        </div>
      </div>
    </div>
  )
}

function FirstQuotationStep({ formData, setFormData }: any) {
  return (
    <div className="space-y-6">
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          Create your first quotation to see how easy it is to generate professional business documents.
        </AlertDescription>
      </Alert>

      <div className="max-w-md mx-auto space-y-4">
        <div>
          <Label htmlFor="quotationTitle">Quotation Title *</Label>
          <Input
            id="quotationTitle"
            value={formData.quotationTitle}
            onChange={(e) => setFormData({ ...formData, quotationTitle: e.target.value })}
            placeholder="Website Development Project"
          />
        </div>

        <div>
          <Label htmlFor="quotationDescription">Description</Label>
          <Textarea
            id="quotationDescription"
            value={formData.quotationDescription}
            onChange={(e) => setFormData({ ...formData, quotationDescription: e.target.value })}
            placeholder="Describe the services or products..."
            rows={3}
          />
        </div>

        <div>
          <Label htmlFor="quotationAmount">Amount *</Label>
          <Input
            id="quotationAmount"
            type="number"
            value={formData.quotationAmount}
            onChange={(e) => setFormData({ ...formData, quotationAmount: e.target.value })}
            placeholder="5000"
          />
        </div>
      </div>
    </div>
  )
}

function ExploreFeaturesStep() {
  const features = [
    {
      title: 'Customer Management',
      description: 'Organize and track all your customer relationships',
      icon: Users,
      link: '/dashboard/customers'
    },
    {
      title: 'Document Generation',
      description: 'Create professional quotations, invoices, and contracts',
      icon: FileText,
      link: '/dashboard/quotations'
    },
    {
      title: 'Business Analytics',
      description: 'Track performance with detailed reports and insights',
      icon: Sparkles,
      link: '/dashboard/analytics'
    }
  ]

  return (
    <div className="space-y-6">
      <div className="text-center">
        <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
        <h2 className="text-xl font-bold text-gray-900 mb-2">
          You're All Set!
        </h2>
        <p className="text-gray-600">
          Explore these key features to get the most out of BusinessSaaS.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {features.map((feature, index) => (
          <div key={index} className="p-4 border rounded-lg hover:bg-gray-50 transition-colors">
            <feature.icon className="h-8 w-8 text-blue-600 mb-3" />
            <h3 className="font-medium text-gray-900 mb-2">{feature.title}</h3>
            <p className="text-sm text-gray-600 mb-3">{feature.description}</p>
            <Button variant="outline" size="sm" className="w-full">
              Explore
            </Button>
          </div>
        ))}
      </div>
    </div>
  )
}
