'use client'

import Link from 'next/link'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { formatDate, formatCurrency, getStatusColor } from '@/lib/utils'
import { 
  TrendingUp,
  FileText,
  Receipt,

  ExternalLink,
  Plus
} from 'lucide-react'

interface CustomerRelatedRecordsProps {
  customer: {
    id: string
    leads: Array<{
      id: string
      title: string
      status: string
      value: number | null
      createdAt: Date
    }>
    quotations: Array<{
      id: string
      quotationNumber: string
      title: string
      status: string
      total: number
      createdAt: Date
    }>
    invoices: Array<{
      id: string
      invoiceNumber: string
      title: string
      status: string
      total: number
      createdAt: Date
    }>
    contracts: Array<{
      id: string
      contractNumber: string
      title: string
      status: string
      value: number | null
      createdAt: Date
    }>
  }
}

export function CustomerRelatedRecords({ customer }: CustomerRelatedRecordsProps) {
  return (
    <div className="space-y-6">
      {/* Leads */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center">
            <TrendingUp className="h-5 w-5 mr-2" />
            Leads ({customer.leads.length})
          </CardTitle>
          <Link href={`/dashboard/leads/new?customerId=${customer.id}`}>
            <Button >
              <Plus className="h-4 w-4 mr-2" />
              New Lead
            </Button>
          </Link>
        </CardHeader>
        <CardContent>
          {customer.leads.length === 0 ? (
            <div className="text-center py-8">
              <TrendingUp className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500">No leads yet</p>
              <p className="text-sm text-gray-400 mt-1">
                Create a lead to start tracking opportunities
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {customer.leads.map((lead) => (
                <div key={lead.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <Link 
                        href={`/dashboard/leads/${lead.id}`}
                        className="font-medium text-gray-900 hover:text-blue-600 flex items-center"
                      >
                        {lead.title}
                        <ExternalLink className="h-3 w-3 ml-1" />
                      </Link>
                      <Badge className={getStatusColor(lead.status)} variant="outline">
                        {lead.status.replace('_', ' ')}
                      </Badge>
                    </div>
                    <div className="flex items-center space-x-4 mt-1">
                      {lead.value && (
                        <span className="text-sm font-medium text-gray-900">
                          {formatCurrency(lead.value)}
                        </span>
                      )}
                      <span className="text-sm text-gray-500">
                        {formatDate(lead.createdAt)}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
              {customer.leads.length >= 5 && (
                <div className="text-center pt-2">
                  <Link 
                    href={`/dashboard/leads?customerId=${customer.id}`}
                    className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                  >
                    View all leads
                  </Link>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quotations */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center">
            <FileText className="h-5 w-5 mr-2" />
            Quotations ({customer.quotations.length})
          </CardTitle>
          <Link href={`/dashboard/quotations/new?customerId=${customer.id}`}>
            <Button >
              <Plus className="h-4 w-4 mr-2" />
              New Quotation
            </Button>
          </Link>
        </CardHeader>
        <CardContent>
          {customer.quotations.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500">No quotations yet</p>
              <p className="text-sm text-gray-400 mt-1">
                Create a quotation to send proposals
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {customer.quotations.map((quotation) => (
                <div key={quotation.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <Link 
                        href={`/dashboard/quotations/${quotation.id}`}
                        className="font-medium text-gray-900 hover:text-blue-600 flex items-center"
                      >
                        {quotation.quotationNumber}
                        <ExternalLink className="h-3 w-3 ml-1" />
                      </Link>
                      <Badge className={getStatusColor(quotation.status)} variant="outline">
                        {quotation.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{quotation.title}</p>
                    <div className="flex items-center space-x-4 mt-1">
                      <span className="text-sm font-medium text-gray-900">
                        {formatCurrency(quotation.total)}
                      </span>
                      <span className="text-sm text-gray-500">
                        {formatDate(quotation.createdAt)}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
              {customer.quotations.length >= 5 && (
                <div className="text-center pt-2">
                  <Link 
                    href={`/dashboard/quotations?customerId=${customer.id}`}
                    className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                  >
                    View all quotations
                  </Link>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Invoices */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center">
            <Receipt className="h-5 w-5 mr-2" />
            Invoices ({customer.invoices.length})
          </CardTitle>
          <Link href={`/dashboard/invoices/new?customerId=${customer.id}`}>
            <Button >
              <Plus className="h-4 w-4 mr-2" />
              New Invoice
            </Button>
          </Link>
        </CardHeader>
        <CardContent>
          {customer.invoices.length === 0 ? (
            <div className="text-center py-8">
              <Receipt className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500">No invoices yet</p>
              <p className="text-sm text-gray-400 mt-1">
                Create an invoice to bill this customer
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {customer.invoices.map((invoice) => (
                <div key={invoice.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <Link 
                        href={`/dashboard/invoices/${invoice.id}`}
                        className="font-medium text-gray-900 hover:text-blue-600 flex items-center"
                      >
                        {invoice.invoiceNumber}
                        <ExternalLink className="h-3 w-3 ml-1" />
                      </Link>
                      <Badge className={getStatusColor(invoice.status)} variant="outline">
                        {invoice.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{invoice.title}</p>
                    <div className="flex items-center space-x-4 mt-1">
                      <span className="text-sm font-medium text-gray-900">
                        {formatCurrency(invoice.total)}
                      </span>
                      <span className="text-sm text-gray-500">
                        {formatDate(invoice.createdAt)}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
              {customer.invoices.length >= 5 && (
                <div className="text-center pt-2">
                  <Link 
                    href={`/dashboard/invoices?customerId=${customer.id}`}
                    className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                  >
                    View all invoices
                  </Link>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Contracts */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center">
            <FileText className="h-5 w-5 mr-2" />
            Contracts ({customer.contracts.length})
          </CardTitle>
          <Link href={`/dashboard/contracts/new?customerId=${customer.id}`}>
            <Button >
              <Plus className="h-4 w-4 mr-2" />
              New Contract
            </Button>
          </Link>
        </CardHeader>
        <CardContent>
          {customer.contracts.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500">No contracts yet</p>
              <p className="text-sm text-gray-400 mt-1">
                Create a contract for ongoing services
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {customer.contracts.map((contract) => (
                <div key={contract.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <Link 
                        href={`/dashboard/contracts/${contract.id}`}
                        className="font-medium text-gray-900 hover:text-blue-600 flex items-center"
                      >
                        {contract.contractNumber}
                        <ExternalLink className="h-3 w-3 ml-1" />
                      </Link>
                      <Badge className={getStatusColor(contract.status)} variant="outline">
                        {contract.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{contract.title}</p>
                    <div className="flex items-center space-x-4 mt-1">
                      {contract.value && (
                        <span className="text-sm font-medium text-gray-900">
                          {formatCurrency(contract.value)}
                        </span>
                      )}
                      <span className="text-sm text-gray-500">
                        {formatDate(contract.createdAt)}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
              {customer.contracts.length >= 5 && (
                <div className="text-center pt-2">
                  <Link 
                    href={`/dashboard/contracts?customerId=${customer.id}`}
                    className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                  >
                    View all contracts
                  </Link>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
