'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { toast } from 'react-hot-toast'
import {
  Settings,
  Globe,
  Shield,
  Zap,
  Database,
  Key,
  Save,
  RefreshCw,
  Loader2
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

const settingsSchema = z.object({
  general: z.object({
    platformName: z.string().min(1, 'Platform name is required'),
    platformDescription: z.string().min(1, 'Platform description is required'),
    supportEmail: z.string().email('Invalid email address'),
    timezone: z.string(),
    language: z.string(),
    maintenanceMode: z.boolean()
  }),
  security: z.object({
    requireTwoFactor: z.boolean(),
    passwordMinLength: z.number().min(6).max(32),
    sessionTimeout: z.number().min(1).max(168),
    maxLoginAttempts: z.number().min(3).max(10),
    enableAuditLogs: z.boolean(),
    requireEmailVerification: z.boolean()
  }),
  features: z.object({
    enableRegistration: z.boolean(),
    enableInvitations: z.boolean(),
    enableApiAccess: z.boolean(),
    enableWebhooks: z.boolean(),
    enableEmailCampaigns: z.boolean(),
    enableAdvancedReporting: z.boolean()
  }),
  limits: z.object({
    maxUsersPerCompany: z.number().min(1),
    maxStoragePerCompany: z.number().min(1),
    maxApiCallsPerHour: z.number().min(100),
    maxEmailsPerMonth: z.number().min(1000)
  })
})

type SettingsFormData = z.infer<typeof settingsSchema>

interface SettingsFormProps {
  initialSettings?: SettingsFormData
}

export function SettingsForm({ initialSettings }: SettingsFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [isResetting, setIsResetting] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue
  } = useForm<SettingsFormData>({
    resolver: zodResolver(settingsSchema),
    defaultValues: initialSettings || {
      general: {
        platformName: 'SaaS Platform',
        platformDescription: 'Enterprise Business Management Platform',
        supportEmail: '<EMAIL>',
        timezone: 'UTC',
        language: 'en',
        maintenanceMode: false
      },
      security: {
        requireTwoFactor: false,
        passwordMinLength: 8,
        sessionTimeout: 24,
        maxLoginAttempts: 5,
        enableAuditLogs: true,
        requireEmailVerification: true
      },
      features: {
        enableRegistration: true,
        enableInvitations: true,
        enableApiAccess: true,
        enableWebhooks: true,
        enableEmailCampaigns: true,
        enableAdvancedReporting: true
      },
      limits: {
        maxUsersPerCompany: 100,
        maxStoragePerCompany: 10,
        maxApiCallsPerHour: 1000,
        maxEmailsPerMonth: 10000
      }
    }
  })

  const onSubmit = async (data: SettingsFormData) => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/super-admin/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update settings')
      }

      toast.success('Settings updated successfully!')
    } catch (error) {
      console.error('Error updating settings:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to update settings'
      toast.error(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const handleReset = async () => {
    setIsResetting(true)
    try {
      const response = await fetch('/api/super-admin/settings', {
        method: 'DELETE',
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to reset settings')
      }

      // Reset form to defaults
      reset()
      toast.success('Settings reset to defaults successfully!')
    } catch (error) {
      console.error('Error resetting settings:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to reset settings'
      toast.error(errorMessage)
    } finally {
      setIsResetting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="p-3 bg-gray-100 rounded-lg">
            <Settings className="h-8 w-8 text-gray-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Platform Settings</h1>
            <p className="text-gray-600">Configure platform-wide settings and preferences</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Button 
            type="button" 
            variant="outline" 
            onClick={handleReset}
            disabled={isLoading || isResetting}
          >
            {isResetting ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Reset to Defaults
          </Button>
          <Button type="submit" disabled={isLoading || isResetting}>
            {isLoading ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Save Changes
          </Button>
        </div>
      </div>

      {/* Settings Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* General Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Globe className="h-5 w-5 text-blue-600" />
              <span>General Settings</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="platformName">Platform Name</Label>
              <Input
                id="platformName"
                {...register('general.platformName')}
                placeholder="Enter platform name"
              />
              {errors.general?.platformName && (
                <p className="text-sm text-red-600">{errors.general.platformName.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="platformDescription">Platform Description</Label>
              <Textarea
                id="platformDescription"
                {...register('general.platformDescription')}
                placeholder="Enter platform description"
                rows={3}
              />
              {errors.general?.platformDescription && (
                <p className="text-sm text-red-600">{errors.general.platformDescription.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="supportEmail">Support Email</Label>
              <Input
                id="supportEmail"
                type="email"
                {...register('general.supportEmail')}
                placeholder="<EMAIL>"
              />
              {errors.general?.supportEmail && (
                <p className="text-sm text-red-600">{errors.general.supportEmail.message}</p>
              )}
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Maintenance Mode</Label>
                <p className="text-sm text-gray-500">Temporarily disable platform access</p>
              </div>
              <Switch 
                checked={watch('general.maintenanceMode')}
                onCheckedChange={(checked) => setValue('general.maintenanceMode', checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Security Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-red-600" />
              <span>Security & Authentication</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Require Two-Factor Authentication</Label>
                <p className="text-sm text-gray-500">Force 2FA for all users</p>
              </div>
              <Switch 
                checked={watch('security.requireTwoFactor')}
                onCheckedChange={(checked) => setValue('security.requireTwoFactor', checked)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="passwordMinLength">Minimum Password Length</Label>
              <Input
                id="passwordMinLength"
                type="number"
                {...register('security.passwordMinLength', { valueAsNumber: true })}
                min="6"
                max="32"
              />
              {errors.security?.passwordMinLength && (
                <p className="text-sm text-red-600">{errors.security.passwordMinLength.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="sessionTimeout">Session Timeout (hours)</Label>
              <Input
                id="sessionTimeout"
                type="number"
                {...register('security.sessionTimeout', { valueAsNumber: true })}
                min="1"
                max="168"
              />
              {errors.security?.sessionTimeout && (
                <p className="text-sm text-red-600">{errors.security.sessionTimeout.message}</p>
              )}
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Enable Audit Logs</Label>
                <p className="text-sm text-gray-500">Track all user activities</p>
              </div>
              <Switch 
                checked={watch('security.enableAuditLogs')}
                onCheckedChange={(checked) => setValue('security.enableAuditLogs', checked)}
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Save Actions */}
      <div className="flex items-center justify-end space-x-4 p-6 bg-gray-50 rounded-lg">
        <p className="text-sm text-gray-600">Changes will be applied immediately to all users</p>
        <Button type="button" variant="outline" onClick={() => reset()}>
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading || isResetting}>
          {isLoading ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <Save className="h-4 w-4 mr-2" />
          )}
          Save All Settings
        </Button>
      </div>
    </form>
  )
}
