'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { toast } from 'react-hot-toast'
import {
  Settings,
  Globe,
  Shield,
  Zap,
  Database,
  Key,
  Save,
  RefreshCw,
  Loader2,
  Mail,
  Bell,
  Users,
  Server,
  Lock
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs'

const settingsSchema = z.object({
  general: z.object({
    platformName: z.string().min(1, 'Platform name is required'),
    platformDescription: z.string().min(1, 'Platform description is required'),
    supportEmail: z.string().email('Invalid email address'),
    companyEmail: z.string().email('Invalid email address'),
    timezone: z.string(),
    language: z.string(),
    maintenanceMode: z.boolean(),
    allowRegistration: z.boolean(),
    defaultUserRole: z.string()
  }),
  security: z.object({
    requireTwoFactor: z.boolean(),
    passwordMinLength: z.number().min(6).max(32),
    sessionTimeout: z.number().min(1).max(168),
    maxLoginAttempts: z.number().min(3).max(10),
    enableAuditLogs: z.boolean(),
    requireEmailVerification: z.boolean(),
    enableIpWhitelist: z.boolean(),
    forcePasswordReset: z.number().min(30).max(365),
    enableSSOOnly: z.boolean()
  }),
  features: z.object({
    enableRegistration: z.boolean(),
    enableInvitations: z.boolean(),
    enableApiAccess: z.boolean(),
    enableWebhooks: z.boolean(),
    enableEmailCampaigns: z.boolean(),
    enableAdvancedReporting: z.boolean(),
    enableFileUploads: z.boolean(),
    enableIntegrations: z.boolean(),
    enableCustomBranding: z.boolean(),
    enableMobileApp: z.boolean()
  }),
  limits: z.object({
    maxUsersPerCompany: z.number().min(1),
    maxStoragePerCompany: z.number().min(1),
    maxApiCallsPerHour: z.number().min(100),
    maxEmailsPerMonth: z.number().min(1000),
    maxFileUploadSize: z.number().min(1).max(100),
    maxCompanies: z.number().min(1),
    maxIntegrationsPerCompany: z.number().min(1)
  }),
  notifications: z.object({
    enableEmailNotifications: z.boolean(),
    enableSlackNotifications: z.boolean(),
    enableWebhookNotifications: z.boolean(),
    notifyOnNewSignup: z.boolean(),
    notifyOnPaymentFailure: z.boolean(),
    notifyOnSystemErrors: z.boolean(),
    dailyReportEmail: z.string().email().optional(),
    weeklyReportEmail: z.string().email().optional()
  })
})

type SettingsFormData = z.infer<typeof settingsSchema>

interface SettingsFormProps {
  initialSettings?: SettingsFormData
}

export function SettingsForm({ initialSettings }: SettingsFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [isResetting, setIsResetting] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue
  } = useForm<SettingsFormData>({
    resolver: zodResolver(settingsSchema),
    defaultValues: initialSettings || {
      general: {
        platformName: 'SaaS Platform',
        platformDescription: 'Enterprise Business Management Platform',
        supportEmail: '<EMAIL>',
        companyEmail: '<EMAIL>',
        timezone: 'UTC',
        language: 'en',
        maintenanceMode: false,
        allowRegistration: true,
        defaultUserRole: 'USER'
      },
      security: {
        requireTwoFactor: false,
        passwordMinLength: 8,
        sessionTimeout: 24,
        maxLoginAttempts: 5,
        enableAuditLogs: true,
        requireEmailVerification: true,
        enableIpWhitelist: false,
        forcePasswordReset: 90,
        enableSSOOnly: false
      },
      features: {
        enableRegistration: true,
        enableInvitations: true,
        enableApiAccess: true,
        enableWebhooks: true,
        enableEmailCampaigns: true,
        enableAdvancedReporting: true,
        enableFileUploads: true,
        enableIntegrations: true,
        enableCustomBranding: false,
        enableMobileApp: false
      },
      limits: {
        maxUsersPerCompany: 100,
        maxStoragePerCompany: 10,
        maxApiCallsPerHour: 1000,
        maxEmailsPerMonth: 10000,
        maxFileUploadSize: 10,
        maxCompanies: 1000,
        maxIntegrationsPerCompany: 10
      },
      notifications: {
        enableEmailNotifications: true,
        enableSlackNotifications: false,
        enableWebhookNotifications: true,
        notifyOnNewSignup: true,
        notifyOnPaymentFailure: true,
        notifyOnSystemErrors: true,
        dailyReportEmail: '<EMAIL>',
        weeklyReportEmail: '<EMAIL>'
      }
    }
  })

  const onSubmit = async (data: SettingsFormData) => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/super-admin/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update settings')
      }

      toast.success('Settings updated successfully!')
    } catch (error) {
      console.error('Error updating settings:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to update settings'
      toast.error(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const handleReset = async () => {
    setIsResetting(true)
    try {
      const response = await fetch('/api/super-admin/settings', {
        method: 'DELETE',
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to reset settings')
      }

      // Reset form to defaults
      reset()
      toast.success('Settings reset to defaults successfully!')
    } catch (error) {
      console.error('Error resetting settings:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to reset settings'
      toast.error(errorMessage)
    } finally {
      setIsResetting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="p-3 bg-gray-100 rounded-lg">
            <Settings className="h-8 w-8 text-gray-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Platform Settings</h1>
            <p className="text-gray-600">Configure platform-wide settings and preferences</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Button 
            type="button" 
            variant="outline" 
            onClick={handleReset}
            disabled={isLoading || isResetting}
          >
            {isResetting ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Reset to Defaults
          </Button>
          <Button type="submit" disabled={isLoading || isResetting}>
            {isLoading ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Save Changes
          </Button>
        </div>
      </div>

      {/* Tabbed Settings Content */}
      <Tabs defaultValue="general" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="features">Features</TabsTrigger>
          <TabsTrigger value="limits">Limits</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
        </TabsList>

        {/* General Settings Tab */}
        <TabsContent value="general" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Globe className="h-5 w-5 text-blue-600" />
                <span>General Platform Settings</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="platformName">Platform Name</Label>
                  <Input
                    id="platformName"
                    {...register('general.platformName')}
                    placeholder="Enter platform name"
                  />
                  {errors.general?.platformName && (
                    <p className="text-sm text-red-600">{errors.general.platformName.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="supportEmail">Support Email</Label>
                  <Input
                    id="supportEmail"
                    type="email"
                    {...register('general.supportEmail')}
                    placeholder="<EMAIL>"
                  />
                  {errors.general?.supportEmail && (
                    <p className="text-sm text-red-600">{errors.general.supportEmail.message}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="platformDescription">Platform Description</Label>
                <Textarea
                  id="platformDescription"
                  {...register('general.platformDescription')}
                  placeholder="Enter platform description"
                  rows={3}
                />
                {errors.general?.platformDescription && (
                  <p className="text-sm text-red-600">{errors.general.platformDescription.message}</p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="timezone">Default Timezone</Label>
                  <Select value={watch('general.timezone')} onValueChange={(value) => setValue('general.timezone', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select timezone" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="UTC">UTC</SelectItem>
                      <SelectItem value="America/New_York">Eastern Time</SelectItem>
                      <SelectItem value="America/Los_Angeles">Pacific Time</SelectItem>
                      <SelectItem value="Europe/London">London</SelectItem>
                      <SelectItem value="Asia/Kolkata">India</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="language">Default Language</Label>
                  <Select value={watch('general.language')} onValueChange={(value) => setValue('general.language', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select language" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="en">English</SelectItem>
                      <SelectItem value="es">Spanish</SelectItem>
                      <SelectItem value="fr">French</SelectItem>
                      <SelectItem value="de">German</SelectItem>
                      <SelectItem value="hi">Hindi</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="defaultUserRole">Default User Role</Label>
                  <Select value={watch('general.defaultUserRole')} onValueChange={(value) => setValue('general.defaultUserRole', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="USER">User</SelectItem>
                      <SelectItem value="ADMIN">Admin</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Maintenance Mode</Label>
                    <p className="text-sm text-gray-500">Temporarily disable platform access</p>
                  </div>
                  <Switch
                    checked={watch('general.maintenanceMode')}
                    onCheckedChange={(checked) => setValue('general.maintenanceMode', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Allow Registration</Label>
                    <p className="text-sm text-gray-500">Allow new user signups</p>
                  </div>
                  <Switch
                    checked={watch('general.allowRegistration')}
                    onCheckedChange={(checked) => setValue('general.allowRegistration', checked)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security Settings Tab */}
        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-red-600" />
                <span>Security & Authentication</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="passwordMinLength">Minimum Password Length</Label>
                  <Input
                    id="passwordMinLength"
                    type="number"
                    {...register('security.passwordMinLength', { valueAsNumber: true })}
                    min="6"
                    max="32"
                  />
                  {errors.security?.passwordMinLength && (
                    <p className="text-sm text-red-600">{errors.security.passwordMinLength.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="sessionTimeout">Session Timeout (hours)</Label>
                  <Input
                    id="sessionTimeout"
                    type="number"
                    {...register('security.sessionTimeout', { valueAsNumber: true })}
                    min="1"
                    max="168"
                  />
                  {errors.security?.sessionTimeout && (
                    <p className="text-sm text-red-600">{errors.security.sessionTimeout.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxLoginAttempts">Max Login Attempts</Label>
                  <Input
                    id="maxLoginAttempts"
                    type="number"
                    {...register('security.maxLoginAttempts', { valueAsNumber: true })}
                    min="3"
                    max="10"
                  />
                  {errors.security?.maxLoginAttempts && (
                    <p className="text-sm text-red-600">{errors.security.maxLoginAttempts.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="forcePasswordReset">Force Password Reset (days)</Label>
                  <Input
                    id="forcePasswordReset"
                    type="number"
                    {...register('security.forcePasswordReset', { valueAsNumber: true })}
                    min="30"
                    max="365"
                  />
                  {errors.security?.forcePasswordReset && (
                    <p className="text-sm text-red-600">{errors.security.forcePasswordReset.message}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Require Two-Factor Authentication</Label>
                    <p className="text-sm text-gray-500">Force 2FA for all users</p>
                  </div>
                  <Switch
                    checked={watch('security.requireTwoFactor')}
                    onCheckedChange={(checked) => setValue('security.requireTwoFactor', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Require Email Verification</Label>
                    <p className="text-sm text-gray-500">Verify email on signup</p>
                  </div>
                  <Switch
                    checked={watch('security.requireEmailVerification')}
                    onCheckedChange={(checked) => setValue('security.requireEmailVerification', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Enable Audit Logs</Label>
                    <p className="text-sm text-gray-500">Track all user activities</p>
                  </div>
                  <Switch
                    checked={watch('security.enableAuditLogs')}
                    onCheckedChange={(checked) => setValue('security.enableAuditLogs', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Enable IP Whitelist</Label>
                    <p className="text-sm text-gray-500">Restrict access by IP</p>
                  </div>
                  <Switch
                    checked={watch('security.enableIpWhitelist')}
                    onCheckedChange={(checked) => setValue('security.enableIpWhitelist', checked)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Features Tab */}
        <TabsContent value="features" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Zap className="h-5 w-5 text-purple-600" />
                <span>Platform Features</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>User Registration</Label>
                    <p className="text-sm text-gray-500">Allow new user signups</p>
                  </div>
                  <Switch
                    checked={watch('features.enableRegistration')}
                    onCheckedChange={(checked) => setValue('features.enableRegistration', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>User Invitations</Label>
                    <p className="text-sm text-gray-500">Allow inviting team members</p>
                  </div>
                  <Switch
                    checked={watch('features.enableInvitations')}
                    onCheckedChange={(checked) => setValue('features.enableInvitations', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>API Access</Label>
                    <p className="text-sm text-gray-500">Enable REST API endpoints</p>
                  </div>
                  <Switch
                    checked={watch('features.enableApiAccess')}
                    onCheckedChange={(checked) => setValue('features.enableApiAccess', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Webhooks</Label>
                    <p className="text-sm text-gray-500">Enable webhook notifications</p>
                  </div>
                  <Switch
                    checked={watch('features.enableWebhooks')}
                    onCheckedChange={(checked) => setValue('features.enableWebhooks', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Email Campaigns</Label>
                    <p className="text-sm text-gray-500">Enable marketing emails</p>
                  </div>
                  <Switch
                    checked={watch('features.enableEmailCampaigns')}
                    onCheckedChange={(checked) => setValue('features.enableEmailCampaigns', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Advanced Reporting</Label>
                    <p className="text-sm text-gray-500">Enable detailed analytics</p>
                  </div>
                  <Switch
                    checked={watch('features.enableAdvancedReporting')}
                    onCheckedChange={(checked) => setValue('features.enableAdvancedReporting', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>File Uploads</Label>
                    <p className="text-sm text-gray-500">Allow file attachments</p>
                  </div>
                  <Switch
                    checked={watch('features.enableFileUploads')}
                    onCheckedChange={(checked) => setValue('features.enableFileUploads', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Third-party Integrations</Label>
                    <p className="text-sm text-gray-500">Enable external integrations</p>
                  </div>
                  <Switch
                    checked={watch('features.enableIntegrations')}
                    onCheckedChange={(checked) => setValue('features.enableIntegrations', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Custom Branding</Label>
                    <p className="text-sm text-gray-500">Allow custom logos/colors</p>
                  </div>
                  <Switch
                    checked={watch('features.enableCustomBranding')}
                    onCheckedChange={(checked) => setValue('features.enableCustomBranding', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Mobile App</Label>
                    <p className="text-sm text-gray-500">Enable mobile app features</p>
                  </div>
                  <Switch
                    checked={watch('features.enableMobileApp')}
                    onCheckedChange={(checked) => setValue('features.enableMobileApp', checked)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Limits Tab */}
        <TabsContent value="limits" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Database className="h-5 w-5 text-orange-600" />
                <span>Platform Limits</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="maxUsersPerCompany">Max Users per Company</Label>
                  <Input
                    id="maxUsersPerCompany"
                    type="number"
                    {...register('limits.maxUsersPerCompany', { valueAsNumber: true })}
                    min="1"
                  />
                  {errors.limits?.maxUsersPerCompany && (
                    <p className="text-sm text-red-600">{errors.limits.maxUsersPerCompany.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxStoragePerCompany">Max Storage per Company (GB)</Label>
                  <Input
                    id="maxStoragePerCompany"
                    type="number"
                    {...register('limits.maxStoragePerCompany', { valueAsNumber: true })}
                    min="1"
                  />
                  {errors.limits?.maxStoragePerCompany && (
                    <p className="text-sm text-red-600">{errors.limits.maxStoragePerCompany.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxApiCallsPerHour">Max API Calls per Hour</Label>
                  <Input
                    id="maxApiCallsPerHour"
                    type="number"
                    {...register('limits.maxApiCallsPerHour', { valueAsNumber: true })}
                    min="100"
                  />
                  {errors.limits?.maxApiCallsPerHour && (
                    <p className="text-sm text-red-600">{errors.limits.maxApiCallsPerHour.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxEmailsPerMonth">Max Emails per Month</Label>
                  <Input
                    id="maxEmailsPerMonth"
                    type="number"
                    {...register('limits.maxEmailsPerMonth', { valueAsNumber: true })}
                    min="1000"
                  />
                  {errors.limits?.maxEmailsPerMonth && (
                    <p className="text-sm text-red-600">{errors.limits.maxEmailsPerMonth.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxFileUploadSize">Max File Upload Size (MB)</Label>
                  <Input
                    id="maxFileUploadSize"
                    type="number"
                    {...register('limits.maxFileUploadSize', { valueAsNumber: true })}
                    min="1"
                    max="100"
                  />
                  {errors.limits?.maxFileUploadSize && (
                    <p className="text-sm text-red-600">{errors.limits.maxFileUploadSize.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxCompanies">Max Companies</Label>
                  <Input
                    id="maxCompanies"
                    type="number"
                    {...register('limits.maxCompanies', { valueAsNumber: true })}
                    min="1"
                  />
                  {errors.limits?.maxCompanies && (
                    <p className="text-sm text-red-600">{errors.limits.maxCompanies.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxIntegrationsPerCompany">Max Integrations per Company</Label>
                  <Input
                    id="maxIntegrationsPerCompany"
                    type="number"
                    {...register('limits.maxIntegrationsPerCompany', { valueAsNumber: true })}
                    min="1"
                  />
                  {errors.limits?.maxIntegrationsPerCompany && (
                    <p className="text-sm text-red-600">{errors.limits.maxIntegrationsPerCompany.message}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Notifications Tab */}
        <TabsContent value="notifications" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Bell className="h-5 w-5 text-green-600" />
                <span>System Notifications</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Email Notifications</Label>
                    <p className="text-sm text-gray-500">Enable system email notifications</p>
                  </div>
                  <Switch
                    checked={watch('notifications.enableEmailNotifications')}
                    onCheckedChange={(checked) => setValue('notifications.enableEmailNotifications', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Slack Notifications</Label>
                    <p className="text-sm text-gray-500">Send alerts to Slack</p>
                  </div>
                  <Switch
                    checked={watch('notifications.enableSlackNotifications')}
                    onCheckedChange={(checked) => setValue('notifications.enableSlackNotifications', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Webhook Notifications</Label>
                    <p className="text-sm text-gray-500">Send webhook events</p>
                  </div>
                  <Switch
                    checked={watch('notifications.enableWebhookNotifications')}
                    onCheckedChange={(checked) => setValue('notifications.enableWebhookNotifications', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>New Signup Alerts</Label>
                    <p className="text-sm text-gray-500">Notify on new user registrations</p>
                  </div>
                  <Switch
                    checked={watch('notifications.notifyOnNewSignup')}
                    onCheckedChange={(checked) => setValue('notifications.notifyOnNewSignup', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Payment Failure Alerts</Label>
                    <p className="text-sm text-gray-500">Notify on payment failures</p>
                  </div>
                  <Switch
                    checked={watch('notifications.notifyOnPaymentFailure')}
                    onCheckedChange={(checked) => setValue('notifications.notifyOnPaymentFailure', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>System Error Alerts</Label>
                    <p className="text-sm text-gray-500">Notify on system errors</p>
                  </div>
                  <Switch
                    checked={watch('notifications.notifyOnSystemErrors')}
                    onCheckedChange={(checked) => setValue('notifications.notifyOnSystemErrors', checked)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="dailyReportEmail">Daily Report Email</Label>
                  <Input
                    id="dailyReportEmail"
                    type="email"
                    {...register('notifications.dailyReportEmail')}
                    placeholder="<EMAIL>"
                  />
                  {errors.notifications?.dailyReportEmail && (
                    <p className="text-sm text-red-600">{errors.notifications.dailyReportEmail.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="weeklyReportEmail">Weekly Report Email</Label>
                  <Input
                    id="weeklyReportEmail"
                    type="email"
                    {...register('notifications.weeklyReportEmail')}
                    placeholder="<EMAIL>"
                  />
                  {errors.notifications?.weeklyReportEmail && (
                    <p className="text-sm text-red-600">{errors.notifications.weeklyReportEmail.message}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Save Actions */}
      <div className="flex items-center justify-end space-x-4 p-6 bg-gray-50 rounded-lg">
        <p className="text-sm text-gray-600">Changes will be applied immediately to all users</p>
        <Button type="button" variant="outline" onClick={() => reset()}>
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading || isResetting}>
          {isLoading ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <Save className="h-4 w-4 mr-2" />
          )}
          Save All Settings
        </Button>
      </div>
    </form>
  )
}
