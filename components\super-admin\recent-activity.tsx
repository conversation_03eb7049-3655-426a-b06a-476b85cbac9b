'use client'

import Link from 'next/link'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { formatTimeAgo } from '@/lib/utils'
import { 
  Activity, 
  Eye,
  Building2,
  Users,
  FileText,
  Receipt,

  Mail,
  Bell,
  Settings,
  Plus,
  Edit,
  Trash2,
  Send,
  CheckCircle,
  AlertTriangle
} from 'lucide-react'

interface RecentActivityProps {
  activities: Array<{
    id: string
    type: string
    title: string
    description: string
    createdAt: Date
    company: {
      name: string
    } | null
    createdBy: {
      name: string | null
      firstName: string | null
      lastName: string | null
    } | null
  }>
}

export function RecentActivity({ activities }: RecentActivityProps) {
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'CUSTOMER':
        return Users
      case 'QUOTATION':
        return FileText
      case 'INVOICE':
        return Receipt
      case 'CONTRACT':
        return FileText
      case 'EMAIL':
        return Mail
      case 'NOTIFICATION':
        return Bell
      case 'COMPANY':
        return Building2
      case 'USER':
        return Users
      case 'SYSTEM':
        return Settings
      default:
        return Activity
    }
  }

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'CUSTOMER':
        return 'text-purple-600 bg-purple-100'
      case 'QUOTATION':
        return 'text-blue-600 bg-blue-100'
      case 'INVOICE':
        return 'text-green-600 bg-green-100'
      case 'CONTRACT':
        return 'text-indigo-600 bg-indigo-100'
      case 'EMAIL':
        return 'text-orange-600 bg-orange-100'
      case 'NOTIFICATION':
        return 'text-yellow-600 bg-yellow-100'
      case 'COMPANY':
        return 'text-red-600 bg-red-100'
      case 'USER':
        return 'text-pink-600 bg-pink-100'
      case 'SYSTEM':
        return 'text-gray-600 bg-gray-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getActionIcon = (description: string) => {
    if (description.includes('created')) return Plus
    if (description.includes('updated') || description.includes('modified')) return Edit
    if (description.includes('deleted')) return Trash2
    if (description.includes('sent')) return Send
    if (description.includes('completed') || description.includes('signed')) return CheckCircle
    if (description.includes('failed') || description.includes('error')) return AlertTriangle
    return Activity
  }

  const getUserName = (user: any) => {
    if (!user) return 'System'
    if (user.name) return user.name
    if (user.firstName && user.lastName) return `${user.firstName} ${user.lastName}`
    if (user.firstName) return user.firstName
    return 'Unknown User'
  }

  const getActivityPriority = (type: string, description: string) => {
    if (description.includes('failed') || description.includes('error')) return 'high'
    if (type === 'COMPANY' || type === 'USER') return 'medium'
    return 'low'
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'border-l-red-500'
      case 'medium':
        return 'border-l-yellow-500'
      case 'low':
        return 'border-l-green-500'
      default:
        return 'border-l-gray-300'
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>Recent Activity</span>
          </CardTitle>
          <Link href="/super-admin/activity">
            <Button variant="ghost" >
              <Eye className="h-4 w-4 mr-2" />
              View All
            </Button>
          </Link>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {activities.length > 0 ? (
            activities.map((activity) => {
              const ActivityIcon = getActivityIcon(activity.type)
              const ActionIcon = getActionIcon(activity.description)
              const priority = getActivityPriority(activity.type, activity.description)
              
              return (
                <div 
                  key={activity.id} 
                  className={`p-3 border-l-4 bg-gray-50 rounded-r-lg ${getPriorityColor(priority)}`}
                >
                  <div className="flex items-start space-x-3">
                    <div className={`p-2 rounded-lg ${getActivityColor(activity.type)} flex-shrink-0`}>
                      <ActivityIcon className="h-4 w-4" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="font-medium text-gray-900 text-sm truncate">
                          {activity.title}
                        </h4>
                        <ActionIcon className="h-3 w-3 text-gray-500" />
                        <Badge variant="outline" className="text-xs">
                          {activity.type}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">
                        {activity.description}
                      </p>
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <div className="flex items-center space-x-2">
                          <span>{formatTimeAgo(activity.createdAt)}</span>
                          {activity.company && (
                            <>
                              <span>•</span>
                              <span className="flex items-center space-x-1">
                                <Building2 className="h-3 w-3" />
                                <span>{activity.company.name}</span>
                              </span>
                            </>
                          )}
                        </div>
                        <span>{getUserName(activity.createdBy)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })
          ) : (
            <div className="text-center py-8">
              <Activity className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <h3 className="font-medium text-gray-900 mb-2">No Recent Activity</h3>
              <p className="text-sm text-gray-500">
                Platform activity will appear here as it happens.
              </p>
            </div>
          )}
        </div>
        
        {activities.length > 0 && (
          <div className="mt-4 pt-4 border-t">
            <div className="flex items-center justify-between text-sm text-gray-600">
              <span>Showing last {Math.min(activities.length, 50)} activities</span>
              <Link href="/super-admin/activity">
                <Button variant="outline" >
                  View Complete Log
                </Button>
              </Link>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
