'use client'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { formatCurrency } from '@/lib/utils'
import { 
  Receipt, 
  DollarSign, 
  TrendingUp, 
  Clock,
  CheckCircle,
  XCircle,
  CreditCard,
  Banknote,
  Smartphone,
  Building
} from 'lucide-react'

interface ReceiptStatsProps {
  stats: {
    totalReceipts: number
    totalAmount: number
    receiptsByStatus: Array<{
      status: string
      _count: number
      _sum: { amount: number | null }
    }>
    receiptsByPaymentMethod: Array<{
      paymentMethod: string
      _count: number
      _sum: { amount: number | null }
    }>
    recentReceiptsCount: number
    pendingReceiptsCount: number
    averageAmount: number
  }
}

export function ReceiptStats({ stats }: ReceiptStatsProps) {
  // Process status data
  const confirmedReceipts = stats.receiptsByStatus.find(s => s.status === 'CONFIRMED')
  const pendingReceipts = stats.receiptsByStatus.find(s => s.status === 'PENDING')
  const cancelledReceipts = stats.receiptsByStatus.find(s => s.status === 'CANCELLED')

  const confirmedAmount = confirmedReceipts?._sum.amount || 0
  const pendingAmount = pendingReceipts?._sum.amount || 0

  // Process payment method data
  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case 'CASH':
        return Banknote
      case 'CREDIT_CARD':
      case 'DEBIT_CARD':
        return CreditCard
      case 'UPI':
        return Smartphone
      case 'BANK_TRANSFER':
        return Building
      default:
        return DollarSign
    }
  }

  const getPaymentMethodColor = (method: string) => {
    switch (method) {
      case 'CASH':
        return 'text-green-600 bg-green-100'
      case 'CREDIT_CARD':
      case 'DEBIT_CARD':
        return 'text-blue-600 bg-blue-100'
      case 'UPI':
        return 'text-purple-600 bg-purple-100'
      case 'BANK_TRANSFER':
        return 'text-indigo-600 bg-indigo-100'
      case 'CHEQUE':
        return 'text-orange-600 bg-orange-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const overviewCards = [
    {
      title: 'Total Receipts',
      value: stats.totalReceipts.toString(),
      icon: Receipt,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      description: 'All payment receipts'
    },
    {
      title: 'Total Amount',
      value: formatCurrency(stats.totalAmount),
      icon: DollarSign,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      description: 'Total payments received'
    },
    {
      title: 'Average Receipt',
      value: formatCurrency(stats.averageAmount),
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      description: 'Average payment amount'
    },
    {
      title: 'Recent Receipts',
      value: stats.recentReceiptsCount.toString(),
      icon: Clock,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      description: 'Last 30 days'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {overviewCards.map((card, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {card.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${card.bgColor}`}>
                <card.icon className={`h-4 w-4 ${card.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">
                {card.value}
              </div>
              <p className="text-xs text-gray-500 mt-1">
                {card.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Detailed Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Status Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle>Receipt Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <div>
                    <p className="font-medium text-green-800">Confirmed</p>
                    <p className="text-sm text-green-600">
                      {confirmedReceipts?._count || 0} receipts
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-bold text-green-800">
                    {formatCurrency(confirmedAmount)}
                  </p>
                  <p className="text-xs text-green-600">
                    {stats.totalAmount > 0 ? ((confirmedAmount / stats.totalAmount) * 100).toFixed(1) : '0'}%
                  </p>
                </div>
              </div>

              <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <Clock className="h-5 w-5 text-yellow-600" />
                  <div>
                    <p className="font-medium text-yellow-800">Pending</p>
                    <p className="text-sm text-yellow-600">
                      {pendingReceipts?._count || 0} receipts
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-bold text-yellow-800">
                    {formatCurrency(pendingAmount)}
                  </p>
                  <p className="text-xs text-yellow-600">
                    {stats.totalAmount > 0 ? ((pendingAmount / stats.totalAmount) * 100).toFixed(1) : '0'}%
                  </p>
                </div>
              </div>

              {cancelledReceipts && (
                <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <XCircle className="h-5 w-5 text-red-600" />
                    <div>
                      <p className="font-medium text-red-800">Cancelled</p>
                      <p className="text-sm text-red-600">
                        {cancelledReceipts._count} receipts
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-red-800">
                      {formatCurrency(cancelledReceipts._sum.amount || 0)}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Payment Methods */}
        <Card>
          <CardHeader>
            <CardTitle>Payment Methods</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats.receiptsByPaymentMethod.map((method, index) => {
                const Icon = getPaymentMethodIcon(method.paymentMethod)
                const colorClasses = getPaymentMethodColor(method.paymentMethod)
                const percentage = stats.totalAmount > 0 
                  ? (((method._sum.amount || 0) / stats.totalAmount) * 100).toFixed(1)
                  : '0'

                return (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded-lg ${colorClasses}`}>
                        <Icon className="h-4 w-4" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">
                          {method.paymentMethod.replace('_', ' ')}
                        </p>
                        <p className="text-sm text-gray-600">
                          {method._count} receipts
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-gray-900">
                        {formatCurrency(method._sum.amount || 0)}
                      </p>
                      <p className="text-xs text-gray-500">
                        {percentage}%
                      </p>
                    </div>
                  </div>
                )
              })}
              
              {stats.receiptsByPaymentMethod.length === 0 && (
                <div className="text-center py-6">
                  <Receipt className="h-8 w-8 text-gray-300 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">No payment methods data</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Insights */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Insights</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-800">
                {stats.pendingReceiptsCount}
              </div>
              <div className="text-sm text-blue-600">Pending Confirmation</div>
              {stats.pendingReceiptsCount > 0 && (
                <div className="text-xs text-blue-500 mt-1">
                  Requires attention
                </div>
              )}
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-800">
                {stats.totalReceipts > 0 ? ((confirmedReceipts?._count || 0) / stats.totalReceipts * 100).toFixed(1) : '0'}%
              </div>
              <div className="text-sm text-green-600">Confirmation Rate</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-800">
                {stats.recentReceiptsCount}
              </div>
              <div className="text-sm text-purple-600">This Month</div>
              <div className="text-xs text-purple-500 mt-1">
                Recent activity
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
