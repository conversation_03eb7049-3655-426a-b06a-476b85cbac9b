import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ContractsTable } from '@/components/contracts/contracts-table'
import { ContractStats } from '@/components/contracts/contract-stats'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Plus, Search, Filter, FileText } from 'lucide-react'
import Link from 'next/link'

interface SearchParams {
  search?: string
  status?: string
  customerId?: string
  quotationId?: string
  page?: string
}

export default async function ContractsPage({
  searchParams,
}: {
  searchParams: SearchParams
}) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  const search = searchParams.search || ''
  const status = searchParams.status || ''
  const customerId = searchParams.customerId || ''
  const quotationId = searchParams.quotationId || ''
  const page = parseInt(searchParams.page || '1')
  const limit = 10
  const offset = (page - 1) * limit

  // Build where clause
  const where: any = {
    companyId: session.user.companyId,
  }

  if (search) {
    where.OR = [
      { contractNumber: { contains: search } },
      { title: { contains: search } },
      { description: { contains: search } },
      { customer: { name: { contains: search } } },
    ]
  }

  if (status) {
    where.status = status
  }

  if (customerId) {
    where.customerId = customerId
  }

  if (quotationId) {
    where.quotationId = quotationId
  }

  // Fetch contracts and stats
  const [contracts, totalCount, stats, customers, quotations] = await Promise.all([
    prisma.contract.findMany({
      where,
      include: {
        customer: {
          select: { id: true, name: true, email: true, company: true }
        },
        quotation: {
          select: { id: true, quotationNumber: true, title: true }
        },
        signatures: {
          include: {
            signedBy: {
              select: { name: true, firstName: true, lastName: true }
            }
          }
        },
        _count: {
          select: {
            signatures: true,
            activities: true,
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      skip: offset,
      take: limit,
    }),
    prisma.contract.count({ where }),
    prisma.contract.groupBy({
      by: ['status'],
      where: { companyId: session.user.companyId },
      _count: true,
      _sum: { value: true },
    }),
    prisma.customer.findMany({
      where: { companyId: session.user.companyId },
      select: { id: true, name: true },
      orderBy: { name: 'asc' },
    }),
    prisma.quotation.findMany({
      where: { 
        companyId: session.user.companyId,
        status: 'ACCEPTED'
      },
      select: { id: true, quotationNumber: true, title: true },
      orderBy: { createdAt: 'desc' },
      take: 50,
    })
  ])

  const totalPages = Math.ceil(totalCount / limit)

  // Calculate stats
  const contractStats = {
    total: totalCount,
    draft: stats.find(s => s.status === 'DRAFT')?._count || 0,
    sent: stats.find(s => s.status === 'SENT')?._count || 0,
    active: stats.find(s => s.status === 'ACTIVE')?._count || 0,
    signed: stats.find(s => s.status === 'SIGNED')?._count || 0,
    executed: stats.find(s => s.status === 'EXECUTED')?._count || 0,
    expired: stats.find(s => s.status === 'EXPIRED')?._count || 0,
    cancelled: stats.find(s => s.status === 'CANCELLED')?._count || 0,
    totalValue: stats.reduce((sum, stat) => sum + (stat._sum.value || 0), 0),
    signedValue: stats.filter(s => ['SIGNED', 'EXECUTED'].includes(s.status))
      .reduce((sum, stat) => sum + (stat._sum.value || 0), 0),
    pendingValue: stats.filter(s => ['SENT', 'ACTIVE'].includes(s.status))
      .reduce((sum, stat) => sum + (stat._sum.value || 0), 0),
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Contracts</h1>
          <p className="text-gray-600 mt-1">
            Manage contracts and digital signatures
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Link href="/dashboard/contracts/templates">
            <Button variant="outline">
              <FileText className="h-4 w-4 mr-2" />
              Templates
            </Button>
          </Link>
          <Link href="/dashboard/contracts/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Contract
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats */}
      <ContractStats stats={contractStats} />

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search contracts..."
              className="pl-10"
              defaultValue={search}
              name="search"
            />
          </div>
        </div>
        <div className="flex gap-2">
          <select
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            defaultValue={status}
            name="status"
          >
            <option value="">All Status</option>
            <option value="DRAFT">Draft</option>
            <option value="SENT">Sent</option>
            <option value="VIEWED">Viewed</option>
            <option value="SIGNED">Signed</option>
            <option value="EXECUTED">Executed</option>
            <option value="EXPIRED">Expired</option>
            <option value="CANCELLED">Cancelled</option>
          </select>
          <select
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            defaultValue={customerId}
            name="customerId"
          >
            <option value="">All Customers</option>
            {customers.map((customer) => (
              <option key={customer.id} value={customer.id}>
                {customer.name}
              </option>
            ))}
          </select>
          {quotations.length > 0 && (
            <select
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              defaultValue={quotationId}
              name="quotationId"
            >
              <option value="">All Quotations</option>
              {quotations.map((quotation) => (
                <option key={quotation.id} value={quotation.id}>
                  {quotation.quotationNumber}
                </option>
              ))}
            </select>
          )}
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
        </div>
      </div>

      {/* Contracts Table */}
      <ContractsTable 
        contracts={contracts}
        currentPage={page}
        totalPages={totalPages}
        totalCount={totalCount}
      />
    </div>
  )
}
