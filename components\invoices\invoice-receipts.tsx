'use client'

import Link from 'next/link'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { formatCurrency, formatDate, getStatusColor } from '@/lib/utils'
import { 
  Receipt, 
  Plus, 
  Eye,
  DollarSign,
  Calendar,
  User,
  CreditCard,
  Banknote,
  Smartphone,
  Building,
  CheckCircle,
  Clock,
  XCircle
} from 'lucide-react'

interface InvoiceReceiptsProps {
  invoiceId: string
  invoiceTotal: number
  invoiceStatus: string
  receipts: Array<{
    id: string
    receiptNumber: string
    amount: number
    paymentMethod: string
    status: string
    paidAt: Date
    createdAt: Date
    createdBy: {
      name: string | null
      firstName: string | null
      lastName: string | null
    } | null
  }>
}

export function InvoiceReceipts({ 
  invoiceId, 
  invoiceTotal, 
  invoiceStatus,
  receipts 
}: InvoiceReceiptsProps) {
  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case 'CASH':
        return Banknote
      case 'CREDIT_CARD':
      case 'DEBIT_CARD':
        return CreditCard
      case 'UPI':
        return Smartphone
      case 'BANK_TRANSFER':
        return Building
      default:
        return DollarSign
    }
  }

  const getPaymentMethodColor = (method: string) => {
    switch (method) {
      case 'CASH':
        return 'text-green-600'
      case 'CREDIT_CARD':
      case 'DEBIT_CARD':
        return 'text-blue-600'
      case 'UPI':
        return 'text-purple-600'
      case 'BANK_TRANSFER':
        return 'text-indigo-600'
      case 'CHEQUE':
        return 'text-orange-600'
      default:
        return 'text-gray-600'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'CONFIRMED':
        return CheckCircle
      case 'PENDING':
        return Clock
      case 'CANCELLED':
        return XCircle
      default:
        return Receipt
    }
  }

  const getUserName = (user: any) => {
    if (!user) return 'System'
    if (user.name) return user.name
    if (user.firstName && user.lastName) return `${user.firstName} ${user.lastName}`
    if (user.firstName) return user.firstName
    return 'Unknown User'
  }

  // Calculate payment summary
  const confirmedReceipts = receipts.filter(r => r.status === 'CONFIRMED')
  const totalPaid = confirmedReceipts.reduce((sum, r) => sum + r.amount, 0)
  const remainingBalance = invoiceTotal - totalPaid
  const isFullyPaid = remainingBalance <= 0

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Receipt className="h-5 w-5 text-green-600" />
            <span>Payment Receipts</span>
          </CardTitle>
          {!['PAID', 'CANCELLED'].includes(invoiceStatus) && (
            <Link href={`/dashboard/receipts/new?invoiceId=${invoiceId}`}>
              <Button >
                <Plus className="h-4 w-4 mr-2" />
                Add Receipt
              </Button>
            </Link>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {/* Payment Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <DollarSign className="h-6 w-6 text-blue-600 mx-auto mb-1" />
            <div className="text-lg font-bold text-blue-800">
              {formatCurrency(invoiceTotal)}
            </div>
            <div className="text-sm text-blue-600">Invoice Total</div>
          </div>
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <CheckCircle className="h-6 w-6 text-green-600 mx-auto mb-1" />
            <div className="text-lg font-bold text-green-800">
              {formatCurrency(totalPaid)}
            </div>
            <div className="text-sm text-green-600">Total Paid</div>
          </div>
          <div className={`text-center p-3 rounded-lg ${
            isFullyPaid ? 'bg-emerald-50' : 'bg-orange-50'
          }`}>
            <DollarSign className={`h-6 w-6 mx-auto mb-1 ${
              isFullyPaid ? 'text-emerald-600' : 'text-orange-600'
            }`} />
            <div className={`text-lg font-bold ${
              isFullyPaid ? 'text-emerald-800' : 'text-orange-800'
            }`}>
              {formatCurrency(Math.abs(remainingBalance))}
            </div>
            <div className={`text-sm ${
              isFullyPaid ? 'text-emerald-600' : 'text-orange-600'
            }`}>
              {isFullyPaid ? 'Overpaid' : 'Balance Due'}
            </div>
          </div>
        </div>

        {/* Receipts List */}
        {receipts.length > 0 ? (
          <div className="space-y-3">
            {receipts.map((receipt) => {
              const PaymentIcon = getPaymentMethodIcon(receipt.paymentMethod)
              const StatusIcon = getStatusIcon(receipt.status)
              
              return (
                <div key={receipt.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className={`p-2 rounded-lg ${
                        receipt.status === 'CONFIRMED' ? 'bg-green-100' :
                        receipt.status === 'PENDING' ? 'bg-yellow-100' : 'bg-red-100'
                      }`}>
                        <StatusIcon className={`h-4 w-4 ${
                          receipt.status === 'CONFIRMED' ? 'text-green-600' :
                          receipt.status === 'PENDING' ? 'text-yellow-600' : 'text-red-600'
                        }`} />
                      </div>
                      
                      <div>
                        <div className="flex items-center space-x-2">
                          <Link 
                            href={`/dashboard/receipts/${receipt.id}`}
                            className="font-medium text-blue-600 hover:text-blue-800"
                          >
                            {receipt.receiptNumber}
                          </Link>
                          <Badge className={getStatusColor(receipt.status)} variant="outline">
                            {receipt.status}
                          </Badge>
                        </div>
                        <div className="text-sm text-gray-600 mt-1">
                          {formatDate(receipt.paidAt)} • by {getUserName(receipt.createdBy)}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <div className="font-bold text-gray-900">
                          {formatCurrency(receipt.amount)}
                        </div>
                        <div className="flex items-center space-x-1 text-sm text-gray-600">
                          <PaymentIcon className={`h-3 w-3 ${getPaymentMethodColor(receipt.paymentMethod)}`} />
                          <span>{receipt.paymentMethod.replace('_', ' ')}</span>
                        </div>
                      </div>
                      
                      <Link href={`/dashboard/receipts/${receipt.id}`}>
                        <Button variant="ghost" >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </Link>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        ) : (
          <div className="text-center py-8">
            <Receipt className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No receipts yet</h3>
            <p className="text-gray-500 mb-4">
              Create a payment receipt to track payments for this invoice.
            </p>
            {!['PAID', 'CANCELLED'].includes(invoiceStatus) && (
              <Link href={`/dashboard/receipts/new?invoiceId=${invoiceId}`}>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create First Receipt
                </Button>
              </Link>
            )}
          </div>
        )}

        {/* Payment Status Summary */}
        {receipts.length > 0 && (
          <div className="mt-6 pt-6 border-t">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Payment Status:</span>
              <div className="flex items-center space-x-2">
                {isFullyPaid ? (
                  <>
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="text-green-600 font-medium">Fully Paid</span>
                  </>
                ) : remainingBalance > 0 ? (
                  <>
                    <Clock className="h-4 w-4 text-orange-600" />
                    <span className="text-orange-600 font-medium">
                      {formatCurrency(remainingBalance)} remaining
                    </span>
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 text-emerald-600" />
                    <span className="text-emerald-600 font-medium">
                      Overpaid by {formatCurrency(Math.abs(remainingBalance))}
                    </span>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
