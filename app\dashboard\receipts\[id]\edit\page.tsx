import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ReceiptForm } from '@/components/receipts/receipt-form'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import { notFound } from 'next/navigation'

export default async function EditReceiptPage({
  params,
}: {
  params: { id: string }
}) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  // Fetch the receipt to edit
  const receipt = await prisma.receipt.findFirst({
    where: {
      id: params.id,
      companyId: session.user.companyId,
    },
    include: {
      invoice: {
        include: {
          customer: {
            select: { id: true, name: true, company: true }
          }
        }
      }
    }
  })

  if (!receipt) {
    notFound()
  }

  // Fetch available invoices that can have receipts
  const availableInvoices = await prisma.invoice.findMany({
    where: { 
      companyId: session.user.companyId,
      OR: [
        { status: { in: ['SENT', 'VIEWED', 'OVERDUE', 'PAID'] } },
        { id: receipt.invoiceId } // Include current invoice even if paid
      ]
    },
    include: {
      customer: {
        select: { id: true, name: true, company: true }
      }
    },
    orderBy: { createdAt: 'desc' }
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href={`/dashboard/receipts/${receipt.id}`}>
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Receipt
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Edit Receipt</h1>
          <p className="text-gray-600 mt-1">
            Update payment receipt {receipt.receiptNumber}
          </p>
        </div>
      </div>

      {/* Receipt Form */}
      <ReceiptForm 
        mode="edit" 
        receipt={receipt}
        availableInvoices={availableInvoices}
      />
    </div>
  )
}
