'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { formatDate, formatTimeAgo } from '@/lib/utils'
import { 
  Bell, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  Mail,
  MessageSquare,
  Calendar,
  DollarSign,
  FileText,
  Users,
  Eye,
  Trash2,
  MoreHorizontal,
  ChevronLeft,
  ChevronRight,
  ExternalLink
} from 'lucide-react'
import toast from 'react-hot-toast'

interface NotificationData {
  id: string
  type: string
  title: string
  message: string
  status: string
  priority: string
  createdAt: Date
  readAt: Date | null
  actionUrl: string | null
  metadata: any
  user: {
    name: string | null
    firstName: string | null
    lastName: string | null
    email: string
  } | null
  createdBy: {
    name: string | null
    firstName: string | null
    lastName: string | null
  } | null
}

interface NotificationsListProps {
  notifications: NotificationData[]
  currentPage: number
  totalPages: number
  totalCount: number
}

export function NotificationsList({ 
  notifications, 
  currentPage, 
  totalPages, 
  totalCount 
}: NotificationsListProps) {
  const [markingAsRead, setMarkingAsRead] = useState<string | null>(null)
  const [deleting, setDeleting] = useState<string | null>(null)

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'INVOICE_OVERDUE':
      case 'PAYMENT_RECEIVED':
        return DollarSign
      case 'QUOTATION_ACCEPTED':
      case 'QUOTATION_EXPIRED':
      case 'CONTRACT_SIGNED':
        return FileText
      case 'CUSTOMER_CREATED':
      case 'LEAD_CONVERTED':
        return Users
      case 'REMINDER':
      case 'DEADLINE':
        return Calendar
      case 'EMAIL':
        return Mail
      case 'SYSTEM':
        return Bell
      default:
        return MessageSquare
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'INVOICE_OVERDUE':
        return 'text-red-600 bg-red-100'
      case 'PAYMENT_RECEIVED':
        return 'text-green-600 bg-green-100'
      case 'QUOTATION_ACCEPTED':
      case 'CONTRACT_SIGNED':
        return 'text-blue-600 bg-blue-100'
      case 'QUOTATION_EXPIRED':
        return 'text-orange-600 bg-orange-100'
      case 'CUSTOMER_CREATED':
      case 'LEAD_CONVERTED':
        return 'text-purple-600 bg-purple-100'
      case 'REMINDER':
      case 'DEADLINE':
        return 'text-yellow-600 bg-yellow-100'
      case 'EMAIL':
        return 'text-indigo-600 bg-indigo-100'
      case 'SYSTEM':
        return 'text-gray-600 bg-gray-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'HIGH':
        return 'text-red-600 border-red-200'
      case 'MEDIUM':
        return 'text-yellow-600 border-yellow-200'
      case 'LOW':
        return 'text-gray-600 border-gray-200'
      default:
        return 'text-gray-600 border-gray-200'
    }
  }

  const formatTypeName = (type: string) => {
    return type.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    ).join(' ')
  }

  const getUserName = (user: any) => {
    if (!user) return 'System'
    if (user.name) return user.name
    if (user.firstName && user.lastName) return `${user.firstName} ${user.lastName}`
    if (user.firstName) return user.firstName
    return user.email
  }

  const handleMarkAsRead = async (notificationId: string) => {
    setMarkingAsRead(notificationId)
    try {
      const response = await fetch(`/api/notifications/${notificationId}/read`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to mark notification as read')
      }

      toast.success('Notification marked as read')
      window.location.reload()
    } catch (error) {
      toast.error('Failed to mark notification as read')
    } finally {
      setMarkingAsRead(null)
    }
  }

  const handleDelete = async (notificationId: string) => {
    if (!confirm('Are you sure you want to delete this notification?')) {
      return
    }

    setDeleting(notificationId)
    try {
      const response = await fetch(`/api/notifications/${notificationId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to delete notification')
      }

      toast.success('Notification deleted')
      window.location.reload()
    } catch (error) {
      toast.error('Failed to delete notification')
    } finally {
      setDeleting(null)
    }
  }

  if (notifications.length === 0) {
    return (
      <Card>
        <CardContent className="py-12">
          <div className="text-center">
            <Bell className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No notifications found</h3>
            <p className="text-gray-500 mb-6">
              You're all caught up! No notifications match your current filters.
            </p>
            <Link href="/dashboard/notifications">
              <Button variant="outline">
                View All Notifications
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {/* Notifications List */}
      <div className="space-y-3">
        {notifications.map((notification) => {
          const TypeIcon = getTypeIcon(notification.type)
          const isUnread = notification.status === 'UNREAD'
          
          return (
            <Card 
              key={notification.id} 
              className={`transition-all hover:shadow-md ${
                isUnread ? 'border-l-4 border-l-blue-500 bg-blue-50/30' : ''
              }`}
            >
              <CardContent className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4 flex-1">
                    {/* Icon */}
                    <div className={`p-2 rounded-lg ${getTypeColor(notification.type)} flex-shrink-0`}>
                      <TypeIcon className="h-5 w-5" />
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className={`font-medium ${isUnread ? 'text-gray-900' : 'text-gray-700'}`}>
                          {notification.title}
                        </h4>
                        <Badge className={getPriorityColor(notification.priority)} variant="outline">
                          {notification.priority}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {formatTypeName(notification.type)}
                        </Badge>
                        {isUnread && (
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        )}
                      </div>
                      
                      <p className={`text-sm mb-2 ${isUnread ? 'text-gray-800' : 'text-gray-600'}`}>
                        {notification.message}
                      </p>
                      
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <span>{formatTimeAgo(notification.createdAt)}</span>
                        <span>•</span>
                        <span>To: {getUserName(notification.user)}</span>
                        {notification.createdBy && (
                          <>
                            <span>•</span>
                            <span>From: {getUserName(notification.createdBy)}</span>
                          </>
                        )}
                        {notification.readAt && (
                          <>
                            <span>•</span>
                            <span>Read: {formatTimeAgo(notification.readAt)}</span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center space-x-2 flex-shrink-0">
                    {notification.actionUrl && (
                      <Link href={notification.actionUrl}>
                        <Button variant="ghost" size="sm" className="text-blue-600">
                          <ExternalLink className="h-4 w-4" />
                        </Button>
                      </Link>
                    )}
                    
                    {isUnread && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleMarkAsRead(notification.id)}
                        disabled={markingAsRead === notification.id}
                        className="text-green-600"
                      >
                        <CheckCircle className="h-4 w-4" />
                      </Button>
                    )}
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(notification.id)}
                      disabled={deleting === notification.id}
                      className="text-red-600"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* Metadata */}
                {notification.metadata && Object.keys(notification.metadata).length > 0 && (
                  <div className="mt-3 pt-3 border-t border-gray-100">
                    <div className="text-xs text-gray-500">
                      <strong>Details:</strong> {JSON.stringify(notification.metadata)}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                Showing {((currentPage - 1) * 20) + 1} to {Math.min(currentPage * 20, totalCount)} of {totalCount} notifications
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  disabled={currentPage <= 1}
                  asChild
                >
                  <Link href={`?page=${currentPage - 1}`}>
                    <ChevronLeft className="h-4 w-4" />
                    Previous
                  </Link>
                </Button>
                
                <div className="flex items-center space-x-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const pageNum = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i
                    const isActive = pageNum === currentPage
                    
                    return (
                      <Button
                        key={pageNum}
                        variant={isActive ? "default" : "outline"}
                        size="sm"
                        asChild
                      >
                        <Link href={`?page=${pageNum}`}>
                          {pageNum}
                        </Link>
                      </Button>
                    )
                  })}
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  disabled={currentPage >= totalPages}
                  asChild
                >
                  <Link href={`?page=${currentPage + 1}`}>
                    Next
                    <ChevronRight className="h-4 w-4" />
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
