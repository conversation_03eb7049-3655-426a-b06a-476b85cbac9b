import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { LeadDetails } from '@/components/leads/lead-details'
import { LeadActivity } from '@/components/leads/lead-activity'
import { LeadQuotations } from '@/components/leads/lead-quotations'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Edit, Trash2, FileText } from 'lucide-react'
import Link from 'next/link'
import { notFound } from 'next/navigation'

export default async function LeadDetailPage({
  params,
}: {
  params: { id: string }
}) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  const lead = await prisma.lead.findFirst({
    where: {
      id: params.id,
      companyId: session.user.companyId,
    },
    include: {
      customer: {
        select: { 
          id: true, 
          name: true, 
          email: true, 
          company: true, 
          phone: true,
          address: true,
          city: true,
          state: true,
          country: true
        }
      },
      quotations: {
        orderBy: { createdAt: 'desc' },
        take: 10,
      },
      activities: {
        include: {
          createdBy: {
            select: { name: true, firstName: true, lastName: true }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 15,
      },
      _count: {
        select: {
          quotations: true,
          activities: true,
        }
      }
    }
  })

  if (!lead) {
    notFound()
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/leads">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Leads
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{lead.title}</h1>
            <p className="text-gray-600 mt-1">
              Lead details and activity history
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Link href={`/dashboard/quotations/new?leadId=${lead.id}&customerId=${lead.customerId}`}>
            <Button variant="outline">
              <FileText className="h-4 w-4 mr-2" />
              Create Quotation
            </Button>
          </Link>
          <Link href={`/dashboard/leads/${lead.id}/edit`}>
            <Button variant="outline">
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
          </Link>
          <Button variant="outline" className="text-red-600 hover:text-red-700">
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Lead Details */}
        <div className="lg:col-span-2 space-y-6">
          <LeadDetails lead={lead} />
          <LeadQuotations quotations={lead.quotations} leadId={lead.id} />
        </div>

        {/* Right Column - Activity */}
        <div>
          <LeadActivity activities={lead.activities} />
        </div>
      </div>
    </div>
  )
}
