'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { toast } from 'react-hot-toast'
import {
  CreditCard,
  Mail,
  Bell,
  Zap,
  Key,
  RefreshCw,
  CheckCircle,
  XCircle,
  Loader2,
  Settings,
  Save,
  Edit,
  Eye,
  EyeOff
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  TabsTrigger,
} from '@/components/ui/tabs'

const integrationConfigSchema = z.object({
  stripe: z.object({
    enabled: z.boolean(),
    publishableKey: z.string().optional(),
    secretKey: z.string().optional(),
    webhookSecret: z.string().optional(),
    testMode: z.boolean()
  }),
  mailgun: z.object({
    enabled: z.boolean(),
    apiKey: z.string().optional(),
    domain: z.string().optional(),
    region: z.enum(['us', 'eu']).optional()
  }),
  slack: z.object({
    enabled: z.boolean(),
    webhookUrl: z.string().optional(),
    channel: z.string().optional(),
    botToken: z.string().optional()
  }),
  webhooks: z.object({
    enabled: z.boolean(),
    defaultUrl: z.string().optional(),
    secret: z.string().optional(),
    retryAttempts: z.number().min(1).max(10).optional()
  })
})

type IntegrationConfigData = z.infer<typeof integrationConfigSchema>

interface Integration {
  name: string
  description: string
  status: 'connected' | 'disconnected' | 'active'
  configured: boolean
  lastChecked: string
}

interface IntegrationsData {
  stripe: Integration
  mailgun: Integration
  slack: Integration
  webhooks: Integration
}

export function IntegrationsConfig() {
  const [integrations, setIntegrations] = useState<IntegrationsData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [testingIntegration, setTestingIntegration] = useState<string | null>(null)
  const [editingIntegration, setEditingIntegration] = useState<string | null>(null)
  const [showSecrets, setShowSecrets] = useState<Record<string, boolean>>({})

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue
  } = useForm<IntegrationConfigData>({
    resolver: zodResolver(integrationConfigSchema),
    defaultValues: {
      stripe: {
        enabled: true,
        testMode: true
      },
      mailgun: {
        enabled: true,
        region: 'us'
      },
      slack: {
        enabled: false
      },
      webhooks: {
        enabled: true,
        retryAttempts: 3
      }
    }
  })

  const fetchIntegrations = async () => {
    try {
      const response = await fetch('/api/super-admin/integrations')
      if (response.ok) {
        const data = await response.json()
        setIntegrations(data)
      } else {
        throw new Error('Failed to fetch integrations')
      }
    } catch (error) {
      console.error('Error fetching integrations:', error)
      toast.error('Failed to load integration status')
    } finally {
      setIsLoading(false)
    }
  }

  const fetchIntegrationConfig = async () => {
    try {
      const response = await fetch('/api/super-admin/integrations/config')
      if (response.ok) {
        const config = await response.json()
        reset(config)
      }
    } catch (error) {
      console.error('Error fetching integration config:', error)
    }
  }

  const testIntegration = async (integrationKey: string) => {
    setTestingIntegration(integrationKey)
    try {
      const response = await fetch('/api/super-admin/integrations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ integration: integrationKey }),
      })

      const result = await response.json()
      
      if (result.success) {
        toast.success(result.message)
      } else {
        toast.error(result.message)
      }
    } catch (error) {
      console.error('Error testing integration:', error)
      toast.error('Failed to test integration')
    } finally {
      setTestingIntegration(null)
    }
  }

  const saveIntegrationConfig = async (data: IntegrationConfigData) => {
    try {
      const response = await fetch('/api/super-admin/integrations/config', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (response.ok) {
        toast.success('Integration configuration saved successfully!')
        setEditingIntegration(null)
        fetchIntegrations() // Refresh status
      } else {
        const error = await response.json()
        throw new Error(error.error || 'Failed to save configuration')
      }
    } catch (error) {
      console.error('Error saving integration config:', error)
      toast.error('Failed to save integration configuration')
    }
  }

  useEffect(() => {
    fetchIntegrations()
    fetchIntegrationConfig()
  }, [])

  const getIntegrationIcon = (key: string) => {
    switch (key) {
      case 'stripe':
        return CreditCard
      case 'mailgun':
        return Mail
      case 'slack':
        return Bell
      case 'webhooks':
        return Zap
      default:
        return Key
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected':
      case 'active':
        return 'text-green-600 border-green-200'
      case 'disconnected':
        return 'text-gray-600 border-gray-200'
      default:
        return 'text-yellow-600 border-yellow-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
      case 'active':
        return CheckCircle
      case 'disconnected':
        return XCircle
      default:
        return RefreshCw
    }
  }

  const toggleSecretVisibility = (field: string) => {
    setShowSecrets(prev => ({
      ...prev,
      [field]: !prev[field]
    }))
  }

  const renderIntegrationConfig = (integrationKey: string) => {
    switch (integrationKey) {
      case 'stripe':
        return (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label>Enable Stripe</Label>
              <Switch
                checked={watch('stripe.enabled')}
                onCheckedChange={(checked) => setValue('stripe.enabled', checked)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="stripe-publishable">Publishable Key</Label>
              <div className="relative">
                <Input
                  id="stripe-publishable"
                  type={showSecrets['stripe-publishable'] ? 'text' : 'password'}
                  {...register('stripe.publishableKey')}
                  placeholder="pk_test_..."
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2"
                  onClick={() => toggleSecretVisibility('stripe-publishable')}
                >
                  {showSecrets['stripe-publishable'] ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="stripe-secret">Secret Key</Label>
              <div className="relative">
                <Input
                  id="stripe-secret"
                  type={showSecrets['stripe-secret'] ? 'text' : 'password'}
                  {...register('stripe.secretKey')}
                  placeholder="sk_test_..."
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2"
                  onClick={() => toggleSecretVisibility('stripe-secret')}
                >
                  {showSecrets['stripe-secret'] ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="stripe-webhook">Webhook Secret</Label>
              <div className="relative">
                <Input
                  id="stripe-webhook"
                  type={showSecrets['stripe-webhook'] ? 'text' : 'password'}
                  {...register('stripe.webhookSecret')}
                  placeholder="whsec_..."
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2"
                  onClick={() => toggleSecretVisibility('stripe-webhook')}
                >
                  {showSecrets['stripe-webhook'] ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <Label>Test Mode</Label>
              <Switch
                checked={watch('stripe.testMode')}
                onCheckedChange={(checked) => setValue('stripe.testMode', checked)}
              />
            </div>
          </div>
        )

      case 'mailgun':
        return (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label>Enable Mailgun</Label>
              <Switch
                checked={watch('mailgun.enabled')}
                onCheckedChange={(checked) => setValue('mailgun.enabled', checked)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="mailgun-api">API Key</Label>
              <div className="relative">
                <Input
                  id="mailgun-api"
                  type={showSecrets['mailgun-api'] ? 'text' : 'password'}
                  {...register('mailgun.apiKey')}
                  placeholder="key-..."
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2"
                  onClick={() => toggleSecretVisibility('mailgun-api')}
                >
                  {showSecrets['mailgun-api'] ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="mailgun-domain">Domain</Label>
              <Input
                id="mailgun-domain"
                {...register('mailgun.domain')}
                placeholder="mg.yourdomain.com"
              />
            </div>

            <div className="space-y-2">
              <Label>Region</Label>
              <div className="flex space-x-4">
                <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    value="us"
                    {...register('mailgun.region')}
                  />
                  <span>US</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    value="eu"
                    {...register('mailgun.region')}
                  />
                  <span>EU</span>
                </label>
              </div>
            </div>
          </div>
        )

      default:
        return <div>Configuration not available for this integration.</div>
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Key className="h-5 w-5 text-orange-600" />
            <span>Third-Party Integrations</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2">Loading integrations...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Key className="h-5 w-5 text-orange-600" />
            <span>Third-Party Integrations</span>
          </div>
          <Button variant="outline" size="sm" onClick={fetchIntegrations}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {integrations && Object.entries(integrations).map(([key, integration]) => {
            const Icon = getIntegrationIcon(key)
            const StatusIcon = getStatusIcon(integration.status)
            const isTestingThis = testingIntegration === key

            return (
              <div key={key} className="p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <Icon className="h-5 w-5 text-blue-600" />
                    <div>
                      <p className="font-medium">{integration.name}</p>
                      <p className="text-sm text-gray-500">{integration.description}</p>
                    </div>
                  </div>
                  <StatusIcon className={`h-4 w-4 ${getStatusColor(integration.status).split(' ')[0]}`} />
                </div>
                
                <div className="flex items-center justify-between mb-3">
                  <Badge variant="outline" className={getStatusColor(integration.status)}>
                    {integration.status === 'connected' ? 'Connected' : 
                     integration.status === 'active' ? 'Active' : 'Disconnected'}
                  </Badge>
                </div>

                <div className="flex items-center justify-between space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => testIntegration(key)}
                    disabled={isTestingThis}
                  >
                    {isTestingThis ? (
                      <Loader2 className="h-3 w-3 animate-spin" />
                    ) : (
                      'Test'
                    )}
                  </Button>

                  <Dialog>
                    <DialogTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <Settings className="h-3 w-3 mr-1" />
                        Configure
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-md">
                      <DialogHeader>
                        <DialogTitle className="flex items-center space-x-2">
                          <Icon className="h-5 w-5" />
                          <span>Configure {integration.name}</span>
                        </DialogTitle>
                        <DialogDescription>
                          Configure the settings for {integration.name} integration.
                        </DialogDescription>
                      </DialogHeader>
                      
                      <form onSubmit={handleSubmit(saveIntegrationConfig)}>
                        {renderIntegrationConfig(key)}
                        
                        <DialogFooter className="mt-6">
                          <Button type="submit">
                            <Save className="h-4 w-4 mr-2" />
                            Save Configuration
                          </Button>
                        </DialogFooter>
                      </form>
                    </DialogContent>
                  </Dialog>
                </div>
                
                <p className="text-xs text-gray-400 mt-2">
                  Last checked: {new Date(integration.lastChecked).toLocaleTimeString()}
                </p>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
