'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { toast } from 'react-hot-toast'
import {
  CreditCard,
  Mail,
  Bell,
  Zap,
  Key,
  RefreshCw,
  CheckCircle,
  XCircle,
  Loader2,
  Settings,
  Save,
  Edit,
  Eye,
  EyeOff,
  Bot,
  Brain,
  Server
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from '@/components/ui/tabs'

const integrationConfigSchema = z.object({
  stripe: z.object({
    enabled: z.boolean(),
    publishableKey: z.string().optional(),
    secretKey: z.string().optional(),
    webhookSecret: z.string().optional(),
    testMode: z.boolean()
  }),
  mailgun: z.object({
    enabled: z.boolean(),
    apiKey: z.string().optional(),
    domain: z.string().optional(),
    region: z.enum(['us', 'eu']).optional()
  }),
  smtp: z.object({
    enabled: z.boolean(),
    host: z.string().optional(),
    port: z.number().min(1).max(65535).optional(),
    username: z.string().optional(),
    password: z.string().optional(),
    secure: z.boolean().optional(),
    fromEmail: z.string().email().optional(),
    fromName: z.string().optional()
  }),
  openai: z.object({
    enabled: z.boolean(),
    apiKey: z.string().optional(),
    model: z.enum(['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo', 'gpt-4o']).optional(),
    maxTokens: z.number().min(1).max(4000).optional(),
    temperature: z.number().min(0).max(2).optional(),
    organizationId: z.string().optional()
  }),
  grok: z.object({
    enabled: z.boolean(),
    apiKey: z.string().optional(),
    model: z.enum(['grok-beta', 'grok-1', 'grok-2']).optional(),
    maxTokens: z.number().min(1).max(4000).optional(),
    temperature: z.number().min(0).max(2).optional()
  }),
  slack: z.object({
    enabled: z.boolean(),
    webhookUrl: z.string().optional(),
    channel: z.string().optional(),
    botToken: z.string().optional()
  }),
  webhooks: z.object({
    enabled: z.boolean(),
    defaultUrl: z.string().optional(),
    secret: z.string().optional(),
    retryAttempts: z.number().min(1).max(10).optional()
  })
})

type IntegrationConfigData = z.infer<typeof integrationConfigSchema>

interface Integration {
  name: string
  description: string
  status: 'connected' | 'disconnected' | 'active'
  configured: boolean
  lastChecked: string
}

interface IntegrationsData {
  stripe: Integration
  mailgun: Integration
  smtp: Integration
  openai: Integration
  grok: Integration
  slack: Integration
  webhooks: Integration
}

export function IntegrationsConfig() {
  const [integrations, setIntegrations] = useState<IntegrationsData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [testingIntegration, setTestingIntegration] = useState<string | null>(null)
  const [editingIntegration, setEditingIntegration] = useState<string | null>(null)
  const [showSecrets, setShowSecrets] = useState<Record<string, boolean>>({})

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue
  } = useForm<IntegrationConfigData>({
    resolver: zodResolver(integrationConfigSchema),
    defaultValues: {
      stripe: {
        enabled: true,
        testMode: true
      },
      mailgun: {
        enabled: true,
        region: 'us'
      },
      smtp: {
        enabled: false,
        port: 587,
        secure: false,
        fromName: 'SaaS Platform'
      },
      openai: {
        enabled: false,
        model: 'gpt-3.5-turbo',
        maxTokens: 1000,
        temperature: 0.7
      },
      grok: {
        enabled: false,
        model: 'grok-beta',
        maxTokens: 1000,
        temperature: 0.7
      },
      slack: {
        enabled: false
      },
      webhooks: {
        enabled: true,
        retryAttempts: 3
      }
    }
  })

  const fetchIntegrations = async () => {
    try {
      const response = await fetch('/api/super-admin/integrations')
      if (response.ok) {
        const data = await response.json()
        setIntegrations(data)
      } else {
        throw new Error('Failed to fetch integrations')
      }
    } catch (error) {
      console.error('Error fetching integrations:', error)
      toast.error('Failed to load integration status')
    } finally {
      setIsLoading(false)
    }
  }

  const fetchIntegrationConfig = async () => {
    try {
      const response = await fetch('/api/super-admin/integrations/config')
      if (response.ok) {
        const config = await response.json()
        reset(config)
      }
    } catch (error) {
      console.error('Error fetching integration config:', error)
    }
  }

  const testIntegration = async (integrationKey: string) => {
    setTestingIntegration(integrationKey)
    try {
      const response = await fetch('/api/super-admin/integrations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ integration: integrationKey }),
      })

      const result = await response.json()
      
      if (result.success) {
        toast.success(result.message)
      } else {
        toast.error(result.message)
      }
    } catch (error) {
      console.error('Error testing integration:', error)
      toast.error('Failed to test integration')
    } finally {
      setTestingIntegration(null)
    }
  }

  const saveIntegrationConfig = async (data: IntegrationConfigData) => {
    try {
      const response = await fetch('/api/super-admin/integrations/config', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (response.ok) {
        toast.success('Integration configuration saved successfully!')
        setEditingIntegration(null)
        fetchIntegrations() // Refresh status
      } else {
        const error = await response.json()
        throw new Error(error.error || 'Failed to save configuration')
      }
    } catch (error) {
      console.error('Error saving integration config:', error)
      toast.error('Failed to save integration configuration')
    }
  }

  useEffect(() => {
    fetchIntegrations()
    fetchIntegrationConfig()
  }, [])

  const getIntegrationIcon = (key: string) => {
    switch (key) {
      case 'stripe':
        return CreditCard
      case 'mailgun':
        return Mail
      case 'smtp':
        return Server
      case 'openai':
        return Bot
      case 'grok':
        return Brain
      case 'slack':
        return Bell
      case 'webhooks':
        return Zap
      default:
        return Key
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected':
      case 'active':
        return 'text-green-600 border-green-200'
      case 'disconnected':
        return 'text-gray-600 border-gray-200'
      default:
        return 'text-yellow-600 border-yellow-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
      case 'active':
        return CheckCircle
      case 'disconnected':
        return XCircle
      default:
        return RefreshCw
    }
  }

  const toggleSecretVisibility = (field: string) => {
    setShowSecrets(prev => ({
      ...prev,
      [field]: !prev[field]
    }))
  }

  const renderIntegrationConfig = (integrationKey: string) => {
    switch (integrationKey) {
      case 'stripe':
        return (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label>Enable Stripe</Label>
              <Switch
                checked={watch('stripe.enabled')}
                onCheckedChange={(checked) => setValue('stripe.enabled', checked)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="stripe-publishable">Publishable Key</Label>
              <div className="relative">
                <Input
                  id="stripe-publishable"
                  type={showSecrets['stripe-publishable'] ? 'text' : 'password'}
                  {...register('stripe.publishableKey')}
                  placeholder="pk_test_..."
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2"
                  onClick={() => toggleSecretVisibility('stripe-publishable')}
                >
                  {showSecrets['stripe-publishable'] ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="stripe-secret">Secret Key</Label>
              <div className="relative">
                <Input
                  id="stripe-secret"
                  type={showSecrets['stripe-secret'] ? 'text' : 'password'}
                  {...register('stripe.secretKey')}
                  placeholder="sk_test_..."
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2"
                  onClick={() => toggleSecretVisibility('stripe-secret')}
                >
                  {showSecrets['stripe-secret'] ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="stripe-webhook">Webhook Secret</Label>
              <div className="relative">
                <Input
                  id="stripe-webhook"
                  type={showSecrets['stripe-webhook'] ? 'text' : 'password'}
                  {...register('stripe.webhookSecret')}
                  placeholder="whsec_..."
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2"
                  onClick={() => toggleSecretVisibility('stripe-webhook')}
                >
                  {showSecrets['stripe-webhook'] ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <Label>Test Mode</Label>
              <Switch
                checked={watch('stripe.testMode')}
                onCheckedChange={(checked) => setValue('stripe.testMode', checked)}
              />
            </div>
          </div>
        )

      case 'mailgun':
        return (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label>Enable Mailgun</Label>
              <Switch
                checked={watch('mailgun.enabled')}
                onCheckedChange={(checked) => setValue('mailgun.enabled', checked)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="mailgun-api">API Key</Label>
              <div className="relative">
                <Input
                  id="mailgun-api"
                  type={showSecrets['mailgun-api'] ? 'text' : 'password'}
                  {...register('mailgun.apiKey')}
                  placeholder="key-..."
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2"
                  onClick={() => toggleSecretVisibility('mailgun-api')}
                >
                  {showSecrets['mailgun-api'] ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="mailgun-domain">Domain</Label>
              <Input
                id="mailgun-domain"
                {...register('mailgun.domain')}
                placeholder="mg.yourdomain.com"
              />
            </div>

            <div className="space-y-2">
              <Label>Region</Label>
              <div className="flex space-x-4">
                <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    value="us"
                    {...register('mailgun.region')}
                  />
                  <span>US</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    value="eu"
                    {...register('mailgun.region')}
                  />
                  <span>EU</span>
                </label>
              </div>
            </div>
          </div>
        )

      case 'smtp':
        return (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label>Enable SMTP Email</Label>
              <Switch
                checked={watch('smtp.enabled')}
                onCheckedChange={(checked) => setValue('smtp.enabled', checked)}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="smtp-host">SMTP Host</Label>
                <Input
                  id="smtp-host"
                  {...register('smtp.host')}
                  placeholder="smtp.gmail.com"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="smtp-port">Port</Label>
                <Input
                  id="smtp-port"
                  type="number"
                  {...register('smtp.port', { valueAsNumber: true })}
                  placeholder="587"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="smtp-username">Username</Label>
              <Input
                id="smtp-username"
                {...register('smtp.username')}
                placeholder="<EMAIL>"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="smtp-password">Password</Label>
              <div className="relative">
                <Input
                  id="smtp-password"
                  type={showSecrets['smtp-password'] ? 'text' : 'password'}
                  {...register('smtp.password')}
                  placeholder="your-app-password"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2"
                  onClick={() => toggleSecretVisibility('smtp-password')}
                >
                  {showSecrets['smtp-password'] ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="smtp-from-email">From Email</Label>
                <Input
                  id="smtp-from-email"
                  type="email"
                  {...register('smtp.fromEmail')}
                  placeholder="<EMAIL>"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="smtp-from-name">From Name</Label>
                <Input
                  id="smtp-from-name"
                  {...register('smtp.fromName')}
                  placeholder="Your Company"
                />
              </div>
            </div>

            <div className="flex items-center justify-between">
              <Label>Use SSL/TLS</Label>
              <Switch
                checked={watch('smtp.secure')}
                onCheckedChange={(checked) => setValue('smtp.secure', checked)}
              />
            </div>
          </div>
        )

      case 'openai':
        return (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label>Enable OpenAI</Label>
              <Switch
                checked={watch('openai.enabled')}
                onCheckedChange={(checked) => setValue('openai.enabled', checked)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="openai-api">API Key</Label>
              <div className="relative">
                <Input
                  id="openai-api"
                  type={showSecrets['openai-api'] ? 'text' : 'password'}
                  {...register('openai.apiKey')}
                  placeholder="sk-..."
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2"
                  onClick={() => toggleSecretVisibility('openai-api')}
                >
                  {showSecrets['openai-api'] ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="openai-model">Model</Label>
              <select
                id="openai-model"
                {...register('openai.model')}
                className="w-full p-2 border rounded-md"
              >
                <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                <option value="gpt-4">GPT-4</option>
                <option value="gpt-4-turbo">GPT-4 Turbo</option>
                <option value="gpt-4o">GPT-4o</option>
              </select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="openai-tokens">Max Tokens</Label>
                <Input
                  id="openai-tokens"
                  type="number"
                  {...register('openai.maxTokens', { valueAsNumber: true })}
                  placeholder="1000"
                  min="1"
                  max="4000"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="openai-temp">Temperature</Label>
                <Input
                  id="openai-temp"
                  type="number"
                  step="0.1"
                  {...register('openai.temperature', { valueAsNumber: true })}
                  placeholder="0.7"
                  min="0"
                  max="2"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="openai-org">Organization ID (Optional)</Label>
              <Input
                id="openai-org"
                {...register('openai.organizationId')}
                placeholder="org-..."
              />
            </div>
          </div>
        )

      case 'grok':
        return (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label>Enable Grok AI</Label>
              <Switch
                checked={watch('grok.enabled')}
                onCheckedChange={(checked) => setValue('grok.enabled', checked)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="grok-api">API Key</Label>
              <div className="relative">
                <Input
                  id="grok-api"
                  type={showSecrets['grok-api'] ? 'text' : 'password'}
                  {...register('grok.apiKey')}
                  placeholder="xai-..."
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2"
                  onClick={() => toggleSecretVisibility('grok-api')}
                >
                  {showSecrets['grok-api'] ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="grok-model">Model</Label>
              <select
                id="grok-model"
                {...register('grok.model')}
                className="w-full p-2 border rounded-md"
              >
                <option value="grok-beta">Grok Beta</option>
                <option value="grok-1">Grok-1</option>
                <option value="grok-2">Grok-2</option>
              </select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="grok-tokens">Max Tokens</Label>
                <Input
                  id="grok-tokens"
                  type="number"
                  {...register('grok.maxTokens', { valueAsNumber: true })}
                  placeholder="1000"
                  min="1"
                  max="4000"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="grok-temp">Temperature</Label>
                <Input
                  id="grok-temp"
                  type="number"
                  step="0.1"
                  {...register('grok.temperature', { valueAsNumber: true })}
                  placeholder="0.7"
                  min="0"
                  max="2"
                />
              </div>
            </div>
          </div>
        )

      default:
        return <div>Configuration not available for this integration.</div>
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Key className="h-5 w-5 text-orange-600" />
            <span>Third-Party Integrations</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2">Loading integrations...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Key className="h-5 w-5 text-orange-600" />
            <span>Third-Party Integrations</span>
          </div>
          <Button variant="outline" size="sm" onClick={fetchIntegrations}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {integrations && Object.entries(integrations).map(([key, integration]) => {
            const Icon = getIntegrationIcon(key)
            const StatusIcon = getStatusIcon(integration.status)
            const isTestingThis = testingIntegration === key

            return (
              <div key={key} className="p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <Icon className="h-5 w-5 text-blue-600" />
                    <div>
                      <p className="font-medium">{integration.name}</p>
                      <p className="text-sm text-gray-500">{integration.description}</p>
                    </div>
                  </div>
                  <StatusIcon className={`h-4 w-4 ${getStatusColor(integration.status).split(' ')[0]}`} />
                </div>
                
                <div className="flex items-center justify-between mb-3">
                  <Badge variant="outline" className={getStatusColor(integration.status)}>
                    {integration.status === 'connected' ? 'Connected' : 
                     integration.status === 'active' ? 'Active' : 'Disconnected'}
                  </Badge>
                </div>

                <div className="flex items-center justify-between space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => testIntegration(key)}
                    disabled={isTestingThis}
                  >
                    {isTestingThis ? (
                      <Loader2 className="h-3 w-3 animate-spin" />
                    ) : (
                      'Test'
                    )}
                  </Button>

                  <Dialog>
                    <DialogTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <Settings className="h-3 w-3 mr-1" />
                        Configure
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-md">
                      <DialogHeader>
                        <DialogTitle className="flex items-center space-x-2">
                          <Icon className="h-5 w-5" />
                          <span>Configure {integration.name}</span>
                        </DialogTitle>
                        <DialogDescription>
                          Configure the settings for {integration.name} integration.
                        </DialogDescription>
                      </DialogHeader>
                      
                      <form onSubmit={handleSubmit(saveIntegrationConfig)}>
                        {renderIntegrationConfig(key)}
                        
                        <DialogFooter className="mt-6">
                          <Button type="submit">
                            <Save className="h-4 w-4 mr-2" />
                            Save Configuration
                          </Button>
                        </DialogFooter>
                      </form>
                    </DialogContent>
                  </Dialog>
                </div>
                
                <p className="text-xs text-gray-400 mt-2">
                  Last checked: {new Date(integration.lastChecked).toLocaleTimeString()}
                </p>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
