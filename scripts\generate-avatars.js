#!/usr/bin/env node

/**
 * Generate placeholder avatar images for demo users
 */

const fs = require('fs')
const path = require('path')

// Create simple SVG avatars
const avatars = [
  {
    name: 'mi<PERSON><PERSON>',
    initials: '<PERSON>',
    color: '#3B82F6'
  },
  {
    name: 'emily',
    initials: '<PERSON>', 
    color: '#10B981'
  },
  {
    name: 'david',
    initials: 'D<PERSON>',
    color: '#F59E0B'
  },
  {
    name: 'lisa',
    initials: 'L<PERSON>',
    color: '#EF4444'
  },
  {
    name: 'jam<PERSON>',
    initials: 'J<PERSON>',
    color: '#8B5CF6'
  },
  {
    name: 'sarah',
    initials: 'SJ',
    color: '#EC4899'
  }
]

function generateSVGAvatar(initials, color) {
  return `<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <circle cx="50" cy="50" r="50" fill="${color}"/>
  <text x="50" y="50" font-family="Arial, sans-serif" font-size="32" font-weight="bold" 
        text-anchor="middle" dominant-baseline="central" fill="white">${initials}</text>
</svg>`
}

// Create avatars directory if it doesn't exist
const avatarsDir = path.join(__dirname, '..', 'public', 'avatars')
if (!fs.existsSync(avatarsDir)) {
  fs.mkdirSync(avatarsDir, { recursive: true })
}

// Generate avatar files
avatars.forEach(avatar => {
  const svgContent = generateSVGAvatar(avatar.initials, avatar.color)
  const filePath = path.join(avatarsDir, `${avatar.name}.svg`)
  
  fs.writeFileSync(filePath, svgContent)
  console.log(`✅ Generated avatar: ${avatar.name}.svg`)
})

console.log('🎉 All avatar images generated successfully!')
