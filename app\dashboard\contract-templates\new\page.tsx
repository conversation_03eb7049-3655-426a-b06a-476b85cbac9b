import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { TemplateForm } from '@/components/contract-templates/template-form'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'

export default async function NewTemplatePage() {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href="/dashboard/contract-templates">
          <Button variant="ghost" >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Templates
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Create Contract Template</h1>
          <p className="text-gray-600 mt-1">
            Create a reusable template for generating contracts
          </p>
        </div>
      </div>

      {/* Template Form */}
      <TemplateForm mode="create" />
    </div>
  )
}
