import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { stripe, stripeConfig } from '@/lib/stripe'
import { prisma } from '@/lib/prisma'
import {
  handleSuccessfulPayment,
  processSubscriptionUpdate,
  processSubscriptionCancellation,
  createPaymentFromInvoice
} from '@/lib/payment-utils'
import <PERSON><PERSON> from 'stripe'

export async function POST(request: NextRequest) {
  const body = await request.text()
  const signature = headers().get('stripe-signature')

  if (!signature) {
    return NextResponse.json(
      { error: 'No signature provided' },
      { status: 400 }
    )
  }

  let event: Stripe.Event

  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      stripeConfig.webhookSecret
    )
  } catch (error) {
    console.error('Webhook signature verification failed:', error)
    return NextResponse.json(
      { error: 'Invalid signature' },
      { status: 400 }
    )
  }

  try {
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutSessionCompleted(event.data.object as Stripe.Checkout.Session)
        break

      case 'invoice.payment_succeeded':
        await createPaymentFromInvoice(event.data.object as Stripe.Invoice, 'SUCCEEDED')
        break

      case 'invoice.payment_failed':
        await createPaymentFromInvoice(event.data.object as Stripe.Invoice, 'FAILED')
        break

      case 'customer.subscription.updated':
        await processSubscriptionUpdate(event.data.object as Stripe.Subscription)
        break

      case 'customer.subscription.deleted':
        await processSubscriptionCancellation(event.data.object as Stripe.Subscription)
        break

      case 'customer.subscription.trial_will_end':
        await handleTrialWillEnd(event.data.object as Stripe.Subscription)
        break

      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return NextResponse.json({ received: true })

  } catch (error) {
    console.error('Webhook handler error:', error)
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500 }
    )
  }
}

async function handleCheckoutSessionCompleted(session: Stripe.Checkout.Session) {
  console.log('Checkout session completed:', session.id)
  await handleSuccessfulPayment(session.id)
}



async function handleTrialWillEnd(subscription: Stripe.Subscription) {
  console.log('Trial will end:', subscription.id)

  const subscriptionRecord = await prisma.subscription.findFirst({
    where: { stripeSubscriptionId: subscription.id },
    include: { company: { include: { owner: true } } }
  })

  if (!subscriptionRecord) return

  // Log activity
  await prisma.activity.create({
    data: {
      type: 'TRIAL',
      title: 'Trial ending soon',
      description: 'Free trial will end in 3 days',
      companyId: subscriptionRecord.companyId,
      createdById: subscriptionRecord.company.ownerId
    }
  })

  // TODO: Send trial ending notification email
}
