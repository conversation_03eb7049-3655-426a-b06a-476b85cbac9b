import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { stripe, stripeConfig } from '@/lib/stripe'
import { prisma } from '@/lib/prisma'
import Stripe from 'stripe'

export async function POST(request: NextRequest) {
  const body = await request.text()
  const signature = headers().get('stripe-signature')

  if (!signature) {
    return NextResponse.json(
      { error: 'No signature provided' },
      { status: 400 }
    )
  }

  let event: Stripe.Event

  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      stripeConfig.webhookSecret
    )
  } catch (error) {
    console.error('Webhook signature verification failed:', error)
    return NextResponse.json(
      { error: 'Invalid signature' },
      { status: 400 }
    )
  }

  try {
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutSessionCompleted(event.data.object as Stripe.Checkout.Session)
        break

      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event.data.object as Stripe.Invoice)
        break

      case 'invoice.payment_failed':
        await handleInvoicePaymentFailed(event.data.object as Stripe.Invoice)
        break

      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object as Stripe.Subscription)
        break

      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object as Stripe.Subscription)
        break

      case 'customer.subscription.trial_will_end':
        await handleTrialWillEnd(event.data.object as Stripe.Subscription)
        break

      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return NextResponse.json({ received: true })

  } catch (error) {
    console.error('Webhook handler error:', error)
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500 }
    )
  }
}

async function handleCheckoutSessionCompleted(session: Stripe.Checkout.Session) {
  console.log('Checkout session completed:', session.id)

  if (!session.metadata?.companyId) {
    console.error('No company ID in session metadata')
    return
  }

  const companyId = session.metadata.companyId

  // Retrieve the subscription
  const subscription = await stripe.subscriptions.retrieve(session.subscription as string)

  // Update company status
  await prisma.company.update({
    where: { id: companyId },
    data: {
      status: 'ACTIVE',
      stripeCustomerId: session.customer as string
    }
  })

  // Create or update subscription record
  await prisma.subscription.upsert({
    where: { stripeSubscriptionId: subscription.id },
    create: {
      companyId,
      stripeSubscriptionId: subscription.id,
      status: subscription.status.toUpperCase(),
      plan: getSubscriptionPlan(subscription),
      amount: subscription.items.data[0].price.unit_amount! / 100,
      currency: subscription.currency.toUpperCase(),
      interval: subscription.items.data[0].price.recurring!.interval.toUpperCase(),
      currentPeriodStart: new Date(subscription.current_period_start * 1000),
      currentPeriodEnd: new Date(subscription.current_period_end * 1000),
      cancelAtPeriodEnd: subscription.cancel_at_period_end
    },
    update: {
      status: subscription.status.toUpperCase(),
      currentPeriodStart: new Date(subscription.current_period_start * 1000),
      currentPeriodEnd: new Date(subscription.current_period_end * 1000),
      cancelAtPeriodEnd: subscription.cancel_at_period_end
    }
  })

  // Log activity
  await prisma.activity.create({
    data: {
      type: 'SUBSCRIPTION',
      title: 'Subscription activated',
      description: 'Subscription successfully activated via Stripe',
      companyId,
      createdById: session.metadata.userId!
    }
  })
}

async function handleInvoicePaymentSucceeded(invoice: Stripe.Invoice) {
  console.log('Invoice payment succeeded:', invoice.id)

  if (!invoice.subscription) return

  const subscription = await prisma.subscription.findFirst({
    where: { stripeSubscriptionId: invoice.subscription as string }
  })

  if (!subscription) {
    console.error('Subscription not found for invoice:', invoice.id)
    return
  }

  // Create payment record
  await prisma.payment.create({
    data: {
      subscriptionId: subscription.id,
      companyId: subscription.companyId,
      amount: invoice.amount_paid / 100,
      currency: invoice.currency.toUpperCase(),
      status: 'SUCCEEDED',
      description: invoice.description || 'Subscription payment',
      stripePaymentIntentId: invoice.payment_intent as string,
      paidAt: new Date(invoice.status_transitions.paid_at! * 1000)
    }
  })

  // Update company status if needed
  await prisma.company.update({
    where: { id: subscription.companyId },
    data: { status: 'ACTIVE' }
  })

  // Log activity
  await prisma.activity.create({
    data: {
      type: 'PAYMENT',
      title: 'Payment received',
      description: `Payment of $${invoice.amount_paid / 100} received`,
      companyId: subscription.companyId,
      createdById: subscription.companyId // Use company owner
    }
  })
}

async function handleInvoicePaymentFailed(invoice: Stripe.Invoice) {
  console.log('Invoice payment failed:', invoice.id)

  if (!invoice.subscription) return

  const subscription = await prisma.subscription.findFirst({
    where: { stripeSubscriptionId: invoice.subscription as string }
  })

  if (!subscription) return

  // Create failed payment record
  await prisma.payment.create({
    data: {
      subscriptionId: subscription.id,
      companyId: subscription.companyId,
      amount: invoice.amount_due / 100,
      currency: invoice.currency.toUpperCase(),
      status: 'FAILED',
      description: invoice.description || 'Subscription payment failed',
      stripePaymentIntentId: invoice.payment_intent as string
    }
  })

  // Log activity
  await prisma.activity.create({
    data: {
      type: 'PAYMENT',
      title: 'Payment failed',
      description: `Payment of $${invoice.amount_due / 100} failed`,
      companyId: subscription.companyId,
      createdById: subscription.companyId
    }
  })

  // TODO: Send payment failed notification email
}

async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  console.log('Subscription updated:', subscription.id)

  await prisma.subscription.updateMany({
    where: { stripeSubscriptionId: subscription.id },
    data: {
      status: subscription.status.toUpperCase(),
      plan: getSubscriptionPlan(subscription),
      amount: subscription.items.data[0].price.unit_amount! / 100,
      currentPeriodStart: new Date(subscription.current_period_start * 1000),
      currentPeriodEnd: new Date(subscription.current_period_end * 1000),
      cancelAtPeriodEnd: subscription.cancel_at_period_end
    }
  })
}

async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  console.log('Subscription deleted:', subscription.id)

  const subscriptionRecord = await prisma.subscription.findFirst({
    where: { stripeSubscriptionId: subscription.id }
  })

  if (!subscriptionRecord) return

  // Update subscription status
  await prisma.subscription.update({
    where: { id: subscriptionRecord.id },
    data: { status: 'CANCELLED' }
  })

  // Update company status
  await prisma.company.update({
    where: { id: subscriptionRecord.companyId },
    data: { status: 'CANCELLED' }
  })

  // Log activity
  await prisma.activity.create({
    data: {
      type: 'SUBSCRIPTION',
      title: 'Subscription cancelled',
      description: 'Subscription was cancelled',
      companyId: subscriptionRecord.companyId,
      createdById: subscriptionRecord.companyId
    }
  })
}

async function handleTrialWillEnd(subscription: Stripe.Subscription) {
  console.log('Trial will end:', subscription.id)

  const subscriptionRecord = await prisma.subscription.findFirst({
    where: { stripeSubscriptionId: subscription.id },
    include: { company: { include: { owner: true } } }
  })

  if (!subscriptionRecord) return

  // Log activity
  await prisma.activity.create({
    data: {
      type: 'TRIAL',
      title: 'Trial ending soon',
      description: 'Free trial will end in 3 days',
      companyId: subscriptionRecord.companyId,
      createdById: subscriptionRecord.company.ownerId
    }
  })

  // TODO: Send trial ending notification email
}

function getSubscriptionPlan(subscription: Stripe.Subscription): string {
  const priceId = subscription.items.data[0].price.id
  
  // Map Stripe price IDs to plan names
  // In a real implementation, you'd store this mapping in your database
  // or use Stripe's lookup_key feature
  
  if (priceId.includes('starter')) return 'STARTER'
  if (priceId.includes('professional')) return 'PROFESSIONAL'
  if (priceId.includes('enterprise')) return 'ENTERPRISE'
  
  return 'PROFESSIONAL' // Default fallback
}
