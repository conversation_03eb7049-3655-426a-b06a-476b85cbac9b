'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Users, 
  FileText,
  Receipt,

  TrendingUp,
  Clock,
  CheckCircle,
  AlertTriangle
} from 'lucide-react'

interface PipelineOverviewProps {
  data: {
    leads: Array<{
      status: string
      _count: number
    }>
    quotations: Array<{
      status: string
      _count: number
    }>
    invoices: Array<{
      status: string
      _count: number
    }>
    contracts: Array<{
      status: string
      _count: number
    }>
  }
}

export function PipelineOverview({ data }: PipelineOverviewProps) {
  // Process lead data
  const leadMetrics = {
    total: data.leads.reduce((sum, lead) => sum + lead._count, 0),
    new: data.leads.find(l => l.status === 'NEW')?._count || 0,
    contacted: data.leads.find(l => l.status === 'CONTACTED')?._count || 0,
    qualified: data.leads.find(l => l.status === 'QUALIFIED')?._count || 0,
    proposal: data.leads.find(l => l.status === 'PROPOSAL')?._count || 0,
    negotiation: data.leads.find(l => l.status === 'NEGOTIATION')?._count || 0,
    won: data.leads.find(l => l.status === 'WON')?._count || 0,
    lost: data.leads.find(l => l.status === 'LOST')?._count || 0,
  }

  // Process quotation data
  const quotationMetrics = {
    total: data.quotations.reduce((sum, quot) => sum + quot._count, 0),
    draft: data.quotations.find(q => q.status === 'DRAFT')?._count || 0,
    sent: data.quotations.find(q => q.status === 'SENT')?._count || 0,
    viewed: data.quotations.find(q => q.status === 'VIEWED')?._count || 0,
    accepted: data.quotations.find(q => q.status === 'ACCEPTED')?._count || 0,
    rejected: data.quotations.find(q => q.status === 'REJECTED')?._count || 0,
    expired: data.quotations.find(q => q.status === 'EXPIRED')?._count || 0,
  }

  // Process invoice data
  const invoiceMetrics = {
    total: data.invoices.reduce((sum, inv) => sum + inv._count, 0),
    draft: data.invoices.find(i => i.status === 'DRAFT')?._count || 0,
    sent: data.invoices.find(i => i.status === 'SENT')?._count || 0,
    viewed: data.invoices.find(i => i.status === 'VIEWED')?._count || 0,
    paid: data.invoices.find(i => i.status === 'PAID')?._count || 0,
    overdue: data.invoices.find(i => i.status === 'OVERDUE')?._count || 0,
    cancelled: data.invoices.find(i => i.status === 'CANCELLED')?._count || 0,
  }

  // Process contract data
  const contractMetrics = {
    total: data.contracts.reduce((sum, cont) => sum + cont._count, 0),
    draft: data.contracts.find(c => c.status === 'DRAFT')?._count || 0,
    sent: data.contracts.find(c => c.status === 'SENT')?._count || 0,
    viewed: data.contracts.find(c => c.status === 'VIEWED')?._count || 0,
    signed: data.contracts.find(c => c.status === 'SIGNED')?._count || 0,
    executed: data.contracts.find(c => c.status === 'EXECUTED')?._count || 0,
    expired: data.contracts.find(c => c.status === 'EXPIRED')?._count || 0,
    cancelled: data.contracts.find(c => c.status === 'CANCELLED')?._count || 0,
  }

  // Calculate health scores
  const leadHealthScore = leadMetrics.total > 0 
    ? Math.round(((leadMetrics.qualified + leadMetrics.proposal + leadMetrics.negotiation) / leadMetrics.total) * 100)
    : 0

  const quotationHealthScore = quotationMetrics.total > 0
    ? Math.round(((quotationMetrics.sent + quotationMetrics.viewed) / quotationMetrics.total) * 100)
    : 0

  const invoiceHealthScore = invoiceMetrics.total > 0
    ? Math.round((invoiceMetrics.paid / invoiceMetrics.total) * 100)
    : 0

  const contractHealthScore = contractMetrics.total > 0
    ? Math.round(((contractMetrics.signed + contractMetrics.executed) / contractMetrics.total) * 100)
    : 0

  const pipelineStages = [
    {
      title: 'Leads',
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      total: leadMetrics.total,
      healthScore: leadHealthScore,
      stages: [
        { label: 'New', count: leadMetrics.new, color: 'bg-gray-100 text-gray-600' },
        { label: 'Contacted', count: leadMetrics.contacted, color: 'bg-blue-100 text-blue-600' },
        { label: 'Qualified', count: leadMetrics.qualified, color: 'bg-yellow-100 text-yellow-600' },
        { label: 'Proposal', count: leadMetrics.proposal, color: 'bg-orange-100 text-orange-600' },
        { label: 'Negotiation', count: leadMetrics.negotiation, color: 'bg-purple-100 text-purple-600' },
        { label: 'Won', count: leadMetrics.won, color: 'bg-green-100 text-green-600' },
        { label: 'Lost', count: leadMetrics.lost, color: 'bg-red-100 text-red-600' },
      ]
    },
    {
      title: 'Quotations',
      icon: FileText,
 Receipt,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      total: quotationMetrics.total,
      healthScore: quotationHealthScore,
      stages: [
        { label: 'Draft', count: quotationMetrics.draft, color: 'bg-gray-100 text-gray-600' },
        { label: 'Sent', count: quotationMetrics.sent, color: 'bg-blue-100 text-blue-600' },
        { label: 'Viewed', count: quotationMetrics.viewed, color: 'bg-yellow-100 text-yellow-600' },
        { label: 'Accepted', count: quotationMetrics.accepted, color: 'bg-green-100 text-green-600' },
        { label: 'Rejected', count: quotationMetrics.rejected, color: 'bg-red-100 text-red-600' },
        { label: 'Expired', count: quotationMetrics.expired, color: 'bg-orange-100 text-orange-600' },
      ]
    },
    {
      title: 'Invoices',
      icon: Receipt,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      total: invoiceMetrics.total,
      healthScore: invoiceHealthScore,
      stages: [
        { label: 'Draft', count: invoiceMetrics.draft, color: 'bg-gray-100 text-gray-600' },
        { label: 'Sent', count: invoiceMetrics.sent, color: 'bg-blue-100 text-blue-600' },
        { label: 'Viewed', count: invoiceMetrics.viewed, color: 'bg-yellow-100 text-yellow-600' },
        { label: 'Paid', count: invoiceMetrics.paid, color: 'bg-green-100 text-green-600' },
        { label: 'Overdue', count: invoiceMetrics.overdue, color: 'bg-red-100 text-red-600' },
        { label: 'Cancelled', count: invoiceMetrics.cancelled, color: 'bg-gray-100 text-gray-600' },
      ]
    },
    {
      title: 'Contracts',
      icon: FileText,
 Receipt,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-100',
      total: contractMetrics.total,
      healthScore: contractHealthScore,
      stages: [
        { label: 'Draft', count: contractMetrics.draft, color: 'bg-gray-100 text-gray-600' },
        { label: 'Sent', count: contractMetrics.sent, color: 'bg-blue-100 text-blue-600' },
        { label: 'Viewed', count: contractMetrics.viewed, color: 'bg-yellow-100 text-yellow-600' },
        { label: 'Signed', count: contractMetrics.signed, color: 'bg-green-100 text-green-600' },
        { label: 'Executed', count: contractMetrics.executed, color: 'bg-emerald-100 text-emerald-600' },
        { label: 'Expired', count: contractMetrics.expired, color: 'bg-red-100 text-red-600' },
        { label: 'Cancelled', count: contractMetrics.cancelled, color: 'bg-gray-100 text-gray-600' },
      ]
    }
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <TrendingUp className="h-5 w-5 text-blue-600" />
          <span>Sales Pipeline</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Pipeline Health Overview */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {pipelineStages.map((stage, index) => (
            <div key={index} className="text-center p-3 bg-gray-50 rounded-lg">
              <div className={`inline-flex items-center justify-center w-8 h-8 rounded-full ${stage.bgColor} mb-2`}>
                <stage.icon className={`h-4 w-4 ${stage.color}`} />
              </div>
              <p className="text-lg font-bold text-gray-900">{stage.total}</p>
              <p className="text-xs text-gray-600">{stage.title}</p>
              <div className="flex items-center justify-center mt-1">
                {stage.healthScore >= 70 ? (
                  <CheckCircle className="h-3 w-3 text-green-600" />
                ) : stage.healthScore >= 40 ? (
                  <Clock className="h-3 w-3 text-yellow-600" />
                ) : (
                  <AlertTriangle className="h-3 w-3 text-red-600" />
                )}
                <span className={`text-xs ml-1 ${
                  stage.healthScore >= 70 ? 'text-green-600' :
                  stage.healthScore >= 40 ? 'text-yellow-600' : 'text-red-600'
                }`}>
                  {stage.healthScore}%
                </span>
              </div>
            </div>
          ))}
        </div>

        {/* Detailed Pipeline Breakdown */}
        <div className="space-y-4">
          {pipelineStages.map((stage, stageIndex) => (
            <div key={stageIndex} className="space-y-2">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium text-gray-900 flex items-center space-x-2">
                  <stage.icon className={`h-4 w-4 ${stage.color}`} />
                  <span>{stage.title}</span>
                </h4>
                <span className="text-sm text-gray-500">Total: {stage.total}</span>
              </div>
              <div className="grid grid-cols-3 md:grid-cols-6 gap-2">
                {stage.stages.map((substage, substageIndex) => (
                  <div key={substageIndex} className={`p-2 rounded text-center ${substage.color}`}>
                    <p className="text-xs font-medium">{substage.label}</p>
                    <p className="text-sm font-bold">{substage.count}</p>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Pipeline Insights */}
        <div className="pt-4 border-t">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Pipeline Insights</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Active Opportunities</span>
                <span className="font-medium text-gray-900">
                  {leadMetrics.qualified + leadMetrics.proposal + leadMetrics.negotiation}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Pending Responses</span>
                <span className="font-medium text-gray-900">
                  {quotationMetrics.sent + quotationMetrics.viewed}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Awaiting Payment</span>
                <span className="font-medium text-gray-900">
                  {invoiceMetrics.sent + invoiceMetrics.viewed}
                </span>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Overdue Items</span>
                <span className={`font-medium ${invoiceMetrics.overdue > 0 ? 'text-red-600' : 'text-gray-900'}`}>
                  {invoiceMetrics.overdue + quotationMetrics.expired + contractMetrics.expired}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Completed Deals</span>
                <span className="font-medium text-green-600">
                  {leadMetrics.won + invoiceMetrics.paid + contractMetrics.executed}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Pipeline Health</span>
                <span className={`font-medium ${
                  (leadHealthScore + quotationHealthScore + invoiceHealthScore + contractHealthScore) / 4 >= 60 
                    ? 'text-green-600' : 'text-yellow-600'
                }`}>
                  {Math.round((leadHealthScore + quotationHealthScore + invoiceHealthScore + contractHealthScore) / 4)}%
                </span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
