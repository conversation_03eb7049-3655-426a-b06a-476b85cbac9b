'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useRouter } from 'next/navigation'
import { toast } from 'react-hot-toast'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Save, Loader2, Palette, Tag } from 'lucide-react'

const categorySchema = z.object({
  name: z.string().min(1, 'Category name is required'),
  description: z.string().optional(),
  color: z.string().optional(),
  icon: z.string().optional(),
  parentId: z.string().optional(),
  isActive: z.boolean().default(true)
})

type CategoryFormData = z.infer<typeof categorySchema>

interface CategoryFormProps {
  mode: 'create' | 'edit'
  category?: any
  categories?: any[]
}

const colorOptions = [
  '#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6',
  '#EC4899', '#06B6D4', '#84CC16', '#F97316', '#6366F1'
]

const iconOptions = [
  'tag', 'package', 'folder', 'star', 'heart', 'bookmark',
  'flag', 'shield', 'award', 'gift', 'tool', 'settings'
]

export function CategoryForm({ mode, category, categories = [] }: CategoryFormProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue
  } = useForm<CategoryFormData>({
    resolver: zodResolver(categorySchema),
    defaultValues: category || {
      name: '',
      description: '',
      color: colorOptions[0],
      icon: iconOptions[0],
      parentId: '',
      isActive: true
    }
  })

  const onSubmit = async (data: CategoryFormData) => {
    setIsLoading(true)

    try {
      const url = mode === 'create' ? '/api/item-categories' : `/api/item-categories?id=${category.id}`
      const method = mode === 'create' ? 'POST' : 'PUT'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to save category')
      }

      const result = await response.json()
      
      toast.success(mode === 'create' ? 'Category created successfully!' : 'Category updated successfully!')
      
      router.push('/dashboard/items/categories')
    } catch (error) {
      console.error('Error saving category:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to save category'
      toast.error(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const selectedColor = watch('color')
  const selectedIcon = watch('icon')

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Form */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Category Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Category Name</Label>
                  <Input
                    id="name"
                    {...register('name')}
                    placeholder="Enter category name"
                  />
                  {errors.name && (
                    <p className="text-sm text-red-600">{errors.name.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="parentId">Parent Category</Label>
                  <select
                    id="parentId"
                    {...register('parentId')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">No Parent (Top Level)</option>
                    {categories.filter(c => c.id !== category?.id).map((cat) => (
                      <option key={cat.id} value={cat.id}>
                        {cat.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  {...register('description')}
                  placeholder="Brief description of the category"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label>Category Color</Label>
                  <div className="grid grid-cols-5 gap-2">
                    {colorOptions.map((color) => (
                      <button
                        key={color}
                        type="button"
                        className={`w-8 h-8 rounded-full border-2 ${
                          selectedColor === color ? 'border-gray-900' : 'border-gray-300'
                        }`}
                        style={{ backgroundColor: color }}
                        onClick={() => setValue('color', color)}
                      />
                    ))}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Category Icon</Label>
                  <div className="grid grid-cols-6 gap-2">
                    {iconOptions.map((icon) => (
                      <button
                        key={icon}
                        type="button"
                        className={`w-8 h-8 rounded border flex items-center justify-center ${
                          selectedIcon === icon 
                            ? 'border-blue-500 bg-blue-50' 
                            : 'border-gray-300 hover:border-gray-400'
                        }`}
                        onClick={() => setValue('icon', icon)}
                      >
                        <Tag className="h-4 w-4" />
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Category Status</Label>
                  <p className="text-sm text-gray-500">Set category as active or inactive</p>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="isActive"
                    {...register('isActive')}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <Label htmlFor="isActive">Active</Label>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Category Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  {selectedColor && (
                    <div 
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: selectedColor }}
                    />
                  )}
                  <Tag className="h-4 w-4 text-gray-400" />
                  <div>
                    <p className="font-medium">{watch('name') || 'Category Name'}</p>
                    {watch('description') && (
                      <p className="text-sm text-gray-600">{watch('description')}</p>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Badge variant={watch('isActive') ? 'default' : 'secondary'}>
                    {watch('isActive') ? 'Active' : 'Inactive'}
                  </Badge>
                </div>

                {categories.find(c => c.id === watch('parentId')) && (
                  <div className="text-sm text-gray-600">
                    <span className="font-medium">Parent:</span>{' '}
                    {categories.find(c => c.id === watch('parentId'))?.name}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Category Guidelines</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm text-gray-600">
                <div>
                  <p className="font-medium">Naming Tips:</p>
                  <ul className="list-disc list-inside space-y-1 mt-1">
                    <li>Use clear, descriptive names</li>
                    <li>Keep names concise</li>
                    <li>Use consistent naming patterns</li>
                  </ul>
                </div>
                <div>
                  <p className="font-medium">Organization:</p>
                  <ul className="list-disc list-inside space-y-1 mt-1">
                    <li>Create logical hierarchies</li>
                    <li>Avoid too many subcategories</li>
                    <li>Group similar items together</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center justify-end space-x-4 p-6 bg-gray-50 rounded-lg">
        <Button
          type="button"
          variant="outline"
          onClick={() => router.back()}
        >
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <Save className="h-4 w-4 mr-2" />
          )}
          {mode === 'create' ? 'Create Category' : 'Update Category'}
        </Button>
      </div>
    </form>
  )
}
