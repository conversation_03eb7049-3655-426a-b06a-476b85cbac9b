import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { redirect } from 'next/navigation'
import { PaymentMethodsConfig } from '@/components/super-admin/payment-methods-config'
import {
  CreditCard,
  Settings,
  Globe,
  DollarSign
} from 'lucide-react'

export default async function SuperAdminPaymentMethodsPage() {
  const session = await getServerSession(authOptions)

  // Check if user is super admin
  if (!session?.user?.isSuperAdmin && session?.user?.role !== 'SUPER_ADMIN') {
    redirect('/dashboard')
  }

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="p-3 bg-blue-100 rounded-lg">
            <CreditCard className="h-8 w-8 text-blue-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Payment Methods Configuration</h1>
            <p className="text-gray-600">Configure supported payment methods and regional settings</p>
          </div>
        </div>
      </div>

      {/* Payment Methods Configuration Component */}
      <PaymentMethodsConfig />
    </div>
  )
}
