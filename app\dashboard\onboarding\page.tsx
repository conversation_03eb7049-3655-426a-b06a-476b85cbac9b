import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { OnboardingFlow } from '@/components/onboarding/onboarding-flow'
import { OnboardingProgress } from '@/components/onboarding/onboarding-progress'
import { QuickStartGuide } from '@/components/onboarding/quick-start-guide'
import { FeatureDiscovery } from '@/components/onboarding/feature-discovery'
import { Button } from '@/components/ui/button'
import { Rocket, Play, BookOpen, Lightbulb } from 'lucide-react'
import Link from 'next/link'
import { redirect } from 'next/navigation'

export default async function OnboardingPage() {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    redirect('/auth/signin')
  }

  // Fetch user and company onboarding data
  const [user, company, onboardingProgress] = await Promise.all([
    prisma.user.findUnique({
      where: { id: session.user.id }
    }),
    prisma.company.findUnique({
      where: { id: session.user.companyId }
    }),
    prisma.onboardingProgress.findFirst({
      where: { 
        companyId: session.user.companyId,
        userId: session.user.id 
      }
    })
  ])

  if (!user || !company) {
    return <div>Error: User or company not found</div>
  }

  // Check if onboarding is already completed
  const isOnboardingCompleted = onboardingProgress?.completed || false

  // Get onboarding steps completion status
  const completedSteps = (onboardingProgress?.completedSteps as string[]) || []
  const currentStep = onboardingProgress?.currentStep || 'welcome'

  // Define onboarding steps
  const onboardingSteps = [
    {
      id: 'welcome',
      title: 'Welcome to BusinessSaaS',
      description: 'Get started with your business management platform',
      completed: completedSteps.includes('welcome')
    },
    {
      id: 'company-setup',
      title: 'Company Setup',
      description: 'Complete your company profile and settings',
      completed: completedSteps.includes('company-setup')
    },
    {
      id: 'first-customer',
      title: 'Add Your First Customer',
      description: 'Create your first customer record',
      completed: completedSteps.includes('first-customer')
    },
    {
      id: 'first-quotation',
      title: 'Create a Quotation',
      description: 'Generate your first quotation',
      completed: completedSteps.includes('first-quotation')
    },
    {
      id: 'explore-features',
      title: 'Explore Features',
      description: 'Discover key platform features',
      completed: completedSteps.includes('explore-features')
    },
    {
      id: 'complete',
      title: 'Onboarding Complete',
      description: 'You\'re ready to use the platform',
      completed: completedSteps.includes('complete')
    }
  ]

  // Calculate progress percentage
  const progressPercentage = (completedSteps.length / (onboardingSteps.length - 1)) * 100

  // Get quick start tasks
  const quickStartTasks = [
    {
      id: 'profile',
      title: 'Complete Your Profile',
      description: 'Add your personal and company information',
      completed: !!user.firstName && !!user.lastName && !!company.address,
      action: '/dashboard/settings/profile',
      icon: '👤'
    },
    {
      id: 'customer',
      title: 'Add Your First Customer',
      description: 'Create a customer to start managing relationships',
      completed: false, // Will be checked dynamically
      action: '/dashboard/customers',
      icon: '👥'
    },
    {
      id: 'quotation',
      title: 'Create a Quotation',
      description: 'Generate your first business quotation',
      completed: false, // Will be checked dynamically
      action: '/dashboard/quotations',
      icon: '📄'
    },
    {
      id: 'billing',
      title: 'Set Up Billing',
      description: 'Configure your subscription and payment method',
      completed: !!company.plan && company.plan !== 'BASIC',
      action: '/dashboard/billing',
      icon: '💳'
    }
  ]

  // Check dynamic completion status
  const [customerCount, quotationCount] = await Promise.all([
    prisma.customer.count({
      where: { companyId: session.user.companyId }
    }),
    prisma.quotation.count({
      where: { companyId: session.user.companyId }
    })
  ])

  quickStartTasks[1].completed = customerCount > 0
  quickStartTasks[2].completed = quotationCount > 0

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Rocket className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {isOnboardingCompleted ? 'Welcome Back!' : 'Welcome to BusinessSaaS'}
            </h1>
            <p className="text-gray-600 mt-1">
              {isOnboardingCompleted 
                ? 'Continue exploring features and growing your business'
                : 'Let\'s get you set up and ready to manage your business'
              }
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Link href="/dashboard/onboarding/tutorials">
            <Button variant="outline">
              <Play className="h-4 w-4 mr-2" />
              Tutorials
            </Button>
          </Link>
          <Link href="/dashboard/support/knowledge-base">
            <Button variant="outline">
              <BookOpen className="h-4 w-4 mr-2" />
              Help Center
            </Button>
          </Link>
        </div>
      </div>

      {/* Onboarding Progress */}
      {!isOnboardingCompleted && (
        <OnboardingProgress 
          steps={onboardingSteps}
          currentStep={currentStep}
          progressPercentage={progressPercentage}
        />
      )}

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Onboarding Flow or Quick Start */}
        <div className="lg:col-span-2">
          {!isOnboardingCompleted ? (
            <OnboardingFlow 
              user={user}
              company={company}
              currentStep={currentStep}
              completedSteps={completedSteps}
              onboardingProgress={onboardingProgress}
            />
          ) : (
            <QuickStartGuide 
              tasks={quickStartTasks}
              company={company}
            />
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Feature Discovery */}
          <FeatureDiscovery 
            plan={company.plan}
            isOnboardingCompleted={isOnboardingCompleted}
          />

          {/* Help & Resources */}
          <div className="bg-white rounded-lg border p-6">
            <h3 className="font-medium text-gray-900 mb-4 flex items-center space-x-2">
              <Lightbulb className="h-5 w-5" />
              <span>Help & Resources</span>
            </h3>
            <div className="space-y-3">
              <Link href="/dashboard/onboarding/tutorials">
                <Button variant="ghost" className="w-full justify-start">
                  <Play className="h-4 w-4 mr-2" />
                  Interactive Tutorials
                </Button>
              </Link>
              <Link href="/dashboard/support/knowledge-base">
                <Button variant="ghost" className="w-full justify-start">
                  <BookOpen className="h-4 w-4 mr-2" />
                  Knowledge Base
                </Button>
              </Link>
              <Link href="/dashboard/support">
                <Button variant="ghost" className="w-full justify-start">
                  <Rocket className="h-4 w-4 mr-2" />
                  Contact Support
                </Button>
              </Link>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200 p-6">
            <h3 className="font-medium text-gray-900 mb-4">Your Progress</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Customers</span>
                <span className="font-medium text-gray-900">{customerCount}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Quotations</span>
                <span className="font-medium text-gray-900">{quotationCount}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Plan</span>
                <span className="font-medium text-gray-900">{company.plan}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Setup Progress</span>
                <span className="font-medium text-gray-900">{Math.round(progressPercentage)}%</span>
              </div>
            </div>
          </div>

          {/* Next Steps */}
          {isOnboardingCompleted && (
            <div className="bg-white rounded-lg border p-6">
              <h3 className="font-medium text-gray-900 mb-4">Recommended Next Steps</h3>
              <div className="space-y-3">
                {company.plan === 'BASIC' && (
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <div className="text-sm font-medium text-blue-900">Upgrade Your Plan</div>
                    <div className="text-xs text-blue-700 mt-1">
                      Unlock advanced features with Professional or Enterprise
                    </div>
                    <Link href="/dashboard/billing">
                      <Button size="sm" className="mt-2">
                        Upgrade Now
                      </Button>
                    </Link>
                  </div>
                )}
                
                <div className="p-3 bg-green-50 rounded-lg">
                  <div className="text-sm font-medium text-green-900">Explore Integrations</div>
                  <div className="text-xs text-green-700 mt-1">
                    Connect with your favorite tools and services
                  </div>
                  <Link href="/dashboard/api">
                    <Button size="sm" variant="outline" className="mt-2">
                      View API
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
