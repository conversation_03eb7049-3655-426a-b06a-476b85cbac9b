'use client'

import { useState, useEffect } from 'react'
import { use<PERSON>outer } from 'next/navigation'
import { useForm, useFieldArray } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Save, X, Plus, Trash2, FileText } from 'lucide-react'
import toast from 'react-hot-toast'
import Link from 'next/link'

const invoiceItemSchema = z.object({
  itemId: z.string().optional(),
  name: z.string().min(1, 'Item name is required'),
  description: z.string().optional(),
  quantity: z.number().min(0.01, 'Quantity must be greater than 0'),
  unitPrice: z.number().min(0, 'Unit price must be non-negative'),
  discount: z.number().min(0).max(100).default(0),
  taxRate: z.number().min(0).max(100).default(18),
})

const invoiceSchema = z.object({
  invoiceNumber: z.string().min(1, 'Invoice number is required'),
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  customerId: z.string().min(1, 'Customer is required'),
  quotationId: z.string().optional(),
  dueDate: z.string().optional(),
  terms: z.string().optional(),
  paymentTerms: z.string().optional(),
  taxRate: z.number().min(0).max(100).default(18),
  discountType: z.enum(['PERCENTAGE', 'FIXED']).default('PERCENTAGE'),
  discountValue: z.number().min(0).default(0),
  items: z.array(invoiceItemSchema).min(1, 'At least one item is required'),
})

type InvoiceFormData = z.infer<typeof invoiceSchema>

interface Customer {
  id: string
  name: string
  email: string | null
  company: string | null
  address: string | null
  city: string | null
  state: string | null
  country: string | null
  postalCode: string | null
}

interface Quotation {
  id: string
  quotationNumber: string
  title: string
  customerId: string
  customer: {
    id: string
    name: string
    company: string | null
  }
  items: Array<{
    id: string
    name: string
    description: string | null
    quantity: number
    unitPrice: number
    discount: number
    taxRate: number
  }>
}

interface Item {
  id: string
  name: string
  description: string | null
  price: number
  category: string | null
  unit: string | null
  taxRate: number | null
}

interface Company {
  name: string | null
  email: string | null
  phone: string | null
  address: string | null
  city: string | null
  state: string | null
  country: string | null
  postalCode: string | null
  website: string | null
  taxId: string | null
}

interface InvoiceFormProps {
  mode: 'create' | 'edit'
  invoice?: any
  customers: Customer[]
  quotations: Quotation[]
  items: Item[]
  company: Company | null
  preselectedCustomerId?: string
  preselectedQuotationId?: string
  preselectedQuotation?: Quotation | null
  invoiceNumber?: string
}

export function InvoiceForm({ 
  mode, 
  invoice, 
  customers, 
  quotations, 
  items, 
  company,
  preselectedCustomerId,
  preselectedQuotationId,
  preselectedQuotation,
  invoiceNumber
}: InvoiceFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const router = useRouter()

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
    control,
  } = useForm<InvoiceFormData>({
    resolver: zodResolver(invoiceSchema),
    defaultValues: invoice ? {
      invoiceNumber: invoice.invoiceNumber || '',
      title: invoice.title || '',
      description: invoice.description || '',
      customerId: invoice.customerId || '',
      quotationId: invoice.quotationId || '',
      dueDate: invoice.dueDate ? new Date(invoice.dueDate).toISOString().split('T')[0] : '',
      terms: invoice.terms || '',
      paymentTerms: invoice.paymentTerms || '',
      taxRate: invoice.taxRate || 18,
      discountType: invoice.discountType || 'PERCENTAGE',
      discountValue: invoice.discountValue || 0,
      items: invoice.items?.map((item: any) => ({
        itemId: item.itemId || '',
        name: item.name || '',
        description: item.description || '',
        quantity: item.quantity || 1,
        unitPrice: item.unitPrice || 0,
        discount: item.discount || 0,
        taxRate: item.taxRate || 18,
      })) || [{ name: '', description: '', quantity: 1, unitPrice: 0, discount: 0, taxRate: 18 }],
    } : {
      invoiceNumber: invoiceNumber || '',
      title: preselectedQuotation?.title || '',
      description: '',
      customerId: preselectedCustomerId || preselectedQuotation?.customerId || '',
      quotationId: preselectedQuotationId || '',
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
      terms: 'Payment is due within 30 days of invoice date. Late payments may incur additional charges.',
      paymentTerms: 'Net 30 days from invoice date',
      taxRate: 18,
      discountType: 'PERCENTAGE',
      discountValue: 0,
      items: preselectedQuotation?.items?.map(item => ({
        itemId: '',
        name: item.name,
        description: item.description || '',
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        discount: item.discount,
        taxRate: item.taxRate,
      })) || [{ name: '', description: '', quantity: 1, unitPrice: 0, discount: 0, taxRate: 18 }],
    }
  })

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'items',
  })

  const watchedItems = watch('items')
  const watchedDiscountType = watch('discountType')
  const watchedDiscountValue = watch('discountValue')
  const watchedTaxRate = watch('taxRate')
  const watchedQuotationId = watch('quotationId')

  // Calculate totals
  const subtotal = watchedItems.reduce((sum, item) => {
    const itemTotal = (item.quantity || 0) * (item.unitPrice || 0)
    const discountAmount = itemTotal * ((item.discount || 0) / 100)
    return sum + (itemTotal - discountAmount)
  }, 0)

  const globalDiscountAmount = watchedDiscountType === 'PERCENTAGE' 
    ? subtotal * ((watchedDiscountValue || 0) / 100)
    : (watchedDiscountValue || 0)

  const discountedSubtotal = subtotal - globalDiscountAmount
  const taxAmount = discountedSubtotal * ((watchedTaxRate || 0) / 100)
  const total = discountedSubtotal + taxAmount

  // Filter quotations by selected customer
  const filteredQuotations = quotations.filter(quotation => 
    !watch('customerId') || quotation.customerId === watch('customerId')
  )

  // Load quotation items when quotation is selected
  useEffect(() => {
    if (watchedQuotationId && mode === 'create') {
      const selectedQuotation = quotations.find(q => q.id === watchedQuotationId)
      if (selectedQuotation) {
        // Clear existing items and add quotation items
        setValue('items', selectedQuotation.items.map(item => ({
          itemId: '',
          name: item.name,
          description: item.description || '',
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          discount: item.discount,
          taxRate: item.taxRate,
        })))
        
        // Update title if not set
        if (!watch('title')) {
          setValue('title', `Invoice for ${selectedQuotation.title}`)
        }
      }
    }
  }, [watchedQuotationId, quotations, setValue, watch, mode])

  const onSubmit = async (data: InvoiceFormData) => {
    setIsLoading(true)
    setError('')

    try {
      const url = mode === 'create' ? '/api/invoices' : `/api/invoices/${invoice.id}`
      const method = mode === 'create' ? 'POST' : 'PUT'

      // Calculate totals for submission
      const calculatedData = {
        ...data,
        subtotal,
        total,
        dueDate: data.dueDate ? new Date(data.dueDate) : null,
      }

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(calculatedData),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to save invoice')
      }

      const result = await response.json()
      
      toast.success(mode === 'create' ? 'Invoice created successfully!' : 'Invoice updated successfully!')
      
      if (mode === 'create') {
        router.push(`/dashboard/invoices/${result.id}`)
      } else {
        router.push('/dashboard/invoices')
      }
    } catch (error) {
      console.error('Error saving invoice:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to save invoice'
      setError(errorMessage)
      toast.error(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    if (mode === 'create') {
      reset()
    } else {
      router.push('/dashboard/invoices')
    }
  }

  const addItem = () => {
    append({ name: '', description: '', quantity: 1, unitPrice: 0, discount: 0, taxRate: 18 })
  }

  const removeItem = (index: number) => {
    if (fields.length > 1) {
      remove(index)
    }
  }

  const selectItem = (index: number, selectedItem: Item) => {
    setValue(`items.${index}.itemId`, selectedItem.id)
    setValue(`items.${index}.name`, selectedItem.name)
    setValue(`items.${index}.description`, selectedItem.description || '')
    setValue(`items.${index}.unitPrice`, selectedItem.price)
    setValue(`items.${index}.taxRate`, selectedItem.taxRate || 18)
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Invoice Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="invoiceNumber">Invoice Number *</Label>
              <Input
                id="invoiceNumber"
                {...register('invoiceNumber')}
                placeholder="INV-202401-0001"
                disabled={isLoading}
              />
              {errors.invoiceNumber && (
                <p className="text-sm text-red-600 mt-1">{errors.invoiceNumber.message}</p>
              )}
            </div>
            <div>
              <Label htmlFor="dueDate">Due Date</Label>
              <Input
                id="dueDate"
                type="date"
                {...register('dueDate')}
                disabled={isLoading}
              />
            </div>
          </div>

          <div>
            <Label htmlFor="title">Title *</Label>
            <Input
              id="title"
              {...register('title')}
              placeholder="Invoice title"
              disabled={isLoading}
            />
            {errors.title && (
              <p className="text-sm text-red-600 mt-1">{errors.title.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <textarea
              id="description"
              {...register('description')}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Describe the invoice..."
              disabled={isLoading}
            />
          </div>
        </CardContent>
      </Card>

      {/* Customer and Quotation Information */}
      <Card>
        <CardHeader>
          <CardTitle>Customer & Quotation Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <Label htmlFor="customerId">Customer *</Label>
                <Link href="/dashboard/customers/new">
                  <Button type="button" variant="outline" >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Customer
                  </Button>
                </Link>
              </div>
              <select
                id="customerId"
                {...register('customerId')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isLoading}
              >
                <option value="">Select a customer</option>
                {customers.map((customer) => (
                  <option key={customer.id} value={customer.id}>
                    {customer.name} {customer.company && `(${customer.company})`}
                  </option>
                ))}
              </select>
              {errors.customerId && (
                <p className="text-sm text-red-600 mt-1">{errors.customerId.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="quotationId">Related Quotation (Optional)</Label>
              <select
                id="quotationId"
                {...register('quotationId')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isLoading}
              >
                <option value="">Select a quotation</option>
                {filteredQuotations.map((quotation) => (
                  <option key={quotation.id} value={quotation.id}>
                    {quotation.quotationNumber} - {quotation.title}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Show selected customer details */}
          {watch('customerId') && (
            <div className="p-3 bg-gray-50 rounded-md">
              {(() => {
                const selectedCustomer = customers.find(c => c.id === watch('customerId'))
                if (!selectedCustomer) return null

                return (
                  <div>
                    <p className="font-medium text-gray-900">{selectedCustomer.name}</p>
                    {selectedCustomer.company && (
                      <p className="text-sm text-gray-600">{selectedCustomer.company}</p>
                    )}
                    {selectedCustomer.email && (
                      <p className="text-sm text-gray-600">{selectedCustomer.email}</p>
                    )}
                    {selectedCustomer.address && (
                      <p className="text-sm text-gray-600">
                        {[selectedCustomer.address, selectedCustomer.city, selectedCustomer.state, selectedCustomer.country]
                          .filter(Boolean).join(', ')}
                      </p>
                    )}
                  </div>
                )
              })()}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Items */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Items</CardTitle>
            <Button type="button" onClick={addItem} variant="outline" >
              <Plus className="h-4 w-4 mr-2" />
              Add Item
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {fields.map((field, index) => (
              <div key={field.id} className="border rounded-lg p-4 space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium text-gray-900">Item {index + 1}</h4>
                  {fields.length > 1 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeItem(index)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor={`items.${index}.name`}>Item Name *</Label>
                    <div className="flex space-x-2">
                      <Input
                        {...register(`items.${index}.name` as const)}
                        placeholder="Item name"
                        disabled={isLoading}
                      />
                      <select
                        onChange={(e) => {
                          const selectedItem = items.find(item => item.id === e.target.value)
                          if (selectedItem) {
                            selectItem(index, selectedItem)
                          }
                        }}
                        className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-0 w-32"
                        disabled={isLoading}
                      >
                        <option value="">Select</option>
                        {items.map((item) => (
                          <option key={item.id} value={item.id}>
                            {item.name}
                          </option>
                        ))}
                      </select>
                    </div>
                    {errors.items?.[index]?.name && (
                      <p className="text-sm text-red-600 mt-1">{errors.items[index]?.name?.message}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor={`items.${index}.description`}>Description</Label>
                    <Input
                      {...register(`items.${index}.description` as const)}
                      placeholder="Item description"
                      disabled={isLoading}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <Label htmlFor={`items.${index}.quantity`}>Quantity *</Label>
                    <Input
                      type="number"
                      step="0.01"
                      min="0.01"
                      {...register(`items.${index}.quantity` as const, { valueAsNumber: true })}
                      placeholder="1"
                      disabled={isLoading}
                    />
                    {errors.items?.[index]?.quantity && (
                      <p className="text-sm text-red-600 mt-1">{errors.items[index]?.quantity?.message}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor={`items.${index}.unitPrice`}>Unit Price (₹) *</Label>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      {...register(`items.${index}.unitPrice` as const, { valueAsNumber: true })}
                      placeholder="0.00"
                      disabled={isLoading}
                    />
                    {errors.items?.[index]?.unitPrice && (
                      <p className="text-sm text-red-600 mt-1">{errors.items[index]?.unitPrice?.message}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor={`items.${index}.discount`}>Discount (%)</Label>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      max="100"
                      {...register(`items.${index}.discount` as const, { valueAsNumber: true })}
                      placeholder="0"
                      disabled={isLoading}
                    />
                  </div>

                  <div>
                    <Label htmlFor={`items.${index}.taxRate`}>Tax Rate (%)</Label>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      max="100"
                      {...register(`items.${index}.taxRate` as const, { valueAsNumber: true })}
                      placeholder="18"
                      disabled={isLoading}
                    />
                  </div>
                </div>

                {/* Item Total */}
                <div className="text-right">
                  <span className="text-sm text-gray-600">Item Total: </span>
                  <span className="font-medium">
                    ₹{((watchedItems[index]?.quantity || 0) * (watchedItems[index]?.unitPrice || 0) *
                      (1 - (watchedItems[index]?.discount || 0) / 100)).toFixed(2)}
                  </span>
                </div>
              </div>
            ))}
          </div>

          {errors.items && (
            <p className="text-sm text-red-600 mt-2">{errors.items.message}</p>
          )}
        </CardContent>
      </Card>

      {/* Pricing and Terms */}
      <Card>
        <CardHeader>
          <CardTitle>Pricing & Terms</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="discountType">Discount Type</Label>
              <select
                id="discountType"
                {...register('discountType')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isLoading}
              >
                <option value="PERCENTAGE">Percentage (%)</option>
                <option value="FIXED">Fixed Amount (₹)</option>
              </select>
            </div>

            <div>
              <Label htmlFor="discountValue">
                Discount {watchedDiscountType === 'PERCENTAGE' ? '(%)' : '(₹)'}
              </Label>
              <Input
                type="number"
                step="0.01"
                min="0"
                {...register('discountValue', { valueAsNumber: true })}
                placeholder="0"
                disabled={isLoading}
              />
            </div>

            <div>
              <Label htmlFor="taxRate">Tax Rate (%)</Label>
              <Input
                type="number"
                step="0.01"
                min="0"
                max="100"
                {...register('taxRate', { valueAsNumber: true })}
                placeholder="18"
                disabled={isLoading}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="terms">Terms & Conditions</Label>
              <textarea
                id="terms"
                {...register('terms')}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Terms and conditions..."
                disabled={isLoading}
              />
            </div>

            <div>
              <Label htmlFor="paymentTerms">Payment Terms</Label>
              <textarea
                id="paymentTerms"
                {...register('paymentTerms')}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Payment terms..."
                disabled={isLoading}
              />
            </div>
          </div>

          {/* Totals Summary */}
          <div className="border-t pt-4">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-medium text-gray-900 mb-3">Invoice Summary</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">Subtotal:</span>
                  <span className="font-medium">₹{subtotal.toFixed(2)}</span>
                </div>
                {globalDiscountAmount > 0 && (
                  <div className="flex justify-between text-green-600">
                    <span>
                      Discount ({watchedDiscountType === 'PERCENTAGE' ? `${watchedDiscountValue}%` : '₹' + watchedDiscountValue}):
                    </span>
                    <span>-₹{globalDiscountAmount.toFixed(2)}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-gray-600">Tax ({watchedTaxRate}%):</span>
                  <span className="font-medium">₹{taxAmount.toFixed(2)}</span>
                </div>
                <div className="border-t pt-2">
                  <div className="flex justify-between text-lg font-bold">
                    <span>Total:</span>
                    <span>₹{total.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex items-center justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={handleCancel}
          disabled={isLoading}
        >
          <X className="h-4 w-4 mr-2" />
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              {mode === 'create' ? 'Creating...' : 'Updating...'}
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              {mode === 'create' ? 'Create Invoice' : 'Update Invoice'}
            </>
          )}
        </Button>
      </div>
    </form>
  )
}
