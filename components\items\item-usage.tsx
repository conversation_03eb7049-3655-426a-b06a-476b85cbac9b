'use client'

import Link from 'next/link'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { formatDate, getStatusColor } from '@/lib/utils'
import { 
  FileText, 
  Receipt, 
  User, 
  Building, 
  Calendar,
  Eye,
  MoreHorizontal
} from 'lucide-react'

interface QuotationItem {
  id: string
  quantity: number
  unitPrice: number
  quotation: {
    id: string
    quotationNumber: string
    title: string
    status: string
    createdAt: Date
    customer: {
      name: string
      company: string | null
    } | null
  }
}

interface InvoiceItem {
  id: string
  quantity: number
  unitPrice: number
  invoice: {
    id: string
    invoiceNumber: string
    title: string
    status: string
    createdAt: Date
    customer: {
      name: string
      company: string | null
    } | null
  }
}

interface ItemUsageProps {
  quotationItems: QuotationItem[]
  invoiceItems: InvoiceItem[]
  totalQuotations: number
  totalInvoices: number
}

export function ItemUsage({ 
  quotationItems, 
  invoiceItems, 
  totalQuotations, 
  totalInvoices 
}: ItemUsageProps) {
  return (
    <div className="space-y-6">
      {/* Recent Quotations */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-blue-600" />
              <span>Recent Quotations</span>
            </CardTitle>
            <span className="text-sm text-gray-500">
              {totalQuotations} total
            </span>
          </div>
        </CardHeader>
        <CardContent>
          {quotationItems.length > 0 ? (
            <div className="space-y-3">
              {quotationItems.map((item) => (
                <div key={item.id} className="border rounded-lg p-3 hover:bg-gray-50">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <Link 
                          href={`/dashboard/quotations/${item.quotation.id}`}
                          className="font-medium text-blue-600 hover:text-blue-800"
                        >
                          {item.quotation.quotationNumber}
                        </Link>
                        <Badge className={getStatusColor(item.quotation.status)} variant="outline">
                          {item.quotation.status}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">
                        {item.quotation.title}
                      </p>
                      {item.quotation.customer && (
                        <div className="flex items-center space-x-1 mt-2">
                          <User className="h-3 w-3 text-gray-400" />
                          <span className="text-xs text-gray-500">
                            {item.quotation.customer.name}
                            {item.quotation.customer.company && 
                              ` (${item.quotation.customer.company})`
                            }
                          </span>
                        </div>
                      )}
                      <div className="flex items-center space-x-1 mt-1">
                        <Calendar className="h-3 w-3 text-gray-400" />
                        <span className="text-xs text-gray-500">
                          {formatDate(item.quotation.createdAt)}
                        </span>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900">
                        Qty: {item.quantity}
                      </p>
                      <p className="text-xs text-gray-500">
                        ₹{item.unitPrice.toFixed(2)}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
              {totalQuotations > quotationItems.length && (
                <div className="text-center py-2">
                  <Link 
                    href={`/dashboard/quotations?itemId=${quotationItems[0]?.quotation.id}`}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    View all {totalQuotations} quotations →
                  </Link>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-6">
              <FileText className="h-8 w-8 text-gray-300 mx-auto mb-2" />
              <p className="text-sm text-gray-500">No quotations yet</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Invoices */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <Receipt className="h-5 w-5 text-green-600" />
              <span>Recent Invoices</span>
            </CardTitle>
            <span className="text-sm text-gray-500">
              {totalInvoices} total
            </span>
          </div>
        </CardHeader>
        <CardContent>
          {invoiceItems.length > 0 ? (
            <div className="space-y-3">
              {invoiceItems.map((item) => (
                <div key={item.id} className="border rounded-lg p-3 hover:bg-gray-50">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <Link 
                          href={`/dashboard/invoices/${item.invoice.id}`}
                          className="font-medium text-green-600 hover:text-green-800"
                        >
                          {item.invoice.invoiceNumber}
                        </Link>
                        <Badge className={getStatusColor(item.invoice.status)} variant="outline">
                          {item.invoice.status}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">
                        {item.invoice.title}
                      </p>
                      {item.invoice.customer && (
                        <div className="flex items-center space-x-1 mt-2">
                          <User className="h-3 w-3 text-gray-400" />
                          <span className="text-xs text-gray-500">
                            {item.invoice.customer.name}
                            {item.invoice.customer.company && 
                              ` (${item.invoice.customer.company})`
                            }
                          </span>
                        </div>
                      )}
                      <div className="flex items-center space-x-1 mt-1">
                        <Calendar className="h-3 w-3 text-gray-400" />
                        <span className="text-xs text-gray-500">
                          {formatDate(item.invoice.createdAt)}
                        </span>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900">
                        Qty: {item.quantity}
                      </p>
                      <p className="text-xs text-gray-500">
                        ₹{item.unitPrice.toFixed(2)}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
              {totalInvoices > invoiceItems.length && (
                <div className="text-center py-2">
                  <Link 
                    href={`/dashboard/invoices?itemId=${invoiceItems[0]?.invoice.id}`}
                    className="text-sm text-green-600 hover:text-green-800"
                  >
                    View all {totalInvoices} invoices →
                  </Link>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-6">
              <Receipt className="h-8 w-8 text-gray-300 mx-auto mb-2" />
              <p className="text-sm text-gray-500">No invoices yet</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Usage Statistics */}
      <Card>
        <CardHeader>
          <CardTitle>Usage Statistics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Total Quotations</span>
              <span className="font-medium text-gray-900">{totalQuotations}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Total Invoices</span>
              <span className="font-medium text-gray-900">{totalInvoices}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Conversion Rate</span>
              <span className="font-medium text-gray-900">
                {totalQuotations > 0 ? ((totalInvoices / totalQuotations) * 100).toFixed(1) : '0'}%
              </span>
            </div>
            <div className="flex items-center justify-between border-t pt-4">
              <span className="text-sm font-medium text-gray-900">Total Usage</span>
              <span className="font-bold text-gray-900">{totalQuotations + totalInvoices}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
