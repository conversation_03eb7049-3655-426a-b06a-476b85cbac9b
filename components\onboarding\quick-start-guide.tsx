'use client'

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  CheckCircle, 
  Circle,
  ArrowRight,
  Rocket,
  TrendingUp,
  Star,
  Target,
  Zap
} from 'lucide-react'
import Link from 'next/link'

interface QuickStartGuideProps {
  tasks: Array<{
    id: string
    title: string
    description: string
    completed: boolean
    action: string
    icon: string
  }>
  company: any
}

export function QuickStartGuide({ tasks, company }: QuickStartGuideProps) {
  const completedTasks = tasks.filter(task => task.completed).length
  const progressPercentage = (completedTasks / tasks.length) * 100

  const getTaskPriority = (taskId: string) => {
    const priorities = {
      'profile': 'high',
      'customer': 'high',
      'quotation': 'medium',
      'billing': 'low'
    }
    return priorities[taskId as keyof typeof priorities] || 'medium'
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'text-red-600 border-red-200'
      case 'medium':
        return 'text-yellow-600 border-yellow-200'
      case 'low':
        return 'text-green-600 border-green-200'
      default:
        return 'text-gray-600 border-gray-200'
    }
  }

  const getPriorityLabel = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'High Priority'
      case 'medium':
        return 'Medium Priority'
      case 'low':
        return 'Low Priority'
      default:
        return 'Normal'
    }
  }

  const recommendations = [
    {
      title: 'Explore Advanced Features',
      description: 'Discover powerful tools to grow your business',
      icon: Rocket,
      action: '/dashboard/analytics',
      color: 'text-blue-600 bg-blue-100'
    },
    {
      title: 'Upgrade Your Plan',
      description: 'Unlock more features with Professional or Enterprise',
      icon: TrendingUp,
      action: '/dashboard/billing',
      color: 'text-purple-600 bg-purple-100',
      show: company.plan === 'BASIC'
    },
    {
      title: 'Set Up Integrations',
      description: 'Connect with your favorite tools and services',
      icon: Zap,
      action: '/dashboard/api',
      color: 'text-green-600 bg-green-100'
    }
  ].filter(rec => rec.show !== false)

  return (
    <div className="space-y-6">
      {/* Quick Start Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <Target className="h-5 w-5" />
              <span>Quick Start Guide</span>
            </CardTitle>
            <Badge variant="outline">
              {completedTasks} of {tasks.length} completed
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Progress Overview */}
            <div>
              <div className="flex justify-between text-sm text-gray-600 mb-2">
                <span>Setup Progress</span>
                <span>{Math.round(progressPercentage)}%</span>
              </div>
              <Progress value={progressPercentage} className="h-2" />
            </div>

            {/* Completion Message */}
            {progressPercentage === 100 ? (
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <div>
                    <div className="font-medium text-green-900">
                      Great job! You've completed all setup tasks.
                    </div>
                    <div className="text-sm text-green-700">
                      You're ready to make the most of BusinessSaaS.
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Target className="h-5 w-5 text-blue-600" />
                  <div>
                    <div className="font-medium text-blue-900">
                      {tasks.length - completedTasks} tasks remaining
                    </div>
                    <div className="text-sm text-blue-700">
                      Complete these tasks to get the most out of your platform.
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Task List */}
      <Card>
        <CardHeader>
          <CardTitle>Setup Tasks</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {tasks.map((task, index) => {
              const priority = getTaskPriority(task.id)
              
              return (
                <div 
                  key={task.id} 
                  className={`p-4 border rounded-lg transition-colors ${
                    task.completed ? 'bg-green-50 border-green-200' : 'hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-start space-x-4">
                    {/* Task Icon */}
                    <div className="flex-shrink-0">
                      {task.completed ? (
                        <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                          <CheckCircle className="h-5 w-5 text-green-600" />
                        </div>
                      ) : (
                        <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                          <Circle className="h-5 w-5 text-gray-400" />
                        </div>
                      )}
                    </div>

                    {/* Task Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className={`font-medium ${
                          task.completed ? 'text-green-900' : 'text-gray-900'
                        }`}>
                          {task.icon} {task.title}
                        </h4>
                        {!task.completed && (
                          <Badge className={getPriorityColor(priority)} variant="outline" >
                            {getPriorityLabel(priority)}
                          </Badge>
                        )}
                        {task.completed && (
                          <Badge variant="secondary" >
                            Complete
                          </Badge>
                        )}
                      </div>
                      <p className={`text-sm ${
                        task.completed ? 'text-green-700' : 'text-gray-600'
                      }`}>
                        {task.description}
                      </p>
                    </div>

                    {/* Action Button */}
                    <div className="flex-shrink-0">
                      {!task.completed && (
                        <Link href={task.action}>
                          <Button >
                            Start
                            <ArrowRight className="h-4 w-4 ml-2" />
                          </Button>
                        </Link>
                      )}
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Star className="h-5 w-5" />
            <span>Recommended Next Steps</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {recommendations.map((rec, index) => (
              <div key={index} className="p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                <div className={`w-10 h-10 rounded-lg flex items-center justify-center mb-3 ${rec.color}`}>
                  <rec.icon className="h-5 w-5" />
                </div>
                <h4 className="font-medium text-gray-900 mb-2">{rec.title}</h4>
                <p className="text-sm text-gray-600 mb-4">{rec.description}</p>
                <Link href={rec.action}>
                  <Button variant="outline" size="sm" className="w-full">
                    Explore
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </Link>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Success Metrics */}
      {progressPercentage === 100 && (
        <Card>
          <CardHeader>
            <CardTitle>Your Success Metrics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-800">100%</div>
                <div className="text-sm text-blue-600">Setup Complete</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-800">{company.plan}</div>
                <div className="text-sm text-green-600">Current Plan</div>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-800">Ready</div>
                <div className="text-sm text-purple-600">Platform Status</div>
              </div>
              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <div className="text-2xl font-bold text-orange-800">Active</div>
                <div className="text-sm text-orange-600">Account Status</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
