import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { redirect } from 'next/navigation'
import { 
  Shield, 
  TrendingUp, 
  Users, 
  Building2, 
  DollarSign,
  Activity,
  Server,
  AlertTriangle,
  CheckCircle,
  Clock,
  ArrowUpRight,
  ArrowDownRight,
  Eye,
  UserPlus,
  FileText,
  CreditCard
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { formatCurrency, formatDate } from '@/lib/utils'
import Link from 'next/link'

export default async function SuperAdminPage() {
  const session = await getServerSession(authOptions)
  
  // Check if user is super admin
  if (!session?.user?.isSuperAdmin && session?.user?.role !== 'SUPER_ADMIN') {
    redirect('/dashboard')
  }

  // Calculate date ranges for analytics
  const now = new Date()
  const startOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const startOfWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
  const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
  const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0)

  // Fetch comprehensive platform analytics
  const [
    // Company metrics
    totalCompanies,
    activeCompanies,
    newCompaniesThisMonth,
    newCompaniesLastMonth,
    
    // User metrics
    totalUsers,
    activeUsers,
    newUsersThisMonth,
    
    // Revenue metrics
    totalRevenue,
    monthlyRevenue,
    lastMonthRevenue,
    
    // Activity metrics
    recentActivities,
    
    // Top companies
    topCompaniesByRevenue,
    topCompaniesByUsers,
    
    // System metrics
    systemHealth
  ] = await Promise.all([
    // Company queries
    prisma.company.count(),
    prisma.company.count({ where: { isActive: true } }),
    prisma.company.count({ where: { createdAt: { gte: startOfMonth } } }),
    prisma.company.count({ 
      where: { 
        createdAt: { 
          gte: startOfLastMonth, 
          lte: endOfLastMonth 
        } 
      } 
    }),
    
    // User queries
    prisma.user.count(),
    prisma.user.count({ 
      where: { 
        lastLoginAt: { gte: startOfWeek } 
      } 
    }),
    prisma.user.count({ where: { createdAt: { gte: startOfMonth } } }),
    
    // Revenue queries
    prisma.invoice.aggregate({
      _sum: { total: true },
      where: { status: 'PAID' }
    }),
    prisma.invoice.aggregate({
      _sum: { total: true },
      where: { 
        status: 'PAID',
        paidAt: { gte: startOfMonth }
      }
    }),
    prisma.invoice.aggregate({
      _sum: { total: true },
      where: { 
        status: 'PAID',
        paidAt: { 
          gte: startOfLastMonth,
          lte: endOfLastMonth
        }
      }
    }),
    
    // Recent activities
    prisma.activity.findMany({
      take: 10,
      orderBy: { createdAt: 'desc' },
      include: {
        company_rel: { select: { name: true } },
        createdBy: { select: { name: true, firstName: true, lastName: true } }
      }
    }),
    
    // Top companies by revenue
    prisma.company.findMany({
      take: 5,
      orderBy: { totalRevenue: 'desc' },
      select: {
        id: true,
        name: true,
        email: true,
        plan: true,
        totalRevenue: true,
        userCount: true,
        isActive: true
      }
    }),
    
    // Top companies by users
    prisma.company.findMany({
      take: 5,
      orderBy: { userCount: 'desc' },
      select: {
        id: true,
        name: true,
        email: true,
        plan: true,
        userCount: true,
        totalRevenue: true
      }
    }),
    
    // System health (mock data for now)
    Promise.resolve({
      uptime: '99.9%',
      responseTime: '120ms',
      errorRate: '0.1%',
      activeConnections: 1247
    })
  ])

  // Calculate growth rates
  const companyGrowthRate = newCompaniesLastMonth > 0 
    ? ((newCompaniesThisMonth - newCompaniesLastMonth) / newCompaniesLastMonth * 100)
    : 0

  const revenueGrowthRate = (lastMonthRevenue._sum.total || 0) > 0
    ? (((monthlyRevenue._sum.total || 0) - (lastMonthRevenue._sum.total || 0)) / (lastMonthRevenue._sum.total || 0) * 100)
    : 0

  // Platform statistics
  const stats = [
    {
      title: 'Total Companies',
      value: totalCompanies.toLocaleString(),
      change: `+${newCompaniesThisMonth} this month`,
      changeType: companyGrowthRate >= 0 ? 'positive' : 'negative',
      icon: Building2,
      color: 'blue'
    },
    {
      title: 'Active Users',
      value: activeUsers.toLocaleString(),
      change: `${totalUsers.toLocaleString()} total`,
      changeType: 'neutral',
      icon: Users,
      color: 'green'
    },
    {
      title: 'Monthly Revenue',
      value: formatCurrency(monthlyRevenue._sum.total || 0),
      change: `${revenueGrowthRate >= 0 ? '+' : ''}${revenueGrowthRate.toFixed(1)}%`,
      changeType: revenueGrowthRate >= 0 ? 'positive' : 'negative',
      icon: DollarSign,
      color: 'purple'
    },
    {
      title: 'Total Revenue',
      value: formatCurrency(totalRevenue._sum.total || 0),
      change: 'All time',
      changeType: 'neutral',
      icon: TrendingUp,
      color: 'orange'
    }
  ]

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="p-3 bg-purple-100 rounded-lg">
            <Shield className="h-8 w-8 text-purple-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Platform Dashboard</h1>
            <p className="text-gray-600">Welcome back, {session.user.name || session.user.email}</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Badge variant="outline" className="text-green-600 border-green-200">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
            System Online
          </Badge>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {stat.title}
                </CardTitle>
                <Icon className={`h-4 w-4 text-${stat.color}-600`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <div className={`text-xs flex items-center mt-1 ${
                  stat.changeType === 'positive' ? 'text-green-600' : 
                  stat.changeType === 'negative' ? 'text-red-600' : 'text-gray-500'
                }`}>
                  {stat.changeType === 'positive' && <ArrowUpRight className="h-3 w-3 mr-1" />}
                  {stat.changeType === 'negative' && <ArrowDownRight className="h-3 w-3 mr-1" />}
                  {stat.change}
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left Column - Charts and Analytics */}
        <div className="lg:col-span-2 space-y-6">
          {/* Top Companies by Revenue */}
          <Card>
            <CardHeader>
              <CardTitle>Top Companies by Revenue</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topCompaniesByRevenue.map((company, index) => (
                  <div key={company.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-blue-600">#{index + 1}</span>
                      </div>
                      <div>
                        <p className="font-medium">{company.name}</p>
                        <p className="text-sm text-gray-500">{company.plan} Plan</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{formatCurrency(company.totalRevenue)}</p>
                      <p className="text-sm text-gray-500">{company.userCount} users</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recent Platform Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Platform Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivities.slice(0, 8).map((activity) => (
                  <div key={activity.id} className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">{activity.title}</p>
                      <p className="text-xs text-gray-500">
                        {activity.company_rel?.name} • {formatDate(activity.createdAt)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Quick Actions and System Status */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button asChild className="w-full justify-start">
                <Link href="/super-admin/companies">
                  <Building2 className="h-4 w-4 mr-2" />
                  Manage Companies
                </Link>
              </Button>
              <Button asChild variant="outline" className="w-full justify-start">
                <Link href="/super-admin/users">
                  <Users className="h-4 w-4 mr-2" />
                  User Management
                </Link>
              </Button>
              <Button asChild variant="outline" className="w-full justify-start">
                <Link href="/super-admin/revenue">
                  <DollarSign className="h-4 w-4 mr-2" />
                  Revenue Analytics
                </Link>
              </Button>
              <Button asChild variant="outline" className="w-full justify-start">
                <Link href="/super-admin/system">
                  <Server className="h-4 w-4 mr-2" />
                  System Health
                </Link>
              </Button>
            </CardContent>
          </Card>

          {/* System Status */}
          <Card>
            <CardHeader>
              <CardTitle>System Status</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm">Uptime</span>
                <Badge variant="outline" className="text-green-600">
                  {systemHealth.uptime}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Response Time</span>
                <Badge variant="outline">
                  {systemHealth.responseTime}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Error Rate</span>
                <Badge variant="outline" className="text-green-600">
                  {systemHealth.errorRate}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Active Connections</span>
                <Badge variant="outline">
                  {systemHealth.activeConnections.toLocaleString()}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Top Companies by Users */}
          <Card>
            <CardHeader>
              <CardTitle>Most Active Companies</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {topCompaniesByUsers.slice(0, 5).map((company, index) => (
                  <div key={company.id} className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium">{company.name}</p>
                      <p className="text-xs text-gray-500">{company.plan}</p>
                    </div>
                    <Badge variant="outline">
                      {company.userCount} users
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
