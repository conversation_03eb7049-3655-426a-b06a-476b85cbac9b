import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { SuperAdminDashboard } from '@/components/super-admin/super-admin-dashboard'
import { PlatformStats } from '@/components/super-admin/platform-stats'
import { RecentActivity } from '@/components/super-admin/recent-activity'
import { SystemHealth } from '@/components/super-admin/system-health'
import { redirect } from 'next/navigation'
import { Shield, TrendingUp, Users, Building2 } from 'lucide-react'

export default async function SuperAdminPage() {
  const session = await getServerSession(authOptions)
  
  // Check if user is super admin
  if (!session?.user?.isSuperAdmin) {
    redirect('/dashboard')
  }

  // Calculate date ranges for analytics
  const now = new Date()
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
  const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
  const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0)
  const last30Days = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)

  // Fetch comprehensive platform analytics
  const [
    // Company metrics
    totalCompanies,
    activeCompanies,
    newCompaniesThisMonth,
    newCompaniesLastMonth,
    
    // User metrics
    totalUsers,
    activeUsers,
    newUsersThisMonth,
    
    // Business metrics
    totalQuotations,
    totalInvoices,
    totalContracts,
    totalRevenue,
    
    // Activity metrics
    recentActivities,
    
    // System metrics
    companiesByPlan,
    usersByRole,
    revenueByMonth,
    
    // Top performing companies
    topCompaniesByRevenue,
    topCompaniesByUsers,
    
    // System health
    systemMetrics
  ] = await Promise.all([
    // Company counts
    prisma.company.count(),
    prisma.company.count({
      where: {
        users: {
          some: {
            lastLoginAt: { gte: last30Days }
          }
        }
      }
    }),
    prisma.company.count({
      where: { createdAt: { gte: startOfMonth, lte: now } }
    }),
    prisma.company.count({
      where: { createdAt: { gte: startOfLastMonth, lte: endOfLastMonth } }
    }),
    
    // User counts
    prisma.user.count(),
    prisma.user.count({
      where: { lastLoginAt: { gte: last30Days } }
    }),
    prisma.user.count({
      where: { createdAt: { gte: startOfMonth, lte: now } }
    }),
    
    // Business metrics
    prisma.quotation.count(),
    prisma.invoice.count(),
    prisma.contract.count(),
    prisma.invoice.aggregate({
      where: { status: 'PAID' },
      _sum: { total: true }
    }),
    
    // Recent activities (last 50)
    prisma.activity.findMany({
      take: 50,
      orderBy: { createdAt: 'desc' },
      include: {
        company_rel: { select: { name: true } },
        createdBy: { select: { name: true, firstName: true, lastName: true } }
      }
    }),
    
    // Analytics breakdowns
    prisma.company.groupBy({
      by: ['plan'],
      _count: true
    }),
    prisma.user.groupBy({
      by: ['role'],
      _count: true
    }),
    // Revenue by month (simplified)
    prisma.invoice.groupBy({
      by: ['createdAt'],
      where: { 
        status: 'PAID',
        createdAt: { gte: new Date(now.getFullYear(), now.getMonth() - 11, 1) }
      },
      _sum: { total: true }
    }),
    
    // Top companies by revenue
    prisma.company.findMany({
      include: {
        invoices: {
          where: { status: 'PAID' },
          select: { total: true }
        },
        _count: {
          select: { users: true }
        }
      },
      take: 10
    }),
    
    // Top companies by users
    prisma.company.findMany({
      include: {
        _count: {
          select: { users: true }
        }
      },
      orderBy: {
        users: { _count: 'desc' }
      },
      take: 10
    }),
    
    // System health metrics (simplified)
    Promise.resolve({
      uptime: '99.9%',
      responseTime: '120ms',
      errorRate: '0.1%',
      activeConnections: 1250
    })
  ])

  // Process top companies by revenue
  const processedTopCompaniesByRevenue = topCompaniesByRevenue
    .map(company => ({
      id: company.id,
      name: company.name,
      email: company.email || '',
      plan: company.plan,
      totalRevenue: company.invoices.reduce((sum, invoice) => sum + invoice.total, 0),
      _count: company._count
    }))
    .sort((a, b) => b.totalRevenue - a.totalRevenue)
    .slice(0, 5)

  // Calculate growth rates
  const companyGrowthRate = newCompaniesLastMonth > 0 
    ? ((newCompaniesThisMonth - newCompaniesLastMonth) / newCompaniesLastMonth * 100).toFixed(1)
    : '0'

  const platformData = {
    companies: {
      total: totalCompanies,
      active: activeCompanies,
      newThisMonth: newCompaniesThisMonth,
      growthRate: parseFloat(companyGrowthRate)
    },
    users: {
      total: totalUsers,
      active: activeUsers,
      newThisMonth: newUsersThisMonth
    },
    business: {
      quotations: totalQuotations,
      invoices: totalInvoices,
      contracts: totalContracts,
      revenue: totalRevenue._sum.total || 0
    },
    breakdowns: {
      companiesByPlan,
      usersByRole,
      revenueByMonth
    },
    topCompanies: {
      byRevenue: processedTopCompaniesByRevenue,
      byUsers: topCompaniesByUsers.slice(0, 5)
    },
    systemHealth: systemMetrics
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-red-100 rounded-lg">
                <Shield className="h-6 w-6 text-red-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Super Admin</h1>
                <p className="text-sm text-gray-600">Platform Management & Analytics</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-600">
                Welcome back, {session.user.name || session.user.email}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Platform Statistics */}
          <PlatformStats data={platformData} />

          {/* Dashboard Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Dashboard */}
            <div className="lg:col-span-2">
              <SuperAdminDashboard data={platformData} />
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* System Health */}
              <SystemHealth metrics={platformData.systemHealth} />
              
              {/* Recent Activity */}
              <RecentActivity activities={recentActivities.map(activity => ({
                id: activity.id,
                type: activity.type,
                title: activity.title,
                description: activity.description || '',
                createdAt: activity.createdAt,
                company: activity.company_rel ? { name: activity.company_rel.name } : null,
                createdBy: activity.createdBy
              }))} />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
