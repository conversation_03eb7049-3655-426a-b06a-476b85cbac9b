import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const progressSchema = z.object({
  stepId: z.string(),
  stepData: z.record(z.any()).optional()
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const onboardingProgress = await prisma.onboardingProgress.findFirst({
      where: { 
        companyId: session.user.companyId,
        userId: session.user.id 
      }
    })

    return NextResponse.json({ progress: onboardingProgress })
  } catch (error) {
    console.error('Error fetching onboarding progress:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = progressSchema.parse(body)

    // Get or create onboarding progress
    let onboardingProgress = await prisma.onboardingProgress.findFirst({
      where: { 
        companyId: session.user.companyId,
        userId: session.user.id 
      }
    })

    if (!onboardingProgress) {
      onboardingProgress = await prisma.onboardingProgress.create({
        data: {
          companyId: session.user.companyId,
          userId: session.user.id,
          currentStep: 'welcome',
          completedSteps: [],
          isCompleted: false
        }
      })
    }

    // Add step to completed steps if not already there
    const completedSteps = onboardingProgress.completedSteps || []
    if (!completedSteps.includes(validatedData.stepId)) {
      completedSteps.push(validatedData.stepId)
    }

    // Process step-specific data
    await processStepData(validatedData.stepId, validatedData.stepData, session.user.companyId, session.user.id)

    // Update progress
    const updatedProgress = await prisma.onboardingProgress.update({
      where: { id: onboardingProgress.id },
      data: {
        completedSteps,
        stepData: {
          ...onboardingProgress.stepData as any,
          [validatedData.stepId]: validatedData.stepData
        }
      }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'ONBOARDING',
        title: 'Onboarding step completed',
        description: `Completed onboarding step: ${validatedData.stepId}`,
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    return NextResponse.json({ progress: updatedProgress })
  } catch (error) {
    console.error('Error saving onboarding progress:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function processStepData(stepId: string, stepData: any, companyId: string, userId: string) {
  try {
    switch (stepId) {
      case 'company-setup':
        await processCompanySetup(stepData, companyId, userId)
        break
      case 'first-customer':
        await processFirstCustomer(stepData, companyId, userId)
        break
      case 'first-quotation':
        await processFirstQuotation(stepData, companyId, userId)
        break
      default:
        // No specific processing needed
        break
    }
  } catch (error) {
    console.error(`Error processing step data for ${stepId}:`, error)
    // Don't throw error to avoid breaking the onboarding flow
  }
}

async function processCompanySetup(stepData: any, companyId: string, userId: string) {
  if (!stepData) return

  // Update company information
  const companyUpdates: any = {}
  if (stepData.companyName) companyUpdates.name = stepData.companyName
  if (stepData.companyEmail) companyUpdates.email = stepData.companyEmail
  if (stepData.companyPhone) companyUpdates.phone = stepData.companyPhone
  if (stepData.companyAddress) companyUpdates.address = stepData.companyAddress
  if (stepData.industry) companyUpdates.industry = stepData.industry

  if (Object.keys(companyUpdates).length > 0) {
    await prisma.company.update({
      where: { id: companyId },
      data: companyUpdates
    })
  }

  // Update user information
  const userUpdates: any = {}
  if (stepData.firstName) userUpdates.firstName = stepData.firstName
  if (stepData.lastName) userUpdates.lastName = stepData.lastName
  if (stepData.jobTitle) userUpdates.jobTitle = stepData.jobTitle

  if (Object.keys(userUpdates).length > 0) {
    await prisma.user.update({
      where: { id: userId },
      data: userUpdates
    })
  }
}

async function processFirstCustomer(stepData: any, companyId: string, userId: string) {
  if (!stepData || !stepData.customerName || !stepData.customerEmail) return

  // Check if customer already exists
  const existingCustomer = await prisma.customer.findFirst({
    where: {
      companyId,
      email: stepData.customerEmail
    }
  })

  if (!existingCustomer) {
    await prisma.customer.create({
      data: {
        name: stepData.customerName,
        email: stepData.customerEmail,
        company: stepData.customerCompany || null,
        companyId,
        createdById: userId
      }
    })
  }
}

async function processFirstQuotation(stepData: any, companyId: string, userId: string) {
  if (!stepData || !stepData.quotationTitle || !stepData.quotationAmount) return

  // Get the first customer for the quotation
  const customer = await prisma.customer.findFirst({
    where: { companyId },
    orderBy: { createdAt: 'asc' }
  })

  if (!customer) return

  // Generate quotation number
  const quotationCount = await prisma.quotation.count({
    where: { companyId }
  })
  const quotationNumber = `QUO-${Date.now()}-${(quotationCount + 1).toString().padStart(4, '0')}`

  await prisma.quotation.create({
    data: {
      quotationNumber,
      title: stepData.quotationTitle,
      description: stepData.quotationDescription || '',
      amount: parseFloat(stepData.quotationAmount),
      currency: 'USD',
      status: 'DRAFT',
      validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
      customerId: customer.id,
      companyId,
      createdById: userId,
      items: [
        {
          description: stepData.quotationTitle,
          quantity: 1,
          unitPrice: parseFloat(stepData.quotationAmount),
          total: parseFloat(stepData.quotationAmount)
        }
      ]
    }
  })
}

// Utility function to check onboarding completion
export async function checkOnboardingCompletion(companyId: string, userId: string) {
  try {
    const progress = await prisma.onboardingProgress.findFirst({
      where: { companyId, userId }
    })

    if (!progress) return false

    const requiredSteps = ['welcome', 'company-setup', 'first-customer', 'first-quotation', 'explore-features']
    const completedSteps = progress.completedSteps || []

    return requiredSteps.every(step => completedSteps.includes(step))
  } catch (error) {
    console.error('Error checking onboarding completion:', error)
    return false
  }
}

// Utility function to get onboarding analytics
export async function getOnboardingAnalytics(companyId?: string) {
  try {
    const where = companyId ? { companyId } : {}

    const [totalUsers, completedUsers, stepAnalytics] = await Promise.all([
      prisma.onboardingProgress.count({ where }),
      prisma.onboardingProgress.count({
        where: { ...where, isCompleted: true }
      }),
      prisma.onboardingProgress.groupBy({
        by: ['currentStep'],
        where,
        _count: true
      })
    ])

    const completionRate = totalUsers > 0 ? (completedUsers / totalUsers) * 100 : 0

    return {
      totalUsers,
      completedUsers,
      completionRate,
      stepAnalytics
    }
  } catch (error) {
    console.error('Error getting onboarding analytics:', error)
    return {
      totalUsers: 0,
      completedUsers: 0,
      completionRate: 0,
      stepAnalytics: []
    }
  }
}
