import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ContractDetails } from '@/components/contracts/contract-details'
import { ContractActivity } from '@/components/contracts/contract-activity'
import { ContractPreview } from '@/components/contracts/contract-preview'
import { ContractSignature } from '@/components/contracts/contract-signature'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Edit, Trash2, Send, Download, Copy, PenTool, Play } from 'lucide-react'
import Link from 'next/link'
import { notFound } from 'next/navigation'

export default async function ContractDetailPage({
  params,
}: {
  params: { id: string }
}) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  const [contract, company] = await Promise.all([
    prisma.contract.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId,
      },
      include: {
        customer: {
          select: { 
            id: true, 
            name: true, 
            email: true, 
            company: true, 
            phone: true,
            address: true,
            city: true,
            state: true,
            country: true,
            postalCode: true
          }
        },
        quotation: {
          select: { id: true, quotationNumber: true, title: true }
        },
        signatures: {
          include: {
            signedBy: {
              select: { name: true, firstName: true, lastName: true, email: true }
            }
          },
          orderBy: { signedAt: 'desc' }
        },
        activities: {
          include: {
            createdBy: {
              select: { name: true, firstName: true, lastName: true }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: 15,
        },
        _count: {
          select: {
            signatures: true,
            activities: true,
          }
        },
        company: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            address: true,
            city: true,
            state: true,
            country: true,
            postalCode: true
          }
        }
      }
    }),
    prisma.company.findUnique({
      where: { id: session.user.companyId },
      select: {
        name: true,
        email: true,
        phone: true,
        address: true,
        city: true,
        state: true,
        country: true,
        postalCode: true,
        website: true,
        taxId: true
      }
    })
  ])

  if (!contract) {
    notFound()
  }

  const isExpired = contract.endDate && 
                   new Date(contract.endDate) < new Date() && 
                   !['CANCELLED', 'EXECUTED'].includes(contract.status)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/contracts">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Contracts
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{contract.contractNumber}</h1>
            <p className="text-gray-600 mt-1">
              {contract.title}
              {isExpired && (
                <span className="ml-2 text-red-600 font-medium">• Expired</span>
              )}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {contract.status === 'DRAFT' && (
            <>
              <Button variant="outline" className="text-blue-600 hover:text-blue-700">
                <Send className="h-4 w-4 mr-2" />
                Send
              </Button>
              <Link href={`/dashboard/contracts/${contract.id}/edit`}>
                <Button variant="outline">
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>
              </Link>
            </>
          )}
          {['SENT', 'VIEWED'].includes(contract.status) && contract.signatureRequired && (
            <Button variant="outline" className="text-green-600 hover:text-green-700">
              <PenTool className="h-4 w-4 mr-2" />
              Sign Contract
            </Button>
          )}
          {contract.status === 'SIGNED' && (
            <Button variant="outline" className="text-green-600 hover:text-green-700">
              <Play className="h-4 w-4 mr-2" />
              Execute
            </Button>
          )}
          <Button variant="outline">
            <Copy className="h-4 w-4 mr-2" />
            Duplicate
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Download PDF
          </Button>
          {contract.status === 'DRAFT' && (
            <Button variant="outline" className="text-red-600 hover:text-red-700">
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Contract Details */}
        <div className="lg:col-span-2 space-y-6">
          <ContractDetails contract={contract} />
          <ContractPreview contract={contract} />
          {contract.signatureRequired && (
            <ContractSignature contract={contract} />
          )}
        </div>

        {/* Right Column - Activity */}
        <div>
          <ContractActivity activities={contract.activities} />
        </div>
      </div>
    </div>
  )
}
