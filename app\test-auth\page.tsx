import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export default async function TestAuthPage() {
  const session = await getServerSession(authOptions)
  
  // Get user from database to verify
  let dbUser = null
  if (session?.user?.email) {
    dbUser = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        companyId: true
      }
    })
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Authentication Test Page</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Session Data */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Session Data</h2>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {JSON.stringify(session, null, 2)}
            </pre>
          </div>

          {/* Database User */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Database User</h2>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {JSON.stringify(dbUser, null, 2)}
            </pre>
          </div>
        </div>

        {/* Quick Links */}
        <div className="mt-8 bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Quick Links</h2>
          <div className="space-y-2">
            <a href="/dashboard" className="block text-blue-600 hover:underline">
              → Regular Dashboard
            </a>
            <a href="/super-admin" className="block text-purple-600 hover:underline">
              → Super Admin Dashboard
            </a>
            <a href="/auth/signin" className="block text-green-600 hover:underline">
              → Sign In Page
            </a>
          </div>
        </div>

        {/* Super Admin Test */}
        <div className="mt-8 bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Super Admin Check</h2>
          <div className="space-y-2">
            <p><strong>Email:</strong> {session?.user?.email || 'Not logged in'}</p>
            <p><strong>Role:</strong> {session?.user?.role || 'No role'}</p>
            <p><strong>Is Super Admin (session):</strong> {session?.user?.isSuperAdmin ? 'Yes' : 'No'}</p>
            <p><strong>Role from DB:</strong> {dbUser?.role || 'No DB user'}</p>
            <p><strong>Should access Super Admin:</strong> {
              (session?.user?.role === 'SUPER_ADMIN' || session?.user?.isSuperAdmin) ? 'Yes' : 'No'
            }</p>
          </div>
        </div>
      </div>
    </div>
  )
}
