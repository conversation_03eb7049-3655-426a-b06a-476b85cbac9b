import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const integrationConfigSchema = z.object({
  stripe: z.object({
    enabled: z.boolean(),
    publishableKey: z.string().optional(),
    secretKey: z.string().optional(),
    webhookSecret: z.string().optional(),
    testMode: z.boolean()
  }).optional(),
  mailgun: z.object({
    enabled: z.boolean(),
    apiKey: z.string().optional(),
    domain: z.string().optional(),
    region: z.enum(['us', 'eu']).optional()
  }).optional(),
  slack: z.object({
    enabled: z.boolean(),
    webhookUrl: z.string().optional(),
    channel: z.string().optional(),
    botToken: z.string().optional()
  }).optional(),
  webhooks: z.object({
    enabled: z.boolean(),
    defaultUrl: z.string().optional(),
    secret: z.string().optional(),
    retryAttempts: z.number().min(1).max(10).optional()
  }).optional()
})

// Get integration configuration
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.isSuperAdmin && session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // In a real app, this would come from a database
    // For now, return configuration based on environment variables
    const config = {
      stripe: {
        enabled: !!(process.env.STRIPE_SECRET_KEY && process.env.STRIPE_PUBLISHABLE_KEY),
        publishableKey: process.env.STRIPE_PUBLISHABLE_KEY ? 
          process.env.STRIPE_PUBLISHABLE_KEY.substring(0, 12) + '...' : '',
        secretKey: process.env.STRIPE_SECRET_KEY ? 
          process.env.STRIPE_SECRET_KEY.substring(0, 12) + '...' : '',
        webhookSecret: process.env.STRIPE_WEBHOOK_SECRET ? 
          process.env.STRIPE_WEBHOOK_SECRET.substring(0, 12) + '...' : '',
        testMode: process.env.NODE_ENV !== 'production'
      },
      mailgun: {
        enabled: !!(process.env.MAILGUN_API_KEY && process.env.MAILGUN_DOMAIN),
        apiKey: process.env.MAILGUN_API_KEY ? 
          process.env.MAILGUN_API_KEY.substring(0, 8) + '...' : '',
        domain: process.env.MAILGUN_DOMAIN || '',
        region: (process.env.MAILGUN_REGION as 'us' | 'eu') || 'us'
      },
      slack: {
        enabled: !!process.env.SLACK_WEBHOOK_URL,
        webhookUrl: process.env.SLACK_WEBHOOK_URL ? 
          process.env.SLACK_WEBHOOK_URL.substring(0, 20) + '...' : '',
        channel: process.env.SLACK_CHANNEL || '#general',
        botToken: process.env.SLACK_BOT_TOKEN ? 
          process.env.SLACK_BOT_TOKEN.substring(0, 12) + '...' : ''
      },
      webhooks: {
        enabled: true, // Always enabled
        defaultUrl: process.env.WEBHOOK_DEFAULT_URL || '',
        secret: process.env.WEBHOOK_SECRET ? 
          process.env.WEBHOOK_SECRET.substring(0, 8) + '...' : '',
        retryAttempts: parseInt(process.env.WEBHOOK_RETRY_ATTEMPTS || '3')
      }
    }

    return NextResponse.json(config)
  } catch (error) {
    console.error('Error fetching integration config:', error)
    return NextResponse.json(
      { error: 'Failed to fetch integration configuration' },
      { status: 500 }
    )
  }
}

// Update integration configuration
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.isSuperAdmin && session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = integrationConfigSchema.parse(body)

    // In a real app, you would save these to a database or update environment variables
    // For now, we'll just validate and log the activity
    
    // Log the integration configuration update
    await prisma.activity.create({
      data: {
        type: 'SYSTEM',
        title: 'Integration configuration updated',
        description: 'Third-party integration settings were updated by super admin',
        companyId: session.user.companyId!,
        createdById: session.user.id,
        metadata: {
          updatedIntegrations: Object.keys(validatedData),
          timestamp: new Date().toISOString(),
          changes: {
            stripe: validatedData.stripe ? {
              enabled: validatedData.stripe.enabled,
              testMode: validatedData.stripe.testMode,
              hasKeys: !!(validatedData.stripe.publishableKey && validatedData.stripe.secretKey)
            } : undefined,
            mailgun: validatedData.mailgun ? {
              enabled: validatedData.mailgun.enabled,
              region: validatedData.mailgun.region,
              hasDomain: !!validatedData.mailgun.domain
            } : undefined,
            slack: validatedData.slack ? {
              enabled: validatedData.slack.enabled,
              hasWebhook: !!validatedData.slack.webhookUrl
            } : undefined,
            webhooks: validatedData.webhooks ? {
              enabled: validatedData.webhooks.enabled,
              retryAttempts: validatedData.webhooks.retryAttempts
            } : undefined
          }
        }
      }
    })

    // In a production environment, you would:
    // 1. Encrypt and store sensitive keys in a secure vault
    // 2. Update environment variables or configuration files
    // 3. Restart services if needed
    // 4. Validate the new configuration

    return NextResponse.json({ 
      success: true, 
      message: 'Integration configuration updated successfully',
      updatedAt: new Date().toISOString(),
      note: 'In production, this would update environment variables and restart services'
    })
  } catch (error) {
    console.error('Error updating integration config:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Failed to update integration configuration' },
      { status: 500 }
    )
  }
}

// Reset integration configuration to defaults
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.isSuperAdmin && session?.user?.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Log the integration configuration reset
    await prisma.activity.create({
      data: {
        type: 'SYSTEM',
        title: 'Integration configuration reset',
        description: 'All integration settings were reset to defaults by super admin',
        companyId: session.user.companyId!,
        createdById: session.user.id,
        metadata: {
          action: 'RESET_INTEGRATIONS',
          timestamp: new Date().toISOString()
        }
      }
    })

    return NextResponse.json({ 
      success: true, 
      message: 'Integration configuration reset to defaults successfully',
      resetAt: new Date().toISOString()
    })
  } catch (error) {
    console.error('Error resetting integration config:', error)
    return NextResponse.json(
      { error: 'Failed to reset integration configuration' },
      { status: 500 }
    )
  }
}
