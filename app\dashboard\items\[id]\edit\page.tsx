import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { notFound } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Package } from 'lucide-react'
import { ItemForm } from '@/components/items/item-form'

interface PageProps {
  params: {
    id: string
  }
}

export default async function EditItemPage({ params }: PageProps) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.companyId) {
    return <div>Error: No company found</div>
  }

  // Fetch the item to edit
  const item = await prisma.item.findFirst({
    where: {
      id: params.id,
      companyId: session.user.companyId
    },
    include: {
      itemCategory: {
        select: { id: true, name: true }
      }
    }
  })

  if (!item) {
    notFound()
  }

  // Fetch existing categories for dropdown
  const categories = await prisma.itemCategory.findMany({
    where: { 
      companyId: session.user.companyId,
      isActive: true 
    },
    include: {
      parent: {
        select: { id: true, name: true }
      }
    },
    orderBy: { name: 'asc' }
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href={`/dashboard/items/${item.id}`}>
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Item
          </Button>
        </Link>
        <div className="p-2 bg-blue-100 rounded-lg">
          <Package className="h-6 w-6 text-blue-600" />
        </div>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Edit Item</h1>
          <p className="text-gray-600 mt-1">
            Modify "{item.name}" details
          </p>
        </div>
      </div>

      {/* Item Form */}
      <ItemForm 
        mode="edit" 
        item={item}
        categories={categories}
      />
    </div>
  )
}
