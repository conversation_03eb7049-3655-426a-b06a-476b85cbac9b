'use client'

import { useState } from 'react'
import { useSession, signOut } from 'next-auth/react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  Bell,
  Search,
  Settings,
  User,
  LogOut,
  Shield,
  Crown,
  Globe,
  BarChart3,
  AlertTriangle,
  CheckCircle,
  FileText
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

export function SuperAdminHeader() {
  const { data: session } = useSession()
  const [showNotifications, setShowNotifications] = useState(false)

  const handleSignOut = () => {
    signOut({ callbackUrl: '/auth/signin' })
  }

  return (
    <header className="fixed top-0 left-0 right-0 h-16 bg-white border-b border-gray-200 z-50">
      <div className="flex items-center justify-between h-full px-6">
        {/* Left Side - Logo and Title */}
        <div className="flex items-center space-x-4">
          <Link href="/super-admin" className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center">
              <Crown className="h-5 w-5 text-white" />
            </div>
            <div>
              <h1 className="text-lg font-bold text-gray-900">Super Admin</h1>
              <p className="text-xs text-purple-600">Platform Control Center</p>
            </div>
          </Link>
        </div>

        {/* Center - Quick Stats */}
        <div className="hidden md:flex items-center space-x-6">
          <div className="flex items-center space-x-2 text-sm">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-gray-600">System Online</span>
          </div>
          <div className="text-sm text-gray-600">
            <span className="font-medium">1,247</span> Active Users
          </div>
          <div className="text-sm text-gray-600">
            <span className="font-medium">$47,892</span> Today's Revenue
          </div>
        </div>

        {/* Right Side - Actions and User Menu */}
        <div className="flex items-center space-x-4">
          {/* Quick Actions */}
          <div className="hidden lg:flex items-center space-x-2">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/super-admin/companies">
                <Globe className="h-4 w-4 mr-2" />
                Companies
              </Link>
            </Button>
            <Button variant="ghost" size="sm" asChild>
              <Link href="/super-admin/analytics">
                <BarChart3 className="h-4 w-4 mr-2" />
                Analytics
              </Link>
            </Button>
          </div>

          {/* Notifications */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="relative">
                <Bell className="h-5 w-5" />
                <span className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                  3
                </span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-80">
              <DropdownMenuLabel>System Notifications</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <div className="space-y-2 p-2">
                <div className="flex items-start space-x-3 p-2 hover:bg-gray-50 rounded">
                  <AlertTriangle className="h-4 w-4 text-yellow-500 mt-1" />
                  <div className="flex-1">
                    <p className="text-sm font-medium">High CPU Usage</p>
                    <p className="text-xs text-gray-500">Server load at 85%</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3 p-2 hover:bg-gray-50 rounded">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-1" />
                  <div className="flex-1">
                    <p className="text-sm font-medium">Backup Completed</p>
                    <p className="text-xs text-gray-500">Daily backup successful</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3 p-2 hover:bg-gray-50 rounded">
                  <FileText className="h-4 w-4 text-blue-500 mt-1" />
                  <div className="flex-1">
                    <p className="text-sm font-medium">New Company Registered</p>
                    <p className="text-xs text-gray-500">TechCorp Inc. joined</p>
                  </div>
                </div>
              </div>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="flex items-center space-x-2">
                <Avatar className="h-8 w-8">
                  <AvatarImage src="/avatars/superadmin.svg" />
                  <AvatarFallback className="bg-purple-600 text-white">
                    <Crown className="h-4 w-4" />
                  </AvatarFallback>
                </Avatar>
                <div className="hidden sm:block text-left">
                  <p className="text-sm font-medium">{session?.user?.name || 'Super Admin'}</p>
                  <p className="text-xs text-purple-600">Platform Administrator</p>
                </div>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>Super Admin Account</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link href="/super-admin/settings">
                  <Settings className="h-4 w-4 mr-2" />
                  Platform Settings
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/dashboard">
                  <User className="h-4 w-4 mr-2" />
                  Switch to User View
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleSignOut} className="text-red-600">
                <LogOut className="h-4 w-4 mr-2" />
                Sign Out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  )
}
