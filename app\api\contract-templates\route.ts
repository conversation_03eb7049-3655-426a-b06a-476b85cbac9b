import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const templateSchema = z.object({
  name: z.string().min(1, 'Template name is required'),
  description: z.string().optional().nullable(),
  category: z.string().optional().nullable(),
  status: z.enum(['ACTIVE', 'DRAFT', 'ARCHIVED']).default('DRAFT'),
  content: z.string().min(1, 'Template content is required'),
  variables: z.array(z.object({
    name: z.string().min(1, 'Variable name is required'),
    label: z.string().min(1, 'Variable label is required'),
    type: z.enum(['TEXT', 'NUMBER', 'DATE', 'BOOLEAN', 'EMAIL', 'PHONE']),
    required: z.boolean().default(false),
    defaultValue: z.string().optional().nullable(),
    description: z.string().optional().nullable(),
  })).default([]),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search') || ''
    const category = searchParams.get('category') || ''
    const status = searchParams.get('status') || ''
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = (page - 1) * limit

    // Build where clause
    const where: any = {
      companyId: session.user.companyId,
    }

    if (search) {
      where.OR = [
        { name: { contains: search } },
        { description: { contains: search } },
        { category: { contains: search } },
        { content: { contains: search } },
      ]
    }

    if (category) {
      where.category = category
    }

    if (status) {
      where.status = status
    }

    const [templates, totalCount] = await Promise.all([
      prisma.contractTemplate.findMany({
        where,
        include: {
          createdBy: {
            select: { name: true, firstName: true, lastName: true }
          },
          _count: {
            select: {
              contracts: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip: offset,
        take: limit,
      }),
      prisma.contractTemplate.count({ where })
    ])

    return NextResponse.json({
      templates,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      }
    })
  } catch (error) {
    console.error('Error fetching contract templates:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = templateSchema.parse(body)

    // Check if template name already exists
    const existingTemplate = await prisma.contractTemplate.findFirst({
      where: {
        name: validatedData.name,
        companyId: session.user.companyId,
      }
    })

    if (existingTemplate) {
      return NextResponse.json(
        { error: 'Template name already exists' },
        { status: 400 }
      )
    }

    // Validate variables have unique names
    const variableNames = validatedData.variables.map(v => v.name)
    const uniqueNames = new Set(variableNames)
    if (variableNames.length !== uniqueNames.size) {
      return NextResponse.json(
        { error: 'Variable names must be unique' },
        { status: 400 }
      )
    }

    // Validate variable names are valid identifiers (allow letters, numbers, underscores)
    const invalidNames = variableNames.filter(name => name && !/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(name))
    if (invalidNames.length > 0) {
      return NextResponse.json(
        { error: `Invalid variable names: ${invalidNames.join(', ')}. Use only letters, numbers, and underscores.` },
        { status: 400 }
      )
    }

    const template = await prisma.contractTemplate.create({
      data: {
        name: validatedData.name,
        description: validatedData.description,
        category: validatedData.category,
        status: validatedData.status,
        content: validatedData.content,
        variables: validatedData.variables,
        companyId: session.user.companyId,
        createdById: session.user.id,
      },
      include: {
        createdBy: {
          select: { name: true, firstName: true, lastName: true }
        },
        _count: {
          select: {
            contracts: true
          }
        }
      }
    })

    // Create activity log
    await prisma.activity.create({
      data: {
        type: 'NOTE',
        title: 'Contract template created',
        description: `Template "${template.name}" was created`,
        companyId: session.user.companyId,
        createdById: session.user.id,
      }
    })

    return NextResponse.json(template, { status: 201 })
  } catch (error) {
    console.error('Error creating contract template:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
