'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { loadStripe } from '@stripe/stripe-js'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Loader2, CreditCard, Shield, Check, ArrowLeft, Smartphone, Building, Wallet } from 'lucide-react'
import { toast } from 'react-hot-toast'
import Link from 'next/link'

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!)

export default function PaymentPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const plan = searchParams.get('plan') || 'professional'
  const interval = searchParams.get('interval') || 'monthly'
  const companyId = searchParams.get('companyId')

  // Plan details
  const planDetails = {
    starter: {
      name: 'Starter',
      monthly: 29,
      yearly: 290,
      features: [
        'Up to 100 customers',
        '50 quotations per month',
        '25 invoices per month',
        'Basic email support',
        'Standard templates',
        '1 user account'
      ]
    },
    professional: {
      name: 'Professional',
      monthly: 79,
      yearly: 790,
      features: [
        'Up to 1,000 customers',
        'Unlimited quotations',
        'Unlimited invoices',
        'Priority email & chat support',
        'Custom templates',
        'Up to 5 user accounts',
        'Advanced reporting & analytics',
        'API access'
      ]
    },
    enterprise: {
      name: 'Enterprise',
      monthly: 199,
      yearly: 1990,
      features: [
        'Unlimited customers',
        'Unlimited quotations',
        'Unlimited invoices',
        '24/7 phone & email support',
        'Fully custom templates',
        'Unlimited user accounts',
        'Enterprise reporting',
        'Full API access',
        'White-label solution'
      ]
    }
  }

  const currentPlan = planDetails[plan as keyof typeof planDetails]
  const price = interval === 'yearly' ? currentPlan?.yearly : currentPlan?.monthly
  const savings = currentPlan ? Math.round((1 - currentPlan.yearly / (currentPlan.monthly * 12)) * 100) : 0

  useEffect(() => {
    if (!companyId) {
      router.push('/auth/signup')
    }
  }, [companyId, router])

  const handlePayment = async () => {
    if (!companyId) {
      setError('Missing company information. Please try signing up again.')
      return
    }

    setIsLoading(true)
    setError('')

    try {
      // Create checkout session
      const response = await fetch('/api/billing/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          plan,
          interval,
          companyId,
          successUrl: `${window.location.origin}/dashboard?payment=success`,
          cancelUrl: `${window.location.origin}/auth/payment?plan=${plan}&interval=${interval}&companyId=${companyId}&cancelled=true`
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create checkout session')
      }

      // Redirect to Stripe Checkout
      const stripe = await stripePromise
      if (!stripe) {
        throw new Error('Stripe failed to load')
      }

      const { error } = await stripe.redirectToCheckout({
        sessionId: data.sessionId,
      })

      if (error) {
        throw new Error(error.message)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred'
      setError(errorMessage)
      toast.error(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSkipTrial = async () => {
    setIsLoading(true)
    try {
      // Start trial without payment
      const response = await fetch('/api/billing/start-trial', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          companyId,
          plan
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to start trial')
      }

      toast.success('Trial started successfully!')
      router.push('/dashboard?trial=started')
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred'
      setError(errorMessage)
      toast.error(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  if (!currentPlan) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <p>Invalid plan selected. Please try again.</p>
            <Link href="/auth/signup">
              <Button className="mt-4">Back to Signup</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Complete Your Subscription</h1>
          <p className="text-gray-600">Start your 14-day free trial, then continue with your selected plan</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Plan Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                {currentPlan.name} Plan
                {interval === 'yearly' && (
                  <Badge variant="secondary">Save {savings}%</Badge>
                )}
              </CardTitle>
              <CardDescription>
                Your selected subscription plan
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-3xl font-bold">
                  ${price}
                  <span className="text-lg font-normal text-gray-500">
                    /{interval === 'yearly' ? 'year' : 'month'}
                  </span>
                </div>
                {interval === 'yearly' && (
                  <div className="text-sm text-green-600 mt-1">
                    Save ${(currentPlan.monthly * 12) - currentPlan.yearly} per year
                  </div>
                )}
              </div>

              <div>
                <h4 className="font-medium mb-2">What's included:</h4>
                <ul className="space-y-1">
                  {currentPlan.features.map((feature, index) => (
                    <li key={index} className="flex items-center text-sm">
                      <Check className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>

              <div className="bg-blue-50 p-3 rounded-lg">
                <div className="flex items-center text-blue-800">
                  <Shield className="h-4 w-4 mr-2" />
                  <span className="text-sm font-medium">14-Day Free Trial</span>
                </div>
                <p className="text-xs text-blue-600 mt-1">
                  No charges until your trial ends. Cancel anytime.
                </p>
              </div>

              {/* Payment Methods */}
              <div>
                <h4 className="font-medium mb-2">Accepted Payment Methods:</h4>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div className="flex items-center space-x-2 p-2 bg-gray-50 rounded">
                    <CreditCard className="h-3 w-3 text-blue-600" />
                    <span>Credit/Debit Cards</span>
                  </div>
                  <div className="flex items-center space-x-2 p-2 bg-gray-50 rounded">
                    <Smartphone className="h-3 w-3 text-green-600" />
                    <span>UPI (PhonePe, GPay)</span>
                  </div>
                  <div className="flex items-center space-x-2 p-2 bg-gray-50 rounded">
                    <Building className="h-3 w-3 text-purple-600" />
                    <span>Net Banking</span>
                  </div>
                  <div className="flex items-center space-x-2 p-2 bg-gray-50 rounded">
                    <Wallet className="h-3 w-3 text-orange-600" />
                    <span>Digital Wallets</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Payment Options */}
          <Card>
            <CardHeader>
              <CardTitle>Choose Your Option</CardTitle>
              <CardDescription>
                Start your free trial or set up billing
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {searchParams.get('cancelled') && (
                <Alert>
                  <AlertDescription>
                    Payment was cancelled. You can still start your free trial.
                  </AlertDescription>
                </Alert>
              )}

              {/* Start Trial Button */}
              <Button
                onClick={handleSkipTrial}
                disabled={isLoading}
                size="lg"
                className="w-full"
                variant="outline"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Starting Trial...
                  </>
                ) : (
                  <>
                    Start 14-Day Free Trial
                  </>
                )}
              </Button>

              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-white px-2 text-muted-foreground">Or</span>
                </div>
              </div>

              {/* Subscribe Now Button */}
              <Button
                onClick={handlePayment}
                disabled={isLoading}
                size="lg"
                className="w-full"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <CreditCard className="mr-2 h-4 w-4" />
                    Subscribe Now - ${price}/{interval === 'yearly' ? 'year' : 'month'}
                  </>
                )}
              </Button>

              <div className="text-center space-y-2">
                <p className="text-xs text-gray-500">
                  Secure payment powered by Stripe
                </p>
                <div className="flex items-center justify-center space-x-2 text-xs text-gray-400">
                  <Shield className="h-3 w-3" />
                  <span>SSL encrypted</span>
                  <span>•</span>
                  <span>Cancel anytime</span>
                </div>
              </div>

              <div className="pt-4 border-t">
                <Link href="/auth/signup">
                  <Button variant="ghost" size="sm" className="w-full">
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back to Plan Selection
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
