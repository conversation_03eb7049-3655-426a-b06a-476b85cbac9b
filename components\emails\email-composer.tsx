'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Send, 
  Save, 
  Eye,
  Paperclip,
  X,
  Mail,
  FileText,

  Loader2,
  CheckCircle
} from 'lucide-react'
import toast from 'react-hot-toast'

interface EmailComposerProps {
  templates: any[]
  customers: any[]
  company: any
  preselectedTemplate?: any
  preselectedRecipient?: any
  entityContext?: any
  entityType?: string
  defaultType?: string
}

export function EmailComposer({
  templates,
  customers,
  company,
  preselectedTemplate,
  preselectedRecipient,
  entityContext,
  entityType,
  defaultType
}: EmailComposerProps) {
  const [emailData, setEmailData] = useState({
    type: defaultType || 'CUSTOM',
    templateId: preselectedTemplate?.id || '',
    toEmail: preselectedRecipient?.email || '',
    toName: preselectedRecipient?.name || '',
    subject: '',
    content: '',
    attachments: []
  })
  const [isSending, setIsSending] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [showPreview, setShowPreview] = useState(false)
  const [error, setError] = useState('')
  const router = useRouter()

  const emailTypes = [
    { value: 'QUOTATION', label: 'Quotation' },
    { value: 'INVOICE', label: 'Invoice' },
    { value: 'CONTRACT', label: 'Contract' },
    { value: 'RECEIPT', label: 'Receipt' },
    { value: 'REMINDER', label: 'Reminder' },
    { value: 'NOTIFICATION', label: 'Notification' },
    { value: 'MARKETING', label: 'Marketing' },
    { value: 'CUSTOM', label: 'Custom' }
  ]

  // Load template when selected
  useEffect(() => {
    if (emailData.templateId) {
      const template = templates.find(t => t.id === emailData.templateId)
      if (template) {
        setEmailData(prev => ({
          ...prev,
          subject: template.subject || '',
          content: processTemplateContent(template.content || '')
        }))
      }
    }
  }, [emailData.templateId, templates])

  // Process template content with variables
  const processTemplateContent = (content: string) => {
    let processedContent = content

    // Replace company variables
    if (company) {
      processedContent = processedContent.replace(/\{\{company\.name\}\}/g, company.name || '')
      processedContent = processedContent.replace(/\{\{company\.email\}\}/g, company.email || '')
      processedContent = processedContent.replace(/\{\{company\.phone\}\}/g, company.phone || '')
      processedContent = processedContent.replace(/\{\{company\.address\}\}/g, company.address || '')
    }

    // Replace recipient variables
    if (preselectedRecipient) {
      processedContent = processedContent.replace(/\{\{customer\.name\}\}/g, preselectedRecipient.name || '')
      processedContent = processedContent.replace(/\{\{customer\.email\}\}/g, preselectedRecipient.email || '')
      processedContent = processedContent.replace(/\{\{customer\.phone\}\}/g, preselectedRecipient.phone || '')
      processedContent = processedContent.replace(/\{\{customer\.company\}\}/g, preselectedRecipient.company || '')
    }

    // Replace entity-specific variables
    if (entityContext) {
      switch (entityType) {
        case 'quotation':
          processedContent = processedContent.replace(/\{\{quotation\.number\}\}/g, entityContext.quotationNumber || '')
          processedContent = processedContent.replace(/\{\{quotation\.total\}\}/g, entityContext.total?.toString() || '')
          processedContent = processedContent.replace(/\{\{quotation\.validUntil\}\}/g, entityContext.validUntil ? new Date(entityContext.validUntil).toLocaleDateString() : '')
          break
        case 'invoice':
          processedContent = processedContent.replace(/\{\{invoice\.number\}\}/g, entityContext.invoiceNumber || '')
          processedContent = processedContent.replace(/\{\{invoice\.total\}\}/g, entityContext.total?.toString() || '')
          processedContent = processedContent.replace(/\{\{invoice\.dueDate\}\}/g, entityContext.dueDate ? new Date(entityContext.dueDate).toLocaleDateString() : '')
          break
        case 'contract':
          processedContent = processedContent.replace(/\{\{contract\.title\}\}/g, entityContext.title || '')
          processedContent = processedContent.replace(/\{\{contract\.value\}\}/g, entityContext.value?.toString() || '')
          processedContent = processedContent.replace(/\{\{contract\.startDate\}\}/g, entityContext.startDate ? new Date(entityContext.startDate).toLocaleDateString() : '')
          break
      }
    }

    return processedContent
  }

  const handleInputChange = (field: string, value: any) => {
    setEmailData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleCustomerSelect = (customerId: string) => {
    const customer = customers.find(c => c.id === customerId)
    if (customer) {
      setEmailData(prev => ({
        ...prev,
        toEmail: customer.email || '',
        toName: customer.name || ''
      }))
    }
  }

  const handleSend = async () => {
    if (!emailData.toEmail || !emailData.subject || !emailData.content) {
      setError('Please fill in all required fields')
      return
    }

    setIsSending(true)
    setError('')

    try {
      const response = await fetch('/api/emails/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...emailData,
          entityId: entityContext?.id,
          entityType
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to send email')
      }

      const result = await response.json()
      toast.success('Email sent successfully!')
      router.push(`/dashboard/emails/${result.id}`)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to send email'
      setError(errorMessage)
      toast.error(errorMessage)
    } finally {
      setIsSending(false)
    }
  }

  const handleSaveDraft = async () => {
    setIsSaving(true)
    try {
      const response = await fetch('/api/emails/draft', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...emailData,
          entityId: entityContext?.id,
          entityType
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to save draft')
      }

      toast.success('Draft saved successfully!')
    } catch (error) {
      toast.error('Failed to save draft')
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Main Composer */}
      <div className="lg:col-span-2 space-y-6">
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Email Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Mail className="h-5 w-5" />
              <span>Email Details</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="type">Email Type</Label>
                <select
                  id="type"
                  value={emailData.type}
                  onChange={(e) => handleInputChange('type', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {emailTypes.map(type => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <Label htmlFor="template">Template (Optional)</Label>
                <select
                  id="template"
                  value={emailData.templateId}
                  onChange={(e) => handleInputChange('templateId', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select a template</option>
                  {templates.map(template => (
                    <option key={template.id} value={template.id}>
                      {template.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="customer">Select Customer</Label>
                <select
                  id="customer"
                  value={preselectedRecipient?.id || ''}
                  onChange={(e) => handleCustomerSelect(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select a customer</option>
                  {customers.map(customer => (
                    <option key={customer.id} value={customer.id}>
                      {customer.name} ({customer.email})
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <Label htmlFor="toEmail">Recipient Email *</Label>
                <Input
                  id="toEmail"
                  type="email"
                  value={emailData.toEmail}
                  onChange={(e) => handleInputChange('toEmail', e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="toName">Recipient Name</Label>
                <Input
                  id="toName"
                  value={emailData.toName}
                  onChange={(e) => handleInputChange('toName', e.target.value)}
                  placeholder="Customer Name"
                />
              </div>
              <div>
                <Label htmlFor="subject">Subject *</Label>
                <Input
                  id="subject"
                  value={emailData.subject}
                  onChange={(e) => handleInputChange('subject', e.target.value)}
                  placeholder="Email subject"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Email Content */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center space-x-2">
                <FileText className="h-5 w-5" />
                <span>Email Content</span>
              </CardTitle>
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowPreview(!showPreview)}
              >
                <Eye className="h-4 w-4 mr-2" />
                {showPreview ? 'Hide Preview' : 'Show Preview'}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <Label htmlFor="content">Message *</Label>
                <textarea
                  id="content"
                  value={emailData.content}
                  onChange={(e) => handleInputChange('content', e.target.value)}
                  rows={15}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter your email content here..."
                />
              </div>

              {showPreview && (
                <div>
                  <Label>Preview</Label>
                  <div className="border rounded-md p-4 bg-gray-50 min-h-[200px] whitespace-pre-wrap">
                    {emailData.content || 'Email content will appear here...'}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <div className="flex items-center justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={handleSaveDraft}
            disabled={isSaving || isSending}
          >
            {isSaving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Draft
              </>
            )}
          </Button>
          <Button
            onClick={handleSend}
            disabled={isSending || isSaving || !emailData.toEmail || !emailData.subject || !emailData.content}
          >
            {isSending ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Sending...
              </>
            ) : (
              <>
                <Send className="h-4 w-4 mr-2" />
                Send Email
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Sidebar */}
      <div className="space-y-6">
        {/* Entity Context */}
        {entityContext && (
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Related {entityType}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                {entityType === 'quotation' && (
                  <>
                    <div><strong>Number:</strong> {entityContext.quotationNumber}</div>
                    <div><strong>Total:</strong> ${entityContext.total}</div>
                    <div><strong>Valid Until:</strong> {new Date(entityContext.validUntil).toLocaleDateString()}</div>
                  </>
                )}
                {entityType === 'invoice' && (
                  <>
                    <div><strong>Number:</strong> {entityContext.invoiceNumber}</div>
                    <div><strong>Total:</strong> ${entityContext.total}</div>
                    <div><strong>Due Date:</strong> {new Date(entityContext.dueDate).toLocaleDateString()}</div>
                  </>
                )}
                {entityType === 'contract' && (
                  <>
                    <div><strong>Title:</strong> {entityContext.title}</div>
                    <div><strong>Value:</strong> ${entityContext.value}</div>
                    <div><strong>Start Date:</strong> {new Date(entityContext.startDate).toLocaleDateString()}</div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Template Variables */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base flex items-center space-x-2">
              <FileText className="h-4 w-4" />
              <span>Available Variables</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <div>
                <div className="font-medium text-gray-900 mb-1">Company</div>
                <div className="space-y-1 text-gray-600">
                  <div><code>{'{{company.name}}'}</code></div>
                  <div><code>{'{{company.email}}'}</code></div>
                  <div><code>{'{{company.phone}}'}</code></div>
                  <div><code>{'{{company.address}}'}</code></div>
                </div>
              </div>
              
              <div>
                <div className="font-medium text-gray-900 mb-1">Customer</div>
                <div className="space-y-1 text-gray-600">
                  <div><code>{'{{customer.name}}'}</code></div>
                  <div><code>{'{{customer.email}}'}</code></div>
                  <div><code>{'{{customer.phone}}'}</code></div>
                  <div><code>{'{{customer.company}}'}</code></div>
                </div>
              </div>

              {entityType && (
                <div>
                  <div className="font-medium text-gray-900 mb-1 capitalize">{entityType}</div>
                  <div className="space-y-1 text-gray-600">
                    {entityType === 'quotation' && (
                      <>
                        <div><code>{'{{quotation.number}}'}</code></div>
                        <div><code>{'{{quotation.total}}'}</code></div>
                        <div><code>{'{{quotation.validUntil}}'}</code></div>
                      </>
                    )}
                    {entityType === 'invoice' && (
                      <>
                        <div><code>{'{{invoice.number}}'}</code></div>
                        <div><code>{'{{invoice.total}}'}</code></div>
                        <div><code>{'{{invoice.dueDate}}'}</code></div>
                      </>
                    )}
                    {entityType === 'contract' && (
                      <>
                        <div><code>{'{{contract.title}}'}</code></div>
                        <div><code>{'{{contract.value}}'}</code></div>
                        <div><code>{'{{contract.startDate}}'}</code></div>
                      </>
                    )}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Email Tips */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Email Tips</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm text-gray-600">
              <div className="flex items-start space-x-2">
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                <span>Use clear, descriptive subject lines</span>
              </div>
              <div className="flex items-start space-x-2">
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                <span>Personalize with customer variables</span>
              </div>
              <div className="flex items-start space-x-2">
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                <span>Include relevant business context</span>
              </div>
              <div className="flex items-start space-x-2">
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                <span>Preview before sending</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
