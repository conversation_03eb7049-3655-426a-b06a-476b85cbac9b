'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Save, X, Plus } from 'lucide-react'
import toast from 'react-hot-toast'
import Link from 'next/link'

const leadSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  status: z.enum(['NEW', 'CONTACTED', 'QUALIFIED', 'PROPOSAL', 'NEGOTIATION', 'CLOSED_WON', 'CLOSED_LOST']).default('NEW'),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).default('MEDIUM'),
  value: z.number().min(0).optional(),
  source: z.string().optional(),
  customerId: z.string().min(1, 'Customer is required'),
})

type LeadFormData = z.infer<typeof leadSchema>

interface Customer {
  id: string
  name: string
  email: string | null
  company: string | null
}

interface LeadFormProps {
  mode: 'create' | 'edit'
  lead?: any
  customers: Customer[]
  preselectedCustomerId?: string
}

export function LeadForm({ mode, lead, customers, preselectedCustomerId }: LeadFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const router = useRouter()

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
  } = useForm<LeadFormData>({
    resolver: zodResolver(leadSchema),
    defaultValues: lead ? {
      title: lead.title || '',
      description: lead.description || '',
      status: lead.status || 'NEW',
      priority: lead.priority || 'MEDIUM',
      value: lead.value || undefined,
      source: lead.source || '',
      customerId: lead.customerId || '',
    } : {
      status: 'NEW',
      priority: 'MEDIUM',
      customerId: preselectedCustomerId || '',
    }
  })

  const onSubmit = async (data: LeadFormData) => {
    setIsLoading(true)
    setError('')

    try {
      const url = mode === 'create' ? '/api/leads' : `/api/leads/${lead.id}`
      const method = mode === 'create' ? 'POST' : 'PUT'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to save lead')
      }

      const result = await response.json()
      
      toast.success(mode === 'create' ? 'Lead created successfully!' : 'Lead updated successfully!')
      
      if (mode === 'create') {
        router.push(`/dashboard/leads/${result.id}`)
      } else {
        router.push('/dashboard/leads')
      }
    } catch (error) {
      console.error('Error saving lead:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to save lead'
      setError(errorMessage)
      toast.error(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    if (mode === 'create') {
      reset()
    } else {
      router.push('/dashboard/leads')
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Lead Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="title">Title *</Label>
            <Input
              id="title"
              {...register('title')}
              placeholder="Lead title or opportunity name"
              disabled={isLoading}
            />
            {errors.title && (
              <p className="text-sm text-red-600 mt-1">{errors.title.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <textarea
              id="description"
              {...register('description')}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Describe the lead opportunity..."
              disabled={isLoading}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="status">Status</Label>
              <select
                id="status"
                {...register('status')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isLoading}
              >
                <option value="NEW">New</option>
                <option value="CONTACTED">Contacted</option>
                <option value="QUALIFIED">Qualified</option>
                <option value="PROPOSAL">Proposal</option>
                <option value="NEGOTIATION">Negotiation</option>
                <option value="CLOSED_WON">Closed Won</option>
                <option value="CLOSED_LOST">Closed Lost</option>
              </select>
            </div>

            <div>
              <Label htmlFor="priority">Priority</Label>
              <select
                id="priority"
                {...register('priority')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isLoading}
              >
                <option value="LOW">Low</option>
                <option value="MEDIUM">Medium</option>
                <option value="HIGH">High</option>
                <option value="URGENT">Urgent</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="value">Estimated Value (₹)</Label>
              <Input
                id="value"
                type="number"
                min="0"
                step="0.01"
                {...register('value', { valueAsNumber: true })}
                placeholder="0.00"
                disabled={isLoading}
              />
            </div>

            <div>
              <Label htmlFor="source">Lead Source</Label>
              <Input
                id="source"
                {...register('source')}
                placeholder="Website, Referral, Cold Call, etc."
                disabled={isLoading}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Customer Information */}
      <Card>
        <CardHeader>
          <CardTitle>Customer Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <div className="flex items-center justify-between mb-2">
              <Label htmlFor="customerId">Customer *</Label>
              <Link href="/dashboard/customers/new">
                <Button type="button" variant="outline" >
                  <Plus className="h-4 w-4 mr-2" />
                  Add New Customer
                </Button>
              </Link>
            </div>
            <select
              id="customerId"
              {...register('customerId')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={isLoading}
            >
              <option value="">Select a customer</option>
              {customers.map((customer) => (
                <option key={customer.id} value={customer.id}>
                  {customer.name} {customer.company && `(${customer.company})`}
                </option>
              ))}
            </select>
            {errors.customerId && (
              <p className="text-sm text-red-600 mt-1">{errors.customerId.message}</p>
            )}
          </div>

          {/* Show selected customer details */}
          {watch('customerId') && (
            <div className="p-3 bg-gray-50 rounded-md">
              {(() => {
                const selectedCustomer = customers.find(c => c.id === watch('customerId'))
                if (!selectedCustomer) return null
                
                return (
                  <div>
                    <p className="font-medium text-gray-900">{selectedCustomer.name}</p>
                    {selectedCustomer.company && (
                      <p className="text-sm text-gray-600">{selectedCustomer.company}</p>
                    )}
                    {selectedCustomer.email && (
                      <p className="text-sm text-gray-600">{selectedCustomer.email}</p>
                    )}
                  </div>
                )
              })()}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex items-center justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={handleCancel}
          disabled={isLoading}
        >
          <X className="h-4 w-4 mr-2" />
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              {mode === 'create' ? 'Creating...' : 'Updating...'}
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              {mode === 'create' ? 'Create Lead' : 'Update Lead'}
            </>
          )}
        </Button>
      </div>
    </form>
  )
}
